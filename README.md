# Overview

This is an open-source benchmarking framework for CTR prediction in online avdertising.

This repository provides a standardized evaluation framework for comparing different CTR prediction models using multiple metrics on public datasets and synthetic dataset.



# Leaderboard

A comprehensive comparison of different models evaluated on 3 public datasets and 1 synthetic dataset.

## Model Performance on Criteo Dataset

| Year↑ | Model     | AUC-ROC    | AUC-PR     | Precision  | Recall     | Accuracy   | MCC        | F1         | Logloss    | MSE        | RMSE       |
| ----- | --------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- |
| <2000 | LSTM      | 0.8048     | 0.6058     | 0.6524     | 0.4040     | 0.7921     | 0.3941     | 0.4990     | 0.4469     | 0.1446     | 0.3802     |
| <2000 | LR        | 0.7930     | 0.5848     | 0.6548     | 0.3554     | 0.7868     | 0.3670     | 0.4607     | 0.4571     | 0.1483     | 0.3851     |
| 2010  | FM        | 0.8074     | 0.6087     | 0.6548     | 0.4059     | 0.7930     | 0.3968     | 0.5012     | 0.4447     | 0.1440     | 0.3794     |
| 2015  | CCPM      | 0.8101     | 0.6145     | 0.6628     | 0.4040     | 0.7946     | 0.4007     | 0.5020     | 0.4417     | 0.1429     | 0.3780     |
| 2016  | XGBoost   | 0.7714     | 0.5557     | 0.6530     | 0.2908     | 0.7787     | 0.3262     | 0.4024     | 0.4739     | 0.1543     | 0.3928     |
| 2016  | PNN       | 0.8134     | 0.6199     | 0.6632     | 0.4151     | 0.7961     | 0.4074     | 0.5106     | 0.4388     | 0.1419     | 0.3767     |
| 2016  | Wide&Deep | 0.8141     | 0.6215     | 0.6629     | **0.4188** | 0.7965     | 0.4094     | **0.5133** | 0.4379     | 0.1416     | 0.3763     |
| 2017  | AFM       | 0.8046     | 0.6050     | 0.6595     | 0.3887     | 0.7919     | 0.3896     | 0.4891     | 0.4467     | 0.1447     | 0.3804     |
| 2017  | NFM       | 0.8089     | 0.6124     | 0.6586     | 0.4061     | 0.7939     | 0.3992     | 0.5024     | 0.4429     | 0.1433     | 0.3786     |
| 2017  | DeepFM    | **0.8142** | **0.6218** | 0.6637     | 0.4184     | **0.7967** | **0.4097** | 0.5132     | **0.4377** | **0.1415** | **0.3762** |
| 2019  | FiGNN     | 0.8137     | 0.6207     | **0.6642** | 0.4152     | 0.7964     | 0.4081     | 0.5110     | 0.4384     | 0.1417     | 0.3765     |
| 2019  | AutoInt   | 0.8116     | 0.6173     | 0.6613     | 0.4126     | 0.7953     | 0.4047     | 0.5081     | 0.4403     | 0.1424     | 0.3774     |
| 2021  | FmFM      | 0.8107     | 0.6152     | 0.6626     | 0.4060     | 0.7948     | 0.4017     | 0.5035     | 0.4412     | 0.1427     | 0.3778     |
| 2023  | GDCN      | 0.8061     | 0.6084     | 0.6575     | 0.3989     | 0.7927     | 0.3943     | 0.4965     | 0.4453     | 0.1442     | 0.3797     |
| 2023  | Uni-CTR   | -          | -          | -          | -          | -          | -          | -          | -          | -          | -          |

## Model Performance on Avazu Dataset

| Year↑ | Model     | AUC-ROC    | AUC-PR     | Precision  | Recall     | Accuracy   | MCC        | F1         | Logloss    | MSE        | RMSE       |
| ----- | --------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- |
| <2000 | LSTM      | 0.7694     | 0.3995     | 0.5836     | 0.1099     | 0.8355     | 0.2003     | 0.1849     | 0.3870     | 0.1217     | 0.3489     |
| <2000 | LR        | 0.7569     | 0.3869     | 0.6115     | 0.0828     | 0.8353     | 0.1804     | 0.1458     | 0.3927     | 0.1233     | 0.3511     |
| 2010  | FM        | 0.7898     | 0.4344     | 0.5813     | 0.1703     | 0.8383     | 0.2507     | 0.2633     | 0.3748     | 0.1180     | 0.3435     |
| 2015  | CCPM      | 0.7906     | 0.4349     | **0.6401** | 0.1097     | 0.8383     | 0.2166     | 0.1872     | 0.3748     | 0.1181     | 0.3437     |
| 2016  | XGBoost   | 0.7417     | 0.3650     | 0.5972     | 0.0636     | 0.8337     | 0.1545     | 0.1149     | 0.4008     | 0.1255     | 0.3542     |
| 2016  | PNN       | **0.7960** | **0.4441** | 0.6157     | 0.1502     | **0.8398** | 0.2468     | 0.2414     | **0.3702** | **0.1167** | **0.3416** |
| 2016  | Wide&Deep | 0.7916     | 0.4375     | 0.6184     | 0.1360     | 0.8390     | 0.2353     | 0.2230     | 0.3731     | 0.1175     | 0.3428     |
| 2017  | AFM       | 0.7782     | 0.4129     | 0.5887     | 0.1204     | 0.8364     | 0.2116     | 0.1999     | 0.3817     | 0.1201     | 0.3466     |
| 2017  | NFM       | 0.7886     | 0.4320     | 0.6235     | 0.1237     | 0.8385     | 0.2255     | 0.2063     | 0.3748     | 0.1181     | 0.3437     |
| 2017  | DeepFM    | 0.7951     | 0.4428     | 0.6169     | 0.1470     | 0.8396     | 0.2445     | 0.2374     | 0.3712     | 0.1169     | 0.3420     |
| 2019  | FiGNN     | 0.7896     | 0.4338     | 0.6113     | 0.1389     | 0.8388     | 0.2355     | 0.2263     | 0.3743     | 0.1179     | 0.3434     |
| 2019  | AutoInt   | 0.7897     | 0.4345     | 0.6113     | 0.1413     | 0.8389     | 0.2377     | 0.2296     | 0.3747     | 0.1180     | 0.3435     |
| 2021  | FmFM      | 0.7909     | 0.4346     | 0.5822     | **0.1719** | 0.8384     | **0.2523** | **0.2653** | 0.3747     | 0.1180     | 0.3435     |
| 2023  | GDCN      | 0.7757     | 0.4065     | 0.5433     | 0.1504     | 0.8343     | 0.2209     | 0.2355     | 0.3842     | 0.1212     | 0.3481     |
| 2023  | Uni-CTR   | -          | -          | -          | -          | -          | -          | -          | -          | -          | -          |

## Model Performance on AntM2C Dataset

| Year↑ | Model        | AUC-ROC    | AUC-PR     | Precision  | Recall     | Accuracy   | MCC        | F1         | Logloss    | MSE        | RMSE       |
| ----- | ------------ | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- |
| <2000 | LSTM         | 0.8378     | 0.8329     | 0.7674     | 0.7117     | 0.7563     | 0.5122     | 0.7385     | 0.5119     | 0.1666     | 0.4082     |
| <2000 | LR           | 0.7516     | 0.7370     | 0.7758     | 0.4443     | 0.6692     | 0.3619     | 0.5650     | 0.6391     | 0.2235     | 0.4728     |
| 2010  | FM           | 0.8383     | 0.8329     | 0.8815     | 0.5203     | 0.7342     | 0.5033     | 0.6544     | 0.5217     | 0.1746     | 0.4178     |
| 2015  | CCPM         | 0.8513     | 0.8419     | 0.7288     | **0.8087** | 0.7619     | 0.5279     | **0.7666** | 0.4895     | 0.1610     | 0.4012     |
| 2016  | XGBoost      | 0.7364     | 0.7714     | **0.9203** | 0.4195     | 0.7017     | 0.4647     | 0.5763     | 0.6375     | 0.2099     | 0.4582     |
| 2016  | PNN          | 0.8532     | 0.8464     | 0.7440     | 0.7882     | 0.7665     | 0.5342     | 0.7655     | 0.4844     | 0.1584     | 0.3980     |
| 2016  | Wide&Deep    | **0.8588** | **0.8515** | 0.7645     | 0.7633     | **0.7719** | **0.5432** | 0.7639     | 0.4794     | **0.1549** | **0.3936** |
| 2017  | AFM          | 0.8528     | 0.8431     | 0.7887     | 0.7124     | 0.7686     | 0.5377     | 0.7486     | 0.4853     | 0.1587     | 0.3984     |
| 2017  | NFM          | 0.8520     | 0.8408     | 0.7884     | 0.7075     | 0.7667     | 0.5341     | 0.7458     | 0.4857     | 0.1587     | 0.3984     |
| 2017  | DeepFM       | 0.8554     | 0.8484     | 0.7496     | 0.7769     | 0.7666     | 0.5336     | 0.7630     | **0.4724** | 0.1552     | 0.3939     |
| 2019  | FiGNN        | 0.8500     | 0.8400     | 0.7511     | 0.7739     | 0.7667     | 0.5335     | 0.7623     | 0.4839     | 0.1584     | 0.3980     |
| 2019  | AutoInt      | 0.8534     | 0.8430     | 0.7711     | 0.7424     | 0.7689     | 0.5371     | 0.7565     | 0.4792     | 0.1568     | 0.3960     |
| 2021  | FmFM         | 0.8474     | 0.8385     | 0.7686     | 0.7355     | 0.7650     | 0.5293     | 0.7517     | 0.4842     | 0.1587     | 0.3984     |
| 2023  | GDCN         | 0.8224     | 0.8179     | 0.7511     | 0.6849     | 0.7379     | 0.4755     | 0.7165     | 0.5181     | 0.1720     | 0.4147     |
| 2023  | Uni-CTR      | 0.8191     | 0.8071     | 0.7578     | 0.6269     | 0.7250     | 0.4517     | 0.6862     | 0.5097     | 0.1709     | 0.4134     |
| 2024  | Qwen2.5-1.5B | 0.4570     | 0.4511     | 0.4350     | 0.2972     | 0.4722     | -0.0723    | 0.3435     | 0.7043     | 0.2555     | 0.5055     |

## Model Performance on Synthetic Dataset

| Year↑ | Model     | AUC-ROC    | AUC-PR     | Precision  | Recall     | Accuracy   | MCC        | F1         | Logloss    | MSE        | RMSE       |
| ----- | --------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- | ---------- |
| <2000 | LSTM      | 0.9063     | 0.7753     | 0.6745     | 0.7064     | 0.8413     | 0.5838     | 0.6900     | 0.3153     | 0.1020     | 0.3193     |
| <2000 | LR        | 0.9045     | 0.7709     | **0.7104** | 0.6052     | 0.8396     | 0.5532     | 0.6536     | 0.3300     | 0.1061     | 0.3257     |
| 2010  | FM        | 0.9055     | 0.7733     | 0.6968     | 0.6332     | 0.8394     | 0.5594     | 0.6635     | 0.3213     | 0.1033     | 0.3214     |
| 2015  | CCPM      | 0.9063     | 0.7753     | 0.6750     | 0.7036     | 0.8412     | 0.5826     | 0.6890     | 0.3151     | 0.1019     | 0.3192     |
| 2016  | XGBoost   | 0.9050     | 0.7722     | 0.6914     | 0.6467     | 0.8395     | 0.5632     | 0.6683     | 0.3221     | 0.1036     | 0.3218     |
| 2016  | PNN       | **0.9064** | **0.7755** | 0.6719     | 0.7148     | **0.8415** | 0.5865     | 0.6927     | **0.3149** | **0.1018** | **0.3191** |
| 2016  | Wide&Deep | **0.9064** | **0.7755** | 0.6716     | 0.7162     | **0.8415** | 0.5870     | 0.6932     | **0.3149** | **0.1018** | **0.3191** |
| 2017  | AFM       | 0.9053     | 0.7734     | 0.6903     | 0.6559     | 0.8404     | 0.5675     | 0.6726     | 0.3195     | 0.1029     | 0.3208     |
| 2017  | NFM       | **0.9064** | **0.7755** | 0.6721     | 0.7139     | 0.8414     | 0.5862     | 0.6924     | 0.3150     | **0.1018** | **0.3191** |
| 2017  | DeepFM    | **0.9064** | **0.7755** | 0.6727     | 0.7113     | 0.8413     | 0.5852     | 0.6915     | **0.3149** | **0.1018** | **0.3191** |
| 2019  | FiGNN     | **0.9064** | 0.7754     | 0.6711     | **0.7169** | 0.8414     | **0.5871** | **0.6933** | 0.3150     | 0.1019     | **0.3191** |
| 2019  | AutoInt   | **0.9064** | **0.7755** | 0.6715     | 0.7160     | 0.8414     | 0.5868     | 0.6930     | **0.3149** | **0.1018** | **0.3191** |
| 2021  | FmFM      | 0.9055     | 0.7734     | 0.6967     | 0.6335     | 0.8394     | 0.5596     | 0.6636     | 0.3212     | 0.1032     | 0.3213     |
| 2023  | GDCN      | 0.9063     | 0.7753     | 0.6725     | 0.7117     | 0.8413     | 0.5853     | 0.6916     | 0.3151     | 0.1019     | 0.3192     |
| 2023  | Uni-CTR   | -          | -          | -          | -          | -          | -          | -          | -          | -          | -          |



# Dependencies

```
pip install -r requirements.txt 
```



# Datasets

You can download the datasets from the official websites. In this paper we download Criteo and Avazu from https://github.com/reczoo/Datasets.

- Criteo, [official website](https://www.kaggle.com/c/criteo-display-ad-challenge)
- Avazu, [official website](https://www.kaggle.com/c/avazu-ctr-prediction)
- AntM2C, [official website](https://www.atecup.cn/OfficalDataSet)
- Synthetic dataset *(need code to generate)*



# Quick Start

After configuring the environment, download dataset, you can start training and evaluation as follows:

```
cd scripts
python train.py
```



# Script Description

```
├── LLM                                                # Uni-CTR codes
│   ├── Layers                                         
│   │   ├── activation.py                              # activation networks
│   │   ├── core.py                                    # core networks including ladders
│   │   ├── interaction.py                             # modules for single-domain models
│   │   ├── sequence.py                                # sequence processing networks
│   │   └── utils.py                                   # other data processing methods and additional 
│   ├── config.py                                    	 # configuration for Uni-CTR
│   ├── config_zero.py                                 # configuration for zero-shot
│   ├── main.py                       	               # train file for Uni-CTR
│   ├── metrics.py                       	             # evaluation metrics
│   ├── process_antm2c.py.py                       	   # data process for antm2c to train Uni-CTR
│   ├── process_antm2c_for_tradition_ctr.py            # data process for antm2c to train traditional models
│   ├── utils.py                                       # other data process methods
│   └── zero_shot.py                                   # zero-shot inference
├── scripts                                            # traditional models codes
│   ├── process_antm2c_data.py                         # antm2c split
│   ├── train.py                                       # train and evaluate model
│   └── train_xgboost.py                               # only for xgboost, train and evaluate
├── src                                                
│   ├── data_process                                   # data preprocess
│   │   ├── data_loader.py                             # data loader code
│   │   ├── data_processor.py                          # data process code
│   │   └── utils.py                                   # basic function
│   ├── layers                                         # folder contains networks for traditional models
│   ├── models                                         # folder contains all the models
│   ├── synthetic                                      
│   │   ├── analyze_data.py                            # draw graphs to analyze data distribution
│   │   ├── create_synthetic_pipeline.py               # pipeline for synthetic dataset generation
│   │   ├── synthetic_configs.py                       # configuration for data features
│   │   └── synthetic_ctr_generator.py                 # calculate score and click
│   └── metrics.py                                     # evaluation metrics
├── Logs
│   ├── AntM2C_logs                                    # Logs for AntM2C
│   ├── Avazu_logs                                     # Logs for Avazu
│   ├── Criteo_logs                                    # Logs for Criteo
│   └── Synthetic_logs                                 # Logs for Synthetic dataset
└── requirements.txt                                 	 # package requirements
```



# Training and Evaluation Process

## Traditional models

Change the parameters and run the code as follows:

```
cd scripts
python train.py
```



If you want to train the model using AntM2C dataset, you need to first download the dataset, and then run the code as follows:

```
cd LLM
python process_antm2c_for_tradition_ctr.py #define the data volume by yourself in this code
cd ../scripts
python process_antm2c_data.py #split the data into train.csv, test.csv and valid.csv
```



## LLM-based models

Change the parameters and run the code as follows to get training data:

```
cd LLM
python process_antm2c.py #define the data volume by yourself in this code
```



Define parameters in this code:

```
config.py
```



Train the model, just use the code:

```
python main.py
```



## Synthetic dataset generation

Run the code as follows to generate synthetic dataset:

```
cd src/synthetic
python create_synthetic_pipeline.py #define the data volume by yourself in this code
```



Change the parameters in this code:

```
synthetic_configs.py           
```



Change the generation rules in this code:

```
synthetic_ctr_generator.py
```



# Logs

We put the logs of Criteo, Avazu, AntM2C and synthetic dataset in Logs folder.

Please note the the reuslts in the paper is the average results across two experimental runs conducted with random seeds 2019 and 2020 for Criteo, Avazu and synthetic dataset.

You can check the logs use this code:

```
cd Logs
cd AntM2C_logs
vim AntM2C_AFM_2019_20241121_0746.log #this is the log for AFM model with seed 2019
```



# License

TO DO 



# Contact

TO DO 



---

For questions or issues, please open an issue in this repository.
