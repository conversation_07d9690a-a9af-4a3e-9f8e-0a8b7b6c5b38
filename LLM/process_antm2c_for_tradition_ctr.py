import pandas as pd

# load data
df = pd.read_csv('/data/antm2c/antm2c_10m_part0.bz2', compression='bz2', nrows=10000000)
print(df.shape)
print(df.columns)

# ignore text content
df = df.drop(columns=['bill_entity_seq','service_entity_seq','query_entity_seq','item_entity_names', 'item_title','deep_features_0','deep_features_7','deep_features_5','deep_features_3'])

# ignore sequence feature
df = df.drop(columns=['deep_features_1','deep_features_2','deep_features_4','deep_features_6','deep_features_8','deep_features_9','deep_features_10','deep_features_11','deep_features_12',
                        'deep_features_13','deep_features_15','deep_features_16','deep_features_17','deep_features_18'])
print('drop columns', df.shape)


# get adverting data
df = df[df['scene'] == 2]
print('get scene 2 ', df.shape)


# remove duplicates
df = df.drop_duplicates()
df = df.reset_index(drop=True)
print('remove duplicates ', df.shape)


print(df['label'].value_counts())

print(df.head())

df = df.drop(columns=['scene'])


df.to_csv('/data/antm2c/data_for_traditional_ctr_model.csv', index=False)
