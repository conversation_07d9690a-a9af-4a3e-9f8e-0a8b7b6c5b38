import os
import sys
import random
import pandas as pd
import numpy as np
import json

from sklearn.metrics import roc_auc_score

import torch
from torch import nn
import torch.nn.functional as F
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data import DataLoader
from torch.utils.data.distributed import DistributedSampler
from torch.utils.tensorboard import SummaryWriter
from torch.cuda.amp import GradScaler, autocast

from transformers import <PERSON><PERSON>oken<PERSON>, AutoModel, BertTokenizer, BertModel, get_linear_schedule_with_warmup, LlamaModel

from torchsummary import summary
from tqdm import tqdm

from layers.core import AvgMeter
from layers.utils import get_lr

from metrics import evaluate_metrics
import wandb

def setup_seed(seed):
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True


class Logger(object):
    def __init__(self, filename="output.txt"):
        self.terminal = sys.stdout
        self.log = open(filename, "w", encoding="utf-8", buffering=1)

    def write(self, message):
        self.terminal.write(message)
        self.log.write(message)
        self.log.flush()

    def flush(self):
        if not self.log.closed:
            self.log.flush()

    def close(self):
        if not self.log.closed:
            self.log.close()


def make_train_valid_dfs(data_path):
    df = pd.read_csv(data_path)
    df = df.sort_values(by='log_time', ascending=True)
    train_data = df.iloc[:int(len(df) * 0.9)].copy()
    test_data = df.iloc[int(len(df) * 0.9):].copy()
    valid_data = train_data.iloc[int(len(train_data) * 0.9):].copy()
    train_data = train_data.iloc[:int(len(train_data) * 0.9)].copy()

    return train_data, valid_data, test_data

class BertDataset(torch.utils.data.Dataset):
    def __init__(self, cfg, text_data, text_label, text_scenario, tokenizer):
        self.cfg = cfg
        self.text_data = list(text_data)
        self.text_label = text_label
        self.text_scenario = text_scenario
        self.tokenizer = tokenizer

    def __getitem__(self, idx):
        encoding = self.tokenizer(
            self.text_data[idx],
            add_special_tokens=True,
            truncation=True,
            max_length=self.cfg.max_length,
            return_token_type_ids=False,
            pad_to_max_length=True,
            return_attention_mask=True,
            return_tensors='pt',
        )
        item = {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'scenario': torch.tensor(self.text_scenario[idx], dtype=torch.int),
            'label': torch.tensor(self.text_label[idx], dtype=torch.int)
        }
        return item

    def __len__(self):
        return len(self.text_data)


def build_loader(cfg, text_input, tokenizer, mode):
    dataset = BertDataset(
        cfg,
        text_input['content'].values,
        text_input['label'].values,
        text_input['scenario'].values,
        tokenizer=tokenizer,
    )

    if mode == "train":
        sampler = DistributedSampler(dataset, shuffle=False)
        shuffle = False

        dataloader = torch.utils.data.DataLoader(
            dataset,
            batch_size=cfg.nlp_finetune_batch_size,
            num_workers=cfg.num_workers,
            pin_memory=True,
            shuffle=shuffle,
            sampler=sampler,
            drop_last=True
        )
    else:
        shuffle = False

        dataloader = torch.utils.data.DataLoader(
            dataset,
            batch_size=cfg.nlp_finetune_batch_size,
            num_workers=cfg.num_workers,
            pin_memory=True,
            shuffle=shuffle,
            drop_last=True
        )
    return dataloader


def train_epoch(cfg, model, train_loader, optimizer, lr_scheduler, step, loss_fnc, new_scenario_id=None):
    loss_meter = AvgMeter()
    auc_meter = AvgMeter()
    specific_loss_meter = AvgMeter()
    general_loss_meter = AvgMeter()

    tqdm_object = tqdm(train_loader, total=len(train_loader))
    scaler = GradScaler() if cfg.mixed_precision else None

    all_labels = []
    all_predictions = []

    if cfg.mixed_precision:
        for i, batch in enumerate(tqdm_object):

            ids = batch['input_ids'].to(cfg.device)
            mask = batch['attention_mask'].to(cfg.device)
            scenario = batch['scenario'].unsqueeze(1).to(cfg.device)
            label = batch['label'].unsqueeze(1).to(cfg.device)

            count = batch['label'].size(0)

            with autocast():
                # print(summary(model, input_size=(ids.shape, mask.shape, scenario.shape)))
                output, _, general_out = model(
                    input_ids=ids,
                    attention_mask=mask,
                    scenario_id=scenario,
                )
                specific_loss = loss_fnc(output, label.float())
                general_loss = loss_fnc(general_out, label.float())
                total_loss = specific_loss + general_loss

                specific_loss_meter.update(specific_loss.item(), count)
                general_loss_meter.update(general_loss.item(), count)
                loss_meter.update(total_loss.item(), count)

                if i % 10 == 0: 
                    with torch.no_grad():
                        specific_preds = torch.sigmoid(output)
                        general_preds = torch.sigmoid(general_out)
                        print("\nBatch", i)
                        print(f"Label distribution: {label.float().mean():.4f}")
                        print(f"Specific predictions mean: {specific_preds.mean():.4f}, std: {specific_preds.std():.4f}")
                        print(f"General predictions mean: {general_preds.mean():.4f}, std: {general_preds.std():.4f}")
                        print(f"Specific Loss: {specific_loss.item():.4f}")
                        print(f"General Loss: {general_loss.item():.4f}")

            scaler.scale(total_loss).backward()

            # 打印梯度信息
            if i % 10 == 0: 
                print("\nGradient Analysis:")
                for name, param in model.named_parameters():
                    if param.grad is not None:
                        grad_norm = param.grad.norm()
                        print(f"{name}: grad_norm = {grad_norm}")

            if (i + 1) % cfg.accumulation_steps == 0 or (i + 1) == len(train_loader):
                scaler.step(optimizer)
                scaler.update()
                optimizer.zero_grad()
                if step == "batch":
                    lr_scheduler.step()
                print(f"\nCurrent learning rate: {get_lr(optimizer):.2e}")

            loss_meter.update(specific_loss.item(), count)

            all_labels.extend(label.detach().cpu().numpy())
            all_predictions.extend(torch.sigmoid(output).detach().cpu().numpy())

            try:
                auc = roc_auc_score(label.detach().cpu().numpy(), torch.sigmoid(output).detach().cpu().numpy())
                auc_meter.update(auc, count)
                tqdm_object.set_postfix(train_loss=f"{loss_meter.avg:.4f}", spec_loss=f"{specific_loss_meter.avg:.4f}", gen_loss=f"{general_loss_meter.avg:.4f}",auc=f"{auc_meter.avg:.4f}",lr=f"{get_lr(optimizer):.2e}")
            except:
                tqdm_object.set_postfix(train_loss=f"{loss_meter.avg:.4f}", spec_loss=f"{specific_loss_meter.avg:.4f}", gen_loss=f"{general_loss_meter.avg:.4f}",lr=f"{get_lr(optimizer):.2e}")

    else:
        for i, batch in enumerate(tqdm_object):

            accumulation_steps = 4

            ids = batch['input_ids'].to(cfg.device)
            mask = batch['attention_mask'].to(cfg.device)
            scenario = batch['scenario'].to(cfg.device)
            label = batch['label'].unsqueeze(1).to(cfg.device)

            output, _, general_out = model(
                input_ids=ids,
                attention_mask=mask,
                scenario_id=scenario,
            )
            if cfg.focal_loss:
                output = output.squeeze(-1).unsqueeze(1)
                output = output.squeeze(-2)
                label = label.float()  # Shape [B,1]
                loss = loss_fnc(output, label)

                general_out = general_out.squeeze()
                general_loss = loss_fnc(general_out, label)
                total_loss = loss + general_loss    
            else:
                print("Initial label shape:", label.shape)
                print("After squeeze:", label.float().squeeze().shape)
                print("Label values:", label)
                output = output.squeeze()  # [B,3]
                general_out = general_out.squeeze()  # [B]
                label = label.float()  # [B]
                
                # Expand label to match output dimension
                label = label.squeeze()  # [3] 
                label = label.unsqueeze(1)  # [3,1]
                label = label.expand(-1, 2)  # [3,3]
                label_general = label[:,0]  # [B]
                
                loss = loss_fnc(output, label)
                general_loss = loss_fnc(general_out, label_general)
                total_loss = loss + general_loss

            count = batch['label'].size(0)
            general_loss_meter.update(general_loss.item(), count)
            loss_meter.update(total_loss.item(), count)

            total_loss = total_loss / accumulation_steps
            total_loss.sum().backward()
            if (i + 1) % accumulation_steps == 0:
                optimizer.step()
                optimizer.zero_grad()
                if step == "batch":
                    lr_scheduler.step()


            all_labels.extend(label.detach().cpu().numpy())
            all_predictions.extend(torch.sigmoid(output).detach().cpu().numpy())
            
            tqdm_object.set_postfix(train_loss=loss_meter.avg, general_loss=general_loss_meter.avg,lr=get_lr(optimizer))

    # Calculate comprehensive metrics at the end of epoch
    SUPPORTED_METRICS = ['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD']
    all_labels = np.array(all_labels).reshape(-1)
    all_predictions = np.array(all_predictions).reshape(-1)
    metrics_results = evaluate_metrics(np.array(all_labels), np.array(all_predictions), metrics=SUPPORTED_METRICS, stage='train')
    print("all_labels",all_labels)
    print("all_predictions",all_predictions)

    print("\nEpoch Summary:")
    print(f"Average Specific Loss: {specific_loss_meter.avg:.4f}")
    print(f"Average General Loss: {general_loss_meter.avg:.4f}")
    print(f"Total Loss: {loss_meter.avg:.4f}")

    return loss_meter, auc_meter


def valid_epoch(cfg, model, loss_fnc):
    scenarios = cfg.train_scenarios
    train_text, valid_text, test_text = make_train_valid_dfs(cfg.data_path)

    tokenizer = AutoTokenizer.from_pretrained(cfg.text_tokenizer, local_files_only=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    valid_loader = build_loader(cfg, valid_text, tokenizer, mode="valid")

    loss_meter = AvgMeter()

    tqdm_object = tqdm(valid_loader, total=len(valid_loader))
    predicts = []
    labels = []

    scenario_predicts = [[] for _ in range(len(scenarios))]
    scenario_labels = [[] for _ in range(len(scenarios))]

    for batch in tqdm_object:
        ids = batch['input_ids'].to(cfg.device)
        mask = batch['attention_mask'].to(cfg.device)
        scenario = batch['scenario'].unsqueeze(1).to(cfg.device)
        label = batch['label'].unsqueeze(1).to(cfg.device)
        output, _, general_out = model(
            input_ids=ids,
            attention_mask=mask,
            scenario_id=scenario,
        )
        loss = loss_fnc(output, label.float())
        count = batch['label'].size(0)

        scenario_cpu = scenario.cpu().data.numpy()

        if cfg.mixed_precision:
            a = torch.sigmoid(output).cpu().data.numpy()
            predicts.extend(a)
            for i in range(len(scenarios)):
                scenario_predicts[i].extend(a[scenario_cpu == scenarios[i]])
        else:
            a = torch.sigmoid(output).cpu().data.numpy()
            predicts.extend(output.cpu().data.numpy())
            for i in range(len(scenarios)):
                scenario_predicts[i].extend(a[scenario_cpu == scenarios[i]])

        b = label.cpu().data.numpy()
        labels.extend(b)
        for i in range(len(scenarios)):
            scenario_labels[i].extend(b[scenario_cpu == scenarios[i]])

        loss_meter.update(loss.item(), count)
        tqdm_object.set_postfix(valid_loss=loss_meter.avg)

    SUPPORTED_METRICS = ['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD']
    metrics_results = evaluate_metrics(np.array(labels), np.array(predicts), metrics=SUPPORTED_METRICS, stage='valid')

    print("Valid auc: ", roc_auc_score(labels, predicts))

    for i in range(len(scenarios)):
        print("valid_auc_scenario_" + str(scenarios[i]) + ": ", roc_auc_score(scenario_labels[i], scenario_predicts[i]))

    return loss_meter, roc_auc_score(labels, predicts)


def test_epoch(cfg, model, loss_fnc):
    all_scenarios = cfg.all_scenarios
    test_scenarios = cfg.test_scenarios

    train_text, valid_text, test_text = make_train_valid_dfs(cfg.data_path)

    tokenizer = AutoTokenizer.from_pretrained(cfg.text_tokenizer, local_files_only=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    test_loader = build_loader(cfg, test_text, tokenizer, mode="test")

    loss_meter = AvgMeter()

    tqdm_object = tqdm(test_loader, total=len(test_loader))
    predicts = []
    labels = []

    scenario_predicts = [[] for _ in range(len(test_scenarios))]
    scenario_labels = [[] for _ in range(len(test_scenarios))]

    shared_predicts = [[] for _ in range(len(test_scenarios))]
    shared_labels = [[] for _ in range(len(test_scenarios))]

    for batch in tqdm_object:
        ids = batch['input_ids'].to(cfg.device)
        mask = batch['attention_mask'].to(cfg.device)
        scenario = batch['scenario'].unsqueeze(1).to(cfg.device)
        label = batch['label'].unsqueeze(1).to(cfg.device)
        output, _, general_out = model(
            input_ids=ids,
            attention_mask=mask,
            scenario_id=scenario,
        )
        loss = loss_fnc(output, label.float())
        count = batch['label'].size(0)

        scenario_cpu = scenario.cpu().data.numpy()

        if cfg.mixed_precision:
            a = torch.sigmoid(output).cpu().data.numpy()
            predicts.extend(a)
            for i in range(len(test_scenarios)):
                scenario_predicts[i].extend(a[scenario_cpu == test_scenarios[i]])

            a_s = torch.sigmoid(general_out).cpu().data.numpy()
            for i in range(len(test_scenarios)):
                shared_predicts[i].extend(a_s[scenario_cpu == test_scenarios[i]])
        else:
            predicts.extend(output.cpu().data.numpy())

        b = label.cpu().data.numpy()
        labels.extend(b)
        for i in range(len(test_scenarios)):
            scenario_labels[i].extend(b[scenario_cpu == test_scenarios[i]])

        for i in range(len(test_scenarios)):
            shared_labels[i].extend(b[scenario_cpu == test_scenarios[i]])

        loss_meter.update(loss.item(), count)

        tqdm_object.set_postfix(test_loss=loss_meter.avg)


    SUPPORTED_METRICS = {'AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'}
    metrics_results = evaluate_metrics(np.array(labels), np.array(predicts), metrics=SUPPORTED_METRICS, stage='test')

    print("test auc: ", roc_auc_score(labels, predicts))

    return loss_meter


def train(cfg, model, rank=None, optimizer=None, lr_scheduler=None, step="epoch", new_scenario_id=None):
    seed = cfg.seed
    data_source = cfg.dataset
    load_path = cfg.load_path
    save_path = cfg.save_path

    n = 1
    while True:
        folder_name = os.path.join(save_path, 'v' + str(n))
        if not os.path.exists(folder_name):
            os.makedirs(folder_name)
            version = n
            save_path = folder_name
            break
        n += 1

    sys.stdout = Logger(os.path.join(save_path, 'output.txt'))
    print("load_path: ", load_path)
    print("save_path: ", save_path)
    for name, value in vars(cfg).items():
        if not name.startswith("__"):
            print(f"{name}: {value}")

    train_text, valid_text, test_text = make_train_valid_dfs(cfg.data_path)
    setup_seed(seed)

    tokenizer = AutoTokenizer.from_pretrained(cfg.text_tokenizer, local_files_only=True, trust_remote_code=True)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    train_loader = build_loader(cfg, train_text, tokenizer, mode="train")
    valid_loader = build_loader(cfg, valid_text, tokenizer, mode="valid")
    test_loader = build_loader(cfg, test_text, tokenizer, mode="test")

    # model = NLP_Model(cfg.text_encoder_model, cfg.scenarios)
    # summary(model)

    if new_scenario_id is not None:
        # 添加新场景并冻结原有模型的参数
        for param in model.parameters():
            param.requires_grad = False
        model.add_new_scenario(new_scenario_id)

    if cfg.use_special_token:
        print("len_tokenizer: ", len(tokenizer))
        print(tokenizer.tokenize("user1"))
        model.nlp_model.resize_token_embeddings(len(tokenizer))

    # Load model to GPU or multiple GPUs if available
    # Using Distributed Data Parallel
    if cfg.distributed:
        device_id = rank % torch.cuda.device_count()
        print("Using Distributed Data Parallel")
        model.to(device_id)
        model = DDP(model, device_ids=[device_id])

        # 加载模型
        if cfg.pretrained:
            print("load model from %s ..." % load_path)
            # 获得上一次的epoch
            current_epoch = torch.load(load_path, map_location=f'cuda:{device_id}')['epoch']
            total_epochs = current_epoch + cfg.epochs
            # 获得模型参数
            model_dict = torch.load(load_path, map_location=f'cuda:{device_id}')['model']
            # 载入参数
            model.module.load_state_dict(model_dict)
            print("load model success!")
        else:
            current_epoch = 0
            total_epochs = cfg.epochs
    else:
        print("Using Data Parallel")
        model.to(cfg.device)
        model = nn.DataParallel(model, device_ids=cfg.device_ids)

        # 加载模型
        if cfg.pretrained:
            print("load model from %s ..." % load_path)
            # 获得上一次的epoch
            current_epoch = torch.load(load_path)['epoch']
            total_epochs = current_epoch + cfg.epochs
            # 获得模型参数
            model_dict = torch.load(load_path)['model']
            # 载入参数
            model.module.load_state_dict(model_dict)
            print("load model success!")
        else:
            current_epoch = 0
            total_epochs = cfg.epochs

    if optimizer is None:
        optimizer = torch.optim.AdamW(model.parameters(), lr=cfg.lr, weight_decay=cfg.weight_decay)
    if lr_scheduler is None:
        lr_scheduler = torch.optim.lr_scheduler.CyclicLR(optimizer, base_lr=cfg.lr, max_lr=cfg.max_lr, step_size_up=5, mode="triangular2", cycle_momentum=False)

    if cfg.mixed_precision:
        loss_fn = nn.BCEWithLogitsLoss()
    else:
        loss_fn = nn.BCELoss()

    print("Begin training ...")

    best_loss = float('inf')
    best_auc = float('inf')

    for epoch in range(current_epoch, total_epochs):
        print(f"Epoch {epoch + 1}")
        print(optimizer.state_dict()['param_groups'][0]['lr'])

        model.train()
        train_loss, train_auc = train_epoch(cfg, model, train_loader, optimizer, lr_scheduler, step, loss_fn)
        lr_scheduler.step()
        wandb.log({'train_loss': train_loss.avg})

        model.eval()
        with torch.no_grad():
            valid_loss, valid_auc = valid_epoch(cfg, model, loss_fn)
            wandb.log({'valid_loss': valid_loss.avg})

            if valid_loss.avg < best_loss or valid_auc > best_auc:
                best_loss = valid_loss.avg
                best_auc = valid_auc
                torch.save({
                    'epoch': epoch,
                    'model': model.module.state_dict(),
                }, os.path.join(save_path, "epoch" + str(((epoch + 1) // 10) * 10) + '.pt'))
                print("Model saved!")

            test_loss = test_epoch(cfg, model, loss_fn)
            wandb.log({'test_loss': test_loss.avg})

def make_config_json_serializable(config):
    serializable_config = {}
    for key, value in config.items():
        # Skip private attributes
        if key.startswith('__'):
            continue
        # Convert non-serializable types to strings or skip them
        try:
            # Try to serialize the value to test if it's JSON serializable
            json.dumps(value)
            serializable_config[key] = value
        except (TypeError, OverflowError):
            # If value is not JSON serializable, convert it to string
            serializable_config[key] = str(value)
    return serializable_config