import pandas as pd
import re
import string

def clean_category_list(categories):
    """
    清洗分类列表，如果元素中包含'|'，则只保留分隔符前的第一部分
    
    Args:
        categories (list): 输入的分类列表
        
    Returns:
        list: 清洗后的分类列表
    """
    def clean_single_category(category):
        """清洗单个分类名称"""
        if (isinstance(category, str)) and ('|' in category):
            return category.split('|')[0]
        return category

    if pd.isna(categories):
        return []
    categories = categories.split(',')
    
    return [clean_single_category(cat) for cat in categories]

def clean_text(text):
    """
    清洗文本数据，执行以下操作：
    1. 删除标点符号
    2. 删除多余空格
    3. 删除特殊字符
    4. 统一单位格式
    
    Args:
        text (str): 输入的文本字符串
        
    Returns:
        str: 清洗后的文本
    """
    
    def standardize_units(text):
        """标准化商品单位"""
        # 处理常见单位写法
        unit_map = {
            'g': '克',
            'kg': '千克',
            'ml': '毫升',
            'l': '升',
            '瓶': '瓶',
            '盒': '盒',
            '片': '片'
        }
        
        # 将 40g, 40ml 等替换为标准格式
        pattern = r'(\d+)([a-zA-Z]+)'
        text = re.sub(pattern, lambda m: m.group(1) + unit_map.get(m.group(2), m.group(2)), text)
        return text
    
    def replace_punctuation(text):
        """将标点符号替换为空格"""
        # 定义中英文标点符号
        punctuation = string.punctuation + "，。！？、；：""''《》【】（）"
        # 将每个标点符号替换为空格
        for p in punctuation:
            text = text.replace(p, ' ')
        return text
    
    def remove_spaces(text):
        """删除多余空格"""
        # 将多个空格替换为单个空格，并删除首尾空格
        return " ".join(text.split())
    
    # 执行清洗步骤
    text = standardize_units(text)          # 统一单位格式
    text = replace_punctuation(text)         # 删除标点符号
    text = remove_spaces(text)              # 删除多余空格
    
    return text

def process_nan(data, nan_list, fill_text="unknown"):
    for col in nan_list:
        # Fill NaN values with 'unknown'
        data[col].fillna(fill_text, inplace=True)
        # Replace empty strings with 'unknown'
        data[col] = data[col].apply(lambda x: fill_text if x == "" else x)
    return data

def process_sequence(seq_str, max_items=3):
    """
    处理序列特征,限制长度并去重
    """
    if pd.isna(seq_str):
        return []
    items = seq_str.split(',')
    # 去重并保持顺序
    seen = set()
    unique_items = [x for x in items if not (x in seen or seen.add(x))]
    return unique_items[:max_items]

def get_rencent_sequence(seq_str, N=2):
    """
    处理序列特征,获取最近N次购物记录
    """
    if seq_str is None:
        return []

    if isinstance(seq_str, list):
        return list(set(seq_str[-N:]))

    if isinstance(seq_str, str):
        items = seq_str.split(',')
        return list(set(items[-N:]))

    return []

def process_text(row):
    """
    生成Qwen模型的提示词模板
    """
    scene_info = f"场景{row['scenario']}: "
    
    # 用户信息
    user_info = f"用户ID为user_{row['user_id']}, "
    user_info += f"用户历史交易为{row['bill_entity_seq']}, "   
    
    # 商品信息 
    item_info = f"当前商品ID为item_{row['item_id']}, "
    item_info += f"当前商品标题为['{row['item_title']}'], "
    item_info += f"当前商品类目为['{row['item_entity_names']}']"
    
    return scene_info + user_info + item_info

def process_text_v2(row):
    """
    生成Qwen模型的提示词模板
    """
    scene_info = f"广告场景{row['scenario']}: "
    
    # 用户信息
    user_info = f"用户ID为user_{row['user_id']}, "
    user_info += f"用户近期历史交易为{row['bill_entity_seq']}, "
    
    # 商品信息 
    item_info = f"当前广告ID为item_{row['item_id']}, "
    item_info += f"当前广告标题为['{row['item_title']}'], "
    item_info += f"当前广告类目为{row['item_entity_names']}"
    
    return scene_info + user_info + item_info

# load data
df = pd.read_csv('/data/antm2c/antm2c_10m_part0.bz2', compression='bz2', nrows=60000)

# ignore deep_features
df = df[['user_id', 'item_id', 'log_time', 'label', 'bill_entity_seq','item_entity_names', 'item_title', 'scene']]
print(df.shape)

# get adverting data
df = df[df['scene'] == 2]
df = df.rename(columns={'scene':'scenario'})
# for one scene to match
df['scenario'] = 0
print('get scene 2 ', df.shape)


# remove duplicates
df = df.drop_duplicates()
df = df.reset_index(drop=True)
print('remove duplicates ', df.shape)

# process nan
df = process_nan(df, ['bill_entity_seq', 'item_entity_names', 'item_title'])

# clean list
df['bill_entity_seq'] = df['bill_entity_seq'].apply(lambda x: clean_category_list(x))

# process sequence
df['bill_entity_seq'] = df['bill_entity_seq'].apply(lambda x: get_rencent_sequence(x))
df['item_entity_names'] = df['item_entity_names'].apply(lambda x: get_rencent_sequence(x))

# process title
df['item_title'] = df['item_title'].apply(lambda x: clean_text(x))
# create prompt
df['content'] = df.apply(process_text_v2, axis=1)


# remove long prompt
df = df[df['content'].str.len() < 600]
print('remove long text', df.shape)

print(df['label'].value_counts())
print(df.shape)
print(df['content'].head())

df.to_csv('/data/antm2c/process_data.csv', encoding='utf-8-sig', index=False)
