from sklearn.metrics import roc_auc_score, log_loss, accuracy_score, precision_score, recall_score, f1_score, matthews_corrcoef, average_precision_score, mean_squared_error
from scipy.stats import entropy
import numpy as np
import logging
import wandb

def evaluate_metrics(y_true, y_pred, metrics, stage, threshold=0.5):
    """
    Evaluate multiple metrics
    
    Args:
        y_true: Ground truth labels
        y_pred: Predicted probabilities
        metrics: List of metric names to evaluate
        stage: Current stage (train/valid/test) for logging
        threshold: Threshold for binary predictions
        
    Returns:
        Dictionary containing computed metrics

    Raises:
        ValueError: If an unsupported metric is provided
    """
    result = dict()

    y_true = np.array(y_true).ravel()
    y_pred = np.array(y_pred).ravel()

    # Define supported metrics
    SUPPORTED_METRICS = {'AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'}
    
    # Check for unsupported metrics
    unsupported_metrics = set(metrics) - SUPPORTED_METRICS
    if unsupported_metrics:
        raise ValueError(f"Unsupported metrics: {unsupported_metrics}! "
                         f"Supported metrics are: {sorted(SUPPORTED_METRICS)}")

    # Convert predictions to binary labels for metrics that need discrete predictions
    y_pred_binary = (y_pred > threshold).astype(np.int32)

    for metric in metrics:
        # Based on confusion matrix
        if metric == 'AUC-ROC':
            result[metric] = roc_auc_score(y_true, y_pred)

        elif metric == 'AUC-PR':
            result[metric] = average_precision_score(y_true, y_pred)

        elif metric == "ACC":
            result[metric] = accuracy_score(y_true, y_pred_binary)
        
        elif metric == 'Precision':
            result[metric] = precision_score(y_true, y_pred_binary)

        elif metric == 'Recall':
            result[metric] = recall_score(y_true, y_pred_binary)

        elif metric == 'F1':
            result[metric] = f1_score(y_true, y_pred_binary)

        elif metric == 'MCC':
            result[metric] = matthews_corrcoef(y_true, y_pred_binary)
        
        # Based on predicted probabilities
        elif metric == 'Logloss':
            result[metric] = log_loss(y_true, y_pred, eps=1e-7)

        elif metric == 'MSE':
            result[metric] = mean_squared_error(y_true, y_pred)

        elif metric == 'RMSE':
            result[metric] = np.sqrt(mean_squared_error(y_true, y_pred))
        
        elif metric == 'COPC':
            actual_clicks = np.sum(y_true)
            predicted_clicks = np.sum(y_pred)
            result[metric] = actual_clicks / predicted_clicks if predicted_clicks > 0 else 0

        elif metric == 'KLD':
            y_true_dist = np.array(y_true) / np.sum(y_true)
            y_pred_dist = np.array(y_pred) / np.sum(y_pred)
            result[metric] = entropy(y_true_dist, y_pred_dist)

    # track metrics
    result = {k: float(v) for k, v in result.items()}

    wandb.log({f"{stage}_{k}": v for k, v in result.items()})
    logging.info(f'{stage} [Metrics] ' + ' - '.join('{}: {:.6f}'.format(k, v) for k, v in result.items()))
    return result
