import sys
import os
from pathlib import Path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))
import torch
import logging
from datetime import datetime
import numpy as np
# Import your original modules
import config_zero as cfg
from main import NLP_Model
from utils import make_train_valid_dfs, build_loader
from transformers import AutoTokenizer
from metrics import evaluate_metrics
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP

import wandb
wandb.login(anonymous="allow")

def zero_shot_evaluate():
    timestamp = datetime.now().strftime('%Y%m%d_%H%M')

    wandb.init(
        project=f"ctr-llm-zero-shot",
        name=f"{timestamp}"
    )

    # Initialize distributed setup
    os.environ['RANK'] = '0'
    os.environ['WORLD_SIZE'] = '1'
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '12355'
    os.environ['LOCAL_RANK'] = '0' 
    dist.init_process_group(backend="nccl")
    torch.cuda.set_device(int(os.environ["LOCAL_RANK"]))
    
    # Setup logging
    log_filename = f"llm_zero_shot_{timestamp}.log"
    log_path = Path('./logs') / log_filename
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[logging.StreamHandler(sys.stdout),
                  logging.FileHandler(log_path)]
    )
    logger = logging.getLogger(__name__)

    # Initialize model
    model = NLP_Model(cfg)
    
    # Move model to GPU and wrap with DDP
    device_id = int(os.environ["LOCAL_RANK"])
    model.to(device_id)
    model = DDP(model, device_ids=[device_id])
    model.eval()  # Set to evaluation mode
    logging.info("load model done!")

    # Load data
    _, _, test_data = make_train_valid_dfs(cfg.data_path)
    logging.info("load data done!")
    
    # Initialize tokenizer
    tokenizer = AutoTokenizer.from_pretrained(
        cfg.text_tokenizer,
        local_files_only=True,
        trust_remote_code=True
    )
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Create test dataloader
    test_loader = build_loader(cfg, test_data, tokenizer, mode="test")
    logging.info("build data done!")

    # Zero-shot evaluation
    all_predictions = []
    all_labels = []
    
    with torch.no_grad():
        for batch in test_loader:
            ids = batch['input_ids'].to(device_id)
            mask = batch['attention_mask'].to(device_id)
            scenario = batch['scenario'].unsqueeze(1).to(device_id)
            label = batch['label'].unsqueeze(1).to(device_id)

            # Get predictions
            output, _, general_out = model(
                input_ids=ids,
                attention_mask=mask,
                scenario_id=scenario,
            )
            
            # Store predictions and labels
            all_predictions.extend(output.cpu().numpy())
            all_labels.extend(label.cpu().numpy())

    # Calculate metrics, get all the metrics

    SUPPORTED_METRICS = ['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD']
    metrics_results = evaluate_metrics(np.array(all_labels), np.array(all_predictions), metrics=SUPPORTED_METRICS, stage='test')

    logger.info(f"Zero-shot metrics: {metrics_results}")
    
    # Clean up
    dist.destroy_process_group()

    wandb.finish()

if __name__ == "__main__":
    zero_shot_evaluate()
    print('finish!')