import pandas as pd
from datetime import datetime


def convert_logtime(datetime_str):
    """
    conver log time to hour, weekday, weekend
    """
    try:
        date_obj = datetime.strptime(datetime_str, '%Y-%m-%d %H:%M:%S')
        time_hour = date_obj.hour
        time_weekday = date_obj.weekday()
        if time_weekday == 6: 
            time_weekday = 0
        else:
            time_weekday += 1
        time_weekend = 1 if time_weekday in [0, 6] else 0
        
        return time_hour, time_weekday, time_weekend
        
    except ValueError as e:
        raise ValueError(f"Invalid datetime format. Expected 'YYYY-MM-DD HH:MM:SS', got '{datetime_str}'") from e

df = pd.read_csv('/data/ctr/AntM2C/data_for_traditional_ctr_model.csv')
print('total data shape', df.shape)

# sort 
df = df.sort_values(by='log_time', ascending=True)
# convert time
df['hour_tmp'] = df['log_time'].apply(lambda x: convert_logtime(x))
df['hour'] = df['hour_tmp'].apply(lambda x: x[0])
df['weekday'] = df['hour_tmp'].apply(lambda x: x[1])
df['weekend'] = df['hour_tmp'].apply(lambda x: x[2])
df = df.drop(columns=['hour_tmp'])

# train, valid, test split
train_data = df.iloc[:int(len(df) * 0.9)].copy()
test_data = df.iloc[int(len(df) * 0.9):].copy()
valid_data = train_data.iloc[int(len(train_data) * 0.9):].copy()
train_data = train_data.iloc[:int(len(train_data) * 0.9)].copy()

print('train', len(train_data))
print('test',len(test_data))
print('valid',len(valid_data))

# save
train_data.to_csv('/data/ctr/AntM2C/train.csv', index=False)
test_data.to_csv('/data/ctr/AntM2C/test.csv', index=False)
valid_data.to_csv('/data/ctr/AntM2C/valid.csv', index=False)





