# =========================================================================
# Copyright (C) 2024. The FuxiCTR Library. All rights reserved.
# Copyright (C) 2022. Huawei Technologies Co., Ltd. All rights reserved.
# 
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# =========================================================================

import torch
from torch import nn
from .embedding import EmbeddingLayer
from .interaction import InnerProductLayer, InnerProductLayer_v2


class LR_Layer(nn.Module):
    def __init__(self, feature_map, final_activation=None, use_bias=True):
        super(LR_Layer, self).__init__()
        self.bias = nn.Parameter(torch.zeros(1), requires_grad=True) if use_bias else None
        self.final_activation = final_activation
        # A trick for quick one-hot encoding in LR
        self.embedding_layer = EmbeddingLayer(feature_map, 1)

    def forward(self, X):
        embed_weights = self.embedding_layer(X)
        output = torch.stack(embed_weights).sum(dim=0)
        if self.bias is not None:
            output += self.bias
        if self.final_activation is not None:
            output = self.final_activation(output)
        return output


class FM_Layer(nn.Module):
    def __init__(self, feature_map, final_activation=None, use_bias=True):
        super(FM_Layer, self).__init__()
        self.inner_product_layer = InnerProductLayer(output="sum")
        self.lr_layer = LR_Layer(feature_map, final_activation=None, use_bias=use_bias)
        self.final_activation = final_activation

    def forward(self, X, feature_emb_list):
        lr_out = self.lr_layer(X)
        dot_out = self.inner_product_layer(feature_emb_list)
        output = dot_out + lr_out
        if self.final_activation is not None:
            output = self.final_activation(output)
        return output


class FM_Layer_v2(nn.Module):
    def __init__(self, feature_map, final_activation=None, use_bias=True):
        super(FM_Layer_v2, self).__init__()
        self.inner_product_layer = InnerProductLayer_v2(feature_map.num_fields, output="sum")
        self.lr_layer = LR_Layer(feature_map, final_activation=None, use_bias=use_bias)
        self.final_activation = final_activation

    def forward(self, X, feature_emb):
        lr_out = self.lr_layer(X)
        dot_sum = self.inner_product_layer(feature_emb)
        output = dot_sum + lr_out
        if self.final_activation is not None:
            output = self.final_activation(output)
        return output

