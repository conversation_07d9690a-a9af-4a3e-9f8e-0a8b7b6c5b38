from typing import Dict, <PERSON>, Union, <PERSON><PERSON>
import numpy as np
import pandas as pd
from dataclasses import dataclass
from scipy.special import expit
from scipy.stats import truncnorm

@dataclass
class FeatureConfig:
    name: str
    feature_type: str  
    distribution: str 
    params: Dict

@dataclass
class TargetConfig:
    """
    Target configuration for features
    """
    value: Union[Dict[str, float], Tuple[float, float, float]]
    is_categorical: bool

@dataclass
class InteractionGroup:
    features: List[str]
    weight: float
    target_values: Dict[str, float]

class SyntheticCTRGenerator:
    def __init__(
        self,
        feature_configs: List[FeatureConfig],
        first_groups: List[InteractionGroup],
        second_groups: List[InteractionGroup],
        third_groups: List[InteractionGroup],
        target_configs: Dict[str, TargetConfig],
        base_ctr: float = 0.01,
        random_seed: int = 2024
    ):
        self.feature_configs = feature_configs
        self.first_groups = first_groups
        self.second_groups = second_groups
        self.third_groups = third_groups
        self.target_configs = target_configs
        self.base_ctr = base_ctr
        self._validate_configs()
        np.random.seed(random_seed)

    def _validate_configs(self):
        feature_names = {fc.name for fc in self.feature_configs}
        
        for group in self.first_groups + self.second_groups + self.third_groups:
            for feature in group.features:
                if feature not in feature_names:
                    raise ValueError(f"Feature {feature} in interaction group not found in feature configs")
    
    def truncated_normal(self, mean, std, size, lower=0, upper=np.inf):
        a, b = (lower - mean) / std, (upper - mean) / std
        return truncnorm.rvs(a, b, loc=mean, scale=std, size=size)

    def _generate_feature_value(self, config: FeatureConfig) -> np.ndarray:
        """
        Generate features
        """
        if config.feature_type == 'categorical':
            return np.random.choice(
                config.params['categories'],
                size=config.params['size']
                    )
        elif config.feature_type == 'numerical':
            if config.name == 'user_age':
                values = self.truncated_normal(
                    mean=config.params['mean'],
                    std=config.params['std'],
                    size=config.params['size'],
                    lower=0,
                    upper=100
                )
                return values
                
            elif config.name == 'item_price':
                values = np.random.lognormal(
                    config.params['mean'],
                    config.params['std'],
                    config.params['size']
                )
                return np.clip(values, 1, 30000)
                
        raise ValueError(f"Unsupported feature type or distribution: {config.feature_type}, {config.distribution}")

    def _calculate_age_similarity(self, values: np.ndarray) -> np.ndarray:
        """
        Process similarity of age feature with enhanced differentiation
        """
        config = self.target_configs['user_age']
        min_val, max_val, optimal = config.value
        
        def calculate_single_similarity(value):
            if value < min_val:
                return 0.3
            elif value > max_val:
                return 0.3
            else:
                dist_to_optimal = abs(value - optimal)
                if dist_to_optimal <= 15:
                    return 1.0
                elif dist_to_optimal <= 20:
                    return 0.8
                else:
                    return max(0.5, 0.6 * np.exp(-0.05 * dist_to_optimal))
                
        return np.array([calculate_single_similarity(v) for v in values])

    def _calculate_price_similarity(self, values: np.ndarray) -> np.ndarray:
        """
        Process similarity of price feature with enhanced differentiation
        """
        config = self.target_configs['item_price']
        min_val, max_val, optimal = config.value
        
        def calculate_single_similarity(value):
            if value < min_val:
                return 0.3
            elif value > max_val:
                return 0.3
            else:
                rel_price = value / optimal
                if 0.5 <= rel_price <= 1.5:  
                    return 1.0
                elif 0.3 <= rel_price <= 2.0:
                    return 0.8
                else:
                    return max(0.5, 0.6 * np.exp(-0.05 * abs(np.log(rel_price))))
                
        return np.array([calculate_single_similarity(v) for v in values])

    def _calculate_similarity(self, values: np.ndarray, feature: str) -> np.ndarray:
        """
        Calculate similarity between feature values and target value with enhanced differentiation
        """
        config = self.target_configs[feature]
        
        if config.is_categorical:
            base_similarity = 0.1
            weights = np.array([max(base_similarity, config.value.get(str(v), base_similarity)) for v in values])
            return weights
        
        if feature == 'user_age':
            return self._calculate_age_similarity(values)
        elif feature == 'item_price':
            return self._calculate_price_similarity(values)
        elif feature == 'historical_ctr':
            return self._calculate_ctr_similarity(values)
        else:
            min_val, max_val, optimal = config.value
            def calculate_default_similarity(value):
                if value < min_val or value > max_val:
                    return 0.1 
                else:
                    rel_dist = abs(value - optimal) / (max_val - min_val)
                    if rel_dist <= 0.3: 
                        return 1.0
                    elif rel_dist <= 0.6:
                        return 0.7
                    else: 
                        return max(0.3, 0.5 * np.exp(-2 * rel_dist))
            
            return np.array([calculate_default_similarity(v) for v in values])

    def _calculate_interaction_impact(self, data: pd.DataFrame, group: InteractionGroup) -> np.ndarray:
        """
        Calculate the impact of feature interactions with enhanced differentiation
        """
        similarities = []
        for feature in group.features:
            similarity = self._calculate_similarity(data[feature].values, feature)
            similarity = np.where(similarity > 0.5, 0.8 + 0.2 * similarity, 0.2 * similarity)
            similarities.append(similarity)
        
        combined_similarity = np.prod(similarities, axis=0)
        
        combined_similarity = np.where(combined_similarity > 0.5, 0.7 + 0.3 * combined_similarity, 0.3 * combined_similarity)
        
        return group.weight * combined_similarity

    def generate_batch(self, batch_size: int) -> pd.DataFrame:
        """
        Generate a batch of synthetic CTR data with natural probability distribution
        """
        data = {}
        for config in self.feature_configs:
            config.params['size'] = batch_size
            data[config.name] = self._generate_feature_value(config)
        
        df = pd.DataFrame(data)
        
        base_probs = np.full(batch_size, self.base_ctr)
        
        first_order_impacts = np.zeros(batch_size)
        for group in self.first_groups:
            impact = self._calculate_interaction_impact(df, group)
            first_order_impacts += impact
        if len(self.first_groups) > 0:
            first_order_impacts = 0.5 * (first_order_impacts / len(self.first_groups))
        
        second_order_impacts = np.zeros(batch_size)
        for group in self.second_groups:
            impact = self._calculate_interaction_impact(df, group)
            second_order_impacts += impact
        if len(self.second_groups) > 0:
            second_order_impacts = 0.2 * (second_order_impacts / len(self.second_groups))
        
        third_order_impacts = np.zeros(batch_size)
        for group in self.third_groups:
            impact = self._calculate_interaction_impact(df, group)
            third_order_impacts += impact
        if len(self.third_groups) > 0:
            third_order_impacts = 0.1 * (third_order_impacts / len(self.third_groups))
        
        combined_scores = base_probs

        if len(self.first_groups) > 0:
            first_order_impacts = first_order_impacts * 2
            combined_scores += first_order_impacts
        
        if len(self.second_groups) > 0:
            second_order_impacts = second_order_impacts * 3
            combined_scores += second_order_impacts
            
        if len(self.third_groups) > 0:
            third_order_impacts = third_order_impacts * 4
            combined_scores += third_order_impacts
        
        combined_scores = np.where(combined_scores > np.median(combined_scores),
                                combined_scores * 1.2,
                                combined_scores * 0.3)

        ctr_probs = np.clip(combined_scores, 0, 1)
        
        print('ctr_probs',ctr_probs)
        
        clicks = np.random.binomial(n=1, p=ctr_probs, size=batch_size)

        df['click'] = clicks
        df['ctr_prob'] = ctr_probs
        
        return df
