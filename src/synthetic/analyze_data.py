import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
import seaborn as sns
from pathlib import Path

def load_and_combine_data(data_dir='/data/ctr/Synthetic'):
    """
    Load all synthetic datasets and combine them for analysis
    """
    data_files = ['train.csv', 'valid.csv', 'test.csv']
    dfs = []
    
    for file in data_files:
        path = Path(data_dir) / file
        df = pd.read_csv(path)
        dfs.append(df)
    
    return pd.concat(dfs, axis=0, ignore_index=True)

def analyze_numerical_distribution(data, column, expected_dist, title):
    """
    Analyze and plot distribution of numerical features
    """
    plt.figure(figsize=(15, 5))
    
    # Plot 1: Histogram with KDE
    plt.subplot(131)
    sns.histplot(data[column], kde=True)
    plt.title(f'Distribution of {title}')
    plt.xlabel(column)
    
    # Plot 2: Q-Q plot
    plt.subplot(132)
    if expected_dist == 'norm':
        stats.probplot(data[column], dist=stats.norm, plot=plt)
    elif expected_dist == 'lognorm':
        shape, loc, scale = stats.lognorm.fit(data[column], floc=0)
        stats.probplot(data[column], dist=stats.lognorm, sparams=(shape, loc, scale), plot=plt)
    plt.title(f'Q-Q Plot for {title}')
    
    # Plot 3: Statistical tests and info
    plt.subplot(133)
    plt.axis('off')
    
    # Calculate statistics
    mean = data[column].mean()
    std = data[column].std()
    skew = data[column].skew()
    kurtosis = data[column].kurtosis()
    
    # Perform Kolmogorov-Smirnov test
    if expected_dist == 'norm':
        _, p_value = stats.kstest(data[column], 'norm', args=(mean, std))
        dist_name = 'Normal'
    elif expected_dist == 'lognorm':
        _, p_value = stats.kstest(data[column], 'lognorm', args=(shape, loc, scale))
        dist_name = 'Log-normal'
    
    # Add text with statistics
    stats_text = (
        f"Distribution Statistics:\n\n"
        f"Expected: {dist_name}\n"
        f"Mean: {mean:.2f}\n"
        f"Std: {std:.2f}\n"
        f"Skewness: {skew:.2f}\n"
        f"Kurtosis: {kurtosis:.2f}\n"
        f"KS-test p-value: {p_value:.4f}"
    )
    plt.text(0.1, 0.7, stats_text, fontsize=10)
    
    plt.tight_layout()
    return plt.gcf()

def analyze_categorical_distributions(data, categorical_columns):
    """
    Analyze distributions of categorical features
    """
    n_cols = 3
    n_rows = (len(categorical_columns) + n_cols - 1) // n_cols
    
    plt.figure(figsize=(20, 5 * n_rows))
    
    for idx, col in enumerate(categorical_columns, 1):
        plt.subplot(n_rows, n_cols, idx)
        
        # Calculate value counts and plot
        value_counts = data[col].value_counts()
        value_counts.plot(kind='bar')
        plt.title(f'Distribution of {col}')
        plt.xticks(rotation=45)
        plt.ylabel('Count')
    
    plt.tight_layout()
    return plt.gcf()

def main():
    # Load data
    print("Loading data...")
    df = load_and_combine_data()
    
    # Analyze numerical features
    print("\nAnalyzing numerical features...")
    
    # Age analysis (Normal distribution)
    age_fig = analyze_numerical_distribution(df, 'user_age', 'norm', {'mean': 30, 'std': 15}, 'User Age')
    age_fig.savefig('age_distribution_analysis.png')
    
    # Price analysis (Log-normal distribution)
    price_fig = analyze_numerical_distribution(df, 'item_price', 'lognorm',{'mean': 4, 'std': 0.5},'Item Price')
    price_fig.savefig('price_distribution_analysis.png')
    
    # Print summary statistics
    print("\nSummary Statistics for Numerical Features:")
    print(df[['user_age', 'item_price']].describe())
    
    # Print click-through rate statistics
    print("\nOverall CTR Statistics:")
    print(f"Average CTR: {df['click'].mean():.4f}")
    print(f"Total Clicks: {df['click'].sum()}")
    print(f"Total Samples: {len(df)}")

if __name__ == "__main__":
    main()