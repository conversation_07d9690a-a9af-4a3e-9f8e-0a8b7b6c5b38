import h5py
import os
import logging
import numpy as np
import pandas as pd
from torch.utils import data

class Dataset(data.Dataset):
    def __init__(self, darray):
        self.darray = darray
        
    def __getitem__(self, index):
        X = self.darray[index, 0:-1]
        y = self.darray[index, -1]
        return X, y
    
    def __len__(self):
        return self.darray.shape[0]


class DataGenerator(data.DataLoader):
    def __init__(self, data_array, batch_size=32, shuffle=False, num_workers=1):
        self.dataset = Dataset(data_array)
        super(DataGenerator, self).__init__(dataset=self.dataset, batch_size=batch_size,
                                            shuffle=shuffle, num_workers=num_workers)
    def __len__(self):
        return int(np.ceil(len(self.dataset) * 1.0 / self.batch_size))
    

class DataIO(object):
    def __init__(self, numeric_features, categorical_features, label_col, output_path, DataProcessor):
        self.numeric_features = numeric_features,
        self.categorical_features = categorical_features
        self.label_col = label_col
        self.output_path = output_path
        self.data_processor = DataProcessor
        self.logger = logging.getLogger(__name__)

    def load_data(self, data_path, use_hdf5=True):
        hdf5_file = data_path[:data_path.lower().rindex('.csv')] + '.h5'
        if use_hdf5 and os.path.exists(hdf5_file):
            try:
                data_array = self.load_hdf5(hdf5_file)
                return data_array
            except:
                self.logger.info(f"Loading h5 file failed!")
        # read csv file
        use_cols = []
        dtype_dict = {}
        if len(self.numeric_features[0]) > 0:
            dtype_dict.update({feat: 'float' for feat in self.numeric_features[0]})
            use_cols.extend(self.numeric_features[0])
        if len(self.categorical_features[0]) > 0:
            dtype_dict.update({feat: 'str' for feat in self.categorical_features})
            use_cols.extend(self.categorical_features)
        # label is 0 or 1
        dtype_dict[self.label_col] = 'float'
        use_cols.extend([self.label_col])
        df = pd.read_csv(data_path, usecols=use_cols, dtype=dtype_dict, memory_map=True)
        # transform features
        data_array = self.data_processor.transform(df)
        if use_hdf5:
            self.save_hdf5(data_array, hdf5_file)
        return data_array

    def save_hdf5(self, data_array, data_path, key="data"):
        self.logger.info(f"Saving h5 data at {data_path}")
        if not os.path.exists(os.path.dirname(data_path)):
            os.makedirs(os.path.dirname(data_path))
        with h5py.File(data_path, 'w') as hf:
            hf.create_dataset(key, data=data_array)

    def load_hdf5(self, data_path, key="data"):
        self.logger.info(f"Load h5 data from {data_path}")
        with h5py.File(data_path, 'r') as hf:
            data_array = hf[key][:]
        return data_array

def data_generator(train_data=None, 
                   valid_data=None, 
                   test_data=None, 
                   batch_size=32, 
                   shuffle_train=None,
                   shuffle_valid=None,
                   shuffle_test=None,
                   use_hdf5=True, 
                   DataProcessor=None, 
                   output_path=None,
                   numeric_features = None,
                   categorical_features = None,
                   label_col = None,
                   ):
    train_gen = None
    valid_gen = None
    test_gen = None
    # convert csv to h5
    data_io = DataIO(numeric_features, categorical_features, label_col, output_path, DataProcessor)
    # load training data
    train_array =  data_io.load_data(train_data, use_hdf5=use_hdf5)
    train_samples = len(train_array)
    # load validation data
    valid_array = data_io.load_data(valid_data, use_hdf5=use_hdf5)
    validation_samples = len(valid_array)
    # load testing data
    test_array = data_io.load_data(test_data, use_hdf5=use_hdf5)
    test_samples = len(test_array)
    # data generation
    train_gen = DataGenerator(train_array, batch_size=batch_size, shuffle=shuffle_train)
    valid_gen = DataGenerator(valid_array, batch_size=batch_size, shuffle=shuffle_valid)
    test_gen = DataGenerator(test_array, batch_size=batch_size, shuffle=shuffle_test)
    logging.info("Train samples: total/{:d}, pos/{:.0f}, neg/{:.0f}, ratio/{:.2f}%" \
                    .format(train_samples, train_array[:, -1].sum(), train_samples-train_array[:, -1].sum(),
                    100 * train_array[:, -1].sum() / train_samples))
    logging.info("Validation samples: total/{:d}, pos/{:.0f}, neg/{:.0f}, ratio/{:.2f}%" \
                    .format(validation_samples, valid_array[:, -1].sum(), validation_samples-valid_array[:, -1].sum(),
                            100 * valid_array[:, -1].sum() / validation_samples))
    logging.info("Test samples: total/{:d}, pos/{:.0f}, neg/{:.0f}, ratio/{:.2f}%" \
                    .format(test_samples, test_array[:, -1].sum(), test_samples-test_array[:, -1].sum(),
                            100 * test_array[:, -1].sum() / test_samples))
    logging.info("Loading data done")
    return train_gen, valid_gen, test_gen