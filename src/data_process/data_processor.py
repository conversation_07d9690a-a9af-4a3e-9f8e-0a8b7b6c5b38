# =========================================================================
# Copyright (C) 2024. The FuxiCTR Library. All rights reserved.
# Copyright (C) 2022. Huawei Technologies Co., Ltd. All rights reserved.
# 
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Modifications:
# - Keep some original functions
# - Change the process pipeline
# - Add new functions to process more datasets
# =========================================================================
import os
import io
import logging
import json
import itertools
import pickle
import numpy as np
import pandas as pd
from collections import Counter, OrderedDict
from datetime import date

class FeatureMap(object):
    def __init__(self, dataset_id):
        self.dataset_id = dataset_id
        self.num_fields = 0
        self.num_features = 0
        self.feature_len = 0
        self.feature_specs = OrderedDict()
        
    def set_feature_index(self):
        idx = 0
        for feature, feature_spec in self.feature_specs.items():
            if feature_spec["type"] != "sequence":
                self.feature_specs[feature]["index"] = idx
                idx += 1
            else:
                seq_indexes = [i + idx for i in range(feature_spec["max_len"])]
                self.feature_specs[feature]["index"] = seq_indexes
                idx += feature_spec["max_len"]
        self.feature_len = idx

    def get_feature_index(self, feature_type=None):
        feature_indexes = []
        if feature_type is not None:
            if not isinstance(feature_type, list):
                feature_type = [feature_type]
            feature_indexes = [feature_spec["index"] for feature, feature_spec in self.feature_specs.items()
                               if feature_spec["type"] in feature_type]
        return feature_indexes

    def load(self, json_file):
        with io.open(json_file, "r", encoding="utf-8") as fd:
            feature_map = json.load(fd, object_pairs_hook=OrderedDict)
        self.num_fields = feature_map["num_fields"]
        self.num_features = feature_map.get("num_features", None)
        self.feature_len = feature_map.get("feature_len", None)
        self.feature_specs = OrderedDict(feature_map["feature_specs"])

    def save(self, json_file):
        if not os.path.exists(os.path.dirname(json_file)):
            os.makedirs(os.path.dirname(json_file))
        feature_map = OrderedDict()
        feature_map["dataset_id"] = self.dataset_id
        feature_map["num_fields"] = self.num_fields
        feature_map["num_features"] = self.num_features
        feature_map["feature_len"] = self.feature_len
        feature_map["feature_specs"] = self.feature_specs
        with open(json_file, "w") as fd:
            json.dump(feature_map, fd, indent=4)

class Tokenizer(object):
    def __init__(self, topk_words=None, na_value=None, min_freq=1, splitter=None, 
                 lower=False, oov_token=0, max_len=0, padding="pre"):
        self._topk_words = topk_words
        self._na_value = na_value
        self._min_freq = min_freq
        self._lower = lower
        self._splitter = splitter
        self.oov_token = oov_token # use 0 for __OOV__
        self.word_counts = Counter()
        self.vocab = dict()
        self.vocab_size = 0 # include oov and padding
        self.max_len = max_len
        self.padding = padding

    def fit_on_texts(self, texts, use_padding=False):
        tokens = list(texts)
        if self._splitter is not None: # for sequence
            text_splits = [text.split(self._splitter) for text in texts if not pd.isnull(text)]
            if self.max_len == 0:
                self.max_len = max(len(x) for x in text_splits)
            tokens = list(itertools.chain(*text_splits))
        if self._lower:
            tokens = [tk.lower() for tk in tokens]
        if self._na_value is not None:
            tokens = [tk for tk in tokens if tk != self._na_value]
        self.word_counts = Counter(tokens)
        words = [token for token, count in self.word_counts.items() if count >= self._min_freq]
        self.word_counts.clear() # empty the dict to save memory
        if self._topk_words:
            words = words[0:self._topk_words]
        self.vocab = dict((token, idx) for idx, token in enumerate(words, 1 + self.oov_token))
        self.vocab["__OOV__"] = self.oov_token
        if use_padding:
            self.vocab["__PAD__"] = len(words) + self.oov_token + 1 # use the last index for __PAD__
        self.vocab_size = len(self.vocab) + self.oov_token
    
    def encode_category(self, categories):
        category_indices = [self.vocab.get(x, self.oov_token) for x in categories]
        return np.array(category_indices)


class DataProcessor(object):
    """
    Process and prepare CTR prediction datasets
    """
    def __init__(self, dataset_name, data_dir, model_name, model_output_path):
        self.dataset_name = dataset_name
        self.data_dir = data_dir
        self.output_folder = os.path.join(model_output_path, model_name, dataset_name)
        self.pickle_file = os.path.join(self.data_dir, "feature_encoder.pkl")
        self.json_file = os.path.join(self.data_dir,  "feature_map.json")
        # Create output folder to save processed data file
        os.makedirs(self.output_folder, exist_ok=True)
        self.feature_map = FeatureMap(dataset_name)
        self.encoders = dict()
        self.logger = logging.getLogger(__name__)
        # Set feature types
        self.set_feature_types()
        
    def set_feature_types(self):
        """
        Set feature types for different datasets
        """
        if self.dataset_name == "Criteo":
            self.numeric_features = ["I1", "I2", "I3", "I4", "I5", "I6", "I7", "I8", "I9", "I10", "I11", "I12", "I13"]
            self.categorical_features = ["C1", "C2", "C3", "C4", "C5", "C6", "C7", "C8", "C9", "C10", "C11", "C12", "C13", "C14", "C15", "C16",
                                         "C17", "C18", "C19", "C20", "C21", "C22", "C23", "C24", "C25", "C26"]
            self.label_col = 'Label'
            self.numeric_normalize = False
            self.min_categr_count = 10
            self.numeric_nan = 0
            self.categorical_nan = ''
            self.numeric_type = 'categorical'
            self.categorical_type = 'categorical'
            self.source = ''
        elif self.dataset_name == "Avazu":
            self.numeric_features = []
            self.categorical_features = ["C1", "banner_pos", "site_id", "site_domain", "site_category", "app_id", "app_domain",
                                         "app_category", "device_id", "device_ip", "device_model", "device_type", "device_conn_type",
                                         "C14", "C15", "C16", "C17", "C18", "C19", "C20", "C21","hour"]
            self.label_col = 'click'
            self.min_categr_count = 1
            self.categorical_nan = ''
            self.numeric_type = ''
            self.categorical_type = 'categorical'
            self.source = ''
        elif self.dataset_name == "AntM2C":
            self.numeric_features = []
            self.categorical_features = ['user_id', 'item_id', 'deep_features_14','deep_features_19', 'deep_features_20', 'deep_features_21',
                                          'deep_features_22', 'deep_features_23', 'deep_features_24','deep_features_25', 'deep_features_26',
                                          'hour','weekday','weekend']
            self.label_col = 'label'
            self.min_categr_count = 1
            self.categorical_nan = ''
            self.numeric_type = ''
            self.categorical_type = 'categorical'
            self.source = ''
        elif self.dataset_name == "Synthetic":
            self.numeric_features = ["user_age", "item_price"]
            self.categorical_features = ['user_gender', 'user_region', 'device_type','ad_category', 'ad_placement','hour_of_day', 'day_of_week', 'scene_type']
            self.label_col = "click"
            self.numeric_normalize = False
            self.min_categr_count = 1
            self.numeric_nan = 0
            self.categorical_nan = ''
            self.numeric_type = 'categorical'
            self.categorical_type = 'categorical'
            self.source = ''
        else:
            raise ValueError(f"Dataset {self.dataset_name} not supported!")
    
    def data_clean(self, df):
        """
        Handles missing values
        """
        if len(self.numeric_features) > 0:
            # use fill_na to fill numeric features
            df[self.numeric_features] = df[self.numeric_features].fillna(self.numeric_nan)

        if len(self.categorical_features) > 0:
         # use '' to fill categorical features
            df[self.categorical_features] = df[self.categorical_features].fillna(self.categorical_nan)
        return df
    
    def convert_to_bucket(self, value):
        """
        Convert value
        """
        if value > 2:
            value = int(np.floor(np.log(value) ** 2))
        else:
            value = int(value)
        return value
    
    def convert_time(self, timestamp):
        """
        Convert the hour to the corresponding time of day.
        """
        date_time = date(int('20' + timestamp[0:2]), int(timestamp[2:4]), int(timestamp[4:6]))
        # convert_hour
        time_hour = int(timestamp[6:8])
        #convert_weekday
        time_weekday = int(date_time.strftime('%w'))
        #convert_weekend
        time_weekend = 1 if date_time.strftime('%w') in ['6', '0'] else 0
        return time_hour, time_weekday, time_weekend
    
    def process_data(self, df):
        """
        Process data by defined methods
        """
        if self.dataset_name == "Criteo":
            for col in self.numeric_features:
                df[col] = df[col].map(self.convert_to_bucket).astype(int)
        elif self.dataset_name == "Avazu":
            # convert time
            df['hour_tmp'] = df['hour'].apply(lambda x: self.convert_time(x))
            df = df.drop(columns=['hour'])
            df['hour'] = df['hour_tmp'].apply(lambda x: x[0])
            df['weekday'] = df['hour_tmp'].apply(lambda x: x[1])
            df['weekend'] = df['hour_tmp'].apply(lambda x: x[2])
            df = df.drop(columns=['hour_tmp'])
            if 'weekday' not in self.categorical_features:
                self.categorical_features.append('weekday')
            if 'weekend' not in self.categorical_features:
                self.categorical_features.append('weekend')
        elif self.dataset_name == "AntM2C":
            pass 
        elif self.dataset_name == "Synthetic":
            df['user_age'] = df['user_age'].astype(float)
            df['item_price'] = df['item_price'].astype(float)
            # conver to bucket
            df['user_age'] = df['user_age'].apply(lambda x: self.convert_to_bucket(x))
            df['item_price'] = df['item_price'].apply(lambda x: self.convert_to_bucket(x))

        return df 
    
    def feature_embedding(self, df):
        """
        Process numeric, categorical features
        """
        # Handle data by type
        if self.numeric_type == 'numeric':
            for col in self.numeric_features:
                self.feature_map.num_fields += 1
                self.feature_map.feature_specs[col] = {"source": self.source, "type": 'numeric'}
                self.logger.info(f"Process numeric column: {col}")
                if self.numeric_normalize == True: 
                    # Add in the future
                    pass

        if self.numeric_type == 'categorical':
            for col in self.numeric_features:
                self.feature_map.num_fields += 1
                self.feature_map.feature_specs[col] = {"source": self.source, "type": 'categorical'}
                self.feature_map.feature_specs[col]["min_categr_count"] = self.min_categr_count
                feature_values = df[col].values
                self.logger.info(f"Process categorical column: {col} ")
                tokenizer = Tokenizer(min_freq=self.min_categr_count, 
                                      na_value=self.numeric_nan)
                tokenizer.fit_on_texts(feature_values, use_padding=False)
                self.encoders[col + "_tokenizer"] = tokenizer
                self.feature_map.num_features += tokenizer.vocab_size
                self.feature_map.feature_specs[col]["vocab_size"] = tokenizer.vocab_size

        if self.categorical_type == 'numeric':
            for col in self.categorical_features:
                self.feature_map.num_fields += 1
                self.feature_map.feature_specs[col] = {"source": self.source, "type": 'numeric'}
                self.logger.info(f"Process numeric column: {col}")
                if self.numeric_normalize == True: 
                    # Add in the future
                    pass

        if self.categorical_type == 'categorical':
            for col in self.categorical_features:
                self.feature_map.num_fields += 1
                self.feature_map.feature_specs[col] = {"source": self.source, "type": 'categorical'}
                self.feature_map.feature_specs[col]["min_categr_count"] = self.min_categr_count
                feature_values = df[col].values
                self.logger.info(f"Process categorical column: {col} ")
                tokenizer = Tokenizer(min_freq=self.min_categr_count, 
                            na_value=self.categorical_nan)
                tokenizer.fit_on_texts(feature_values, use_padding=False)
                self.encoders[col + "_tokenizer"] = tokenizer
                self.feature_map.num_features += tokenizer.vocab_size
                self.feature_map.feature_specs[col]["vocab_size"] = tokenizer.vocab_size

    def save_pickle(self, pickle_file):
        """
        Save processed data to pickle file
        """
        self.logger.info("Pickle feature_encode: " + pickle_file)
        os.makedirs(os.path.dirname(pickle_file), exist_ok=True)
        pickle.dump(self, open(pickle_file, "wb"))

    def process_encoder_map(self):
        """
        Get feature encoder and feature map by training dataset
        """
        self.logger.info("Start get feature encoder and feature map")
        # Load training data
        train_path = os.path.join(self.data_dir, "train.csv")
        
        # Read with appropriate dtypes for memory efficiency
        use_cols = []
        dtype_dict = {}
        if len(self.numeric_features) > 0:
            dtype_dict.update({feat: 'float' for feat in self.numeric_features})
            use_cols.extend(self.numeric_features)
        if len(self.categorical_features) > 0:
            dtype_dict.update({feat: 'str' for feat in self.categorical_features})
            use_cols.extend(self.categorical_features)
        # label is 0 or 1
        dtype_dict[self.label_col] = 'float'
        use_cols.extend([self.label_col])
        df = pd.read_csv(train_path, usecols=use_cols, dtype=dtype_dict, memory_map=True)
        self.logger.info(f"Reading training data from {train_path}")

        # Data preprocess
        # Fill NaN
        df = self.data_clean(df)
        self.logger.info("Fill NaN done!")
        # Process data using defined function
        df = self.process_data(df)
        # Feature embedding
        self.feature_map.num_fields = 0
        self.feature_embedding(df)
        self.logger.info(f"Set feature index")
        self.feature_map.set_feature_index()
        # Save feature encoder
        self.save_pickle(self.pickle_file)
        # Save feature map
        self.feature_map.save(self.json_file)
        self.logger.info("Save feature_map to json: " + self.json_file)

    def transform(self, df):
        self.logger.info("Start process data for training, validation or testing")
        if self.dataset_name == 'Avazu':
            df['weekday'] = ''
            df['weekend'] = ''
        df = self.data_clean(df)    
        self.logger.info("Fill NaN done!")
        df = self.process_data(df)

        self.logger.info("Transform feature")
        data_arrays = []
        for feature, feature_spec in self.feature_map.feature_specs.items():
            feature_type = feature_spec["type"]
            if feature_type == "numeric":
                numeric_array = df.loc[:, feature].fillna(0).apply(lambda x: float(x)).values
                normalizer = self.encoders.get(feature + "_normalizer")
                if normalizer:
                     numeric_array = normalizer.normalize(numeric_array)
                data_arrays.append(numeric_array) 
            elif feature_type == "categorical":
                encoder = feature_spec.get("encoder", "")
                if encoder == "":
                    data_arrays.append(self.encoders.get(feature + "_tokenizer").encode_category(df.loc[:, feature].values))
            elif feature_type == "sequence":
                data_arrays.append(self.encoders.get(feature + "_tokenizer").encode_sequence(df.loc[:, feature].values))
        label_name = self.label_col
        if df[label_name].dtype != np.float64:
            df.loc[:, label_name] = df.loc[:, label_name].apply(lambda x: float(x))
        data_arrays.append(df.loc[:, label_name].values) # add the label column at last
        data_arrays = [item.reshape(-1, 1) if item.ndim == 1 else item for item in data_arrays]
        data_array = np.hstack(data_arrays)
        return data_array