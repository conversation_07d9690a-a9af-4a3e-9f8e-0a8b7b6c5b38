# -*- coding: UTF-8 -*-
import torch
import torch.nn as nn
from .base_model import BaseModel
import sys
from pathlib import Path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))
from layers.gdcn_layer import MultiLayerPerceptron

class GDCNP(nn.Module):
    def __init__(self, field_dims, embed_dim, cn_layers=3, mlp_layers=(400, 400, 400), dropout=0.5):
        super(GDCNP, self).__init__()
        self.embedding = FeaturesEmbedding(field_dims, embed_dim, concat=True)
        if isinstance(embed_dim, int):
            self.embed_output_dim = len(field_dims) * embed_dim
        else:
            self.embed_output_dim = sum(embed_dim)
        self.cross_net = GateCorssLayer(self.embed_output_dim, cn_layers)
        self.mlp = MultiLayerPerceptron(self.embed_output_dim, mlp_layers, output_layer=False, dropout=dropout)
        self.fc = torch.nn.Linear(mlp_layers[-1] + self.embed_output_dim, 1)

    def forward(self, x):
        x_emb = self.embedding(x)
        cross_cn = self.cross_net(x_emb)
        cross_mlp = self.mlp(x_emb)
        pred_y = self.fc(torch.cat([cross_cn, cross_mlp], dim=1))
        return pred_y


class GDCNS(torch.nn.Module):
    def __init__(self, field_dims, embed_dim, cn_layers=3, mlp_layers=(400, 400, 400), dropout=0.5):
        super(GDCNS, self).__init__()
        self.embedding = FeaturesEmbedding(field_dims, embed_dim, concat=True)
        if isinstance(embed_dim, int):
            self.embed_output_dim = len(field_dims) * embed_dim
        else:
            self.embed_output_dim = sum(embed_dim)
        self.cross_net = GateCorssLayer(self.embed_output_dim, cn_layers)
        self.pred_layer = MultiLayerPerceptron(self.embed_output_dim, mlp_layers, output_layer=True,
                                               dropout=dropout)

    def forward(self, x):
        x_embed = self.embedding(x)
        # x_embed = self.embedding(x).view(-1, self.embed_output_dim)
        cross_cn = self.cross_net(x_embed)
        pred_y = self.pred_layer(cross_cn)
        return pred_y


class GateCorssNetwork(torch.nn.Module):
    def __init__(self, field_dims, embed_dim, cn_layers=3):
        super(GateCorssNetwork, self).__init__()
        self.embedding = FeaturesEmbedding(field_dims, embed_dim, concat=True)
        if isinstance(embed_dim, int):
            self.embed_output_dim = len(field_dims) * embed_dim
        else:
            self.embed_output_dim = sum(embed_dim)
        self.cross_net = GateCorssLayer(self.embed_output_dim, cn_layers)
        self.pred_layer = torch.nn.Linear(self.embed_output_dim, 1)

    def forward(self, x):
        x_embed = self.embedding(x)
        cross_cn = self.cross_net(x_embed)
        pred_y = self.pred_layer(cross_cn)
        return pred_y


class GateCorssLayer(nn.Module):
    def __init__(self, input_dim, cn_layers=3):
        super().__init__()
        self.cn_layers = cn_layers
        self.w = torch.nn.ModuleList([
            torch.nn.Linear(input_dim, input_dim, bias=False) for _ in range(cn_layers)
        ])
        self.wg = torch.nn.ModuleList([
            torch.nn.Linear(input_dim, input_dim, bias=False) for _ in range(cn_layers)
        ])

        self.b = torch.nn.ParameterList([torch.nn.Parameter(
            torch.zeros((input_dim,))) for _ in range(cn_layers)])

        for i in range(cn_layers):
            torch.nn.init.uniform_(self.b[i].data)

        self.activation = nn.Sigmoid()
     
    def forward(self, x):
        x0 = x
        for i in range(self.cn_layers):
            xw = self.w[i](x) # Feature Crossing
            xg = self.activation(self.wg[i](x)) # Information Gate
            x = x0 * (xw + self.b[i]) * xg + x
        return x

class FeaturesEmbedding(nn.Module):
    def __init__(self, feature_map, embedding_dim):
        super().__init__()
        self.embed_dict = nn.ModuleDict()
        self.embedding_dim = embedding_dim
        self.feature_map = feature_map
        
        for feature_name, feature_spec in feature_map.feature_specs.items():
            if feature_spec["type"] == "categorical":
                vocab_size = feature_spec["vocab_size"]
                embed = torch.nn.Embedding(vocab_size, embedding_dim)
                torch.nn.init.xavier_uniform_(embed.weight)
                self.embed_dict[feature_name] = embed

    def forward(self, X):
        feature_emb = []
        for feature_name, embedding_layer in self.embed_dict.items():
            feature_idx = X[:, self.feature_map.feature_specs[feature_name]["index"]].long()
            emb = embedding_layer(feature_idx)
            feature_emb.append(emb)
        if len(feature_emb) > 0:
            feature_emb = torch.cat(feature_emb, dim=1)
        else:
            feature_emb = torch.zeros((X.size(0), 0), device=X.device)
        return feature_emb

class GDCN(BaseModel):
    def __init__(self, 
                 feature_map,
                 model_id="GDCN",
                 gpu=-1,
                 task="binary_classification",
                 learning_rate=1e-3,
                 embedding_dim=10,
                 embedding_dropout=0,
                 cn_layers=3,
                 mlp_layers=[400, 400, 400],
                 dropout=0.5,
                 batch_norm=True,
                 embedding_regularizer=None,
                 net_regularizer=None,
                 **kwargs):
        super(GDCN, self).__init__(feature_map,
                                  model_id=model_id,
                                  gpu=gpu,
                                  embedding_regularizer=embedding_regularizer,
                                  net_regularizer=net_regularizer,
                                  **kwargs)
                                  
        self.categorical_feature_count = sum(1 for _, spec in feature_map.feature_specs.items() 
                                          if spec["type"] == "categorical")
        
        self.embedding = FeaturesEmbedding(feature_map, embedding_dim)
        self.embedding_dropout = nn.Dropout(embedding_dropout)
        self.embed_output_dim = self.categorical_feature_count * embedding_dim
        self.cross_net = GateCorssLayer(self.embed_output_dim, cn_layers)
        
        layers = []
        input_dim = self.embed_output_dim
        for i in range(len(mlp_layers)):
            layers.append(nn.Linear(input_dim, mlp_layers[i]))
            if batch_norm:
                layers.append(nn.BatchNorm1d(mlp_layers[i]))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(p=dropout))
            input_dim = mlp_layers[i]
        self.mlp = nn.Sequential(*layers)
        
        self.fc = nn.Linear(mlp_layers[-1] + self.embed_output_dim, 1)
        self.final_activation = self.get_final_activation(task)
        
        self.compile(kwargs["optimizer"], loss=kwargs["loss"], lr=learning_rate)
        
    def forward(self, inputs):
        X, y = self.inputs_to_device(inputs)
        
        if not isinstance(X, torch.Tensor):
            X = torch.tensor(X, device=self.device)
        
        feature_emb = self.embedding(X)
        feature_emb = self.embedding_dropout(feature_emb)
        
        cross_out = self.cross_net(feature_emb)
        mlp_out = self.mlp(feature_emb)
        
        concat_out = torch.cat([cross_out, mlp_out], dim=1)
        y_pred = self.fc(concat_out)
        if self.final_activation is not None:
            y_pred = self.final_activation(y_pred)
            
        loss = self.loss_with_reg(y_pred, y)
        return_dict = {"loss": loss, "y_pred": y_pred}
        return return_dict