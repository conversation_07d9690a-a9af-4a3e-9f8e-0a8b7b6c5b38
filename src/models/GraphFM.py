import torch
import torch.nn as nn
import torch.nn.functional as F
from .base_model import BaseModel
from ..layers import EmbeddingLayer

class GraphAttention(nn.Module):
    def __init__(self, num_fields, embedding_dim, num_heads, k, dropout_keep_prob=0.5):
        super(GraphAttention, self).__init__()
        self.num_fields = num_fields
        self.num_heads = num_heads
        self.k = k
        self.dropout = nn.Dropout(1 - dropout_keep_prob)
        
        # Linear projections for attention
        self.attention = nn.Linear(num_fields, num_heads, bias=False)
        self.value_proj = nn.Linear(embedding_dim, embedding_dim, bias=False)
        self.gsl1 = nn.Linear(embedding_dim, 16)
        self.gsl2 = nn.Linear(16, 1)
        
    def forward(self, values, training=True):
        batch_size = values.shape[0]
        
        # Generate node scoring for top-k selection
        S = F.relu(self.gsl1(values))
        S = torch.sigmoid(self.gsl2(S))  # [batch_size, field_size, 1]
        S = S.squeeze(-1)  # [batch_size, field_size]
        
        # Select top-k nodes
        values, indices = torch.topk(S, k=self.k, dim=1)
        kth = values.min(dim=1, keepdim=True)[0]
        topk_mask = (S >= kth).float()
        S = S * topk_mask
        S = S.unsqueeze(-1)  # [batch_size, field_size, 1]
        
        # Multi-head attention
        H = self.value_proj(values)  # [batch_size, field_size, embedding_dim]
        H_split = torch.stack(H.split(H.shape[-1] // self.num_heads, dim=-1))  # [num_heads, batch_size, field_size, embedding_dim//num_heads]
        
        # Compute attention scores
        A = F.relu(self.attention(values))  # [batch_size, field_size, num_heads]
        A = A.permute(0, 2, 1)  # [batch_size, num_heads, field_size]
        A = A * S.squeeze(-1).unsqueeze(1)  # Apply top-k mask
        A = F.softmax(A, dim=-1)  # [batch_size, num_heads, field_size]
        
        # Apply attention and combine heads
        if training:
            A = self.dropout(A)
        O = torch.matmul(A.unsqueeze(-2), H_split)  # [num_heads, batch_size, 1, embedding_dim//num_heads]
        O = O.squeeze(-2)  # [num_heads, batch_size, embedding_dim//num_heads]
        O = torch.cat(O.unbind(0), dim=-1)  # [batch_size, embedding_dim]
        
        return O, S.transpose(1, 2)  # Return attention output and visibility matrix

class GraphFM(BaseModel):
    def __init__(self, 
                 feature_map,
                 model_id="GraphFM",
                 embedding_dim=10,
                 num_heads=2,
                 gnn_layers=2,
                 block_shape=[16, 16],
                 k=15,
                 use_residual=True,
                 embedding_regularizer=None,
                 net_regularizer=None,
                 **kwargs):
        super(GraphFM, self).__init__(feature_map, 
                                     model_id=model_id, 
                                     embedding_regularizer=embedding_regularizer,
                                     net_regularizer=net_regularizer,
                                     **kwargs)
        self.embedding_dim = embedding_dim
        self.num_fields = feature_map.num_fields
        self.gnn_layers = gnn_layers
        self.block_shape = block_shape
        self.use_residual = use_residual
        
        self.embedding_layer = EmbeddingLayer(feature_map, embedding_dim)
        self.gnn_layers = nn.ModuleList([
            GraphAttention(self.num_fields, 
                         block_shape[min(i, len(block_shape)-1)], 
                         num_heads,
                         k) 
            for i in range(gnn_layers)
        ])
        
        # Output projection
        final_dim = sum(block_shape[:gnn_layers])
        self.prediction = nn.Linear(final_dim, 1, bias=True)
        self.final_activation = self.get_final_activation(kwargs["task"])
        
        # Initialize weights
        self.init_weights()
        self.compile(kwargs["optimizer"], loss=kwargs["loss"], lr=kwargs["learning_rate"])
        
    def forward(self, inputs):
        X, y = self.inputs_to_device(inputs)
        
        # Get embeddings
        feature_emb_list = self.embedding_layer(X)
        feature_emb = torch.stack(feature_emb_list, dim=1)  # [batch_size, num_fields, embedding_dim]
        
        # GNN layers
        h_list = []
        v_list = []
        h = feature_emb
        
        for i, gnn in enumerate(self.gnn_layers):
            h_next, v = gnn(h, training=self.training)
            if self.use_residual:
                h_next = h_next + h
            h = h_next
            h_list.append(h)
            v_list.append(v)
            
        # Concatenate all layers
        final_h = torch.cat(h_list, dim=-1)
        
        # Global pooling and prediction
        pooled = torch.mean(final_h, dim=1)
        logits = self.prediction(pooled)
        
        if self.final_activation is not None:
            y_pred = self.final_activation(logits)
        else:
            y_pred = logits
            
        # Store attention visualization
        self.v_list = torch.stack(v_list, dim=1)
        
        loss = self.loss_with_reg(y_pred, y)
        return {"y_pred": y_pred, "loss": loss}