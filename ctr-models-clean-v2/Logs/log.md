2025-07-04 04:49:15,675 [INFO] __main__: 开始精调的GAN训练 - 温和平衡策略

2025-07-04 04:49:15,675 [INFO] __main__: 参数: {'dataset_name': 'Criteo', 'dataset_path': '/data/Criteo_x4', 'output_dir': '/data/gan', 'noise_dim': 128, 'embedding_dim': 16, 'epochs': 30, 'batch_size': 512, 'generator_lr': 5e-05, 'discriminator_lr': 2e-05, 'initial_temperature': 2.5, 'min_temperature': 1.2, 'temperature_decay': 0.9998, 'max_vocab_size': 10000, 'sample_strategy': 'full', 'max_samples': 500000, 'max_grad_norm': 1.0, 'log_interval': 100, 'save_interval': 5, 'eval_interval': 5, 'seed': 2024, 'num_workers': 2, 'resume': None, 'debug': False}

2025-07-04 04:49:16,199 [INFO] __main__: 加载 Criteo 数据集从 /data/Criteo_x4

2025-07-04 04:49:16,200 [INFO] root: Loading cached processed data from /data/gan/data/processed_Criteo_train_full_500000_10000.pkl

2025-07-04 04:49:16,354 [INFO] src.gan.data_prep: Max vocab size per feature: 10000

2025-07-04 04:49:16,354 [INFO] root: Loaded 500000 cached samples with max_vocab_size=10000

2025-07-04 04:49:16,354 [INFO] __main__: 最终数据集大小: 500000 样本

2025-07-04 04:49:16,355 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}

2025-07-04 04:49:16,355 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520

2025-07-04 04:49:16,355 [INFO] __main__: 特征信息: {'numeric_features': ['I1', 'I2', 'I3', 'I4', 'I5', 'I6', 'I7', 'I8', 'I9', 'I10', 'I11', 'I12', 'I13'], 'categorical_features': ['C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10', 'C11', 'C12', 'C13', 'C14', 'C15', 'C16', 'C17', 'C18', 'C19', 'C20', 'C21', 'C22', 'C23', 'C24', 'C25', 'C26'], 'vocab_sizes': [1032, 529, 10001, 10001, 226, 16, 10001, 448, 5, 10001, 4613, 10001, 3073, 28, 7962, 10001, 12, 3556, 1699, 5, 10001, 16, 17, 10001, 69, 10001], 'label_col': 'Label', 'dataset_name': 'Criteo', 'vocab_info_summary': {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}}

2025-07-04 04:49:16,568 [INFO] __main__: 创建数据加载器，共 976 批次

2025-07-04 04:49:16,578 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}

2025-07-04 04:49:16,578 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520

2025-07-04 04:49:16,578 [INFO] __main__: 创建精调的Generator和Discriminator模型

2025-07-04 04:49:16,679 [INFO] __main__: Generator参数量: 10,183,879

2025-07-04 04:49:16,679 [INFO] __main__: Discriminator参数量: 1,969,266

2025-07-04 04:49:16,679 [INFO] __main__: 参数比例 (G/D): 5.17

2025-07-04 04:49:16,679 [INFO] __main__: 学习率比例 (G/D): 2.50

2025-07-04 04:49:17,275 [INFO] __main__: 开始精调的训练循环

2025-07-04 04:49:17,275 [INFO] __main__: 开始第1/30个epoch (阶段: 0)

2025-07-04 04:49:17,897 [INFO] src.gan.trainer: Epoch 0, Batch 0/976, Stage 0, D_loss: 0.8884, G_loss: -0.1478, Num_std: 0.255694, Num_range: 1.8270, Real_score: 0.5008, Fake_score: 0.4845

2025-07-04 04:49:32,788 [INFO] src.gan.trainer: Epoch 0, Batch 100/976, Stage 0, D_loss: 0.7757, G_loss: -0.1979, Num_std: 0.042657, Num_range: 1.9611, Real_score: 0.5327, Fake_score: 0.4905

2025-07-04 04:49:47,632 [INFO] src.gan.trainer: Epoch 0, Batch 200/976, Stage 0, D_loss: 0.7302, G_loss: -0.2067, Num_std: 0.029949, Num_range: 1.9289, Real_score: 0.5354, Fake_score: 0.5039

2025-07-04 04:50:02,607 [INFO] src.gan.trainer: Epoch 0, Batch 300/976, Stage 0, D_loss: 0.7327, G_loss: -0.1977, Num_std: 0.043173, Num_range: 1.9398, Real_score: 0.5371, Fake_score: 0.5061

2025-07-04 04:50:17,502 [INFO] src.gan.trainer: Epoch 0, Batch 400/976, Stage 0, D_loss: 0.7460, G_loss: -0.1946, Num_std: 0.046343, Num_range: 1.5856, Real_score: 0.5312, Fake_score: 0.5158

2025-07-04 04:50:32,461 [INFO] src.gan.trainer: Epoch 0, Batch 500/976, Stage 0, D_loss: 0.7446, G_loss: -0.1687, Num_std: 0.027120, Num_range: 0.8604, Real_score: 0.5176, Fake_score: 0.5050

2025-07-04 04:50:47,269 [INFO] src.gan.trainer: Epoch 0, Batch 600/976, Stage 0, D_loss: 0.7296, G_loss: -0.1662, Num_std: 0.023741, Num_range: 1.0904, Real_score: 0.5119, Fake_score: 0.5007

2025-07-04 04:51:02,152 [INFO] src.gan.trainer: Epoch 0, Batch 700/976, Stage 0, D_loss: 0.7430, G_loss: -0.1637, Num_std: 0.029235, Num_range: 1.0742, Real_score: 0.5096, Fake_score: 0.5001

2025-07-04 04:51:17,052 [INFO] src.gan.trainer: Epoch 0, Batch 800/976, Stage 0, D_loss: 0.6970, G_loss: -0.1510, Num_std: 0.020866, Num_range: 0.5959, Real_score: 0.5060, Fake_score: 0.4931

2025-07-04 04:51:32,082 [INFO] src.gan.trainer: Epoch 0, Batch 900/976, Stage 0, D_loss: 0.7308, G_loss: -0.1482, Num_std: 0.018449, Num_range: 0.6241, Real_score: 0.5000, Fake_score: 0.4972

2025-07-04 04:51:43,242 [INFO] __main__: 第1个epoch完成. 指标: {'d_loss': 0.7431164262968983, 'd_adv_loss': 0.15615211202778292, 'd_ctr_loss': 0.5861870801595394, 'd_reg_loss': 0.038861714456169344, 'real_d_score': 0.5189759456170876, 'fake_d_score': 0.5054279468098628, 'g_loss': -0.18278902671885136, 'g_adv_loss': 0.24637805957021439, 'g_feature_loss': 0.01541594261839658, 'g_diversity_loss': -0.998879541139133, 'g_numeric_diversity_loss': -0.017120023353373252, 'fake_d_score_g': 0.5049113359689062, 'temperature': 2.4999999999999556, 'numeric_std': 0.03789356812870361, 'numeric_range': 1.3863051874369707, 'training_stage': 0.0}

2025-07-04 04:51:43,243 [INFO] __main__: 开始第2/30个epoch (阶段: 0)

2025-07-04 04:51:43,510 [INFO] src.gan.trainer: Epoch 1, Batch 0/976, Stage 0, D_loss: 0.7005, G_loss: -0.1543, Num_std: 0.022984, Num_range: 0.7627, Real_score: 0.5057, Fake_score: 0.5002

2025-07-04 04:51:58,492 [INFO] src.gan.trainer: Epoch 1, Batch 100/976, Stage 0, D_loss: 0.6538, G_loss: -0.1470, Num_std: 0.022235, Num_range: 0.6179, Real_score: 0.4974, Fake_score: 0.4959

2025-07-04 04:52:13,382 [INFO] src.gan.trainer: Epoch 1, Batch 200/976, Stage 0, D_loss: 0.7253, G_loss: -0.1395, Num_std: 0.023294, Num_range: 0.6446, Real_score: 0.4958, Fake_score: 0.4899

2025-07-04 04:52:28,228 [INFO] src.gan.trainer: Epoch 1, Batch 300/976, Stage 0, D_loss: 0.6819, G_loss: -0.1394, Num_std: 0.037143, Num_range: 1.3762, Real_score: 0.4999, Fake_score: 0.4895

2025-07-04 04:52:43,211 [INFO] src.gan.trainer: Epoch 1, Batch 400/976, Stage 0, D_loss: 0.6990, G_loss: -0.1463, Num_std: 0.057986, Num_range: 1.9561, Real_score: 0.5030, Fake_score: 0.4951

2025-07-04 04:52:58,212 [INFO] src.gan.trainer: Epoch 1, Batch 500/976, Stage 0, D_loss: 0.6715, G_loss: -0.1510, Num_std: 0.113499, Num_range: 1.9662, Real_score: 0.5071, Fake_score: 0.4933

2025-07-04 04:53:13,220 [INFO] src.gan.trainer: Epoch 1, Batch 600/976, Stage 0, D_loss: 0.6349, G_loss: -0.1521, Num_std: 0.143217, Num_range: 1.9944, Real_score: 0.5123, Fake_score: 0.4956

2025-07-04 04:53:28,078 [INFO] src.gan.trainer: Epoch 1, Batch 700/976, Stage 0, D_loss: 0.7106, G_loss: -0.1485, Num_std: 0.128592, Num_range: 1.9943, Real_score: 0.5092, Fake_score: 0.4925

2025-07-04 04:53:42,908 [INFO] src.gan.trainer: Epoch 1, Batch 800/976, Stage 0, D_loss: 0.6726, G_loss: -0.1554, Num_std: 0.070571, Num_range: 1.9943, Real_score: 0.5149, Fake_score: 0.5079

2025-07-04 04:53:57,814 [INFO] src.gan.trainer: Epoch 1, Batch 900/976, Stage 0, D_loss: 0.6875, G_loss: -0.1531, Num_std: 0.068726, Num_range: 1.9914, Real_score: 0.5140, Fake_score: 0.5039

2025-07-04 04:54:09,030 [INFO] __main__: 第2个epoch完成. 指标: {'d_loss': 0.6808205486931769, 'd_adv_loss': 0.15724796731574608, 'd_ctr_loss': 0.522785772523675, 'd_reg_loss': 0.03934042790874111, 'real_d_score': 0.5058300136359499, 'fake_d_score': 0.4953677231354307, 'g_loss': -0.1470133376109308, 'g_adv_loss': 0.25644555883838355, 'g_feature_loss': 0.006126792304493894, 'g_diversity_loss': -0.9974110257796582, 'g_numeric_diversity_loss': -0.0031662591311947346, 'fake_d_score_g': 0.4950247689206259, 'temperature': 2.4994999999999776, 'numeric_std': 0.07441756089696343, 'numeric_range': 1.6186567659342228, 'training_stage': 0.0}

2025-07-04 04:54:09,030 [INFO] __main__: 开始第3/30个epoch (阶段: 0)

2025-07-04 04:54:09,295 [INFO] src.gan.trainer: Epoch 2, Batch 0/976, Stage 0, D_loss: 0.6974, G_loss: -0.1502, Num_std: 0.080684, Num_range: 1.9954, Real_score: 0.5177, Fake_score: 0.5053

2025-07-04 04:54:24,308 [INFO] src.gan.trainer: Epoch 2, Batch 100/976, Stage 0, D_loss: 0.6696, G_loss: -0.1519, Num_std: 0.067918, Num_range: 1.9937, Real_score: 0.5175, Fake_score: 0.5000

2025-07-04 04:54:39,236 [INFO] src.gan.trainer: Epoch 2, Batch 200/976, Stage 0, D_loss: 0.6699, G_loss: -0.1419, Num_std: 0.091620, Num_range: 1.9967, Real_score: 0.5152, Fake_score: 0.4931

2025-07-04 04:54:54,271 [INFO] src.gan.trainer: Epoch 2, Batch 300/976, Stage 0, D_loss: 0.6355, G_loss: -0.1402, Num_std: 0.060711, Num_range: 1.9947, Real_score: 0.5208, Fake_score: 0.4934

2025-07-04 04:55:09,175 [INFO] src.gan.trainer: Epoch 2, Batch 400/976, Stage 0, D_loss: 0.6420, G_loss: -0.1306, Num_std: 0.075817, Num_range: 1.9944, Real_score: 0.5205, Fake_score: 0.4865

2025-07-04 04:55:24,092 [INFO] src.gan.trainer: Epoch 2, Batch 500/976, Stage 0, D_loss: 0.6905, G_loss: -0.1197, Num_std: 0.079827, Num_range: 1.9940, Real_score: 0.5220, Fake_score: 0.4773

2025-07-04 04:55:39,063 [INFO] src.gan.trainer: Epoch 2, Batch 600/976, Stage 0, D_loss: 0.6397, G_loss: -0.1146, Num_std: 0.047377, Num_range: 1.9428, Real_score: 0.5340, Fake_score: 0.4784

2025-07-04 04:55:54,033 [INFO] src.gan.trainer: Epoch 2, Batch 700/976, Stage 0, D_loss: 0.6306, G_loss: -0.0984, Num_std: 0.060826, Num_range: 1.9746, Real_score: 0.5371, Fake_score: 0.4641

2025-07-04 04:56:08,945 [INFO] src.gan.trainer: Epoch 2, Batch 800/976, Stage 0, D_loss: 0.6439, G_loss: -0.1002, Num_std: 0.067132, Num_range: 1.9927, Real_score: 0.5522, Fake_score: 0.4665

2025-07-04 04:56:23,811 [INFO] src.gan.trainer: Epoch 2, Batch 900/976, Stage 0, D_loss: 0.6137, G_loss: -0.0817, Num_std: 0.074349, Num_range: 1.9960, Real_score: 0.5556, Fake_score: 0.4509

2025-07-04 04:56:35,003 [INFO] __main__: 第3个epoch完成. 指标: {'d_loss': 0.6467572793242378, 'd_adv_loss': 0.14503483673496548, 'd_ctr_loss': 0.5010172503038509, 'd_reg_loss': 0.03525956782901694, 'real_d_score': 0.5289620903671765, 'fake_d_score': 0.47783844908852124, 'g_loss': -0.12040829577590122, 'g_adv_loss': 0.27789920345559466, 'g_feature_loss': 0.011192418195550894, 'g_diversity_loss': -0.9976109429746065, 'g_numeric_diversity_loss': -0.0013104208178770197, 'fake_d_score_g': 0.47704060991482994, 'temperature': 2.4990000999999724, 'numeric_std': 0.07419599209119479, 'numeric_range': 1.9863323857346193, 'training_stage': 0.0}

2025-07-04 04:56:35,004 [INFO] __main__: 开始第4/30个epoch (阶段: 0)

2025-07-04 04:56:35,265 [INFO] src.gan.trainer: Epoch 3, Batch 0/976, Stage 0, D_loss: 0.6081, G_loss: -0.0669, Num_std: 0.060612, Num_range: 1.9945, Real_score: 0.5628, Fake_score: 0.4359

2025-07-04 04:56:50,407 [INFO] src.gan.trainer: Epoch 3, Batch 100/976, Stage 0, D_loss: 0.5783, G_loss: -0.0497, Num_std: 0.078035, Num_range: 1.9951, Real_score: 0.5836, Fake_score: 0.4310

2025-07-04 04:57:05,278 [INFO] src.gan.trainer: Epoch 3, Batch 200/976, Stage 0, D_loss: 0.5771, G_loss: -0.0162, Num_std: 0.067818, Num_range: 1.9927, Real_score: 0.5903, Fake_score: 0.4011

2025-07-04 04:57:20,194 [INFO] src.gan.trainer: Epoch 3, Batch 300/976, Stage 0, D_loss: 0.6274, G_loss: 0.0197, Num_std: 0.075813, Num_range: 1.9918, Real_score: 0.6119, Fake_score: 0.3977

2025-07-04 04:57:35,124 [INFO] src.gan.trainer: Epoch 3, Batch 400/976, Stage 0, D_loss: 0.6220, G_loss: 0.0256, Num_std: 0.068294, Num_range: 1.9925, Real_score: 0.6149, Fake_score: 0.3791

2025-07-04 04:57:50,083 [INFO] src.gan.trainer: Epoch 3, Batch 500/976, Stage 0, D_loss: 0.5749, G_loss: 0.0722, Num_std: 0.075580, Num_range: 1.9913, Real_score: 0.6404, Fake_score: 0.3556

2025-07-04 04:58:04,958 [INFO] src.gan.trainer: Epoch 3, Batch 600/976, Stage 0, D_loss: 0.5974, G_loss: 0.1137, Num_std: 0.037130, Num_range: 1.9467, Real_score: 0.6488, Fake_score: 0.3260

2025-07-04 04:58:19,931 [INFO] src.gan.trainer: Epoch 3, Batch 700/976, Stage 0, D_loss: 0.5641, G_loss: 0.1234, Num_std: 0.064062, Num_range: 1.9920, Real_score: 0.6765, Fake_score: 0.3108

2025-07-04 04:58:34,909 [INFO] src.gan.trainer: Epoch 3, Batch 800/976, Stage 0, D_loss: 0.5544, G_loss: 0.1679, Num_std: 0.066006, Num_range: 1.9947, Real_score: 0.6884, Fake_score: 0.2853

2025-07-04 04:58:49,884 [INFO] src.gan.trainer: Epoch 3, Batch 900/976, Stage 0, D_loss: 0.5315, G_loss: 0.1793, Num_std: 0.061785, Num_range: 1.9941, Real_score: 0.6983, Fake_score: 0.2729

2025-07-04 04:59:01,102 [INFO] __main__: 第4个epoch完成. 指标: {'d_loss': 0.5861875739742505, 'd_adv_loss': 0.09345990619774852, 'd_ctr_loss': 0.4921427485762072, 'd_reg_loss': 0.029245999767674044, 'real_d_score': 0.6351211293921118, 'fake_d_score': 0.3561906842362191, 'g_loss': 0.06607871791664344, 'g_adv_loss': 0.437991721484342, 'g_feature_loss': 0.09095320381577858, 'g_diversity_loss': -0.9921187423373178, 'g_numeric_diversity_loss': -0.0011757312339661869, 'fake_d_score_g': 0.3549528314421584, 'temperature': 2.4985002999800185, 'numeric_std': 0.06279986542636841, 'numeric_range': 1.943442887641856, 'training_stage': 0.0}

2025-07-04 04:59:01,103 [INFO] __main__: 开始第5/30个epoch (阶段: 0)

2025-07-04 04:59:01,369 [INFO] src.gan.trainer: Epoch 4, Batch 0/976, Stage 0, D_loss: 0.5246, G_loss: 0.2006, Num_std: 0.062890, Num_range: 1.9916, Real_score: 0.7103, Fake_score: 0.2735

2025-07-04 04:59:15,655 [INFO] src.gan.trainer: 应用温和的平衡调整...

2025-07-04 04:59:15,656 [INFO] src.gan.trainer: 温和调整后 - G学习率: 5.50e-05, D学习率: 1.90e-05, 温度: 2.500

2025-07-04 04:59:16,413 [INFO] src.gan.trainer: Epoch 4, Batch 100/976, Stage 0, D_loss: 0.5687, G_loss: 0.2394, Num_std: 0.062941, Num_range: 1.9903, Real_score: 0.6951, Fake_score: 0.2642

2025-07-04 04:59:31,411 [INFO] src.gan.trainer: Epoch 4, Batch 200/976, Stage 0, D_loss: 0.5662, G_loss: 0.2410, Num_std: 0.064134, Num_range: 1.9894, Real_score: 0.7326, Fake_score: 0.2810

2025-07-04 04:59:46,493 [INFO] src.gan.trainer: Epoch 4, Batch 300/976, Stage 0, D_loss: 0.5735, G_loss: 0.2734, Num_std: 0.059728, Num_range: 1.9882, Real_score: 0.7350, Fake_score: 0.2510

2025-07-04 05:00:01,448 [INFO] src.gan.trainer: Epoch 4, Batch 400/976, Stage 0, D_loss: 0.5631, G_loss: 0.2889, Num_std: 0.060842, Num_range: 1.9900, Real_score: 0.7587, Fake_score: 0.2486

2025-07-04 05:00:16,445 [INFO] src.gan.trainer: Epoch 4, Batch 500/976, Stage 0, D_loss: 0.5414, G_loss: 0.3274, Num_std: 0.072177, Num_range: 1.9812, Real_score: 0.7617, Fake_score: 0.2308

2025-07-04 05:00:31,308 [INFO] src.gan.trainer: Epoch 4, Batch 600/976, Stage 0, D_loss: 0.5212, G_loss: 0.3184, Num_std: 0.064111, Num_range: 1.9908, Real_score: 0.7617, Fake_score: 0.1959

2025-07-04 05:00:46,260 [INFO] src.gan.trainer: Epoch 4, Batch 700/976, Stage 0, D_loss: 0.5284, G_loss: 0.3482, Num_std: 0.072078, Num_range: 1.9887, Real_score: 0.7858, Fake_score: 0.2118

2025-07-04 05:01:01,114 [INFO] src.gan.trainer: Epoch 4, Batch 800/976, Stage 0, D_loss: 0.5113, G_loss: 0.3353, Num_std: 0.059593, Num_range: 1.9888, Real_score: 0.7912, Fake_score: 0.2052

2025-07-04 05:01:15,985 [INFO] src.gan.trainer: Epoch 4, Batch 900/976, Stage 0, D_loss: 0.5435, G_loss: 0.3619, Num_std: 0.070219, Num_range: 1.9854, Real_score: 0.7842, Fake_score: 0.2076

2025-07-04 05:01:27,267 [INFO] __main__: 第5个epoch完成. 指标: {'d_loss': 0.5396075082606965, 'd_adv_loss': 0.05357935194491, 'd_ctr_loss': 0.4852647999393159, 'd_reg_loss': 0.03816782704508688, 'real_d_score': 0.75618896505139, 'fake_d_score': 0.23324661498858765, 'g_loss': 0.2967592517087041, 'g_adv_loss': 0.6172248966421909, 'g_feature_loss': 0.2548371676325066, 'g_diversity_loss': -0.9853919002079886, 'g_numeric_diversity_loss': -0.0013800161050213041, 'fake_d_score_g': 0.23150375567865203, 'temperature': 2.499803337697006, 'numeric_std': 0.05861060612846446, 'numeric_range': 1.9026231010714187, 'training_stage': 0.0}

2025-07-04 05:01:27,267 [INFO] __main__: 在第5个epoch评估精调的GAN

2025-07-04 05:01:27,306 [INFO] __main__: 精调GAN评估指标:

2025-07-04 05:01:27,306 [INFO] __main__:   数值特征质量评分: 0.200

2025-07-04 05:01:27,306 [INFO] __main__:   特征平均标准差: 0.019843

2025-07-04 05:01:27,306 [INFO] __main__:   零标准差特征数: 0.0

2025-07-04 05:01:27,307 [INFO] __main__:   类别特征平均熵: 0.787

2025-07-04 05:01:27,307 [INFO] __main__: 训练质量: 需要改进

2025-07-04 05:01:27,308 [INFO] __main__: 新的最佳数值特征质量: 0.200

2025-07-04 05:01:27,499 [INFO] src.gan.trainer: 检查点已保存到 /data/gan/refined_checkpoint_epoch_5.pt

2025-07-04 05:01:27,684 [INFO] src.gan.trainer: 检查点已保存到 /data/gan/refined_best_model.pt

2025-07-04 05:01:27,685 [INFO] __main__: 保存最佳模型，数值质量评分: 0.200

2025-07-04 05:01:27,685 [INFO] __main__: 开始第6/30个epoch (阶段: 0)

2025-07-04 05:01:27,952 [INFO] src.gan.trainer: Epoch 5, Batch 0/976, Stage 0, D_loss: 0.5240, G_loss: 0.3611, Num_std: 0.055876, Num_range: 1.9844, Real_score: 0.8029, Fake_score: 0.2046

2025-07-04 05:01:43,039 [INFO] src.gan.trainer: Epoch 5, Batch 100/976, Stage 0, D_loss: 0.5072, G_loss: 0.3817, Num_std: 0.044136, Num_range: 1.9760, Real_score: 0.8066, Fake_score: 0.1905

2025-07-04 05:01:58,351 [INFO] src.gan.trainer: Epoch 5, Batch 200/976, Stage 0, D_loss: 0.5541, G_loss: 0.3856, Num_std: 0.031522, Num_range: 1.4520, Real_score: 0.8076, Fake_score: 0.1906

2025-07-04 05:02:13,434 [INFO] src.gan.trainer: Epoch 5, Batch 300/976, Stage 0, D_loss: 0.5354, G_loss: 0.4153, Num_std: 0.061517, Num_range: 1.9854, Real_score: 0.8183, Fake_score: 0.1822

2025-07-04 05:02:28,436 [INFO] src.gan.trainer: Epoch 5, Batch 400/976, Stage 0, D_loss: 0.4536, G_loss: 0.4117, Num_std: 0.061923, Num_range: 1.9889, Real_score: 0.8139, Fake_score: 0.1648

2025-07-04 05:02:43,514 [INFO] src.gan.trainer: Epoch 5, Batch 500/976, Stage 0, D_loss: 0.5343, G_loss: 0.4422, Num_std: 0.048083, Num_range: 1.9817, Real_score: 0.8255, Fake_score: 0.1867

2025-07-04 05:02:58,525 [INFO] src.gan.trainer: Epoch 5, Batch 600/976, Stage 0, D_loss: 0.5004, G_loss: 0.4272, Num_std: 0.047508, Num_range: 1.9587, Real_score: 0.8232, Fake_score: 0.1667

2025-07-04 05:03:13,578 [INFO] src.gan.trainer: Epoch 5, Batch 700/976, Stage 0, D_loss: 0.5248, G_loss: 0.4468, Num_std: 0.052198, Num_range: 1.9783, Real_score: 0.8137, Fake_score: 0.1695

2025-07-04 05:03:28,518 [INFO] src.gan.trainer: Epoch 5, Batch 800/976, Stage 0, D_loss: 0.5087, G_loss: 0.4362, Num_std: 0.062451, Num_range: 1.9750, Real_score: 0.8362, Fake_score: 0.1627

2025-07-04 05:03:43,460 [INFO] src.gan.trainer: Epoch 5, Batch 900/976, Stage 0, D_loss: 0.4875, G_loss: 0.4648, Num_std: 0.042730, Num_range: 1.8236, Real_score: 0.8307, Fake_score: 0.1642

2025-07-04 05:03:54,721 [INFO] __main__: 第6个epoch完成. 指标: {'d_loss': 0.5158113062870303, 'd_adv_loss': 0.036843406759797866, 'd_ctr_loss': 0.47807572180496954, 'd_reg_loss': 0.044608954386022207, 'real_d_score': 0.8152783653530927, 'fake_d_score': 0.17645065210087868, 'g_loss': 0.421104281998234, 'g_adv_loss': 0.7032079194860714, 'g_feature_loss': 0.3826438519045139, 'g_diversity_loss': -0.9833165744696157, 'g_numeric_diversity_loss': -0.0017850811481828904, 'fake_d_score_g': 0.17516606131599094, 'temperature': 2.4994999999999776, 'numeric_std': 0.05052880092798803, 'numeric_range': 1.8597427029467986, 'training_stage': 0.0}

2025-07-04 05:03:54,721 [INFO] __main__: 开始第7/30个epoch (阶段: 0)

2025-07-04 05:03:54,994 [INFO] src.gan.trainer: Epoch 6, Batch 0/976, Stage 0, D_loss: 0.5215, G_loss: 0.4770, Num_std: 0.033652, Num_range: 1.4778, Real_score: 0.8429, Fake_score: 0.1553

2025-07-04 05:04:10,055 [INFO] src.gan.trainer: Epoch 6, Batch 100/976, Stage 0, D_loss: 0.4756, G_loss: 0.4823, Num_std: 0.047629, Num_range: 1.4735, Real_score: 0.8378, Fake_score: 0.1464

2025-07-04 05:04:25,070 [INFO] src.gan.trainer: Epoch 6, Batch 200/976, Stage 0, D_loss: 0.4830, G_loss: 0.5039, Num_std: 0.053338, Num_range: 1.9833, Real_score: 0.8401, Fake_score: 0.1554

2025-07-04 05:04:40,135 [INFO] src.gan.trainer: Epoch 6, Batch 300/976, Stage 0, D_loss: 0.5131, G_loss: 0.4776, Num_std: 0.056706, Num_range: 1.9883, Real_score: 0.8367, Fake_score: 0.1489

2025-07-04 05:04:55,063 [INFO] src.gan.trainer: Epoch 6, Batch 400/976, Stage 0, D_loss: 0.5111, G_loss: 0.4565, Num_std: 0.042136, Num_range: 1.9828, Real_score: 0.8366, Fake_score: 0.1506

2025-07-04 05:05:10,003 [INFO] src.gan.trainer: Epoch 6, Batch 500/976, Stage 0, D_loss: 0.4895, G_loss: 0.4741, Num_std: 0.054584, Num_range: 1.9784, Real_score: 0.8408, Fake_score: 0.1567

2025-07-04 05:05:24,893 [INFO] src.gan.trainer: Epoch 6, Batch 600/976, Stage 0, D_loss: 0.5602, G_loss: 0.4917, Num_std: 0.056877, Num_range: 1.9653, Real_score: 0.8369, Fake_score: 0.1570

2025-07-04 05:05:39,754 [INFO] src.gan.trainer: Epoch 6, Batch 700/976, Stage 0, D_loss: 0.4798, G_loss: 0.5051, Num_std: 0.030154, Num_range: 1.3579, Real_score: 0.8675, Fake_score: 0.1440

2025-07-04 05:05:54,690 [INFO] src.gan.trainer: Epoch 6, Batch 800/976, Stage 0, D_loss: 0.4780, G_loss: 0.4933, Num_std: 0.035639, Num_range: 1.9527, Real_score: 0.8573, Fake_score: 0.1437

2025-07-04 05:06:09,628 [INFO] src.gan.trainer: Epoch 6, Batch 900/976, Stage 0, D_loss: 0.5046, G_loss: 0.5087, Num_std: 0.044105, Num_range: 1.9574, Real_score: 0.8584, Fake_score: 0.1399

2025-07-04 05:06:20,867 [INFO] __main__: 第7个epoch完成. 指标: {'d_loss': 0.5015574487567436, 'd_adv_loss': 0.02822617383780663, 'd_ctr_loss': 0.4723820128462838, 'd_reg_loss': 0.047463109201492125, 'real_d_score': 0.8460577399027148, 'fake_d_score': 0.14987308705286653, 'g_loss': 0.4856244878833251, 'g_adv_loss': 0.7433897448408474, 'g_feature_loss': 0.45652496637623813, 'g_diversity_loss': -0.9761611190242846, 'g_numeric_diversity_loss': -0.0021291490379379626, 'fake_d_score_g': 0.14883072262304173, 'temperature': 2.4990000999999724, 'numeric_std': 0.04574522489326866, 'numeric_range': 1.8639705795285801, 'training_stage': 0.0}

2025-07-04 05:06:20,867 [INFO] __main__: 开始第8/30个epoch (阶段: 0)

2025-07-04 05:06:21,127 [INFO] src.gan.trainer: Epoch 7, Batch 0/976, Stage 0, D_loss: 0.4971, G_loss: 0.5091, Num_std: 0.054694, Num_range: 1.9787, Real_score: 0.8612, Fake_score: 0.1255

2025-07-04 05:06:36,102 [INFO] src.gan.trainer: Epoch 7, Batch 100/976, Stage 0, D_loss: 0.5104, G_loss: 0.5231, Num_std: 0.045562, Num_range: 1.8866, Real_score: 0.8590, Fake_score: 0.1286

2025-07-04 05:06:46,064 [INFO] src.gan.trainer: 应用温和的平衡调整...

2025-07-04 05:06:46,064 [INFO] src.gan.trainer: 温和调整后 - G学习率: 6.04e-05, D学习率: 1.80e-05, 温度: 2.500

2025-07-04 05:06:50,966 [INFO] src.gan.trainer: Epoch 7, Batch 200/976, Stage 0, D_loss: 0.4926, G_loss: 0.5168, Num_std: 0.044965, Num_range: 1.8682, Real_score: 0.8461, Fake_score: 0.1392

2025-07-04 05:07:05,821 [INFO] src.gan.trainer: Epoch 7, Batch 300/976, Stage 0, D_loss: 0.4748, G_loss: 0.5098, Num_std: 0.050439, Num_range: 1.8781, Real_score: 0.8746, Fake_score: 0.1358

2025-07-04 05:07:20,662 [INFO] src.gan.trainer: Epoch 7, Batch 400/976, Stage 0, D_loss: 0.5392, G_loss: 0.5185, Num_std: 0.036905, Num_range: 1.5128, Real_score: 0.8543, Fake_score: 0.1405

2025-07-04 05:07:35,589 [INFO] src.gan.trainer: Epoch 7, Batch 500/976, Stage 0, D_loss: 0.5042, G_loss: 0.5222, Num_std: 0.045645, Num_range: 1.9057, Real_score: 0.8665, Fake_score: 0.1341

2025-07-04 05:07:50,449 [INFO] src.gan.trainer: Epoch 7, Batch 600/976, Stage 0, D_loss: 0.4785, G_loss: 0.5226, Num_std: 0.049567, Num_range: 1.8791, Real_score: 0.8443, Fake_score: 0.1265

2025-07-04 05:08:05,315 [INFO] src.gan.trainer: Epoch 7, Batch 700/976, Stage 0, D_loss: 0.4894, G_loss: 0.5384, Num_std: 0.042731, Num_range: 1.8582, Real_score: 0.8651, Fake_score: 0.1207

2025-07-04 05:08:20,264 [INFO] src.gan.trainer: Epoch 7, Batch 800/976, Stage 0, D_loss: 0.4903, G_loss: 0.5271, Num_std: 0.038385, Num_range: 1.6866, Real_score: 0.8603, Fake_score: 0.1226

2025-07-04 05:08:35,381 [INFO] src.gan.trainer: Epoch 7, Batch 900/976, Stage 0, D_loss: 0.4770, G_loss: 0.5442, Num_std: 0.041377, Num_range: 1.7396, Real_score: 0.8701, Fake_score: 0.1306

2025-07-04 05:08:46,560 [INFO] __main__: 第8个epoch完成. 指标: {'d_loss': 0.4910274155926509, 'd_adv_loss': 0.023352354479625376, 'd_ctr_loss': 0.46668562491531235, 'd_reg_loss': 0.04947178502048019, 'real_d_score': 0.8631689084968615, 'fake_d_score': 0.13257178077168888, 'g_loss': 0.5225891934127592, 'g_adv_loss': 0.769364086799121, 'g_feature_loss': 0.5124596631754946, 'g_diversity_loss': -0.9895201052839324, 'g_numeric_diversity_loss': -0.0023523741655794056, 'fake_d_score_g': 0.13210873853903807, 'temperature': 2.499741854914544, 'numeric_std': 0.045215126405638004, 'numeric_range': 1.8083399958664264, 'training_stage': 0.0}

2025-07-04 05:08:46,560 [INFO] __main__: 开始第9/30个epoch (阶段: 0)

2025-07-04 05:08:46,821 [INFO] src.gan.trainer: Epoch 8, Batch 0/976, Stage 0, D_loss: 0.4239, G_loss: 0.5549, Num_std: 0.042464, Num_range: 1.8986, Real_score: 0.8705, Fake_score: 0.1394

2025-07-04 05:09:01,801 [INFO] src.gan.trainer: Epoch 8, Batch 100/976, Stage 0, D_loss: 0.4583, G_loss: 0.5205, Num_std: 0.042811, Num_range: 1.8850, Real_score: 0.8749, Fake_score: 0.1365

2025-07-04 05:09:16,801 [INFO] src.gan.trainer: Epoch 8, Batch 200/976, Stage 0, D_loss: 0.5333, G_loss: 0.5402, Num_std: 0.040228, Num_range: 1.7005, Real_score: 0.8706, Fake_score: 0.1386

2025-07-04 05:09:31,876 [INFO] src.gan.trainer: Epoch 8, Batch 300/976, Stage 0, D_loss: 0.4691, G_loss: 0.5474, Num_std: 0.045123, Num_range: 1.9273, Real_score: 0.8783, Fake_score: 0.1294

2025-07-04 05:09:46,774 [INFO] src.gan.trainer: Epoch 8, Batch 400/976, Stage 0, D_loss: 0.4970, G_loss: 0.5259, Num_std: 0.051384, Num_range: 1.9069, Real_score: 0.8656, Fake_score: 0.1327

2025-07-04 05:10:01,658 [INFO] src.gan.trainer: Epoch 8, Batch 500/976, Stage 0, D_loss: 0.5036, G_loss: 0.5453, Num_std: 0.037457, Num_range: 1.4204, Real_score: 0.8595, Fake_score: 0.1319

2025-07-04 05:10:16,618 [INFO] src.gan.trainer: Epoch 8, Batch 600/976, Stage 0, D_loss: 0.4688, G_loss: 0.5266, Num_std: 0.039122, Num_range: 1.4961, Real_score: 0.8798, Fake_score: 0.1271

2025-07-04 05:10:31,677 [INFO] src.gan.trainer: Epoch 8, Batch 700/976, Stage 0, D_loss: 0.5136, G_loss: 0.5385, Num_std: 0.040358, Num_range: 1.6696, Real_score: 0.8859, Fake_score: 0.1448

2025-07-04 05:10:46,659 [INFO] src.gan.trainer: Epoch 8, Batch 800/976, Stage 0, D_loss: 0.4932, G_loss: 0.5602, Num_std: 0.037292, Num_range: 1.7704, Real_score: 0.8837, Fake_score: 0.1120

2025-07-04 05:11:01,595 [INFO] src.gan.trainer: Epoch 8, Batch 900/976, Stage 0, D_loss: 0.4391, G_loss: 0.5732, Num_std: 0.045788, Num_range: 1.8540, Real_score: 0.8837, Fake_score: 0.1293

2025-07-04 05:11:12,823 [INFO] __main__: 第9个epoch完成. 指标: {'d_loss': 0.4829186444766212, 'd_adv_loss': 0.019960864032924054, 'd_ctr_loss': 0.46196776933845984, 'd_reg_loss': 0.04950053531096361, 'real_d_score': 0.8748725115886474, 'fake_d_score': 0.12717418034454114, 'g_loss': 0.5483595510344192, 'g_adv_loss': 0.7770959683821179, 'g_feature_loss': 0.5597725024782737, 'g_diversity_loss': -0.9789826667211102, 'g_numeric_diversity_loss': -0.0025375495448921965, 'fake_d_score_g': 0.1264510831332517, 'temperature': 2.4994999999999776, 'numeric_std': 0.04303490271145493, 'numeric_range': 1.8151634789881166, 'training_stage': 0.0}

2025-07-04 05:11:12,823 [INFO] __main__: 开始第10/30个epoch (阶段: 0)

2025-07-04 05:11:13,089 [INFO] src.gan.trainer: Epoch 9, Batch 0/976, Stage 0, D_loss: 0.4805, G_loss: 0.5820, Num_std: 0.036977, Num_range: 1.9733, Real_score: 0.8843, Fake_score: 0.1270

2025-07-04 05:11:28,082 [INFO] src.gan.trainer: Epoch 9, Batch 100/976, Stage 0, D_loss: 0.4708, G_loss: 0.5580, Num_std: 0.050159, Num_range: 1.9516, Real_score: 0.8793, Fake_score: 0.1174

2025-07-04 05:11:42,968 [INFO] src.gan.trainer: Epoch 9, Batch 200/976, Stage 0, D_loss: 0.4946, G_loss: 0.5643, Num_std: 0.051406, Num_range: 1.9073, Real_score: 0.8887, Fake_score: 0.1219

2025-07-04 05:11:58,030 [INFO] src.gan.trainer: Epoch 9, Batch 300/976, Stage 0, D_loss: 0.4858, G_loss: 0.5663, Num_std: 0.047065, Num_range: 1.9632, Real_score: 0.8790, Fake_score: 0.1139

2025-07-04 05:12:12,915 [INFO] src.gan.trainer: Epoch 9, Batch 400/976, Stage 0, D_loss: 0.4619, G_loss: 0.5577, Num_std: 0.035914, Num_range: 1.4743, Real_score: 0.8843, Fake_score: 0.1338

2025-07-04 05:12:28,053 [INFO] src.gan.trainer: Epoch 9, Batch 500/976, Stage 0, D_loss: 0.4818, G_loss: 0.5707, Num_std: 0.043754, Num_range: 1.7962, Real_score: 0.8850, Fake_score: 0.1199

2025-07-04 05:12:43,093 [INFO] src.gan.trainer: Epoch 9, Batch 600/976, Stage 0, D_loss: 0.4790, G_loss: 0.5602, Num_std: 0.040243, Num_range: 1.9443, Real_score: 0.8760, Fake_score: 0.1229

2025-07-04 05:12:58,127 [INFO] src.gan.trainer: Epoch 9, Batch 700/976, Stage 0, D_loss: 0.5000, G_loss: 0.5701, Num_std: 0.036051, Num_range: 1.9134, Real_score: 0.8763, Fake_score: 0.1146

2025-07-04 05:13:13,180 [INFO] src.gan.trainer: Epoch 9, Batch 800/976, Stage 0, D_loss: 0.4974, G_loss: 0.5774, Num_std: 0.044383, Num_range: 1.9606, Real_score: 0.8806, Fake_score: 0.1131

2025-07-04 05:13:28,319 [INFO] src.gan.trainer: Epoch 9, Batch 900/976, Stage 0, D_loss: 0.4877, G_loss: 0.5937, Num_std: 0.040678, Num_range: 1.6845, Real_score: 0.8814, Fake_score: 0.1068

2025-07-04 05:13:39,666 [INFO] __main__: 第10个epoch完成. 指标: {'d_loss': 0.4756099971957867, 'd_adv_loss': 0.016955127357505245, 'd_ctr_loss': 0.4576592978212189, 'd_reg_loss': 0.04977860442958168, 'real_d_score': 0.8826863495785683, 'fake_d_score': 0.1185689863718313, 'g_loss': 0.5673149419653286, 'g_adv_loss': 0.7903882511815097, 'g_feature_loss': 0.583094378719565, 'g_diversity_loss': -0.9807159976188935, 'g_numeric_diversity_loss': -0.0028576108013057748, 'fake_d_score_g': 0.11777522694828411, 'temperature': 2.4990000999999724, 'numeric_std': 0.044271987056247804, 'numeric_range': 1.8776352387227002, 'training_stage': 0.0}

2025-07-04 05:13:39,667 [INFO] __main__: 在第10个epoch评估精调的GAN

2025-07-04 05:13:39,689 [INFO] __main__: 精调GAN评估指标:

2025-07-04 05:13:39,689 [INFO] __main__:   数值特征质量评分: 0.400

2025-07-04 05:13:39,689 [INFO] __main__:   特征平均标准差: 0.026019

2025-07-04 05:13:39,689 [INFO] __main__:   零标准差特征数: 0.0

2025-07-04 05:13:39,689 [INFO] __main__:   类别特征平均熵: 0.757

2025-07-04 05:13:39,689 [INFO] __main__: 训练质量: 需要改进

2025-07-04 05:13:39,691 [INFO] __main__: 新的最佳数值特征质量: 0.400

2025-07-04 05:13:39,882 [INFO] src.gan.trainer: 检查点已保存到 /data/gan/refined_checkpoint_epoch_10.pt

2025-07-04 05:13:40,157 [INFO] src.gan.trainer: 检查点已保存到 /data/gan/refined_best_model.pt

2025-07-04 05:13:40,158 [INFO] __main__: 保存最佳模型，数值质量评分: 0.400

2025-07-04 05:13:40,158 [INFO] __main__: 开始第11/30个epoch (阶段: 0)

2025-07-04 05:13:40,437 [INFO] src.gan.trainer: Epoch 10, Batch 0/976, Stage 0, D_loss: 0.4929, G_loss: 0.5778, Num_std: 0.041047, Num_range: 1.8061, Real_score: 0.8886, Fake_score: 0.1124

2025-07-04 05:13:55,524 [INFO] src.gan.trainer: Epoch 10, Batch 100/976, Stage 0, D_loss: 0.4833, G_loss: 0.5582, Num_std: 0.052370, Num_range: 1.9460, Real_score: 0.8798, Fake_score: 0.1193

2025-07-04 05:14:10,686 [INFO] src.gan.trainer: Epoch 10, Batch 200/976, Stage 0, D_loss: 0.4677, G_loss: 0.5639, Num_std: 0.050526, Num_range: 1.8610, Real_score: 0.8691, Fake_score: 0.1111

2025-07-04 05:14:25,549 [INFO] src.gan.trainer: Epoch 10, Batch 300/976, Stage 0, D_loss: 0.4682, G_loss: 0.5881, Num_std: 0.050630, Num_range: 1.9549, Real_score: 0.8788, Fake_score: 0.1113

2025-07-04 05:14:40,406 [INFO] src.gan.trainer: Epoch 10, Batch 400/976, Stage 0, D_loss: 0.4471, G_loss: 0.5837, Num_std: 0.044661, Num_range: 1.9631, Real_score: 0.8946, Fake_score: 0.1076

2025-07-04 05:14:55,363 [INFO] src.gan.trainer: Epoch 10, Batch 500/976, Stage 0, D_loss: 0.4149, G_loss: 0.5813, Num_std: 0.038503, Num_range: 1.6523, Real_score: 0.8853, Fake_score: 0.1132

2025-07-04 05:15:10,367 [INFO] src.gan.trainer: Epoch 10, Batch 600/976, Stage 0, D_loss: 0.4507, G_loss: 0.5908, Num_std: 0.040868, Num_range: 1.8230, Real_score: 0.8838, Fake_score: 0.1039

2025-07-04 05:15:25,216 [INFO] src.gan.trainer: Epoch 10, Batch 700/976, Stage 0, D_loss: 0.4681, G_loss: 0.5702, Num_std: 0.045953, Num_range: 1.9007, Real_score: 0.8763, Fake_score: 0.0990

2025-07-04 05:15:40,122 [INFO] src.gan.trainer: Epoch 10, Batch 800/976, Stage 0, D_loss: 0.4692, G_loss: 0.5870, Num_std: 0.053773, Num_range: 1.9529, Real_score: 0.8795, Fake_score: 0.1109

2025-07-04 05:15:54,965 [INFO] src.gan.trainer: Epoch 10, Batch 900/976, Stage 0, D_loss: 0.4674, G_loss: 0.5948, Num_std: 0.044671, Num_range: 1.9132, Real_score: 0.8903, Fake_score: 0.1225

2025-07-04 05:16:06,190 [INFO] __main__: 第11个epoch完成. 指标: {'d_loss': 0.46935701635895166, 'd_adv_loss': 0.01501731540469574, 'd_ctr_loss': 0.453355825979446, 'd_reg_loss': 0.04919377275834187, 'real_d_score': 0.885423883795738, 'fake_d_score': 0.11454161969547876, 'g_loss': 0.5776875928410742, 'g_adv_loss': 0.7958133493453421, 'g_feature_loss': 0.5990550794073795, 'g_diversity_loss': -0.9773968441686663, 'g_numeric_diversity_loss': -0.0034417702162862864, 'fake_d_score_g': 0.11401577279623092, 'temperature': 2.4985002999800185, 'numeric_std': 0.0470667608811321, 'numeric_range': 1.8853588429142214, 'training_stage': 0.0}

2025-07-04 05:16:06,191 [INFO] __main__: 开始第12/30个epoch (阶段: 0)

2025-07-04 05:16:06,586 [INFO] src.gan.trainer: Epoch 11, Batch 0/976, Stage 0, D_loss: 0.4745, G_loss: 0.5633, Num_std: 0.050081, Num_range: 1.9250, Real_score: 0.8974, Fake_score: 0.1112

2025-07-04 05:16:21,686 [INFO] src.gan.trainer: Epoch 11, Batch 100/976, Stage 0, D_loss: 0.4370, G_loss: 0.5836, Num_std: 0.044593, Num_range: 1.9729, Real_score: 0.8880, Fake_score: 0.1119

2025-07-04 05:16:36,675 [INFO] src.gan.trainer: Epoch 11, Batch 200/976, Stage 0, D_loss: 0.4613, G_loss: 0.5770, Num_std: 0.053041, Num_range: 1.8437, Real_score: 0.8992, Fake_score: 0.1127

2025-07-04 05:16:51,643 [INFO] src.gan.trainer: Epoch 11, Batch 300/976, Stage 0, D_loss: 0.4371, G_loss: 0.5913, Num_std: 0.041392, Num_range: 1.9544, Real_score: 0.8717, Fake_score: 0.1038

2025-07-04 05:17:06,694 [INFO] src.gan.trainer: Epoch 11, Batch 400/976, Stage 0, D_loss: 0.4946, G_loss: 0.5668, Num_std: 0.054323, Num_range: 1.9609, Real_score: 0.8839, Fake_score: 0.1194

2025-07-04 05:17:21,768 [INFO] src.gan.trainer: Epoch 11, Batch 500/976, Stage 0, D_loss: 0.4816, G_loss: 0.5954, Num_std: 0.039272, Num_range: 1.7331, Real_score: 0.8891, Fake_score: 0.1099

2025-07-04 05:17:36,707 [INFO] src.gan.trainer: Epoch 11, Batch 600/976, Stage 0, D_loss: 0.4364, G_loss: 0.5852, Num_std: 0.042406, Num_range: 1.9523, Real_score: 0.8954, Fake_score: 0.1138

2025-07-04 05:17:51,522 [INFO] src.gan.trainer: Epoch 11, Batch 700/976, Stage 0, D_loss: 0.4353, G_loss: 0.5981, Num_std: 0.054394, Num_range: 1.9437, Real_score: 0.8884, Fake_score: 0.1038

2025-07-04 05:18:06,426 [INFO] src.gan.trainer: Epoch 11, Batch 800/976, Stage 0, D_loss: 0.4843, G_loss: 0.5816, Num_std: 0.055067, Num_range: 1.9536, Real_score: 0.8949, Fake_score: 0.1151

2025-07-04 05:18:21,354 [INFO] src.gan.trainer: Epoch 11, Batch 900/976, Stage 0, D_loss: 0.4772, G_loss: 0.6088, Num_std: 0.052347, Num_range: 1.9220, Real_score: 0.8821, Fake_score: 0.1093

2025-07-04 05:18:32,575 [INFO] __main__: 第12个epoch完成. 指标: {'d_loss': 0.46470086734558685, 'd_adv_loss': 0.01434318986091548, 'd_ctr_loss': 0.44937217895124776, 'd_reg_loss': 0.049274900378506674, 'real_d_score': 0.886187452029008, 'fake_d_score': 0.11152892440679615, 'g_loss': 0.5937169281144944, 'g_adv_loss': 0.8012666154469629, 'g_feature_loss': 0.6314110356724002, 'g_diversity_loss': -0.9767082202059981, 'g_numeric_diversity_loss': -0.0031448543094250592, 'fake_d_score_g': 0.1107228971595362, 'temperature': 2.498000599920009, 'numeric_std': 0.04996679266189509, 'numeric_range': 1.882168876933435, 'training_stage': 0.0}

2025-07-04 05:18:32,575 [INFO] __main__: 开始第13/30个epoch (阶段: 0)

2025-07-04 05:18:32,839 [INFO] src.gan.trainer: Epoch 12, Batch 0/976, Stage 0, D_loss: 0.4709, G_loss: 0.5969, Num_std: 0.061872, Num_range: 1.9463, Real_score: 0.8968, Fake_score: 0.1140

2025-07-04 05:18:47,728 [INFO] src.gan.trainer: Epoch 12, Batch 100/976, Stage 0, D_loss: 0.4709, G_loss: 0.5876, Num_std: 0.055059, Num_range: 1.9119, Real_score: 0.8884, Fake_score: 0.1130

2025-07-04 05:19:02,661 [INFO] src.gan.trainer: Epoch 12, Batch 200/976, Stage 0, D_loss: 0.4528, G_loss: 0.6158, Num_std: 0.047773, Num_range: 1.9185, Real_score: 0.8845, Fake_score: 0.1064

2025-07-04 05:19:17,655 [INFO] src.gan.trainer: Epoch 12, Batch 300/976, Stage 0, D_loss: 0.4613, G_loss: 0.6151, Num_std: 0.050332, Num_range: 1.8808, Real_score: 0.8665, Fake_score: 0.1021

2025-07-04 05:19:32,628 [INFO] src.gan.trainer: Epoch 12, Batch 400/976, Stage 0, D_loss: 0.4450, G_loss: 0.6079, Num_std: 0.051557, Num_range: 1.8451, Real_score: 0.8849, Fake_score: 0.1027

2025-07-04 05:19:47,585 [INFO] src.gan.trainer: Epoch 12, Batch 500/976, Stage 0, D_loss: 0.4516, G_loss: 0.6099, Num_std: 0.063610, Num_range: 1.9192, Real_score: 0.8985, Fake_score: 0.1051

2025-07-04 05:20:02,656 [INFO] src.gan.trainer: Epoch 12, Batch 600/976, Stage 0, D_loss: 0.4490, G_loss: 0.6095, Num_std: 0.056221, Num_range: 1.9481, Real_score: 0.9037, Fake_score: 0.1071

2025-07-04 05:20:17,600 [INFO] src.gan.trainer: Epoch 12, Batch 700/976, Stage 0, D_loss: 0.4292, G_loss: 0.6040, Num_std: 0.048948, Num_range: 1.9267, Real_score: 0.8729, Fake_score: 0.1085

2025-07-04 05:20:32,515 [INFO] src.gan.trainer: Epoch 12, Batch 800/976, Stage 0, D_loss: 0.4319, G_loss: 0.6166, Num_std: 0.046558, Num_range: 1.8645, Real_score: 0.8862, Fake_score: 0.1012

2025-07-04 05:20:47,447 [INFO] src.gan.trainer: Epoch 12, Batch 900/976, Stage 0, D_loss: 0.4250, G_loss: 0.6168, Num_std: 0.048500, Num_range: 1.9392, Real_score: 0.8803, Fake_score: 0.1109

2025-07-04 05:20:58,707 [INFO] __main__: 第13个epoch完成. 指标: {'d_loss': 0.4597643194872818, 'd_adv_loss': 0.012546193030220072, 'd_ctr_loss': 0.446241960967662, 'd_reg_loss': 0.04880828984829851, 'real_d_score': 0.8901493759428863, 'fake_d_score': 0.10883891555770753, 'g_loss': 0.6060445230311715, 'g_adv_loss': 0.8041777041196183, 'g_feature_loss': 0.6670341017386292, 'g_diversity_loss': -0.9793471623354599, 'g_numeric_diversity_loss': -0.00325227296341668, 'fake_d_score_g': 0.10845044275104514, 'temperature': 2.497500999799979, 'numeric_std': 0.05250755663836488, 'numeric_range': 1.8616610664487516, 'training_stage': 0.0}

2025-07-04 05:20:58,708 [INFO] __main__: 开始第14/30个epoch (阶段: 0)

2025-07-04 05:20:58,967 [INFO] src.gan.trainer: Epoch 13, Batch 0/976, Stage 0, D_loss: 0.4844, G_loss: 0.5833, Num_std: 0.038807, Num_range: 1.9163, Real_score: 0.8920, Fake_score: 0.1235

2025-07-04 05:21:13,965 [INFO] src.gan.trainer: Epoch 13, Batch 100/976, Stage 0, D_loss: 0.4510, G_loss: 0.5948, Num_std: 0.050761, Num_range: 1.9500, Real_score: 0.9053, Fake_score: 0.1210

2025-07-04 05:21:29,052 [INFO] src.gan.trainer: Epoch 13, Batch 200/976, Stage 0, D_loss: 0.4447, G_loss: 0.6100, Num_std: 0.035846, Num_range: 1.9025, Real_score: 0.8836, Fake_score: 0.1071

2025-07-04 05:21:44,135 [INFO] src.gan.trainer: Epoch 13, Batch 300/976, Stage 0, D_loss: 0.4909, G_loss: 0.6070, Num_std: 0.035131, Num_range: 1.4257, Real_score: 0.8822, Fake_score: 0.1097

2025-07-04 05:21:59,389 [INFO] src.gan.trainer: Epoch 13, Batch 400/976, Stage 0, D_loss: 0.4445, G_loss: 0.5978, Num_std: 0.042221, Num_range: 1.5306, Real_score: 0.8913, Fake_score: 0.1199

2025-07-04 05:22:14,415 [INFO] src.gan.trainer: Epoch 13, Batch 500/976, Stage 0, D_loss: 0.4617, G_loss: 0.6079, Num_std: 0.042498, Num_range: 1.9029, Real_score: 0.8992, Fake_score: 0.1112

2025-07-04 05:22:29,431 [INFO] src.gan.trainer: Epoch 13, Batch 600/976, Stage 0, D_loss: 0.4419, G_loss: 0.6106, Num_std: 0.051097, Num_range: 1.9696, Real_score: 0.9075, Fake_score: 0.1114

2025-07-04 05:22:44,413 [INFO] src.gan.trainer: Epoch 13, Batch 700/976, Stage 0, D_loss: 0.4149, G_loss: 0.6146, Num_std: 0.049614, Num_range: 1.9405, Real_score: 0.8820, Fake_score: 0.1085

2025-07-04 05:22:59,450 [INFO] src.gan.trainer: Epoch 13, Batch 800/976, Stage 0, D_loss: 0.4594, G_loss: 0.6084, Num_std: 0.045712, Num_range: 1.9415, Real_score: 0.8958, Fake_score: 0.1046

2025-07-04 05:23:14,491 [INFO] src.gan.trainer: Epoch 13, Batch 900/976, Stage 0, D_loss: 0.4574, G_loss: 0.6008, Num_std: 0.035100, Num_range: 1.4952, Real_score: 0.8911, Fake_score: 0.1109

2025-07-04 05:23:25,771 [INFO] __main__: 第14个epoch完成. 指标: {'d_loss': 0.4554910139776153, 'd_adv_loss': 0.01138019726037231, 'd_ctr_loss': 0.44314749465614045, 'd_reg_loss': 0.04816611416134069, 'real_d_score': 0.8921472353891262, 'fake_d_score': 0.10821767307085083, 'g_loss': 0.607504343603804, 'g_adv_loss': 0.8045904358716599, 'g_feature_loss': 0.6628655899784275, 'g_diversity_loss': -0.9731114272414977, 'g_numeric_diversity_loss': -0.0033505983991660364, 'fake_d_score_g': 0.1077253061443404, 'temperature': 2.497001499600061, 'numeric_std': 0.04628529855943299, 'numeric_range': 1.8939623639516792, 'training_stage': 0.0}

2025-07-04 05:23:25,772 [INFO] __main__: 开始第15/30个epoch (阶段: 0)

2025-07-04 05:23:26,033 [INFO] src.gan.trainer: Epoch 14, Batch 0/976, Stage 0, D_loss: 0.4453, G_loss: 0.6175, Num_std: 0.042319, Num_range: 1.9698, Real_score: 0.9004, Fake_score: 0.1039

2025-07-04 05:23:40,969 [INFO] src.gan.trainer: Epoch 14, Batch 100/976, Stage 0, D_loss: 0.4585, G_loss: 0.6118, Num_std: 0.048140, Num_range: 1.7515, Real_score: 0.8983, Fake_score: 0.1063

2025-07-04 05:23:56,030 [INFO] src.gan.trainer: Epoch 14, Batch 200/976, Stage 0, D_loss: 0.4247, G_loss: 0.6188, Num_std: 0.051173, Num_range: 1.7262, Real_score: 0.8898, Fake_score: 0.1086

2025-07-04 05:24:11,061 [INFO] src.gan.trainer: Epoch 14, Batch 300/976, Stage 0, D_loss: 0.4206, G_loss: 0.6346, Num_std: 0.043043, Num_range: 1.7443, Real_score: 0.8947, Fake_score: 0.1087

2025-07-04 05:24:26,005 [INFO] src.gan.trainer: Epoch 14, Batch 400/976, Stage 0, D_loss: 0.4935, G_loss: 0.6110, Num_std: 0.066488, Num_range: 1.9371, Real_score: 0.9010, Fake_score: 0.1026

2025-07-04 05:24:40,955 [INFO] src.gan.trainer: Epoch 14, Batch 500/976, Stage 0, D_loss: 0.4576, G_loss: 0.6281, Num_std: 0.051907, Num_range: 1.8611, Real_score: 0.8864, Fake_score: 0.1037

2025-07-04 05:24:55,977 [INFO] src.gan.trainer: Epoch 14, Batch 600/976, Stage 0, D_loss: 0.4128, G_loss: 0.6035, Num_std: 0.053280, Num_range: 1.8877, Real_score: 0.8940, Fake_score: 0.1056

2025-07-04 05:25:10,928 [INFO] src.gan.trainer: Epoch 14, Batch 700/976, Stage 0, D_loss: 0.4416, G_loss: 0.6357, Num_std: 0.049341, Num_range: 1.4612, Real_score: 0.8864, Fake_score: 0.0960

2025-07-04 05:25:25,887 [INFO] src.gan.trainer: Epoch 14, Batch 800/976, Stage 0, D_loss: 0.4452, G_loss: 0.6126, Num_std: 0.063872, Num_range: 1.9211, Real_score: 0.8875, Fake_score: 0.1094

2025-07-04 05:25:40,857 [INFO] src.gan.trainer: Epoch 14, Batch 900/976, Stage 0, D_loss: 0.4481, G_loss: 0.6312, Num_std: 0.057198, Num_range: 1.9271, Real_score: 0.8946, Fake_score: 0.0994

2025-07-04 05:25:52,109 [INFO] __main__: 第15个epoch完成. 指标: {'d_loss': 0.45138847779055136, 'd_adv_loss': 0.010401976106833435, 'd_ctr_loss': 0.4400281204124457, 'd_reg_loss': 0.047919032007425255, 'real_d_score': 0.8928013647311058, 'fake_d_score': 0.10521610872056637, 'g_loss': 0.618796124464012, 'g_adv_loss': 0.8094461449712023, 'g_feature_loss': 0.6957486822916186, 'g_diversity_loss': -0.9797493016825684, 'g_numeric_diversity_loss': -0.0037374507423596492, 'fake_d_score_g': 0.10461739506928733, 'temperature': 2.4965020993001397, 'numeric_std': 0.05387697984970842, 'numeric_range': 1.828196219346723, 'training_stage': 0.0}

2025-07-04 05:25:52,110 [INFO] __main__: 在第15个epoch评估精调的GAN

2025-07-04 05:25:52,131 [INFO] __main__: 精调GAN评估指标:

2025-07-04 05:25:52,132 [INFO] __main__:   数值特征质量评分: 0.200

2025-07-04 05:25:52,132 [INFO] __main__:   特征平均标准差: 0.024533

2025-07-04 05:25:52,132 [INFO] __main__:   零标准差特征数: 0.0

2025-07-04 05:25:52,132 [INFO] __main__:   类别特征平均熵: 0.810

2025-07-04 05:25:52,132 [INFO] __main__: 训练质量: 需要改进

2025-07-04 05:25:52,398 [INFO] src.gan.trainer: 检查点已保存到 /data/gan/refined_checkpoint_epoch_15.pt

2025-07-04 05:25:52,399 [INFO] __main__: 开始第16/30个epoch (阶段: 0)

2025-07-04 05:25:52,669 [INFO] src.gan.trainer: Epoch 15, Batch 0/976, Stage 0, D_loss: 0.4639, G_loss: 0.6390, Num_std: 0.047514, Num_range: 1.9130, Real_score: 0.8953, Fake_score: 0.1044

2025-07-04 05:26:07,634 [INFO] src.gan.trainer: Epoch 15, Batch 100/976, Stage 0, D_loss: 0.4553, G_loss: 0.6230, Num_std: 0.047894, Num_range: 1.5019, Real_score: 0.9040, Fake_score: 0.1041

2025-07-04 05:26:22,587 [INFO] src.gan.trainer: Epoch 15, Batch 200/976, Stage 0, D_loss: 0.4397, G_loss: 0.6076, Num_std: 0.065651, Num_range: 1.9243, Real_score: 0.8994, Fake_score: 0.1108

2025-07-04 05:26:37,704 [INFO] src.gan.trainer: Epoch 15, Batch 300/976, Stage 0, D_loss: 0.4257, G_loss: 0.6342, Num_std: 0.058831, Num_range: 1.9416, Real_score: 0.8877, Fake_score: 0.1012

2025-07-04 05:26:52,865 [INFO] src.gan.trainer: Epoch 15, Batch 400/976, Stage 0, D_loss: 0.4604, G_loss: 0.6447, Num_std: 0.040951, Num_range: 1.9172, Real_score: 0.8941, Fake_score: 0.0943

2025-07-04 05:27:07,874 [INFO] src.gan.trainer: Epoch 15, Batch 500/976, Stage 0, D_loss: 0.4319, G_loss: 0.6471, Num_std: 0.047377, Num_range: 1.9345, Real_score: 0.8848, Fake_score: 0.0958

2025-07-04 05:27:22,745 [INFO] src.gan.trainer: Epoch 15, Batch 600/976, Stage 0, D_loss: 0.4560, G_loss: 0.6221, Num_std: 0.060213, Num_range: 1.9540, Real_score: 0.8967, Fake_score: 0.1012

2025-07-04 05:27:37,758 [INFO] src.gan.trainer: Epoch 15, Batch 700/976, Stage 0, D_loss: 0.4613, G_loss: 0.6305, Num_std: 0.052825, Num_range: 1.9481, Real_score: 0.8966, Fake_score: 0.1073

2025-07-04 05:27:52,816 [INFO] src.gan.trainer: Epoch 15, Batch 800/976, Stage 0, D_loss: 0.4436, G_loss: 0.6250, Num_std: 0.038847, Num_range: 1.8357, Real_score: 0.8925, Fake_score: 0.1067

2025-07-04 05:28:07,848 [INFO] src.gan.trainer: Epoch 15, Batch 900/976, Stage 0, D_loss: 0.3977, G_loss: 0.6353, Num_std: 0.055749, Num_range: 1.9430, Real_score: 0.8930, Fake_score: 0.1090

2025-07-04 05:28:19,065 [INFO] __main__: 第16个epoch完成. 指标: {'d_loss': 0.4480394345448643, 'd_adv_loss': 0.009656442115064221, 'd_ctr_loss': 0.4374319025177938, 'd_reg_loss': 0.04755451150818679, 'real_d_score': 0.8938140074615598, 'fake_d_score': 0.10432368488676956, 'g_loss': 0.6289337452480703, 'g_adv_loss': 0.8102372235776295, 'g_feature_loss': 0.7377064049732492, 'g_diversity_loss': -0.9857593481861497, 'g_numeric_diversity_loss': -0.004155828450941457, 'fake_d_score_g': 0.10387827726233696, 'temperature': 2.4960027988802205, 'numeric_std': 0.05083534927896217, 'numeric_range': 1.8744004833934425, 'training_stage': 0.0}

2025-07-04 05:28:19,066 [INFO] __main__: 开始第17/30个epoch (阶段: 0)

2025-07-04 05:28:19,323 [INFO] src.gan.trainer: Epoch 16, Batch 0/976, Stage 0, D_loss: 0.4501, G_loss: 0.6357, Num_std: 0.031437, Num_range: 1.1014, Real_score: 0.8979, Fake_score: 0.0967

2025-07-04 05:28:34,280 [INFO] src.gan.trainer: Epoch 16, Batch 100/976, Stage 0, D_loss: 0.4582, G_loss: 0.6134, Num_std: 0.068170, Num_range: 1.9737, Real_score: 0.8973, Fake_score: 0.1167

2025-07-04 05:28:49,422 [INFO] src.gan.trainer: Epoch 16, Batch 200/976, Stage 0, D_loss: 0.4500, G_loss: 0.6338, Num_std: 0.063043, Num_range: 1.9304, Real_score: 0.8931, Fake_score: 0.0935

2025-07-04 05:29:04,417 [INFO] src.gan.trainer: Epoch 16, Batch 300/976, Stage 0, D_loss: 0.4082, G_loss: 0.6502, Num_std: 0.041796, Num_range: 1.8962, Real_score: 0.9002, Fake_score: 0.1011

2025-07-04 05:29:19,404 [INFO] src.gan.trainer: Epoch 16, Batch 400/976, Stage 0, D_loss: 0.4364, G_loss: 0.6331, Num_std: 0.046113, Num_range: 1.9392, Real_score: 0.8869, Fake_score: 0.1114

2025-07-04 05:29:34,395 [INFO] src.gan.trainer: Epoch 16, Batch 500/976, Stage 0, D_loss: 0.4407, G_loss: 0.6442, Num_std: 0.054004, Num_range: 1.9508, Real_score: 0.9001, Fake_score: 0.1043

2025-07-04 05:29:49,426 [INFO] src.gan.trainer: Epoch 16, Batch 600/976, Stage 0, D_loss: 0.4330, G_loss: 0.6354, Num_std: 0.074175, Num_range: 1.9568, Real_score: 0.8951, Fake_score: 0.1025

2025-07-04 05:30:04,372 [INFO] src.gan.trainer: Epoch 16, Batch 700/976, Stage 0, D_loss: 0.4429, G_loss: 0.6411, Num_std: 0.057987, Num_range: 1.8675, Real_score: 0.8976, Fake_score: 0.1059

2025-07-04 05:30:19,307 [INFO] src.gan.trainer: Epoch 16, Batch 800/976, Stage 0, D_loss: 0.4362, G_loss: 0.6422, Num_std: 0.061060, Num_range: 1.9651, Real_score: 0.9023, Fake_score: 0.1071

2025-07-04 05:30:34,318 [INFO] src.gan.trainer: Epoch 16, Batch 900/976, Stage 0, D_loss: 0.4591, G_loss: 0.6277, Num_std: 0.052776, Num_range: 1.9429, Real_score: 0.8954, Fake_score: 0.1032

2025-07-04 05:30:45,600 [INFO] __main__: 第17个epoch完成. 指标: {'d_loss': 0.44420124187332677, 'd_adv_loss': 0.008986110455971048, 'd_ctr_loss': 0.4342772066898519, 'd_reg_loss': 0.04689623076529775, 'real_d_score': 0.8937378820092946, 'fake_d_score': 0.10418730342882829, 'g_loss': 0.6380703199675166, 'g_adv_loss': 0.8099653903456017, 'g_feature_loss': 0.7685211449454388, 'g_diversity_loss': -0.9866646243656282, 'g_numeric_diversity_loss': -0.0038927807850757503, 'fake_d_score_g': 0.10370178472077662, 'temperature': 2.4955035983204676, 'numeric_std': 0.0564351119514544, 'numeric_range': 1.9048073583287606, 'training_stage': 0.0}

2025-07-04 05:30:45,600 [INFO] __main__: 开始第18/30个epoch (阶段: 0)

2025-07-04 05:30:46,008 [INFO] src.gan.trainer: Epoch 17, Batch 0/976, Stage 0, D_loss: 0.4220, G_loss: 0.6300, Num_std: 0.043279, Num_range: 1.9366, Real_score: 0.8860, Fake_score: 0.1027

2025-07-04 05:31:01,124 [INFO] src.gan.trainer: Epoch 17, Batch 100/976, Stage 0, D_loss: 0.4015, G_loss: 0.6614, Num_std: 0.049272, Num_range: 1.9399, Real_score: 0.9010, Fake_score: 0.1081

2025-07-04 05:31:16,083 [INFO] src.gan.trainer: Epoch 17, Batch 200/976, Stage 0, D_loss: 0.4024, G_loss: 0.6295, Num_std: 0.054137, Num_range: 1.9381, Real_score: 0.8795, Fake_score: 0.1036

2025-07-04 05:31:30,914 [INFO] src.gan.trainer: Epoch 17, Batch 300/976, Stage 0, D_loss: 0.4240, G_loss: 0.6273, Num_std: 0.068882, Num_range: 1.9477, Real_score: 0.8961, Fake_score: 0.1156

2025-07-04 05:31:45,854 [INFO] src.gan.trainer: Epoch 17, Batch 400/976, Stage 0, D_loss: 0.4796, G_loss: 0.6270, Num_std: 0.056638, Num_range: 1.8591, Real_score: 0.8822, Fake_score: 0.1039

2025-07-04 05:32:00,655 [INFO] src.gan.trainer: Epoch 17, Batch 500/976, Stage 0, D_loss: 0.4210, G_loss: 0.6600, Num_std: 0.045024, Num_range: 1.9147, Real_score: 0.8944, Fake_score: 0.1071

2025-07-04 05:32:15,642 [INFO] src.gan.trainer: Epoch 17, Batch 600/976, Stage 0, D_loss: 0.4371, G_loss: 0.6281, Num_std: 0.041273, Num_range: 1.7121, Real_score: 0.8868, Fake_score: 0.1154

2025-07-04 05:32:30,758 [INFO] src.gan.trainer: Epoch 17, Batch 700/976, Stage 0, D_loss: 0.4626, G_loss: 0.6555, Num_std: 0.045233, Num_range: 1.9520, Real_score: 0.8916, Fake_score: 0.0979

2025-07-04 05:32:45,780 [INFO] src.gan.trainer: Epoch 17, Batch 800/976, Stage 0, D_loss: 0.4694, G_loss: 0.6583, Num_std: 0.040787, Num_range: 1.9470, Real_score: 0.8890, Fake_score: 0.0975

2025-07-04 05:33:00,655 [INFO] src.gan.trainer: Epoch 17, Batch 900/976, Stage 0, D_loss: 0.4486, G_loss: 0.6588, Num_std: 0.053725, Num_range: 1.7627, Real_score: 0.8905, Fake_score: 0.0989

2025-07-04 05:33:11,937 [INFO] __main__: 第18个epoch完成. 指标: {'d_loss': 0.4421364069290339, 'd_adv_loss': 0.008955945137727886, 'd_ctr_loss': 0.43224926149380943, 'd_reg_loss': 0.046559983837708346, 'real_d_score': 0.893382385678466, 'fake_d_score': 0.10536221445339641, 'g_loss': 0.6421721675560456, 'g_adv_loss': 0.8082110126772533, 'g_feature_loss': 0.7702263840194293, 'g_diversity_loss': -0.9742392194075653, 'g_numeric_diversity_loss': -0.003705535064984332, 'fake_d_score_g': 0.10481666058016911, 'temperature': 2.4950044976008314, 'numeric_std': 0.049424915498789294, 'numeric_range': 1.8657333794268744, 'training_stage': 0.0}

2025-07-04 05:33:11,937 [INFO] __main__: 开始第19/30个epoch (阶段: 0)

2025-07-04 05:33:12,207 [INFO] src.gan.trainer: Epoch 18, Batch 0/976, Stage 0, D_loss: 0.4435, G_loss: 0.6581, Num_std: 0.050450, Num_range: 1.8349, Real_score: 0.9005, Fake_score: 0.0997

2025-07-04 05:33:27,163 [INFO] src.gan.trainer: Epoch 18, Batch 100/976, Stage 0, D_loss: 0.4583, G_loss: 0.6579, Num_std: 0.059965, Num_range: 1.8411, Real_score: 0.9041, Fake_score: 0.1077

2025-07-04 05:33:42,162 [INFO] src.gan.trainer: Epoch 18, Batch 200/976, Stage 0, D_loss: 0.4517, G_loss: 0.6362, Num_std: 0.060581, Num_range: 1.9395, Real_score: 0.8959, Fake_score: 0.1039

2025-07-04 05:33:57,286 [INFO] src.gan.trainer: Epoch 18, Batch 300/976, Stage 0, D_loss: 0.4267, G_loss: 0.6738, Num_std: 0.047088, Num_range: 1.9627, Real_score: 0.9014, Fake_score: 0.0951

2025-07-04 05:34:12,246 [INFO] src.gan.trainer: Epoch 18, Batch 400/976, Stage 0, D_loss: 0.4395, G_loss: 0.6518, Num_std: 0.046711, Num_range: 1.9372, Real_score: 0.8916, Fake_score: 0.1060

2025-07-04 05:34:27,209 [INFO] src.gan.trainer: Epoch 18, Batch 500/976, Stage 0, D_loss: 0.4316, G_loss: 0.6765, Num_std: 0.046297, Num_range: 1.9307, Real_score: 0.8895, Fake_score: 0.1141

2025-07-04 05:34:42,301 [INFO] src.gan.trainer: Epoch 18, Batch 600/976, Stage 0, D_loss: 0.4564, G_loss: 0.6393, Num_std: 0.065166, Num_range: 1.9210, Real_score: 0.8950, Fake_score: 0.1021

2025-07-04 05:34:57,287 [INFO] src.gan.trainer: Epoch 18, Batch 700/976, Stage 0, D_loss: 0.5052, G_loss: 0.6533, Num_std: 0.064687, Num_range: 1.9426, Real_score: 0.8925, Fake_score: 0.0989

2025-07-04 05:35:12,231 [INFO] src.gan.trainer: Epoch 18, Batch 800/976, Stage 0, D_loss: 0.4641, G_loss: 0.6477, Num_std: 0.050318, Num_range: 1.9315, Real_score: 0.8895, Fake_score: 0.1024

2025-07-04 05:35:27,252 [INFO] src.gan.trainer: Epoch 18, Batch 900/976, Stage 0, D_loss: 0.4077, G_loss: 0.6437, Num_std: 0.046901, Num_range: 1.9505, Real_score: 0.8925, Fake_score: 0.1068

2025-07-04 05:35:38,565 [INFO] __main__: 第19个epoch完成. 指标: {'d_loss': 0.438905709804815, 'd_adv_loss': 0.007934749047233739, 'd_ctr_loss': 0.43004975703041065, 'd_reg_loss': 0.04606022386521586, 'real_d_score': 0.894835693242609, 'fake_d_score': 0.10420834537229073, 'g_loss': 0.6481369948777994, 'g_adv_loss': 0.8085177863434053, 'g_feature_loss': 0.795165279584993, 'g_diversity_loss': -0.9766755578377858, 'g_numeric_diversity_loss': -0.004130074252952418, 'fake_d_score_g': 0.10419915128713009, 'temperature': 2.494505496701346, 'numeric_std': 0.054366430553322645, 'numeric_range': 1.8892714752932722, 'training_stage': 0.0}

2025-07-04 05:35:38,565 [INFO] __main__: 开始第20/30个epoch (阶段: 0)

2025-07-04 05:35:38,832 [INFO] src.gan.trainer: Epoch 19, Batch 0/976, Stage 0, D_loss: 0.3962, G_loss: 0.6703, Num_std: 0.057331, Num_range: 1.9335, Real_score: 0.8913, Fake_score: 0.0982

2025-07-04 05:35:53,956 [INFO] src.gan.trainer: Epoch 19, Batch 100/976, Stage 0, D_loss: 0.4450, G_loss: 0.6440, Num_std: 0.053351, Num_range: 1.7319, Real_score: 0.9022, Fake_score: 0.1064

2025-07-04 05:36:09,042 [INFO] src.gan.trainer: Epoch 19, Batch 200/976, Stage 0, D_loss: 0.4300, G_loss: 0.6425, Num_std: 0.073907, Num_range: 1.9542, Real_score: 0.9029, Fake_score: 0.1097

2025-07-04 05:36:24,069 [INFO] src.gan.trainer: Epoch 19, Batch 300/976, Stage 0, D_loss: 0.4305, G_loss: 0.6398, Num_std: 0.051798, Num_range: 1.9624, Real_score: 0.8883, Fake_score: 0.0962

2025-07-04 05:36:39,069 [INFO] src.gan.trainer: Epoch 19, Batch 400/976, Stage 0, D_loss: 0.3879, G_loss: 0.6490, Num_std: 0.050977, Num_range: 1.9110, Real_score: 0.8966, Fake_score: 0.1059

2025-07-04 05:36:54,031 [INFO] src.gan.trainer: Epoch 19, Batch 500/976, Stage 0, D_loss: 0.4059, G_loss: 0.6686, Num_std: 0.048492, Num_range: 1.8169, Real_score: 0.8898, Fake_score: 0.1054

2025-07-04 05:37:08,985 [INFO] src.gan.trainer: Epoch 19, Batch 600/976, Stage 0, D_loss: 0.4200, G_loss: 0.6348, Num_std: 0.054138, Num_range: 1.5298, Real_score: 0.9000, Fake_score: 0.1209

2025-07-04 05:37:23,957 [INFO] src.gan.trainer: Epoch 19, Batch 700/976, Stage 0, D_loss: 0.4658, G_loss: 0.6567, Num_std: 0.072665, Num_range: 1.8908, Real_score: 0.8989, Fake_score: 0.1011

2025-07-04 05:37:38,907 [INFO] src.gan.trainer: Epoch 19, Batch 800/976, Stage 0, D_loss: 0.3869, G_loss: 0.6531, Num_std: 0.092457, Num_range: 1.9797, Real_score: 0.8979, Fake_score: 0.0990