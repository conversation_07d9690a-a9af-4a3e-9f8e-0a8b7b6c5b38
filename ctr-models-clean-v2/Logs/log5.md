2025-07-04 09:15:57,160 [INFO] __main__: 开始改进的GAN训练 - 更好的平衡策略
2025-07-04 09:15:57,160 [INFO] __main__: 参数: {'dataset_name': 'Criteo', 'dataset_path': '/data/Criteo_x4', 'output_dir': '/data/balanced_gan_v2', 'noise_dim': 128, 'embedding_dim': 16, 'epochs': 30, 'batch_size': 512, 'generator_lr': 0.0004, 'discriminator_lr': 4e-05, 'initial_temperature': 3.5, 'min_temperature': 2.0, 'temperature_decay': 0.999, 'max_vocab_size': 10000, 'sample_strategy': 'full', 'max_samples': 500000, 'max_grad_norm': 1.0, 'log_interval': 100, 'save_interval': 2, 'eval_interval': 2, 'seed': 2024, 'num_workers': 2, 'resume': None, 'debug': False}
2025-07-04 09:15:57,805 [INFO] __main__: 加载 Criteo 数据集从 /data/Criteo_x4
2025-07-04 09:15:57,805 [INFO] src.gan.data_prep: Max vocab size per feature: 10000
2025-07-04 09:16:02,118 [INFO] root: Loaded 500000 samples from /data/Criteo_x4/train.csv
2025-07-04 09:16:02,120 [INFO] root: CTR rate in sampled data: 0.2517
2025-07-04 09:16:02,120 [INFO] src.gan.data_prep: Starting GAN preprocessing for 500000 samples
2025-07-04 09:16:02,652 [INFO] src.gan.data_prep: Numeric feature I1: min=0.0000, max=780.0000
2025-07-04 09:16:02,656 [INFO] src.gan.data_prep: Numeric feature I2: min=-3.0000, max=12868.0000
2025-07-04 09:16:02,660 [INFO] src.gan.data_prep: Numeric feature I3: min=0.0000, max=65535.0000
2025-07-04 09:16:02,663 [INFO] src.gan.data_prep: Numeric feature I4: min=0.0000, max=406.0000
2025-07-04 09:16:02,666 [INFO] src.gan.data_prep: Numeric feature I5: min=0.0000, max=2539061.0000
2025-07-04 09:16:02,669 [INFO] src.gan.data_prep: Numeric feature I6: min=0.0000, max=63909.0000
2025-07-04 09:16:02,672 [INFO] src.gan.data_prep: Numeric feature I7: min=0.0000, max=26297.0000
2025-07-04 09:16:02,675 [INFO] src.gan.data_prep: Numeric feature I8: min=0.0000, max=3949.0000
2025-07-04 09:16:02,678 [INFO] src.gan.data_prep: Numeric feature I9: min=0.0000, max=8430.0000
2025-07-04 09:16:02,681 [INFO] src.gan.data_prep: Numeric feature I10: min=0.0000, max=8.0000
2025-07-04 09:16:02,684 [INFO] src.gan.data_prep: Numeric feature I11: min=0.0000, max=125.0000
2025-07-04 09:16:02,686 [INFO] src.gan.data_prep: Numeric feature I12: min=0.0000, max=693.0000
2025-07-04 09:16:02,689 [INFO] src.gan.data_prep: Numeric feature I13: min=0.0000, max=3890.0000
2025-07-04 09:16:02,693 [INFO] src.gan.data_prep: Feature C1: 1031 unique values
2025-07-04 09:16:02,694 [INFO] src.gan.data_prep: Categorical feature C1: final_vocab_size=1032
2025-07-04 09:16:02,697 [INFO] src.gan.data_prep: Feature C2: 528 unique values
2025-07-04 09:16:02,697 [INFO] src.gan.data_prep: Categorical feature C2: final_vocab_size=529
2025-07-04 09:16:02,706 [INFO] src.gan.data_prep: Feature C3: 184172 unique values
2025-07-04 09:16:02,706 [WARNING] src.gan.data_prep: Feature C3 has 184172 categories! Reducing to top 10000
2025-07-04 09:16:02,709 [INFO] src.gan.data_prep: Top 10000 categories cover 62.13% of samples
2025-07-04 09:16:02,712 [INFO] src.gan.data_prep: Categorical feature C3: final_vocab_size=10001
2025-07-04 09:16:02,805 [INFO] src.gan.data_prep: Feature C4: 79226 unique values
2025-07-04 09:16:02,805 [WARNING] src.gan.data_prep: Feature C4 has 79226 categories! Reducing to top 10000
2025-07-04 09:16:02,808 [INFO] src.gan.data_prep: Top 10000 categories cover 82.95% of samples
2025-07-04 09:16:02,811 [INFO] src.gan.data_prep: Categorical feature C4: final_vocab_size=10001
2025-07-04 09:16:02,853 [INFO] src.gan.data_prep: Feature C5: 225 unique values
2025-07-04 09:16:02,854 [INFO] src.gan.data_prep: Categorical feature C5: final_vocab_size=226
2025-07-04 09:16:02,856 [INFO] src.gan.data_prep: Feature C6: 15 unique values
2025-07-04 09:16:02,856 [INFO] src.gan.data_prep: Categorical feature C6: final_vocab_size=16
2025-07-04 09:16:02,859 [INFO] src.gan.data_prep: Feature C7: 10358 unique values
2025-07-04 09:16:02,860 [WARNING] src.gan.data_prep: Feature C7 has 10358 categories! Reducing to top 10000
2025-07-04 09:16:02,862 [INFO] src.gan.data_prep: Top 10000 categories cover 99.93% of samples
2025-07-04 09:16:02,864 [INFO] src.gan.data_prep: Categorical feature C7: final_vocab_size=10001
2025-07-04 09:16:02,874 [INFO] src.gan.data_prep: Feature C8: 447 unique values
2025-07-04 09:16:02,875 [INFO] src.gan.data_prep: Categorical feature C8: final_vocab_size=448
2025-07-04 09:16:02,878 [INFO] src.gan.data_prep: Feature C9: 4 unique values
2025-07-04 09:16:02,878 [INFO] src.gan.data_prep: Categorical feature C9: final_vocab_size=5
2025-07-04 09:16:02,882 [INFO] src.gan.data_prep: Feature C10: 24212 unique values
2025-07-04 09:16:02,882 [WARNING] src.gan.data_prep: Feature C10 has 24212 categories! Reducing to top 10000
2025-07-04 09:16:02,884 [INFO] src.gan.data_prep: Top 10000 categories cover 96.03% of samples
2025-07-04 09:16:02,887 [INFO] src.gan.data_prep: Categorical feature C10: final_vocab_size=10001
2025-07-04 09:16:02,904 [INFO] src.gan.data_prep: Feature C11: 4612 unique values
2025-07-04 09:16:02,905 [INFO] src.gan.data_prep: Categorical feature C11: final_vocab_size=4613
2025-07-04 09:16:02,915 [INFO] src.gan.data_prep: Feature C12: 162318 unique values
2025-07-04 09:16:02,915 [WARNING] src.gan.data_prep: Feature C12 has 162318 categories! Reducing to top 10000
2025-07-04 09:16:02,918 [INFO] src.gan.data_prep: Top 10000 categories cover 66.46% of samples
2025-07-04 09:16:02,921 [INFO] src.gan.data_prep: Categorical feature C12: final_vocab_size=10001
2025-07-04 09:16:03,002 [INFO] src.gan.data_prep: Feature C13: 3072 unique values
2025-07-04 09:16:03,003 [INFO] src.gan.data_prep: Categorical feature C13: final_vocab_size=3073
2025-07-04 09:16:03,008 [INFO] src.gan.data_prep: Feature C14: 27 unique values
2025-07-04 09:16:03,008 [INFO] src.gan.data_prep: Categorical feature C14: final_vocab_size=28
2025-07-04 09:16:03,011 [INFO] src.gan.data_prep: Feature C15: 7961 unique values
2025-07-04 09:16:03,014 [INFO] src.gan.data_prep: Categorical feature C15: final_vocab_size=7962
2025-07-04 09:16:03,024 [INFO] src.gan.data_prep: Feature C16: 128551 unique values
2025-07-04 09:16:03,024 [WARNING] src.gan.data_prep: Feature C16 has 128551 categories! Reducing to top 10000
2025-07-04 09:16:03,027 [INFO] src.gan.data_prep: Top 10000 categories cover 73.14% of samples
2025-07-04 09:16:03,029 [INFO] src.gan.data_prep: Categorical feature C16: final_vocab_size=10001
2025-07-04 09:16:03,094 [INFO] src.gan.data_prep: Feature C17: 11 unique values
2025-07-04 09:16:03,094 [INFO] src.gan.data_prep: Categorical feature C17: final_vocab_size=12
2025-07-04 09:16:03,097 [INFO] src.gan.data_prep: Feature C18: 3555 unique values
2025-07-04 09:16:03,098 [INFO] src.gan.data_prep: Categorical feature C18: final_vocab_size=3556
2025-07-04 09:16:03,102 [INFO] src.gan.data_prep: Feature C19: 1698 unique values
2025-07-04 09:16:03,103 [INFO] src.gan.data_prep: Categorical feature C19: final_vocab_size=1699
2025-07-04 09:16:03,106 [INFO] src.gan.data_prep: Feature C20: 4 unique values
2025-07-04 09:16:03,106 [INFO] src.gan.data_prep: Categorical feature C20: final_vocab_size=5
2025-07-04 09:16:03,113 [INFO] src.gan.data_prep: Feature C21: 147885 unique values
2025-07-04 09:16:03,113 [WARNING] src.gan.data_prep: Feature C21 has 147885 categories! Reducing to top 10000
2025-07-04 09:16:03,116 [INFO] src.gan.data_prep: Top 10000 categories cover 69.30% of samples
2025-07-04 09:16:03,118 [INFO] src.gan.data_prep: Categorical feature C21: final_vocab_size=10001
2025-07-04 09:16:03,193 [INFO] src.gan.data_prep: Feature C22: 15 unique values
2025-07-04 09:16:03,194 [INFO] src.gan.data_prep: Categorical feature C22: final_vocab_size=16
2025-07-04 09:16:03,196 [INFO] src.gan.data_prep: Feature C23: 16 unique values
2025-07-04 09:16:03,196 [INFO] src.gan.data_prep: Categorical feature C23: final_vocab_size=17
2025-07-04 09:16:03,199 [INFO] src.gan.data_prep: Feature C24: 29660 unique values
2025-07-04 09:16:03,200 [WARNING] src.gan.data_prep: Feature C24 has 29660 categories! Reducing to top 10000
2025-07-04 09:16:03,202 [INFO] src.gan.data_prep: Top 10000 categories cover 95.38% of samples
2025-07-04 09:16:03,205 [INFO] src.gan.data_prep: Categorical feature C24: final_vocab_size=10001
2025-07-04 09:16:03,223 [INFO] src.gan.data_prep: Feature C25: 68 unique values
2025-07-04 09:16:03,224 [INFO] src.gan.data_prep: Categorical feature C25: final_vocab_size=69
2025-07-04 09:16:03,227 [INFO] src.gan.data_prep: Feature C26: 22328 unique values
2025-07-04 09:16:03,227 [WARNING] src.gan.data_prep: Feature C26 has 22328 categories! Reducing to top 10000
2025-07-04 09:16:03,229 [INFO] src.gan.data_prep: Top 10000 categories cover 97.38% of samples
2025-07-04 09:16:03,232 [INFO] src.gan.data_prep: Categorical feature C26: final_vocab_size=10001
2025-07-04 09:16:03,245 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 09:16:03,245 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 09:16:03,596 [INFO] src.gan.data_prep: Preprocessing info saved to /data/balanced_gan_v2/data/gan_preprocessing_info.pkl
2025-07-04 09:16:03,598 [INFO] src.gan.data_prep: GAN preprocessing completed
2025-07-04 09:16:03,598 [INFO] root: Saving processed data cache to /data/balanced_gan_v2/data/processed_Criteo_train_full_500000_10000.pkl
2025-07-04 09:16:03,985 [INFO] __main__: 最终数据集大小: 500000 样本
2025-07-04 09:16:03,986 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 09:16:03,986 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 09:16:03,986 [INFO] __main__: 特征信息: {'numeric_features': ['I1', 'I2', 'I3', 'I4', 'I5', 'I6', 'I7', 'I8', 'I9', 'I10', 'I11', 'I12', 'I13'], 'categorical_features': ['C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10', 'C11', 'C12', 'C13', 'C14', 'C15', 'C16', 'C17', 'C18', 'C19', 'C20', 'C21', 'C22', 'C23', 'C24', 'C25', 'C26'], 'vocab_sizes': [1032, 529, 10001, 10001, 226, 16, 10001, 448, 5, 10001, 4613, 10001, 3073, 28, 7962, 10001, 12, 3556, 1699, 5, 10001, 16, 17, 10001, 69, 10001], 'label_col': 'Label', 'dataset_name': 'Criteo', 'vocab_info_summary': {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}}
2025-07-04 09:16:04,313 [INFO] __main__: 创建数据加载器，共 976 批次
2025-07-04 09:16:04,340 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 09:16:04,341 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 09:16:04,341 [INFO] __main__: 创建改进的Generator和Discriminator模型
2025-07-04 09:16:04,461 [INFO] __main__: Generator参数量: 10,183,879
2025-07-04 09:16:04,461 [INFO] __main__: Discriminator参数量: 1,969,266
2025-07-04 09:16:04,461 [INFO] __main__: 参数比例 (G/D): 5.17
2025-07-04 09:16:04,461 [INFO] __main__: 学习率比例 (G/D): 10.00
2025-07-04 09:16:05,591 [INFO] __main__: 开始改进的训练循环
2025-07-04 09:16:05,591 [INFO] __main__: 开始第1/30个epoch (阶段: 0)
2025-07-04 09:16:06,494 [INFO] src.gan.trainer: Epoch 0, Batch 0/976, Stage 0, D_loss: 0.8883, G_loss: -2.1960, Num_std: 0.377706, Num_range: 3.6944, Real_score: 0.5008, Fake_score: 0.4852
2025-07-04 09:16:21,851 [INFO] src.gan.trainer: Epoch 0, Batch 100/976, Stage 0, D_loss: 0.6683, G_loss: -3.8174, Num_std: 0.084741, Num_range: 3.9932, Real_score: 0.6108, Fake_score: 0.3640
2025-07-04 09:16:37,196 [INFO] src.gan.trainer: Epoch 0, Batch 200/976, Stage 0, D_loss: 0.6199, G_loss: -3.7194, Num_std: 0.087651, Num_range: 3.9982, Real_score: 0.6825, Fake_score: 0.3071
2025-07-04 09:16:52,682 [INFO] src.gan.trainer: Epoch 0, Batch 300/976, Stage 0, D_loss: 0.6407, G_loss: -3.7671, Num_std: 0.074313, Num_range: 3.9994, Real_score: 0.6924, Fake_score: 0.3051
2025-07-04 09:17:07,947 [INFO] src.gan.trainer: Epoch 0, Batch 400/976, Stage 0, D_loss: 0.6512, G_loss: -3.7487, Num_std: 0.078082, Num_range: 3.9996, Real_score: 0.6949, Fake_score: 0.3027
2025-07-04 09:17:23,218 [INFO] src.gan.trainer: Epoch 0, Batch 500/976, Stage 0, D_loss: 0.6428, G_loss: -3.7679, Num_std: 0.089047, Num_range: 3.9998, Real_score: 0.6989, Fake_score: 0.3060
2025-07-04 09:17:38,525 [INFO] src.gan.trainer: Epoch 0, Batch 600/976, Stage 0, D_loss: 0.6141, G_loss: -3.7735, Num_std: 0.060040, Num_range: 3.9998, Real_score: 0.6868, Fake_score: 0.3063
2025-07-04 09:17:54,024 [INFO] src.gan.trainer: Epoch 0, Batch 700/976, Stage 0, D_loss: 0.5932, G_loss: -3.5743, Num_std: 0.074078, Num_range: 3.9999, Real_score: 0.6925, Fake_score: 0.3032
2025-07-04 09:18:09,438 [INFO] src.gan.trainer: Epoch 0, Batch 800/976, Stage 0, D_loss: 0.5727, G_loss: -3.7497, Num_std: 0.079996, Num_range: 3.9999, Real_score: 0.6915, Fake_score: 0.3066
2025-07-04 09:18:24,809 [INFO] src.gan.trainer: Epoch 0, Batch 900/976, Stage 0, D_loss: 0.5873, G_loss: -3.7167, Num_std: 0.071858, Num_range: 3.9999, Real_score: 0.6945, Fake_score: 0.3055
2025-07-04 09:18:36,409 [INFO] __main__: 第1个epoch完成. 指标: {'d_loss': 0.6292658177677736, 'd_adv_loss': 0.053958362182144226, 'd_ctr_loss': 0.5587069775725977, 'd_reg_loss': 0.11066983880867107, 'real_d_score': 0.6751739368086948, 'fake_d_score': 0.32076184775252814, 'g_loss': -3.7172828551198656, 'g_adv_loss': 0.46585972461584835, 'g_feature_loss': 0.9080296062172242, 'g_diversity_loss': -0.9996680957901358, 'g_numeric_diversity_loss': -0.4036094955056703, 'fake_d_score_g': 0.3201674283609363, 'temperature': 3.4999999999999174, 'numeric_std': 0.08178587625111083, 'numeric_range': 3.994520514796337, 'training_stage': 0.0}
2025-07-04 09:18:36,410 [INFO] __main__: 开始第2/30个epoch (阶段: 0)
2025-07-04 09:18:36,688 [INFO] src.gan.trainer: Epoch 1, Batch 0/976, Stage 0, D_loss: 0.5615, G_loss: -3.7647, Num_std: 0.071238, Num_range: 4.0000, Real_score: 0.7034, Fake_score: 0.3045
2025-07-04 09:18:52,267 [INFO] src.gan.trainer: Epoch 1, Batch 100/976, Stage 0, D_loss: 0.5239, G_loss: -3.7587, Num_std: 0.072720, Num_range: 4.0000, Real_score: 0.6931, Fake_score: 0.3025
2025-07-04 09:19:07,721 [INFO] src.gan.trainer: Epoch 1, Batch 200/976, Stage 0, D_loss: 0.6006, G_loss: -3.7486, Num_std: 0.072150, Num_range: 4.0000, Real_score: 0.6944, Fake_score: 0.3040
2025-07-04 09:19:23,222 [INFO] src.gan.trainer: Epoch 1, Batch 300/976, Stage 0, D_loss: 0.5546, G_loss: -3.7517, Num_std: 0.069035, Num_range: 4.0000, Real_score: 0.6994, Fake_score: 0.3175
2025-07-04 09:19:38,641 [INFO] src.gan.trainer: Epoch 1, Batch 400/976, Stage 0, D_loss: 0.5882, G_loss: -3.7077, Num_std: 0.074292, Num_range: 4.0000, Real_score: 0.6945, Fake_score: 0.3093
2025-07-04 09:19:54,153 [INFO] src.gan.trainer: Epoch 1, Batch 500/976, Stage 0, D_loss: 0.5638, G_loss: -3.3679, Num_std: 0.073201, Num_range: 4.0000, Real_score: 0.6931, Fake_score: 0.3072
2025-07-04 09:20:09,556 [INFO] src.gan.trainer: Epoch 1, Batch 600/976, Stage 0, D_loss: 0.5329, G_loss: -3.6850, Num_std: 0.078692, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3066
2025-07-04 09:20:24,948 [INFO] src.gan.trainer: Epoch 1, Batch 700/976, Stage 0, D_loss: 0.5804, G_loss: -3.6915, Num_std: 0.106150, Num_range: 4.0000, Real_score: 0.7017, Fake_score: 0.3068
2025-07-04 09:20:40,362 [INFO] src.gan.trainer: Epoch 1, Batch 800/976, Stage 0, D_loss: 0.5553, G_loss: -3.6970, Num_std: 0.084471, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3003
2025-07-04 09:20:55,897 [INFO] src.gan.trainer: Epoch 1, Batch 900/976, Stage 0, D_loss: 0.5753, G_loss: -3.6978, Num_std: 0.099148, Num_range: 4.0000, Real_score: 0.6968, Fake_score: 0.3020
2025-07-04 09:21:07,513 [INFO] __main__: 第2个epoch完成. 指标: {'d_loss': 0.5613675668347082, 'd_adv_loss': 0.04472807405485968, 'd_ctr_loss': 0.4968778769806271, 'd_reg_loss': 0.1317441118804769, 'real_d_score': 0.6953048228484682, 'fake_d_score': 0.30555391974258655, 'g_loss': -3.6696377089806957, 'g_adv_loss': 0.48592875773708033, 'g_feature_loss': 0.7637113166229975, 'g_diversity_loss': -0.9376784765207368, 'g_numeric_diversity_loss': -0.4070091947780849, 'fake_d_score_g': 0.3047760324501763, 'temperature': 3.4965000000000583, 'numeric_std': 0.07766828486311651, 'numeric_range': 3.9999774060125572, 'training_stage': 0.0}
2025-07-04 09:21:07,514 [INFO] __main__: 在第2个epoch评估改进的GAN
2025-07-04 09:21:07,533 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 09:21:07,533 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 09:21:07,534 [INFO] __main__: 改进GAN评估指标:
2025-07-04 09:21:07,534 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 09:21:07,535 [INFO] __main__:   特征平均标准差: 0.000011
2025-07-04 09:21:07,535 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 09:21:07,535 [INFO] __main__: 训练质量: 需要改进
2025-07-04 09:21:07,536 [INFO] __main__: 新的最佳数值特征质量: 0.400
2025-07-04 09:21:07,757 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_best_model.pt
2025-07-04 09:21:07,758 [INFO] __main__: 保存最佳模型，数值质量评分: 0.400
2025-07-04 09:21:07,956 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_2.pt
2025-07-04 09:21:07,957 [INFO] __main__: 开始第3/30个epoch (阶段: 0)
2025-07-04 09:21:08,233 [INFO] src.gan.trainer: Epoch 2, Batch 0/976, Stage 0, D_loss: 0.5751, G_loss: -3.6075, Num_std: 0.066279, Num_range: 4.0000, Real_score: 0.6950, Fake_score: 0.3048
2025-07-04 09:21:23,647 [INFO] src.gan.trainer: Epoch 2, Batch 100/976, Stage 0, D_loss: 0.5651, G_loss: -3.7184, Num_std: 0.068506, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3009
2025-07-04 09:21:39,239 [INFO] src.gan.trainer: Epoch 2, Batch 200/976, Stage 0, D_loss: 0.5591, G_loss: -3.7179, Num_std: 0.088168, Num_range: 4.0000, Real_score: 0.6934, Fake_score: 0.3029
2025-07-04 09:21:54,658 [INFO] src.gan.trainer: Epoch 2, Batch 300/976, Stage 0, D_loss: 0.5394, G_loss: -3.7342, Num_std: 0.056295, Num_range: 4.0000, Real_score: 0.6923, Fake_score: 0.3035
2025-07-04 09:22:10,111 [INFO] src.gan.trainer: Epoch 2, Batch 400/976, Stage 0, D_loss: 0.5384, G_loss: -3.7367, Num_std: 0.067071, Num_range: 4.0000, Real_score: 0.6983, Fake_score: 0.2989
2025-07-04 09:22:25,572 [INFO] src.gan.trainer: Epoch 2, Batch 500/976, Stage 0, D_loss: 0.5948, G_loss: -3.6551, Num_std: 0.064389, Num_range: 4.0000, Real_score: 0.6930, Fake_score: 0.3043
2025-07-04 09:22:41,027 [INFO] src.gan.trainer: Epoch 2, Batch 600/976, Stage 0, D_loss: 0.5506, G_loss: -3.7484, Num_std: 0.071149, Num_range: 4.0000, Real_score: 0.6945, Fake_score: 0.3023
2025-07-04 09:22:56,354 [INFO] src.gan.trainer: Epoch 2, Batch 700/976, Stage 0, D_loss: 0.5315, G_loss: -3.7512, Num_std: 0.066774, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3022
2025-07-04 09:23:11,644 [INFO] src.gan.trainer: Epoch 2, Batch 800/976, Stage 0, D_loss: 0.5476, G_loss: -3.7538, Num_std: 0.085844, Num_range: 4.0000, Real_score: 0.6952, Fake_score: 0.3002
2025-07-04 09:23:26,908 [INFO] src.gan.trainer: Epoch 2, Batch 900/976, Stage 0, D_loss: 0.5468, G_loss: -3.7639, Num_std: 0.063796, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3056
2025-07-04 09:23:38,435 [INFO] __main__: 第3个epoch完成. 指标: {'d_loss': 0.5430910839225913, 'd_adv_loss': 0.04337718172884382, 'd_ctr_loss': 0.4793603947905241, 'd_reg_loss': 0.13569004140549973, 'real_d_score': 0.6961558628521992, 'fake_d_score': 0.3028368068339886, 'g_loss': -3.708115216853515, 'g_adv_loss': 0.48824704661354645, 'g_feature_loss': 0.6913033080557003, 'g_diversity_loss': -0.9580362731275348, 'g_numeric_diversity_loss': -0.40911139422121706, 'fake_d_score_g': 0.30267353115674556, 'temperature': 3.4930035000000896, 'numeric_std': 0.07568778273111319, 'numeric_range': 3.999996521433846, 'training_stage': 0.0}
2025-07-04 09:23:38,436 [INFO] __main__: 开始第4/30个epoch (阶段: 0)
2025-07-04 09:23:38,715 [INFO] src.gan.trainer: Epoch 3, Batch 0/976, Stage 0, D_loss: 0.5241, G_loss: -3.7489, Num_std: 0.074696, Num_range: 4.0000, Real_score: 0.6967, Fake_score: 0.3014
2025-07-04 09:23:54,305 [INFO] src.gan.trainer: Epoch 3, Batch 100/976, Stage 0, D_loss: 0.4958, G_loss: -3.7709, Num_std: 0.057081, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3024
2025-07-04 09:24:09,708 [INFO] src.gan.trainer: Epoch 3, Batch 200/976, Stage 0, D_loss: 0.5402, G_loss: -3.5841, Num_std: 0.073048, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3013
2025-07-04 09:24:25,062 [INFO] src.gan.trainer: Epoch 3, Batch 300/976, Stage 0, D_loss: 0.5731, G_loss: -3.7714, Num_std: 0.083206, Num_range: 4.0000, Real_score: 0.6976, Fake_score: 0.3034
2025-07-04 09:24:40,542 [INFO] src.gan.trainer: Epoch 3, Batch 400/976, Stage 0, D_loss: 0.5625, G_loss: -3.4030, Num_std: 0.084151, Num_range: 4.0000, Real_score: 0.6948, Fake_score: 0.3058
2025-07-04 09:24:56,034 [INFO] src.gan.trainer: Epoch 3, Batch 500/976, Stage 0, D_loss: 0.5130, G_loss: -3.7752, Num_std: 0.077860, Num_range: 4.0000, Real_score: 0.6964, Fake_score: 0.3017
2025-07-04 09:25:11,595 [INFO] src.gan.trainer: Epoch 3, Batch 600/976, Stage 0, D_loss: 0.5605, G_loss: -3.7653, Num_std: 0.069200, Num_range: 4.0000, Real_score: 0.7000, Fake_score: 0.3011
2025-07-04 09:25:27,091 [INFO] src.gan.trainer: Epoch 3, Batch 700/976, Stage 0, D_loss: 0.5491, G_loss: -3.7575, Num_std: 0.048193, Num_range: 4.0000, Real_score: 0.7010, Fake_score: 0.3043
2025-07-04 09:25:42,664 [INFO] src.gan.trainer: Epoch 3, Batch 800/976, Stage 0, D_loss: 0.5337, G_loss: -3.7673, Num_std: 0.080608, Num_range: 4.0000, Real_score: 0.6935, Fake_score: 0.3019
2025-07-04 09:25:58,128 [INFO] src.gan.trainer: Epoch 3, Batch 900/976, Stage 0, D_loss: 0.4991, G_loss: -3.7721, Num_std: 0.081298, Num_range: 4.0000, Real_score: 0.6967, Fake_score: 0.3030
2025-07-04 09:26:09,716 [INFO] __main__: 第4个epoch完成. 指标: {'d_loss': 0.5314926491653329, 'd_adv_loss': 0.04275944053012207, 'd_ctr_loss': 0.4682705695511866, 'd_reg_loss': 0.1364175900816918, 'real_d_score': 0.696798939807494, 'fake_d_score': 0.30258403257390526, 'g_loss': -3.7495620094361817, 'g_adv_loss': 0.48793465473124215, 'g_feature_loss': 0.8003249480872531, 'g_diversity_loss': -0.9955894574522964, 'g_numeric_diversity_loss': -0.41024043185056214, 'fake_d_score_g': 0.30256034693390627, 'temperature': 3.489510496499961, 'numeric_std': 0.07361446655752356, 'numeric_range': 3.999999300459712, 'training_stage': 0.0}
2025-07-04 09:26:09,717 [INFO] __main__: 在第4个epoch评估改进的GAN
2025-07-04 09:26:09,733 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 09:26:09,733 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 09:26:09,734 [INFO] __main__: 改进GAN评估指标:
2025-07-04 09:26:09,734 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 09:26:09,735 [INFO] __main__:   特征平均标准差: 0.000002
2025-07-04 09:26:09,735 [INFO] __main__:   零标准差特征数: 7.0
2025-07-04 09:26:09,735 [INFO] __main__: 训练质量: 需要改进
2025-07-04 09:26:09,936 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_4.pt
2025-07-04 09:26:09,937 [INFO] __main__: 开始第5/30个epoch (阶段: 0)
2025-07-04 09:26:10,221 [INFO] src.gan.trainer: Epoch 4, Batch 0/976, Stage 0, D_loss: 0.4849, G_loss: -3.7653, Num_std: 0.071956, Num_range: 4.0000, Real_score: 0.7007, Fake_score: 0.3037
2025-07-04 09:26:25,585 [INFO] src.gan.trainer: Epoch 4, Batch 100/976, Stage 0, D_loss: 0.5199, G_loss: -3.7689, Num_std: 0.081322, Num_range: 4.0000, Real_score: 0.6945, Fake_score: 0.3025
2025-07-04 09:26:41,113 [INFO] src.gan.trainer: Epoch 4, Batch 200/976, Stage 0, D_loss: 0.5158, G_loss: -3.7646, Num_std: 0.095318, Num_range: 4.0000, Real_score: 0.6981, Fake_score: 0.3021
2025-07-04 09:26:56,554 [INFO] src.gan.trainer: Epoch 4, Batch 300/976, Stage 0, D_loss: 0.5310, G_loss: -3.7771, Num_std: 0.063881, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3027
2025-07-04 09:27:12,102 [INFO] src.gan.trainer: Epoch 4, Batch 400/976, Stage 0, D_loss: 0.5432, G_loss: -3.7341, Num_std: 0.070468, Num_range: 4.0000, Real_score: 0.7006, Fake_score: 0.3017
2025-07-04 09:27:27,447 [INFO] src.gan.trainer: Epoch 4, Batch 500/976, Stage 0, D_loss: 0.5220, G_loss: -3.7695, Num_std: 0.070938, Num_range: 4.0000, Real_score: 0.6946, Fake_score: 0.3027
2025-07-04 09:27:42,933 [INFO] src.gan.trainer: Epoch 4, Batch 600/976, Stage 0, D_loss: 0.5017, G_loss: -3.7730, Num_std: 0.075727, Num_range: 4.0000, Real_score: 0.6960, Fake_score: 0.3002
2025-07-04 09:27:58,242 [INFO] src.gan.trainer: Epoch 4, Batch 700/976, Stage 0, D_loss: 0.5086, G_loss: -3.5590, Num_std: 0.056873, Num_range: 4.0000, Real_score: 0.6936, Fake_score: 0.3010
2025-07-04 09:28:13,568 [INFO] src.gan.trainer: Epoch 4, Batch 800/976, Stage 0, D_loss: 0.5362, G_loss: -3.7698, Num_std: 0.089313, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.3058
2025-07-04 09:28:28,879 [INFO] src.gan.trainer: Epoch 4, Batch 900/976, Stage 0, D_loss: 0.5251, G_loss: -3.7477, Num_std: 0.054009, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3031
2025-07-04 09:28:40,515 [INFO] __main__: 第5个epoch完成. 指标: {'d_loss': 0.5226903046618717, 'd_adv_loss': 0.04262526411196736, 'd_ctr_loss': 0.4596393154170667, 'd_reg_loss': 0.13617150021381097, 'real_d_score': 0.6965983550934525, 'fake_d_score': 0.3024547039912859, 'g_loss': -3.754510369009337, 'g_adv_loss': 0.4880453155279324, 'g_feature_loss': 0.87573147239418, 'g_diversity_loss': -0.9987016155542247, 'g_numeric_diversity_loss': -0.41095508031465655, 'fake_d_score_g': 0.30240244915088066, 'temperature': 3.48602098600344, 'numeric_std': 0.07335272014759407, 'numeric_range': 3.9999998539197668, 'training_stage': 0.0}
2025-07-04 09:28:40,516 [INFO] __main__: 开始第6/30个epoch (阶段: 0)
2025-07-04 09:28:40,794 [INFO] src.gan.trainer: Epoch 5, Batch 0/976, Stage 0, D_loss: 0.4874, G_loss: -3.7720, Num_std: 0.072688, Num_range: 4.0000, Real_score: 0.6996, Fake_score: 0.3001
2025-07-04 09:28:56,138 [INFO] src.gan.trainer: Epoch 5, Batch 100/976, Stage 0, D_loss: 0.4994, G_loss: -3.7666, Num_std: 0.068650, Num_range: 4.0000, Real_score: 0.6925, Fake_score: 0.2988
2025-07-04 09:29:11,455 [INFO] src.gan.trainer: Epoch 5, Batch 200/976, Stage 0, D_loss: 0.5473, G_loss: -3.7702, Num_std: 0.062514, Num_range: 4.0000, Real_score: 0.6984, Fake_score: 0.3034
2025-07-04 09:29:26,758 [INFO] src.gan.trainer: Epoch 5, Batch 300/976, Stage 0, D_loss: 0.5555, G_loss: -3.7636, Num_std: 0.074328, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.3006
2025-07-04 09:29:42,533 [INFO] src.gan.trainer: Epoch 5, Batch 400/976, Stage 0, D_loss: 0.4464, G_loss: -3.7718, Num_std: 0.084962, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.3028
2025-07-04 09:29:57,869 [INFO] src.gan.trainer: Epoch 5, Batch 500/976, Stage 0, D_loss: 0.5371, G_loss: -3.7669, Num_std: 0.080488, Num_range: 4.0000, Real_score: 0.6947, Fake_score: 0.3011
2025-07-04 09:30:13,339 [INFO] src.gan.trainer: Epoch 5, Batch 600/976, Stage 0, D_loss: 0.5110, G_loss: -3.7680, Num_std: 0.079092, Num_range: 4.0000, Real_score: 0.6938, Fake_score: 0.3006
2025-07-04 09:30:28,783 [INFO] src.gan.trainer: Epoch 5, Batch 700/976, Stage 0, D_loss: 0.5432, G_loss: -3.7682, Num_std: 0.070104, Num_range: 4.0000, Real_score: 0.6976, Fake_score: 0.3026
2025-07-04 09:30:44,332 [INFO] src.gan.trainer: Epoch 5, Batch 800/976, Stage 0, D_loss: 0.5162, G_loss: -3.7699, Num_std: 0.062972, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.3064
2025-07-04 09:30:59,925 [INFO] src.gan.trainer: Epoch 5, Batch 900/976, Stage 0, D_loss: 0.4955, G_loss: -3.7679, Num_std: 0.079905, Num_range: 4.0000, Real_score: 0.6938, Fake_score: 0.3018
2025-07-04 09:31:11,530 [INFO] __main__: 第6个epoch完成. 指标: {'d_loss': 0.5153166752797174, 'd_adv_loss': 0.04242365800424429, 'd_ctr_loss': 0.45243189044174625, 'd_reg_loss': 0.13640750671324667, 'real_d_score': 0.6968967077917748, 'fake_d_score': 0.3024633962538887, 'g_loss': -3.752925027345049, 'g_adv_loss': 0.487845111058139, 'g_feature_loss': 0.9362638062425978, 'g_diversity_loss': -0.9993765890354008, 'g_numeric_diversity_loss': -0.4110258423412841, 'fake_d_score_g': 0.30244862827691227, 'temperature': 3.48253496501745, 'numeric_std': 0.07169444910810413, 'numeric_range': 3.999999999022832, 'training_stage': 0.0}
2025-07-04 09:31:11,531 [INFO] __main__: 在第6个epoch评估改进的GAN
2025-07-04 09:31:11,547 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 09:31:11,547 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 09:31:11,548 [INFO] __main__: 改进GAN评估指标:
2025-07-04 09:31:11,548 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 09:31:11,548 [INFO] __main__:   特征平均标准差: 0.000001
2025-07-04 09:31:11,548 [INFO] __main__:   零标准差特征数: 9.0
2025-07-04 09:31:11,548 [INFO] __main__: 训练质量: 需要改进
2025-07-04 09:31:11,749 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_6.pt
2025-07-04 09:31:11,749 [INFO] __main__: 开始第7/30个epoch (阶段: 0)
2025-07-04 09:31:12,026 [INFO] src.gan.trainer: Epoch 6, Batch 0/976, Stage 0, D_loss: 0.5474, G_loss: -3.7667, Num_std: 0.066002, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3028
2025-07-04 09:31:27,519 [INFO] src.gan.trainer: Epoch 6, Batch 100/976, Stage 0, D_loss: 0.4840, G_loss: -3.7655, Num_std: 0.072057, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3004
2025-07-04 09:31:43,140 [INFO] src.gan.trainer: Epoch 6, Batch 200/976, Stage 0, D_loss: 0.4901, G_loss: -3.6952, Num_std: 0.084003, Num_range: 4.0000, Real_score: 0.7005, Fake_score: 0.3141
2025-07-04 09:31:58,602 [INFO] src.gan.trainer: Epoch 6, Batch 300/976, Stage 0, D_loss: 0.5178, G_loss: -3.7164, Num_std: 0.075139, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3048
2025-07-04 09:32:14,039 [INFO] src.gan.trainer: Epoch 6, Batch 400/976, Stage 0, D_loss: 0.4933, G_loss: -3.5973, Num_std: 0.066462, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3040
2025-07-04 09:32:29,605 [INFO] src.gan.trainer: Epoch 6, Batch 500/976, Stage 0, D_loss: 0.5070, G_loss: -3.7285, Num_std: 0.067664, Num_range: 4.0000, Real_score: 0.6976, Fake_score: 0.3046
2025-07-04 09:32:45,089 [INFO] src.gan.trainer: Epoch 6, Batch 600/976, Stage 0, D_loss: 0.5572, G_loss: -3.7381, Num_std: 0.062242, Num_range: 4.0000, Real_score: 0.6955, Fake_score: 0.3023
2025-07-04 09:33:00,463 [INFO] src.gan.trainer: Epoch 6, Batch 700/976, Stage 0, D_loss: 0.5028, G_loss: -3.7307, Num_std: 0.071780, Num_range: 4.0000, Real_score: 0.6992, Fake_score: 0.3043
2025-07-04 09:33:15,917 [INFO] src.gan.trainer: Epoch 6, Batch 800/976, Stage 0, D_loss: 0.4635, G_loss: -3.7304, Num_std: 0.068937, Num_range: 4.0000, Real_score: 0.6984, Fake_score: 0.3014
2025-07-04 09:33:31,412 [INFO] src.gan.trainer: Epoch 6, Batch 900/976, Stage 0, D_loss: 0.5377, G_loss: -3.7296, Num_std: 0.077185, Num_range: 4.0000, Real_score: 0.6942, Fake_score: 0.3049
2025-07-04 09:33:43,156 [INFO] __main__: 第7个epoch完成. 指标: {'d_loss': 0.5092799142919112, 'd_adv_loss': 0.04260237984832565, 'd_ctr_loss': 0.4462807845385353, 'd_reg_loss': 0.13597832991146167, 'real_d_score': 0.6969933395625132, 'fake_d_score': 0.30296687723793964, 'g_loss': -3.7193378706773155, 'g_adv_loss': 0.48766682127012184, 'g_feature_loss': 1.4645082926367468, 'g_diversity_loss': -0.9900861050465402, 'g_numeric_diversity_loss': -0.4112680002314142, 'fake_d_score_g': 0.3027861864596111, 'temperature': 3.4790524300524415, 'numeric_std': 0.0717225526631275, 'numeric_range': 3.999999999999957, 'training_stage': 0.0}
2025-07-04 09:33:43,156 [INFO] __main__: 开始第8/30个epoch (阶段: 0)
2025-07-04 09:33:43,440 [INFO] src.gan.trainer: Epoch 7, Batch 0/976, Stage 0, D_loss: 0.5090, G_loss: -3.7323, Num_std: 0.069799, Num_range: 4.0000, Real_score: 0.6936, Fake_score: 0.3021
2025-07-04 09:33:58,941 [INFO] src.gan.trainer: Epoch 7, Batch 100/976, Stage 0, D_loss: 0.5240, G_loss: -3.7359, Num_std: 0.061595, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3007
2025-07-04 09:34:14,436 [INFO] src.gan.trainer: Epoch 7, Batch 200/976, Stage 0, D_loss: 0.4902, G_loss: -3.7325, Num_std: 0.070074, Num_range: 4.0000, Real_score: 0.6959, Fake_score: 0.3039
2025-07-04 09:34:29,973 [INFO] src.gan.trainer: Epoch 7, Batch 300/976, Stage 0, D_loss: 0.4964, G_loss: -3.7354, Num_std: 0.052550, Num_range: 4.0000, Real_score: 0.6967, Fake_score: 0.3023
2025-07-04 09:34:45,591 [INFO] src.gan.trainer: Epoch 7, Batch 400/976, Stage 0, D_loss: 0.5495, G_loss: -3.7087, Num_std: 0.065303, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3017
2025-07-04 09:35:01,012 [INFO] src.gan.trainer: Epoch 7, Batch 500/976, Stage 0, D_loss: 0.5152, G_loss: -3.7361, Num_std: 0.060176, Num_range: 4.0000, Real_score: 0.6992, Fake_score: 0.3042
2025-07-04 09:35:16,476 [INFO] src.gan.trainer: Epoch 7, Batch 600/976, Stage 0, D_loss: 0.4826, G_loss: -3.7108, Num_std: 0.080554, Num_range: 4.0000, Real_score: 0.6953, Fake_score: 0.3007
2025-07-04 09:35:31,917 [INFO] src.gan.trainer: Epoch 7, Batch 700/976, Stage 0, D_loss: 0.5008, G_loss: -3.7395, Num_std: 0.067100, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3053
2025-07-04 09:35:47,502 [INFO] src.gan.trainer: Epoch 7, Batch 800/976, Stage 0, D_loss: 0.5071, G_loss: -3.7616, Num_std: 0.064396, Num_range: 4.0000, Real_score: 0.6997, Fake_score: 0.3023
2025-07-04 09:36:02,946 [INFO] src.gan.trainer: Epoch 7, Batch 900/976, Stage 0, D_loss: 0.4989, G_loss: -3.7616, Num_std: 0.088117, Num_range: 4.0000, Real_score: 0.6933, Fake_score: 0.3023
2025-07-04 09:36:14,564 [INFO] __main__: 第8个epoch完成. 指标: {'d_loss': 0.5043016443120655, 'd_adv_loss': 0.042362169576350785, 'd_ctr_loss': 0.44151691811495075, 'd_reg_loss': 0.1361503770086365, 'real_d_score': 0.6973160438728138, 'fake_d_score': 0.30301372719104147, 'g_loss': -3.7292696154508436, 'g_adv_loss': 0.48739570570610863, 'g_feature_loss': 1.3744058339892198, 'g_diversity_loss': -0.993270953109519, 'g_numeric_diversity_loss': -0.4115143324772685, 'fake_d_score_g': 0.30278977807713, 'temperature': 3.4755733776224775, 'numeric_std': 0.07229627347920255, 'numeric_range': 3.999999999999957, 'training_stage': 0.0}
2025-07-04 09:36:14,565 [INFO] __main__: 在第8个epoch评估改进的GAN
2025-07-04 09:36:14,581 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 09:36:14,581 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 09:36:14,582 [INFO] __main__: 改进GAN评估指标:
2025-07-04 09:36:14,582 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 09:36:14,582 [INFO] __main__:   特征平均标准差: 0.000000
2025-07-04 09:36:14,582 [INFO] __main__:   零标准差特征数: 11.0
2025-07-04 09:36:14,582 [INFO] __main__: 训练质量: 需要改进
2025-07-04 09:36:14,783 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_8.pt
2025-07-04 09:36:14,784 [INFO] __main__: 开始第9/30个epoch (阶段: 0)
2025-07-04 09:36:15,062 [INFO] src.gan.trainer: Epoch 8, Batch 0/976, Stage 0, D_loss: 0.4376, G_loss: -3.7638, Num_std: 0.076199, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3022
2025-07-04 09:36:30,580 [INFO] src.gan.trainer: Epoch 8, Batch 100/976, Stage 0, D_loss: 0.4713, G_loss: -3.4063, Num_std: 0.053080, Num_range: 4.0000, Real_score: 0.6988, Fake_score: 0.3037
2025-07-04 09:36:46,176 [INFO] src.gan.trainer: Epoch 8, Batch 200/976, Stage 0, D_loss: 0.5381, G_loss: -3.7654, Num_std: 0.074702, Num_range: 4.0000, Real_score: 0.6990, Fake_score: 0.2995
2025-07-04 09:37:01,678 [INFO] src.gan.trainer: Epoch 8, Batch 300/976, Stage 0, D_loss: 0.4848, G_loss: -3.7661, Num_std: 0.072267, Num_range: 4.0000, Real_score: 0.6968, Fake_score: 0.3033
2025-07-04 09:37:17,155 [INFO] src.gan.trainer: Epoch 8, Batch 400/976, Stage 0, D_loss: 0.5263, G_loss: -3.7688, Num_std: 0.070592, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3015
2025-07-04 09:37:32,606 [INFO] src.gan.trainer: Epoch 8, Batch 500/976, Stage 0, D_loss: 0.5107, G_loss: -3.7710, Num_std: 0.070233, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3053
2025-07-04 09:37:48,180 [INFO] src.gan.trainer: Epoch 8, Batch 600/976, Stage 0, D_loss: 0.4999, G_loss: -3.7670, Num_std: 0.069902, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.2997
2025-07-04 09:38:03,623 [INFO] src.gan.trainer: Epoch 8, Batch 700/976, Stage 0, D_loss: 0.5303, G_loss: -3.7613, Num_std: 0.073372, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3013
2025-07-04 09:38:19,035 [INFO] src.gan.trainer: Epoch 8, Batch 800/976, Stage 0, D_loss: 0.5215, G_loss: -3.7593, Num_std: 0.078812, Num_range: 4.0000, Real_score: 0.6959, Fake_score: 0.3030
2025-07-04 09:38:34,601 [INFO] src.gan.trainer: Epoch 8, Batch 900/976, Stage 0, D_loss: 0.4477, G_loss: -3.7569, Num_std: 0.069493, Num_range: 4.0000, Real_score: 0.6992, Fake_score: 0.3027
2025-07-04 09:38:46,191 [INFO] __main__: 第9个epoch完成. 指标: {'d_loss': 0.4996524915465571, 'd_adv_loss': 0.042333309477592056, 'd_ctr_loss': 0.43685823358351067, 'd_reg_loss': 0.13640631953651297, 'real_d_score': 0.6970579975330439, 'fake_d_score': 0.30256464500285535, 'g_loss': -3.7477492522957214, 'g_adv_loss': 0.4877563112216896, 'g_feature_loss': 0.9564064712024459, 'g_diversity_loss': -0.9958280087983016, 'g_numeric_diversity_loss': -0.4109372346779041, 'fake_d_score_g': 0.3024860410009578, 'temperature': 3.4720978042448434, 'numeric_std': 0.07210660899309898, 'numeric_range': 3.999999999999957, 'training_stage': 0.0}
2025-07-04 09:38:46,191 [INFO] __main__: 开始第10/30个epoch (阶段: 0)
2025-07-04 09:38:46,465 [INFO] src.gan.trainer: Epoch 9, Batch 0/976, Stage 0, D_loss: 0.4947, G_loss: -3.7612, Num_std: 0.068502, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3035
2025-07-04 09:39:01,937 [INFO] src.gan.trainer: Epoch 9, Batch 100/976, Stage 0, D_loss: 0.5021, G_loss: -3.5646, Num_std: 0.073058, Num_range: 4.0000, Real_score: 0.6981, Fake_score: 0.3022
2025-07-04 09:39:17,394 [INFO] src.gan.trainer: Epoch 9, Batch 200/976, Stage 0, D_loss: 0.4926, G_loss: -3.7577, Num_std: 0.068887, Num_range: 4.0000, Real_score: 0.6994, Fake_score: 0.3041
2025-07-04 09:39:32,882 [INFO] src.gan.trainer: Epoch 9, Batch 300/976, Stage 0, D_loss: 0.4925, G_loss: -3.7570, Num_std: 0.074831, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3021
2025-07-04 09:39:48,511 [INFO] src.gan.trainer: Epoch 9, Batch 400/976, Stage 0, D_loss: 0.4811, G_loss: -3.7483, Num_std: 0.070392, Num_range: 4.0000, Real_score: 0.6976, Fake_score: 0.2994
2025-07-04 09:40:03,990 [INFO] src.gan.trainer: Epoch 9, Batch 500/976, Stage 0, D_loss: 0.4926, G_loss: -3.7500, Num_std: 0.073118, Num_range: 4.0000, Real_score: 0.6955, Fake_score: 0.3015
2025-07-04 09:40:19,479 [INFO] src.gan.trainer: Epoch 9, Batch 600/976, Stage 0, D_loss: 0.5011, G_loss: -3.7570, Num_std: 0.060953, Num_range: 4.0000, Real_score: 0.6960, Fake_score: 0.3021
2025-07-04 09:40:35,011 [INFO] src.gan.trainer: Epoch 9, Batch 700/976, Stage 0, D_loss: 0.5027, G_loss: -3.7582, Num_std: 0.066731, Num_range: 4.0000, Real_score: 0.6948, Fake_score: 0.3020
2025-07-04 09:40:50,663 [INFO] src.gan.trainer: Epoch 9, Batch 800/976, Stage 0, D_loss: 0.5072, G_loss: -3.1231, Num_std: 0.058665, Num_range: 4.0000, Real_score: 0.6966, Fake_score: 0.2986
2025-07-04 09:41:06,165 [INFO] src.gan.trainer: Epoch 9, Batch 900/976, Stage 0, D_loss: 0.5037, G_loss: -3.7580, Num_std: 0.081164, Num_range: 4.0000, Real_score: 0.7005, Fake_score: 0.3046
2025-07-04 09:41:17,826 [INFO] __main__: 第10个epoch完成. 指标: {'d_loss': 0.49568899629301744, 'd_adv_loss': 0.04231319606273637, 'd_ctr_loss': 0.4329460179280557, 'd_reg_loss': 0.1361985467679677, 'real_d_score': 0.6971919339333406, 'fake_d_score': 0.30282311956779895, 'g_loss': -3.7435553451704826, 'g_adv_loss': 0.48724780665313605, 'g_feature_loss': 1.1233100290384779, 'g_diversity_loss': -0.9942490778904145, 'g_numeric_diversity_loss': -0.4115899467301339, 'fake_d_score_g': 0.30283361403627834, 'temperature': 3.4686257064405845, 'numeric_std': 0.07193485583634184, 'numeric_range': 3.999999999999957, 'training_stage': 0.0}
2025-07-04 09:41:17,826 [INFO] __main__: 在第10个epoch评估改进的GAN
2025-07-04 09:41:17,842 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 09:41:17,843 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 09:41:17,843 [INFO] __main__: 改进GAN评估指标:
2025-07-04 09:41:17,844 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 09:41:17,844 [INFO] __main__:   特征平均标准差: 0.000000
2025-07-04 09:41:17,844 [INFO] __main__:   零标准差特征数: 13.0
2025-07-04 09:41:17,844 [INFO] __main__: 训练质量: 需要改进
2025-07-04 09:41:18,046 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_10.pt
2025-07-04 09:41:18,047 [INFO] __main__: 开始第11/30个epoch (阶段: 0)
2025-07-04 09:41:18,336 [INFO] src.gan.trainer: Epoch 10, Batch 0/976, Stage 0, D_loss: 0.5270, G_loss: -3.7605, Num_std: 0.071164, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3042
2025-07-04 09:41:34,051 [INFO] src.gan.trainer: Epoch 10, Batch 100/976, Stage 0, D_loss: 0.4995, G_loss: -3.7600, Num_std: 0.075403, Num_range: 4.0000, Real_score: 0.6984, Fake_score: 0.3039
2025-07-04 09:41:49,538 [INFO] src.gan.trainer: Epoch 10, Batch 200/976, Stage 0, D_loss: 0.4926, G_loss: -3.7598, Num_std: 0.081934, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3052
2025-07-04 09:42:05,034 [INFO] src.gan.trainer: Epoch 10, Batch 300/976, Stage 0, D_loss: 0.4641, G_loss: -3.7585, Num_std: 0.073807, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3035
2025-07-04 09:42:20,561 [INFO] src.gan.trainer: Epoch 10, Batch 400/976, Stage 0, D_loss: 0.4661, G_loss: -3.7588, Num_std: 0.071927, Num_range: 4.0000, Real_score: 0.6959, Fake_score: 0.3011
2025-07-04 09:42:36,193 [INFO] src.gan.trainer: Epoch 10, Batch 500/976, Stage 0, D_loss: 0.4321, G_loss: -3.7597, Num_std: 0.070053, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3041
2025-07-04 09:42:51,661 [INFO] src.gan.trainer: Epoch 10, Batch 600/976, Stage 0, D_loss: 0.4789, G_loss: -3.7611, Num_std: 0.063119, Num_range: 4.0000, Real_score: 0.7001, Fake_score: 0.3015
2025-07-04 09:43:07,147 [INFO] src.gan.trainer: Epoch 10, Batch 700/976, Stage 0, D_loss: 0.4886, G_loss: -3.7600, Num_std: 0.064223, Num_range: 4.0000, Real_score: 0.6959, Fake_score: 0.3022
2025-07-04 09:43:22,633 [INFO] src.gan.trainer: Epoch 10, Batch 800/976, Stage 0, D_loss: 0.4993, G_loss: -3.7604, Num_std: 0.059108, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3035
2025-07-04 09:43:38,191 [INFO] src.gan.trainer: Epoch 10, Batch 900/976, Stage 0, D_loss: 0.5057, G_loss: -3.7525, Num_std: 0.088927, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3015
2025-07-04 09:43:49,787 [INFO] __main__: 第11个epoch完成. 指标: {'d_loss': 0.4919498600676412, 'd_adv_loss': 0.04219018800000919, 'd_ctr_loss': 0.4292908630898747, 'd_reg_loss': 0.13645871911869673, 'real_d_score': 0.6971727431919724, 'fake_d_score': 0.3025503765608437, 'g_loss': -3.7477288989177175, 'g_adv_loss': 0.48758095570030713, 'g_feature_loss': 1.0775483193211866, 'g_diversity_loss': -0.9962393753230577, 'g_numeric_diversity_loss': -0.41161848705445203, 'fake_d_score_g': 0.30250755431832815, 'temperature': 3.4651570807341785, 'numeric_std': 0.07147518544371342, 'numeric_range': 3.999999999999957, 'training_stage': 0.0}
2025-07-04 09:43:49,787 [INFO] __main__: 开始第12/30个epoch (阶段: 0)
2025-07-04 09:43:50,070 [INFO] src.gan.trainer: Epoch 11, Batch 0/976, Stage 0, D_loss: 0.4917, G_loss: -3.7578, Num_std: 0.078137, Num_range: 4.0000, Real_score: 0.6966, Fake_score: 0.2994
2025-07-04 09:44:05,522 [INFO] src.gan.trainer: Epoch 11, Batch 100/976, Stage 0, D_loss: 0.4464, G_loss: -3.7448, Num_std: 0.056293, Num_range: 4.0000, Real_score: 0.6943, Fake_score: 0.3051
2025-07-04 09:44:20,955 [INFO] src.gan.trainer: Epoch 11, Batch 200/976, Stage 0, D_loss: 0.4866, G_loss: -3.7567, Num_std: 0.077106, Num_range: 4.0000, Real_score: 0.6967, Fake_score: 0.3020
2025-07-04 09:44:36,504 [INFO] src.gan.trainer: Epoch 11, Batch 300/976, Stage 0, D_loss: 0.4943, G_loss: -3.7571, Num_std: 0.064575, Num_range: 4.0000, Real_score: 0.6993, Fake_score: 0.3056
2025-07-04 09:44:51,889 [INFO] src.gan.trainer: Epoch 11, Batch 400/976, Stage 0, D_loss: 0.5338, G_loss: -3.7569, Num_std: 0.071385, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.3025
2025-07-04 09:45:07,270 [INFO] src.gan.trainer: Epoch 11, Batch 500/976, Stage 0, D_loss: 0.4978, G_loss: -3.7524, Num_std: 0.081667, Num_range: 4.0000, Real_score: 0.6993, Fake_score: 0.2983
2025-07-04 09:45:22,679 [INFO] src.gan.trainer: Epoch 11, Batch 600/976, Stage 0, D_loss: 0.4691, G_loss: -3.7471, Num_std: 0.076386, Num_range: 4.0000, Real_score: 0.6965, Fake_score: 0.3050
2025-07-04 09:45:38,190 [INFO] src.gan.trainer: Epoch 11, Batch 700/976, Stage 0, D_loss: 0.4713, G_loss: -3.7528, Num_std: 0.076849, Num_range: 4.0000, Real_score: 0.7011, Fake_score: 0.3015
2025-07-04 09:45:53,565 [INFO] src.gan.trainer: Epoch 11, Batch 800/976, Stage 0, D_loss: 0.4913, G_loss: -3.7499, Num_std: 0.068770, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3044
2025-07-04 09:46:08,914 [INFO] src.gan.trainer: Epoch 11, Batch 900/976, Stage 0, D_loss: 0.5064, G_loss: -3.7480, Num_std: 0.075858, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3056
2025-07-04 09:46:20,475 [INFO] __main__: 第12个epoch完成. 指标: {'d_loss': 0.48802954599749837, 'd_adv_loss': 0.04210321776584152, 'd_ctr_loss': 0.4254346649299879, 'd_reg_loss': 0.1366110826827219, 'real_d_score': 0.6973309858289908, 'fake_d_score': 0.3025503288954495, 'g_loss': -3.744288732974581, 'g_adv_loss': 0.48764761333745676, 'g_feature_loss': 1.1424490816361907, 'g_diversity_loss': -0.9950009969169978, 'g_numeric_diversity_loss': -0.411757225542243, 'fake_d_score_g': 0.3024092255353601, 'temperature': 3.461691923653452, 'numeric_std': 0.07153687963982325, 'numeric_range': 3.999999999999957, 'training_stage': 0.0}
2025-07-04 09:46:20,476 [INFO] __main__: 在第12个epoch评估改进的GAN
2025-07-04 09:46:20,492 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 09:46:20,492 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 09:46:20,493 [INFO] __main__: 改进GAN评估指标:
2025-07-04 09:46:20,493 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 09:46:20,493 [INFO] __main__:   特征平均标准差: 0.000000
2025-07-04 09:46:20,493 [INFO] __main__:   零标准差特征数: 13.0
2025-07-04 09:46:20,493 [INFO] __main__: 训练质量: 需要改进
2025-07-04 09:46:20,693 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_12.pt
2025-07-04 09:46:20,694 [INFO] __main__: 开始第13/30个epoch (阶段: 0)
2025-07-04 09:46:20,978 [INFO] src.gan.trainer: Epoch 12, Batch 0/976, Stage 0, D_loss: 0.4691, G_loss: -3.7528, Num_std: 0.071847, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.3027
2025-07-04 09:46:36,568 [INFO] src.gan.trainer: Epoch 12, Batch 100/976, Stage 0, D_loss: 0.4938, G_loss: -3.7560, Num_std: 0.059713, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3021
2025-07-04 09:46:51,966 [INFO] src.gan.trainer: Epoch 12, Batch 200/976, Stage 0, D_loss: 0.4651, G_loss: -3.7535, Num_std: 0.077262, Num_range: 4.0000, Real_score: 0.6929, Fake_score: 0.3009
2025-07-04 09:47:07,364 [INFO] src.gan.trainer: Epoch 12, Batch 300/976, Stage 0, D_loss: 0.4977, G_loss: -3.7553, Num_std: 0.078338, Num_range: 4.0000, Real_score: 0.6949, Fake_score: 0.3007
2025-07-04 09:47:22,766 [INFO] src.gan.trainer: Epoch 12, Batch 400/976, Stage 0, D_loss: 0.4761, G_loss: -3.7596, Num_std: 0.066497, Num_range: 4.0000, Real_score: 0.6963, Fake_score: 0.3030
2025-07-04 09:47:38,217 [INFO] src.gan.trainer: Epoch 12, Batch 500/976, Stage 0, D_loss: 0.4810, G_loss: -3.7559, Num_std: 0.078180, Num_range: 4.0000, Real_score: 0.6951, Fake_score: 0.3065
2025-07-04 09:47:53,648 [INFO] src.gan.trainer: Epoch 12, Batch 600/976, Stage 0, D_loss: 0.4725, G_loss: -3.7608, Num_std: 0.071793, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3051
2025-07-04 09:48:09,104 [INFO] src.gan.trainer: Epoch 12, Batch 700/976, Stage 0, D_loss: 0.4528, G_loss: -3.6028, Num_std: 0.054847, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3044
2025-07-04 09:48:24,576 [INFO] src.gan.trainer: Epoch 12, Batch 800/976, Stage 0, D_loss: 0.4695, G_loss: -3.7378, Num_std: 0.067595, Num_range: 4.0000, Real_score: 0.6964, Fake_score: 0.3019
2025-07-04 09:48:40,141 [INFO] src.gan.trainer: Epoch 12, Batch 900/976, Stage 0, D_loss: 0.4403, G_loss: -3.7554, Num_std: 0.091938, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3008
2025-07-04 09:48:51,712 [INFO] __main__: 第13个epoch完成. 指标: {'d_loss': 0.48453291401755616, 'd_adv_loss': 0.04201377122510287, 'd_ctr_loss': 0.4219983019484362, 'd_reg_loss': 0.1368056028073684, 'real_d_score': 0.6973492791418159, 'fake_d_score': 0.3023815335797481, 'g_loss': -3.7421675071608838, 'g_adv_loss': 0.4875427164707355, 'g_feature_loss': 1.1312874620729467, 'g_diversity_loss': -0.9949753272036709, 'g_numeric_diversity_loss': -0.4114124088095189, 'fake_d_score_g': 0.302433455138992, 'temperature': 3.458230231729817, 'numeric_std': 0.07220885310070137, 'numeric_range': 3.999999999999957, 'training_stage': 0.0}
2025-07-04 09:48:51,712 [INFO] __main__: 开始第14/30个epoch (阶段: 0)
2025-07-04 09:48:51,991 [INFO] src.gan.trainer: Epoch 13, Batch 0/976, Stage 0, D_loss: 0.5146, G_loss: -3.7540, Num_std: 0.073252, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3005
2025-07-04 09:49:07,491 [INFO] src.gan.trainer: Epoch 13, Batch 100/976, Stage 0, D_loss: 0.4776, G_loss: -3.7569, Num_std: 0.085030, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.3054
2025-07-04 09:49:22,858 [INFO] src.gan.trainer: Epoch 13, Batch 200/976, Stage 0, D_loss: 0.4610, G_loss: -3.7593, Num_std: 0.063694, Num_range: 4.0000, Real_score: 0.6966, Fake_score: 0.3050
2025-07-04 09:49:38,464 [INFO] src.gan.trainer: Epoch 13, Batch 300/976, Stage 0, D_loss: 0.4937, G_loss: -3.7602, Num_std: 0.077798, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3020
2025-07-04 09:49:53,934 [INFO] src.gan.trainer: Epoch 13, Batch 400/976, Stage 0, D_loss: 0.4651, G_loss: -3.7576, Num_std: 0.066602, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3013
2025-07-04 09:50:09,483 [INFO] src.gan.trainer: Epoch 13, Batch 500/976, Stage 0, D_loss: 0.4841, G_loss: -3.5694, Num_std: 0.074757, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3029
2025-07-04 09:50:25,007 [INFO] src.gan.trainer: Epoch 13, Batch 600/976, Stage 0, D_loss: 0.4773, G_loss: -3.7581, Num_std: 0.085352, Num_range: 4.0000, Real_score: 0.6992, Fake_score: 0.3028
2025-07-04 09:50:40,544 [INFO] src.gan.trainer: Epoch 13, Batch 700/976, Stage 0, D_loss: 0.4410, G_loss: -3.7597, Num_std: 0.071980, Num_range: 4.0000, Real_score: 0.7004, Fake_score: 0.3013
2025-07-04 09:50:56,041 [INFO] src.gan.trainer: Epoch 13, Batch 800/976, Stage 0, D_loss: 0.4914, G_loss: -3.7593, Num_std: 0.065447, Num_range: 4.0000, Real_score: 0.6945, Fake_score: 0.3022
2025-07-04 09:51:11,513 [INFO] src.gan.trainer: Epoch 13, Batch 900/976, Stage 0, D_loss: 0.4748, G_loss: -3.7637, Num_std: 0.070959, Num_range: 4.0000, Real_score: 0.6993, Fake_score: 0.3024
2025-07-04 09:51:23,137 [INFO] __main__: 第14个epoch完成. 指标: {'d_loss': 0.48148662764884476, 'd_adv_loss': 0.041974247120259704, 'd_ctr_loss': 0.4189782497457799, 'd_reg_loss': 0.13689420108118502, 'real_d_score': 0.6974351950234076, 'fake_d_score': 0.3023831442487041, 'g_loss': -3.751391089760543, 'g_adv_loss': 0.4874584924307512, 'g_feature_loss': 1.098871893285077, 'g_diversity_loss': -0.9967021612562447, 'g_numeric_diversity_loss': -0.41213637686594085, 'fake_d_score_g': 0.3024829309739053, 'temperature': 3.4547720014980263, 'numeric_std': 0.07116779426552981, 'numeric_range': 3.999999999999957, 'training_stage': 0.0}
2025-07-04 09:51:23,137 [INFO] __main__: 在第14个epoch评估改进的GAN
2025-07-04 09:51:23,153 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 09:51:23,154 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 09:51:23,155 [INFO] __main__: 改进GAN评估指标:
2025-07-04 09:51:23,155 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 09:51:23,155 [INFO] __main__:   特征平均标准差: 0.000000
2025-07-04 09:51:23,155 [INFO] __main__:   零标准差特征数: 12.0
2025-07-04 09:51:23,155 [INFO] __main__: 训练质量: 需要改进
2025-07-04 09:51:23,358 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_14.pt
2025-07-04 09:51:23,358 [INFO] __main__: 开始第15/30个epoch (阶段: 0)
2025-07-04 09:51:23,639 [INFO] src.gan.trainer: Epoch 14, Batch 0/976, Stage 0, D_loss: 0.4717, G_loss: -3.0399, Num_std: 0.054531, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3021
2025-07-04 09:51:39,361 [INFO] src.gan.trainer: Epoch 14, Batch 100/976, Stage 0, D_loss: 0.4807, G_loss: -3.7617, Num_std: 0.079089, Num_range: 4.0000, Real_score: 0.6994, Fake_score: 0.3056
2025-07-04 09:51:54,863 [INFO] src.gan.trainer: Epoch 14, Batch 200/976, Stage 0, D_loss: 0.4468, G_loss: -3.7622, Num_std: 0.063715, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3034
2025-07-04 09:52:10,330 [INFO] src.gan.trainer: Epoch 14, Batch 300/976, Stage 0, D_loss: 0.4633, G_loss: -3.7645, Num_std: 0.069578, Num_range: 4.0000, Real_score: 0.6979, Fake_score: 0.3019
2025-07-04 09:52:25,762 [INFO] src.gan.trainer: Epoch 14, Batch 400/976, Stage 0, D_loss: 0.5064, G_loss: -3.7601, Num_std: 0.062698, Num_range: 4.0000, Real_score: 0.6951, Fake_score: 0.3004
2025-07-04 09:52:41,406 [INFO] src.gan.trainer: Epoch 14, Batch 500/976, Stage 0, D_loss: 0.4841, G_loss: -3.7573, Num_std: 0.074177, Num_range: 4.0000, Real_score: 0.6959, Fake_score: 0.3016
2025-07-04 09:52:56,911 [INFO] src.gan.trainer: Epoch 14, Batch 600/976, Stage 0, D_loss: 0.4503, G_loss: -3.7606, Num_std: 0.073244, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.3027
2025-07-04 09:53:12,441 [INFO] src.gan.trainer: Epoch 14, Batch 700/976, Stage 0, D_loss: 0.4781, G_loss: -3.7396, Num_std: 0.067874, Num_range: 4.0000, Real_score: 0.6960, Fake_score: 0.3012
2025-07-04 09:53:27,948 [INFO] src.gan.trainer: Epoch 14, Batch 800/976, Stage 0, D_loss: 0.4664, G_loss: -3.7616, Num_std: 0.075707, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.3053
2025-07-04 09:53:43,557 [INFO] src.gan.trainer: Epoch 14, Batch 900/976, Stage 0, D_loss: 0.4915, G_loss: -3.7603, Num_std: 0.070347, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3018
2025-07-04 09:53:55,097 [INFO] __main__: 第15个epoch完成. 指标: {'d_loss': 0.47782813586661044, 'd_adv_loss': 0.04202394757694641, 'd_ctr_loss': 0.41529447255564467, 'd_reg_loss': 0.13673143546845093, 'real_d_score': 0.697409635195966, 'fake_d_score': 0.30249705122875364, 'g_loss': -3.7484177519959214, 'g_adv_loss': 0.48750073317173764, 'g_feature_loss': 1.0900909299936785, 'g_diversity_loss': -0.9974430829612283, 'g_numeric_diversity_loss': -0.41162249304435733, 'fake_d_score_g': 0.3024633290459876, 'temperature': 3.4513172294964614, 'numeric_std': 0.07096145310708905, 'numeric_range': 3.999999999999957, 'training_stage': 0.0}
2025-07-04 09:53:55,097 [INFO] __main__: 开始第16/30个epoch (阶段: 0)
2025-07-04 09:53:55,371 [INFO] src.gan.trainer: Epoch 15, Batch 0/976, Stage 0, D_loss: 0.4943, G_loss: -3.7574, Num_std: 0.062839, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3008
2025-07-04 09:54:10,914 [INFO] src.gan.trainer: Epoch 15, Batch 100/976, Stage 0, D_loss: 0.5061, G_loss: -3.7579, Num_std: 0.068887, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3051
2025-07-04 09:54:26,436 [INFO] src.gan.trainer: Epoch 15, Batch 200/976, Stage 0, D_loss: 0.4735, G_loss: -3.4953, Num_std: 0.068124, Num_range: 4.0000, Real_score: 0.6954, Fake_score: 0.3018
2025-07-04 09:54:42,062 [INFO] src.gan.trainer: Epoch 15, Batch 300/976, Stage 0, D_loss: 0.4627, G_loss: -3.6373, Num_std: 0.066783, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3026
2025-07-04 09:54:57,607 [INFO] src.gan.trainer: Epoch 15, Batch 400/976, Stage 0, D_loss: 0.4981, G_loss: -3.7599, Num_std: 0.057481, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3029
2025-07-04 09:55:13,139 [INFO] src.gan.trainer: Epoch 15, Batch 500/976, Stage 0, D_loss: 0.4664, G_loss: -3.7351, Num_std: 0.068766, Num_range: 4.0000, Real_score: 0.6955, Fake_score: 0.3030
2025-07-04 09:55:28,685 [INFO] src.gan.trainer: Epoch 15, Batch 600/976, Stage 0, D_loss: 0.4965, G_loss: -2.5267, Num_std: 0.059520, Num_range: 4.0000, Real_score: 0.6983, Fake_score: 0.3007
2025-07-04 09:55:44,338 [INFO] src.gan.trainer: Epoch 15, Batch 700/976, Stage 0, D_loss: 0.4999, G_loss: -3.7574, Num_std: 0.072722, Num_range: 4.0000, Real_score: 0.6966, Fake_score: 0.3012
2025-07-04 09:55:59,880 [INFO] src.gan.trainer: Epoch 15, Batch 800/976, Stage 0, D_loss: 0.4596, G_loss: -3.7602, Num_std: 0.080265, Num_range: 4.0000, Real_score: 0.7000, Fake_score: 0.3029
2025-07-04 09:56:15,431 [INFO] src.gan.trainer: Epoch 15, Batch 900/976, Stage 0, D_loss: 0.4431, G_loss: -3.7597, Num_std: 0.097759, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.3040
2025-07-04 09:56:27,116 [INFO] __main__: 第16个epoch完成. 指标: {'d_loss': 0.4749950893589707, 'd_adv_loss': 0.041942220200311166, 'd_ctr_loss': 0.41251554123324446, 'd_reg_loss': 0.13691551728387816, 'real_d_score': 0.6974148320370025, 'fake_d_score': 0.3023265814561334, 'g_loss': -3.664484809412328, 'g_adv_loss': 0.48757631905743337, 'g_feature_loss': 1.132142605799468, 'g_diversity_loss': -0.9975005000259695, 'g_numeric_diversity_loss': -0.4013959696068544, 'fake_d_score_g': 0.30236535418595467, 'temperature': 3.4478659122669706, 'numeric_std': 0.07618562655825575, 'numeric_range': 3.9999987817689764, 'training_stage': 0.0}
2025-07-04 09:56:27,117 [INFO] __main__: 在第16个epoch评估改进的GAN
2025-07-04 09:56:27,133 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 09:56:27,133 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 09:56:27,134 [INFO] __main__: 改进GAN评估指标:
2025-07-04 09:56:27,134 [INFO] __main__:   数值特征质量评分: 0.700
2025-07-04 09:56:27,134 [INFO] __main__:   特征平均标准差: 0.064252
2025-07-04 09:56:27,134 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 09:56:27,134 [INFO] __main__: 训练质量: 需要改进
2025-07-04 09:56:27,136 [INFO] __main__: 新的最佳数值特征质量: 0.700
2025-07-04 09:56:27,432 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_best_model.pt
2025-07-04 09:56:27,432 [INFO] __main__: 保存最佳模型，数值质量评分: 0.700
2025-07-04 09:56:27,733 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_16.pt
2025-07-04 09:56:27,734 [INFO] __main__: 开始第17/30个epoch (阶段: 0)
2025-07-04 09:56:28,022 [INFO] src.gan.trainer: Epoch 16, Batch 0/976, Stage 0, D_loss: 0.4650, G_loss: -3.7582, Num_std: 0.076878, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.3026
2025-07-04 09:56:43,483 [INFO] src.gan.trainer: Epoch 16, Batch 100/976, Stage 0, D_loss: 0.4830, G_loss: -3.7645, Num_std: 0.070444, Num_range: 4.0000, Real_score: 0.6984, Fake_score: 0.3028
2025-07-04 09:56:58,895 [INFO] src.gan.trainer: Epoch 16, Batch 200/976, Stage 0, D_loss: 0.4585, G_loss: -3.7585, Num_std: 0.053715, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3028
2025-07-04 09:57:14,315 [INFO] src.gan.trainer: Epoch 16, Batch 300/976, Stage 0, D_loss: 0.4389, G_loss: -3.7601, Num_std: 0.075996, Num_range: 4.0000, Real_score: 0.6981, Fake_score: 0.3026
2025-07-04 09:57:29,921 [INFO] src.gan.trainer: Epoch 16, Batch 400/976, Stage 0, D_loss: 0.4674, G_loss: -3.7594, Num_std: 0.091291, Num_range: 4.0000, Real_score: 0.6943, Fake_score: 0.3001
2025-07-04 09:57:45,325 [INFO] src.gan.trainer: Epoch 16, Batch 500/976, Stage 0, D_loss: 0.4786, G_loss: -3.7575, Num_std: 0.083824, Num_range: 4.0000, Real_score: 0.6993, Fake_score: 0.2990
2025-07-04 09:58:00,708 [INFO] src.gan.trainer: Epoch 16, Batch 600/976, Stage 0, D_loss: 0.4576, G_loss: -3.7626, Num_std: 0.078949, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3051
2025-07-04 09:58:16,096 [INFO] src.gan.trainer: Epoch 16, Batch 700/976, Stage 0, D_loss: 0.4709, G_loss: -3.7614, Num_std: 0.067984, Num_range: 4.0000, Real_score: 0.6994, Fake_score: 0.3032
2025-07-04 09:58:31,593 [INFO] src.gan.trainer: Epoch 16, Batch 800/976, Stage 0, D_loss: 0.4603, G_loss: -3.7611, Num_std: 0.056808, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3016
2025-07-04 09:58:47,072 [INFO] src.gan.trainer: Epoch 16, Batch 900/976, Stage 0, D_loss: 0.4851, G_loss: -3.7633, Num_std: 0.058854, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3001
2025-07-04 09:58:58,699 [INFO] __main__: 第17个epoch完成. 指标: {'d_loss': 0.471772808856408, 'd_adv_loss': 0.041945006622032086, 'd_ctr_loss': 0.40929758783857356, 'd_reg_loss': 0.13686808760537486, 'real_d_score': 0.6974155012457104, 'fake_d_score': 0.30236158690980197, 'g_loss': -3.6699917096787145, 'g_adv_loss': 0.48767759961386575, 'g_feature_loss': 1.11495063913976, 'g_diversity_loss': -0.9975341789912983, 'g_numeric_diversity_loss': -0.4019853326700672, 'fake_d_score_g': 0.3022868127709693, 'temperature': 3.4444180463548033, 'numeric_std': 0.07133286443519025, 'numeric_range': 3.999994861379342, 'training_stage': 0.0}
2025-07-04 09:58:58,699 [INFO] __main__: 开始第18/30个epoch (阶段: 0)
2025-07-04 09:58:58,977 [INFO] src.gan.trainer: Epoch 17, Batch 0/976, Stage 0, D_loss: 0.4510, G_loss: -3.7592, Num_std: 0.086051, Num_range: 4.0000, Real_score: 0.6997, Fake_score: 0.3033
2025-07-04 09:59:14,445 [INFO] src.gan.trainer: Epoch 17, Batch 100/976, Stage 0, D_loss: 0.4489, G_loss: -3.7587, Num_std: 0.058703, Num_range: 4.0000, Real_score: 0.6998, Fake_score: 0.3026
2025-07-04 09:59:30,054 [INFO] src.gan.trainer: Epoch 17, Batch 200/976, Stage 0, D_loss: 0.4206, G_loss: -3.7696, Num_std: 0.065756, Num_range: 4.0000, Real_score: 0.6929, Fake_score: 0.3029
2025-07-04 09:59:45,542 [INFO] src.gan.trainer: Epoch 17, Batch 300/976, Stage 0, D_loss: 0.4394, G_loss: -3.7628, Num_std: 0.069712, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.2997
2025-07-04 10:00:01,005 [INFO] src.gan.trainer: Epoch 17, Batch 400/976, Stage 0, D_loss: 0.5134, G_loss: -3.7577, Num_std: 0.089930, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.2985
2025-07-04 10:00:16,498 [INFO] src.gan.trainer: Epoch 17, Batch 500/976, Stage 0, D_loss: 0.4493, G_loss: -3.7602, Num_std: 0.097128, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3019
2025-07-04 10:00:31,999 [INFO] src.gan.trainer: Epoch 17, Batch 600/976, Stage 0, D_loss: 0.4697, G_loss: -3.7712, Num_std: 0.055868, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3047
2025-07-04 10:00:47,394 [INFO] src.gan.trainer: Epoch 17, Batch 700/976, Stage 0, D_loss: 0.5014, G_loss: -3.7642, Num_std: 0.076894, Num_range: 4.0000, Real_score: 0.6956, Fake_score: 0.3029
2025-07-04 10:01:02,810 [INFO] src.gan.trainer: Epoch 17, Batch 800/976, Stage 0, D_loss: 0.5019, G_loss: -3.7587, Num_std: 0.087894, Num_range: 4.0000, Real_score: 0.7004, Fake_score: 0.3007
2025-07-04 10:01:18,219 [INFO] src.gan.trainer: Epoch 17, Batch 900/976, Stage 0, D_loss: 0.4802, G_loss: -3.7630, Num_std: 0.076407, Num_range: 4.0000, Real_score: 0.7000, Fake_score: 0.3024
2025-07-04 10:01:29,776 [INFO] __main__: 第18个epoch完成. 指标: {'d_loss': 0.4686153655658005, 'd_adv_loss': 0.0419554160144485, 'd_ctr_loss': 0.40613726776887143, 'd_reg_loss': 0.1368178749823423, 'real_d_score': 0.6974116414174688, 'fake_d_score': 0.3023969342351937, 'g_loss': -3.6778834164753347, 'g_adv_loss': 0.4876314090269679, 'g_feature_loss': 1.0949690556102731, 'g_diversity_loss': -0.9976216399319523, 'g_numeric_diversity_loss': -0.4028302046109011, 'fake_d_score_g': 0.3023250739655046, 'temperature': 3.4409736283084036, 'numeric_std': 0.0709951717868595, 'numeric_range': 3.9999963817049196, 'training_stage': 0.0}
2025-07-04 10:01:29,776 [INFO] __main__: 在第18个epoch评估改进的GAN
2025-07-04 10:01:29,793 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 10:01:29,794 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 10:01:29,794 [INFO] __main__: 改进GAN评估指标:
2025-07-04 10:01:29,795 [INFO] __main__:   数值特征质量评分: 0.700
2025-07-04 10:01:29,795 [INFO] __main__:   特征平均标准差: 0.077018
2025-07-04 10:01:29,795 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 10:01:29,795 [INFO] __main__: 训练质量: 需要改进
2025-07-04 10:01:30,100 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_18.pt
2025-07-04 10:01:30,101 [INFO] __main__: 开始第19/30个epoch (阶段: 0)
2025-07-04 10:01:30,379 [INFO] src.gan.trainer: Epoch 18, Batch 0/976, Stage 0, D_loss: 0.4664, G_loss: -3.7632, Num_std: 0.055117, Num_range: 4.0000, Real_score: 0.6968, Fake_score: 0.3040
2025-07-04 10:01:45,889 [INFO] src.gan.trainer: Epoch 18, Batch 100/976, Stage 0, D_loss: 0.4613, G_loss: -3.7580, Num_std: 0.100871, Num_range: 4.0000, Real_score: 0.7013, Fake_score: 0.3022
2025-07-04 10:02:01,374 [INFO] src.gan.trainer: Epoch 18, Batch 200/976, Stage 0, D_loss: 0.4678, G_loss: -3.7620, Num_std: 0.073569, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.2997
2025-07-04 10:02:16,922 [INFO] src.gan.trainer: Epoch 18, Batch 300/976, Stage 0, D_loss: 0.4648, G_loss: -3.7578, Num_std: 0.095635, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3021
2025-07-04 10:02:32,503 [INFO] src.gan.trainer: Epoch 18, Batch 400/976, Stage 0, D_loss: 0.4717, G_loss: -3.7656, Num_std: 0.058809, Num_range: 4.0000, Real_score: 0.6960, Fake_score: 0.3019
2025-07-04 10:02:47,868 [INFO] src.gan.trainer: Epoch 18, Batch 500/976, Stage 0, D_loss: 0.4648, G_loss: -3.7608, Num_std: 0.097410, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3031
2025-07-04 10:03:03,266 [INFO] src.gan.trainer: Epoch 18, Batch 600/976, Stage 0, D_loss: 0.4652, G_loss: -3.7546, Num_std: 0.109390, Num_range: 4.0000, Real_score: 0.6968, Fake_score: 0.3024
2025-07-04 10:03:18,563 [INFO] src.gan.trainer: Epoch 18, Batch 700/976, Stage 0, D_loss: 0.5286, G_loss: -3.7601, Num_std: 0.060416, Num_range: 4.0000, Real_score: 0.6998, Fake_score: 0.3020
2025-07-04 10:03:34,033 [INFO] src.gan.trainer: Epoch 18, Batch 800/976, Stage 0, D_loss: 0.4856, G_loss: -3.0635, Num_std: 0.059702, Num_range: 4.0000, Real_score: 0.6994, Fake_score: 0.3043
2025-07-04 10:03:49,581 [INFO] src.gan.trainer: Epoch 18, Batch 900/976, Stage 0, D_loss: 0.4349, G_loss: -3.7645, Num_std: 0.080219, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3019
2025-07-04 10:04:01,129 [INFO] __main__: 第19个epoch完成. 指标: {'d_loss': 0.46568486885335564, 'd_adv_loss': 0.04196269485000213, 'd_ctr_loss': 0.403204486415279, 'd_reg_loss': 0.13678457719258594, 'real_d_score': 0.6974073173325579, 'fake_d_score': 0.30241918927211253, 'g_loss': -3.6722063047027653, 'g_adv_loss': 0.48752848028403833, 'g_feature_loss': 1.0761265492504424, 'g_diversity_loss': -0.9977970413782224, 'g_numeric_diversity_loss': -0.40196800841619085, 'fake_d_score_g': 0.3023853683895098, 'temperature': 3.4375326546800222, 'numeric_std': 0.07078203126965821, 'numeric_range': 3.9999962758496825, 'training_stage': 0.0}
2025-07-04 10:04:01,129 [INFO] __main__: 开始第20/30个epoch (阶段: 0)
2025-07-04 10:04:01,414 [INFO] src.gan.trainer: Epoch 19, Batch 0/976, Stage 0, D_loss: 0.4276, G_loss: -3.7586, Num_std: 0.088705, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.3033
2025-07-04 10:04:16,987 [INFO] src.gan.trainer: Epoch 19, Batch 100/976, Stage 0, D_loss: 0.4586, G_loss: -3.7632, Num_std: 0.081116, Num_range: 4.0000, Real_score: 0.6994, Fake_score: 0.3023
2025-07-04 10:04:32,672 [INFO] src.gan.trainer: Epoch 19, Batch 200/976, Stage 0, D_loss: 0.4421, G_loss: -3.7584, Num_std: 0.064406, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3015
2025-07-04 10:04:48,175 [INFO] src.gan.trainer: Epoch 19, Batch 300/976, Stage 0, D_loss: 0.4591, G_loss: -3.7604, Num_std: 0.086022, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3036
2025-07-04 10:05:03,669 [INFO] src.gan.trainer: Epoch 19, Batch 400/976, Stage 0, D_loss: 0.4149, G_loss: -3.5984, Num_std: 0.057085, Num_range: 4.0000, Real_score: 0.6977, Fake_score: 0.3046
2025-07-04 10:05:19,124 [INFO] src.gan.trainer: Epoch 19, Batch 500/976, Stage 0, D_loss: 0.4280, G_loss: -3.7623, Num_std: 0.082751, Num_range: 4.0000, Real_score: 0.6950, Fake_score: 0.3021
2025-07-04 10:05:34,698 [INFO] src.gan.trainer: Epoch 19, Batch 600/976, Stage 0, D_loss: 0.4229, G_loss: -3.7645, Num_std: 0.067255, Num_range: 4.0000, Real_score: 0.6949, Fake_score: 0.3028
2025-07-04 10:05:50,167 [INFO] src.gan.trainer: Epoch 19, Batch 700/976, Stage 0, D_loss: 0.4754, G_loss: -3.7647, Num_std: 0.073948, Num_range: 4.0000, Real_score: 0.6993, Fake_score: 0.3023
2025-07-04 10:06:05,617 [INFO] src.gan.trainer: Epoch 19, Batch 800/976, Stage 0, D_loss: 0.4106, G_loss: -3.7615, Num_std: 0.080549, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.3028
2025-07-04 10:06:21,099 [INFO] src.gan.trainer: Epoch 19, Batch 900/976, Stage 0, D_loss: 0.4753, G_loss: -3.7641, Num_std: 0.064764, Num_range: 4.0000, Real_score: 0.6977, Fake_score: 0.3011
2025-07-04 10:06:32,872 [INFO] __main__: 第20个epoch完成. 指标: {'d_loss': 0.46235737705328434, 'd_adv_loss': 0.041939468069582236, 'd_ctr_loss': 0.39989555981315605, 'd_reg_loss': 0.13681565620554767, 'real_d_score': 0.6974112075127538, 'fake_d_score': 0.30238678889562853, 'g_loss': -3.6821188333093193, 'g_adv_loss': 0.4875401187118124, 'g_feature_loss': 1.06639398626291, 'g_diversity_loss': -0.9978685787588852, 'g_numeric_diversity_loss': -0.4031387592151843, 'fake_d_score_g': 0.30237489776899595, 'temperature': 3.434095122025459, 'numeric_std': 0.07016628640137068, 'numeric_range': 3.999994999968288, 'training_stage': 0.0}
2025-07-04 10:06:32,873 [INFO] __main__: 在第20个epoch评估改进的GAN
2025-07-04 10:06:32,889 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 10:06:32,889 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 10:06:32,890 [INFO] __main__: 改进GAN评估指标:
2025-07-04 10:06:32,891 [INFO] __main__:   数值特征质量评分: 0.700
2025-07-04 10:06:32,891 [INFO] __main__:   特征平均标准差: 0.057538
2025-07-04 10:06:32,891 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 10:06:32,891 [INFO] __main__: 训练质量: 需要改进
2025-07-04 10:06:33,093 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_20.pt
2025-07-04 10:06:33,094 [INFO] __main__: 开始第21/30个epoch (阶段: 0)
2025-07-04 10:06:33,373 [INFO] src.gan.trainer: Epoch 20, Batch 0/976, Stage 0, D_loss: 0.4216, G_loss: -3.7602, Num_std: 0.070099, Num_range: 4.0000, Real_score: 0.6996, Fake_score: 0.3015
2025-07-04 10:06:48,790 [INFO] src.gan.trainer: Epoch 20, Batch 100/976, Stage 0, D_loss: 0.4292, G_loss: -3.7637, Num_std: 0.068684, Num_range: 4.0000, Real_score: 0.6994, Fake_score: 0.3050
2025-07-04 10:07:04,246 [INFO] src.gan.trainer: Epoch 20, Batch 200/976, Stage 0, D_loss: 0.4980, G_loss: -3.7642, Num_std: 0.084622, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3033
2025-07-04 10:07:19,738 [INFO] src.gan.trainer: Epoch 20, Batch 300/976, Stage 0, D_loss: 0.4332, G_loss: -3.7673, Num_std: 0.060807, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.3016
2025-07-04 10:07:35,326 [INFO] src.gan.trainer: Epoch 20, Batch 400/976, Stage 0, D_loss: 0.4363, G_loss: -3.7644, Num_std: 0.055625, Num_range: 4.0000, Real_score: 0.6995, Fake_score: 0.3024
2025-07-04 10:07:50,869 [INFO] src.gan.trainer: Epoch 20, Batch 500/976, Stage 0, D_loss: 0.4295, G_loss: -3.7666, Num_std: 0.071823, Num_range: 4.0000, Real_score: 0.6996, Fake_score: 0.3024
2025-07-04 10:08:06,335 [INFO] src.gan.trainer: Epoch 20, Batch 600/976, Stage 0, D_loss: 0.4538, G_loss: -3.7657, Num_std: 0.063694, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3013
2025-07-04 10:08:21,712 [INFO] src.gan.trainer: Epoch 20, Batch 700/976, Stage 0, D_loss: 0.4717, G_loss: -3.6985, Num_std: 0.066727, Num_range: 4.0000, Real_score: 0.6964, Fake_score: 0.3046
2025-07-04 10:08:37,175 [INFO] src.gan.trainer: Epoch 20, Batch 800/976, Stage 0, D_loss: 0.4489, G_loss: -3.7659, Num_std: 0.083329, Num_range: 4.0000, Real_score: 0.6998, Fake_score: 0.3018
2025-07-04 10:08:52,615 [INFO] src.gan.trainer: Epoch 20, Batch 900/976, Stage 0, D_loss: 0.4749, G_loss: -3.7657, Num_std: 0.067493, Num_range: 4.0000, Real_score: 0.6983, Fake_score: 0.3025
2025-07-04 10:09:04,259 [INFO] __main__: 第21个epoch完成. 指标: {'d_loss': 0.45914881664221435, 'd_adv_loss': 0.04192670716400271, 'd_ctr_loss': 0.39669729005850707, 'd_reg_loss': 0.13683212598877362, 'real_d_score': 0.6974177901007108, 'fake_d_score': 0.30237410861815606, 'g_loss': -3.678666248021881, 'g_adv_loss': 0.4875015240325469, 'g_feature_loss': 1.0543429957183659, 'g_diversity_loss': -0.9981298643912441, 'g_numeric_diversity_loss': -0.40259438197896935, 'fake_d_score_g': 0.3023879143913263, 'temperature': 3.4306610269033375, 'numeric_std': 0.07105542904423749, 'numeric_range': 3.9999951791079345, 'training_stage': 0.0}
2025-07-04 10:09:04,260 [INFO] __main__: 开始第22/30个epoch (阶段: 0)
2025-07-04 10:09:04,537 [INFO] src.gan.trainer: Epoch 21, Batch 0/976, Stage 0, D_loss: 0.4677, G_loss: -3.4005, Num_std: 0.077285, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3024
2025-07-04 10:09:20,045 [INFO] src.gan.trainer: Epoch 21, Batch 100/976, Stage 0, D_loss: 0.4626, G_loss: -3.7682, Num_std: 0.076093, Num_range: 4.0000, Real_score: 0.6955, Fake_score: 0.3012
2025-07-04 10:09:35,715 [INFO] src.gan.trainer: Epoch 21, Batch 200/976, Stage 0, D_loss: 0.4186, G_loss: -3.7624, Num_std: 0.079739, Num_range: 4.0000, Real_score: 0.6990, Fake_score: 0.3022
2025-07-04 10:09:51,237 [INFO] src.gan.trainer: Epoch 21, Batch 300/976, Stage 0, D_loss: 0.4326, G_loss: -3.7693, Num_std: 0.061725, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3002
2025-07-04 10:10:06,737 [INFO] src.gan.trainer: Epoch 21, Batch 400/976, Stage 0, D_loss: 0.4643, G_loss: -3.7601, Num_std: 0.083865, Num_range: 4.0000, Real_score: 0.6941, Fake_score: 0.2984
2025-07-04 10:10:22,204 [INFO] src.gan.trainer: Epoch 21, Batch 500/976, Stage 0, D_loss: 0.4577, G_loss: -3.7657, Num_std: 0.063637, Num_range: 4.0000, Real_score: 0.6953, Fake_score: 0.3027
2025-07-04 10:10:37,782 [INFO] src.gan.trainer: Epoch 21, Batch 600/976, Stage 0, D_loss: 0.4423, G_loss: -3.7669, Num_std: 0.064615, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.2992
2025-07-04 10:10:53,241 [INFO] src.gan.trainer: Epoch 21, Batch 700/976, Stage 0, D_loss: 0.4762, G_loss: -3.7624, Num_std: 0.080697, Num_range: 4.0000, Real_score: 0.6963, Fake_score: 0.3030
2025-07-04 10:11:08,725 [INFO] src.gan.trainer: Epoch 21, Batch 800/976, Stage 0, D_loss: 0.4357, G_loss: -3.7682, Num_std: 0.059882, Num_range: 4.0000, Real_score: 0.6997, Fake_score: 0.3022
2025-07-04 10:11:24,285 [INFO] src.gan.trainer: Epoch 21, Batch 900/976, Stage 0, D_loss: 0.4351, G_loss: -3.7671, Num_std: 0.079639, Num_range: 4.0000, Real_score: 0.6988, Fake_score: 0.3028
2025-07-04 10:11:36,083 [INFO] __main__: 第22个epoch完成. 指标: {'d_loss': 0.45674073586209935, 'd_adv_loss': 0.041904582603849834, 'd_ctr_loss': 0.39430368789395337, 'd_reg_loss': 0.13688309475413138, 'real_d_score': 0.6974534565796606, 'fake_d_score': 0.30236203522711486, 'g_loss': -3.6910992663333286, 'g_adv_loss': 0.48760399520600756, 'g_feature_loss': 1.048503409786141, 'g_diversity_loss': -0.9983618008837998, 'g_numeric_diversity_loss': -0.4040958290828054, 'fake_d_score_g': 0.30229850694577914, 'temperature': 3.4272303658764303, 'numeric_std': 0.07097194468862948, 'numeric_range': 3.999995258010799, 'training_stage': 0.0}
2025-07-04 10:11:36,083 [INFO] __main__: 在第22个epoch评估改进的GAN
2025-07-04 10:11:36,099 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 10:11:36,100 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 10:11:36,101 [INFO] __main__: 改进GAN评估指标:
2025-07-04 10:11:36,101 [INFO] __main__:   数值特征质量评分: 0.700
2025-07-04 10:11:36,101 [INFO] __main__:   特征平均标准差: 0.069912
2025-07-04 10:11:36,101 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 10:11:36,101 [INFO] __main__: 训练质量: 需要改进
2025-07-04 10:11:36,303 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_22.pt
2025-07-04 10:11:36,303 [INFO] __main__: 开始第23/30个epoch (阶段: 0)
2025-07-04 10:11:36,588 [INFO] src.gan.trainer: Epoch 22, Batch 0/976, Stage 0, D_loss: 0.4145, G_loss: -3.7665, Num_std: 0.056217, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.3012
2025-07-04 10:11:52,125 [INFO] src.gan.trainer: Epoch 22, Batch 100/976, Stage 0, D_loss: 0.4422, G_loss: -3.7639, Num_std: 0.090757, Num_range: 4.0000, Real_score: 0.6999, Fake_score: 0.3011
2025-07-04 10:12:07,667 [INFO] src.gan.trainer: Epoch 22, Batch 200/976, Stage 0, D_loss: 0.4244, G_loss: -3.7674, Num_std: 0.070208, Num_range: 4.0000, Real_score: 0.6968, Fake_score: 0.2994
2025-07-04 10:12:23,315 [INFO] src.gan.trainer: Epoch 22, Batch 300/976, Stage 0, D_loss: 0.4775, G_loss: -3.7662, Num_std: 0.067043, Num_range: 4.0000, Real_score: 0.6989, Fake_score: 0.3015
2025-07-04 10:12:38,805 [INFO] src.gan.trainer: Epoch 22, Batch 400/976, Stage 0, D_loss: 0.4632, G_loss: -2.4235, Num_std: 0.053832, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3035
2025-07-04 10:12:54,308 [INFO] src.gan.trainer: Epoch 22, Batch 500/976, Stage 0, D_loss: 0.4318, G_loss: -3.7665, Num_std: 0.053149, Num_range: 4.0000, Real_score: 0.6988, Fake_score: 0.3007
2025-07-04 10:13:09,790 [INFO] src.gan.trainer: Epoch 22, Batch 600/976, Stage 0, D_loss: 0.4297, G_loss: -3.7650, Num_std: 0.070408, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3021
2025-07-04 10:13:25,371 [INFO] src.gan.trainer: Epoch 22, Batch 700/976, Stage 0, D_loss: 0.4475, G_loss: -3.1672, Num_std: 0.036937, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3018
2025-07-04 10:13:40,844 [INFO] src.gan.trainer: Epoch 22, Batch 800/976, Stage 0, D_loss: 0.4522, G_loss: -3.7734, Num_std: 0.064002, Num_range: 4.0000, Real_score: 0.6964, Fake_score: 0.3025
2025-07-04 10:13:56,296 [INFO] src.gan.trainer: Epoch 22, Batch 900/976, Stage 0, D_loss: 0.5015, G_loss: -3.7654, Num_std: 0.071051, Num_range: 4.0000, Real_score: 0.6938, Fake_score: 0.3002
2025-07-04 10:14:07,934 [INFO] __main__: 第23个epoch完成. 指标: {'d_loss': 0.45399914510914524, 'd_adv_loss': 0.04185863025104772, 'd_ctr_loss': 0.39158832889477785, 'd_reg_loss': 0.13701456844745605, 'real_d_score': 0.6975388073774628, 'fake_d_score': 0.30233105299536367, 'g_loss': -3.7010422724355116, 'g_adv_loss': 0.4875992800775777, 'g_feature_loss': 1.0396685451269148, 'g_diversity_loss': -0.9985479043766112, 'g_numeric_diversity_loss': -0.4052596342256237, 'fake_d_score_g': 0.30229898379311515, 'temperature': 3.423803135510544, 'numeric_std': 0.07042172328340253, 'numeric_range': 3.9999947241746683, 'training_stage': 0.0}
2025-07-04 10:14:07,935 [INFO] __main__: 开始第24/30个epoch (阶段: 0)
2025-07-04 10:14:08,222 [INFO] src.gan.trainer: Epoch 23, Batch 0/976, Stage 0, D_loss: 0.4785, G_loss: -3.7727, Num_std: 0.073636, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3040
2025-07-04 10:14:23,926 [INFO] src.gan.trainer: Epoch 23, Batch 100/976, Stage 0, D_loss: 0.4560, G_loss: -3.7696, Num_std: 0.066065, Num_range: 4.0000, Real_score: 0.6968, Fake_score: 0.3040
2025-07-04 10:14:39,451 [INFO] src.gan.trainer: Epoch 23, Batch 200/976, Stage 0, D_loss: 0.4496, G_loss: -3.7702, Num_std: 0.081445, Num_range: 4.0000, Real_score: 0.6988, Fake_score: 0.3016
2025-07-04 10:14:54,851 [INFO] src.gan.trainer: Epoch 23, Batch 300/976, Stage 0, D_loss: 0.4695, G_loss: -3.7647, Num_std: 0.069539, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3023
2025-07-04 10:15:10,223 [INFO] src.gan.trainer: Epoch 23, Batch 400/976, Stage 0, D_loss: 0.4681, G_loss: -3.7627, Num_std: 0.046224, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3017
2025-07-04 10:15:25,642 [INFO] src.gan.trainer: Epoch 23, Batch 500/976, Stage 0, D_loss: 0.4164, G_loss: -3.7650, Num_std: 0.079050, Num_range: 4.0000, Real_score: 0.6955, Fake_score: 0.3036
2025-07-04 10:15:41,026 [INFO] src.gan.trainer: Epoch 23, Batch 600/976, Stage 0, D_loss: 0.4611, G_loss: -3.7662, Num_std: 0.085120, Num_range: 4.0000, Real_score: 0.6960, Fake_score: 0.3001
2025-07-04 10:15:56,358 [INFO] src.gan.trainer: Epoch 23, Batch 700/976, Stage 0, D_loss: 0.4328, G_loss: -3.7612, Num_std: 0.069502, Num_range: 4.0000, Real_score: 0.6950, Fake_score: 0.3020
2025-07-04 10:16:11,688 [INFO] src.gan.trainer: Epoch 23, Batch 800/976, Stage 0, D_loss: 0.4716, G_loss: -3.7686, Num_std: 0.068844, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3030
2025-07-04 10:16:27,152 [INFO] src.gan.trainer: Epoch 23, Batch 900/976, Stage 0, D_loss: 0.4619, G_loss: -3.7645, Num_std: 0.072216, Num_range: 4.0000, Real_score: 0.6990, Fake_score: 0.3014
2025-07-04 10:16:38,821 [INFO] __main__: 第24个epoch完成. 指标: {'d_loss': 0.45157773902670245, 'd_adv_loss': 0.0419275654273749, 'd_ctr_loss': 0.38912726160077793, 'd_reg_loss': 0.13681940825991956, 'real_d_score': 0.6973796761671059, 'fake_d_score': 0.3023455515503883, 'g_loss': -3.678411140182959, 'g_adv_loss': 0.48772248787591715, 'g_feature_loss': 1.033533151602484, 'g_diversity_loss': -0.9981821048903003, 'g_numeric_diversity_loss': -0.40245352268137524, 'fake_d_score_g': 0.30225428626389156, 'temperature': 3.420379332375029, 'numeric_std': 0.0695491062417344, 'numeric_range': 3.9999957497491234, 'training_stage': 0.0}
2025-07-04 10:16:38,821 [INFO] __main__: 在第24个epoch评估改进的GAN
2025-07-04 10:16:38,837 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 10:16:38,838 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 10:16:38,838 [INFO] __main__: 改进GAN评估指标:
2025-07-04 10:16:38,839 [INFO] __main__:   数值特征质量评分: 0.700
2025-07-04 10:16:38,839 [INFO] __main__:   特征平均标准差: 0.057998
2025-07-04 10:16:38,839 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 10:16:38,839 [INFO] __main__: 训练质量: 需要改进
2025-07-04 10:16:39,041 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_24.pt
2025-07-04 10:16:39,042 [INFO] __main__: 开始第25/30个epoch (阶段: 0)
2025-07-04 10:16:39,320 [INFO] src.gan.trainer: Epoch 24, Batch 0/976, Stage 0, D_loss: 0.4462, G_loss: -3.7615, Num_std: 0.072308, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3005
2025-07-04 10:16:54,780 [INFO] src.gan.trainer: Epoch 24, Batch 100/976, Stage 0, D_loss: 0.4429, G_loss: -3.3363, Num_std: 0.047850, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3007
2025-07-04 10:17:10,152 [INFO] src.gan.trainer: Epoch 24, Batch 200/976, Stage 0, D_loss: 0.4496, G_loss: -3.7658, Num_std: 0.063201, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3007
2025-07-04 10:17:25,696 [INFO] src.gan.trainer: Epoch 24, Batch 300/976, Stage 0, D_loss: 0.4568, G_loss: -3.7676, Num_std: 0.067706, Num_range: 4.0000, Real_score: 0.6952, Fake_score: 0.3023
2025-07-04 10:17:41,063 [INFO] src.gan.trainer: Epoch 24, Batch 400/976, Stage 0, D_loss: 0.3941, G_loss: -3.7684, Num_std: 0.074344, Num_range: 4.0000, Real_score: 0.6981, Fake_score: 0.3050
2025-07-04 10:17:56,439 [INFO] src.gan.trainer: Epoch 24, Batch 500/976, Stage 0, D_loss: 0.4968, G_loss: -3.7691, Num_std: 0.066104, Num_range: 4.0000, Real_score: 0.6976, Fake_score: 0.3036
2025-07-04 10:18:11,780 [INFO] src.gan.trainer: Epoch 24, Batch 600/976, Stage 0, D_loss: 0.4567, G_loss: -3.7625, Num_std: 0.090351, Num_range: 4.0000, Real_score: 0.6948, Fake_score: 0.3028
2025-07-04 10:18:27,360 [INFO] src.gan.trainer: Epoch 24, Batch 700/976, Stage 0, D_loss: 0.4245, G_loss: -3.7643, Num_std: 0.072643, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3041
2025-07-04 10:18:42,817 [INFO] src.gan.trainer: Epoch 24, Batch 800/976, Stage 0, D_loss: 0.4502, G_loss: -3.7708, Num_std: 0.067295, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3029
2025-07-04 10:18:58,230 [INFO] src.gan.trainer: Epoch 24, Batch 900/976, Stage 0, D_loss: 0.4315, G_loss: -3.7628, Num_std: 0.069524, Num_range: 4.0000, Real_score: 0.7000, Fake_score: 0.3023
2025-07-04 10:19:09,807 [INFO] __main__: 第25个epoch完成. 指标: {'d_loss': 0.4484154932872681, 'd_adv_loss': 0.041883871230281505, 'd_ctr_loss': 0.3859921456238285, 'd_reg_loss': 0.13692983667381478, 'real_d_score': 0.6974564427357217, 'fake_d_score': 0.30232085547119913, 'g_loss': -3.719102504556293, 'g_adv_loss': 0.4875972681459995, 'g_feature_loss': 1.0350601134689468, 'g_diversity_loss': -0.9985624786893851, 'g_numeric_diversity_loss': -0.4074862872001008, 'fake_d_score_g': 0.3023158126800767, 'temperature': 3.4169589530427884, 'numeric_std': 0.07187663229894345, 'numeric_range': 3.999995182772147, 'training_stage': 0.0}
2025-07-04 10:19:09,808 [INFO] __main__: 开始第26/30个epoch (阶段: 0)
2025-07-04 10:19:10,089 [INFO] src.gan.trainer: Epoch 25, Batch 0/976, Stage 0, D_loss: 0.4798, G_loss: -3.7644, Num_std: 0.077134, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3003
2025-07-04 10:19:25,677 [INFO] src.gan.trainer: Epoch 25, Batch 100/976, Stage 0, D_loss: 0.4110, G_loss: -3.7636, Num_std: 0.086857, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3038
2025-07-04 10:19:41,113 [INFO] src.gan.trainer: Epoch 25, Batch 200/976, Stage 0, D_loss: 0.4180, G_loss: -3.7720, Num_std: 0.072200, Num_range: 4.0000, Real_score: 0.6965, Fake_score: 0.3019
2025-07-04 10:19:56,550 [INFO] src.gan.trainer: Epoch 25, Batch 300/976, Stage 0, D_loss: 0.4093, G_loss: -3.7689, Num_std: 0.066441, Num_range: 4.0000, Real_score: 0.6965, Fake_score: 0.3006
2025-07-04 10:20:12,182 [INFO] src.gan.trainer: Epoch 25, Batch 400/976, Stage 0, D_loss: 0.4559, G_loss: -3.7677, Num_std: 0.069654, Num_range: 4.0000, Real_score: 0.6963, Fake_score: 0.3028
2025-07-04 10:20:27,837 [INFO] src.gan.trainer: Epoch 25, Batch 500/976, Stage 0, D_loss: 0.4522, G_loss: -3.7651, Num_std: 0.052839, Num_range: 4.0000, Real_score: 0.6989, Fake_score: 0.3033
2025-07-04 10:20:43,372 [INFO] src.gan.trainer: Epoch 25, Batch 600/976, Stage 0, D_loss: 0.5008, G_loss: -3.7687, Num_std: 0.074362, Num_range: 4.0000, Real_score: 0.6966, Fake_score: 0.3032
2025-07-04 10:20:58,829 [INFO] src.gan.trainer: Epoch 25, Batch 700/976, Stage 0, D_loss: 0.5244, G_loss: -3.7648, Num_std: 0.066011, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3016
2025-07-04 10:21:14,245 [INFO] src.gan.trainer: Epoch 25, Batch 800/976, Stage 0, D_loss: 0.4237, G_loss: -3.7587, Num_std: 0.102061, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3017
2025-07-04 10:21:29,776 [INFO] src.gan.trainer: Epoch 25, Batch 900/976, Stage 0, D_loss: 0.4643, G_loss: -3.7595, Num_std: 0.081792, Num_range: 4.0000, Real_score: 0.6976, Fake_score: 0.3007
2025-07-04 10:21:41,340 [INFO] __main__: 第26个epoch完成. 指标: {'d_loss': 0.44583607190212254, 'd_adv_loss': 0.04185103919341792, 'd_ctr_loss': 0.38343014055099633, 'd_reg_loss': 0.13703260976882262, 'real_d_score': 0.6975070829884918, 'fake_d_score': 0.3022811571899491, 'g_loss': -3.722778159351461, 'g_adv_loss': 0.48753044465200523, 'g_feature_loss': 1.0272055249966552, 'g_diversity_loss': -0.9986123392979298, 'g_numeric_diversity_loss': -0.4078820676904157, 'fake_d_score_g': 0.30235844364013187, 'temperature': 3.4135419940896403, 'numeric_std': 0.0713119840068933, 'numeric_range': 3.999996978077072, 'training_stage': 0.0}
2025-07-04 10:21:41,340 [INFO] __main__: 在第26个epoch评估改进的GAN
2025-07-04 10:21:41,357 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 10:21:41,357 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 10:21:41,358 [INFO] __main__: 改进GAN评估指标:
2025-07-04 10:21:41,358 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 10:21:41,358 [INFO] __main__:   特征平均标准差: 0.000006
2025-07-04 10:21:41,358 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 10:21:41,358 [INFO] __main__: 训练质量: 需要改进
2025-07-04 10:21:41,558 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_26.pt
2025-07-04 10:21:41,559 [INFO] __main__: 开始第27/30个epoch (阶段: 0)
2025-07-04 10:21:41,839 [INFO] src.gan.trainer: Epoch 26, Batch 0/976, Stage 0, D_loss: 0.4259, G_loss: -3.7362, Num_std: 0.052615, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3039
2025-07-04 10:21:57,245 [INFO] src.gan.trainer: Epoch 26, Batch 100/976, Stage 0, D_loss: 0.4564, G_loss: -3.7724, Num_std: 0.066644, Num_range: 4.0000, Real_score: 0.6957, Fake_score: 0.3012
2025-07-04 10:22:12,598 [INFO] src.gan.trainer: Epoch 26, Batch 200/976, Stage 0, D_loss: 0.4360, G_loss: -3.7675, Num_std: 0.052074, Num_range: 4.0000, Real_score: 0.6981, Fake_score: 0.3009
2025-07-04 10:22:28,220 [INFO] src.gan.trainer: Epoch 26, Batch 300/976, Stage 0, D_loss: 0.4728, G_loss: -3.7687, Num_std: 0.062489, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3033
2025-07-04 10:22:43,729 [INFO] src.gan.trainer: Epoch 26, Batch 400/976, Stage 0, D_loss: 0.4680, G_loss: -3.7659, Num_std: 0.084194, Num_range: 4.0000, Real_score: 0.6993, Fake_score: 0.3034
2025-07-04 10:22:59,206 [INFO] src.gan.trainer: Epoch 26, Batch 500/976, Stage 0, D_loss: 0.4609, G_loss: -3.7576, Num_std: 0.086530, Num_range: 4.0000, Real_score: 0.6995, Fake_score: 0.2995
2025-07-04 10:23:14,578 [INFO] src.gan.trainer: Epoch 26, Batch 600/976, Stage 0, D_loss: 0.5089, G_loss: -3.7636, Num_std: 0.084752, Num_range: 4.0000, Real_score: 0.6964, Fake_score: 0.3023
2025-07-04 10:23:30,030 [INFO] src.gan.trainer: Epoch 26, Batch 700/976, Stage 0, D_loss: 0.4388, G_loss: -3.7626, Num_std: 0.072685, Num_range: 4.0000, Real_score: 0.6993, Fake_score: 0.3028
2025-07-04 10:23:45,448 [INFO] src.gan.trainer: Epoch 26, Batch 800/976, Stage 0, D_loss: 0.4644, G_loss: -3.7646, Num_std: 0.077418, Num_range: 4.0000, Real_score: 0.6992, Fake_score: 0.3002
2025-07-04 10:24:00,848 [INFO] src.gan.trainer: Epoch 26, Batch 900/976, Stage 0, D_loss: 0.4748, G_loss: -3.7633, Num_std: 0.065985, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3037
2025-07-04 10:24:12,392 [INFO] __main__: 第27个epoch完成. 指标: {'d_loss': 0.4431895140375263, 'd_adv_loss': 0.04181539546698323, 'd_ctr_loss': 0.3808084745570771, 'd_reg_loss': 0.13710429041539574, 'real_d_score': 0.6975734353554054, 'fake_d_score': 0.3022781111544275, 'g_loss': -3.7338643597001253, 'g_adv_loss': 0.4875368764471329, 'g_feature_loss': 1.0265493370348302, 'g_diversity_loss': -0.9986950301366738, 'g_numeric_diversity_loss': -0.40925420891452835, 'fake_d_score_g': 0.30233325757330554, 'temperature': 3.4101284520955066, 'numeric_std': 0.07072490961053131, 'numeric_range': 3.9999978764623814, 'training_stage': 0.0}
2025-07-04 10:24:12,393 [INFO] __main__: 开始第28/30个epoch (阶段: 0)
2025-07-04 10:24:12,674 [INFO] src.gan.trainer: Epoch 27, Batch 0/976, Stage 0, D_loss: 0.4522, G_loss: -3.7654, Num_std: 0.086932, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.3020
2025-07-04 10:24:28,231 [INFO] src.gan.trainer: Epoch 27, Batch 100/976, Stage 0, D_loss: 0.4273, G_loss: -3.7646, Num_std: 0.074677, Num_range: 4.0000, Real_score: 0.6954, Fake_score: 0.3040
2025-07-04 10:24:43,674 [INFO] src.gan.trainer: Epoch 27, Batch 200/976, Stage 0, D_loss: 0.4240, G_loss: -3.7722, Num_std: 0.077887, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3034
2025-07-04 10:24:59,080 [INFO] src.gan.trainer: Epoch 27, Batch 300/976, Stage 0, D_loss: 0.4354, G_loss: -3.7677, Num_std: 0.074850, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3009
2025-07-04 10:25:14,495 [INFO] src.gan.trainer: Epoch 27, Batch 400/976, Stage 0, D_loss: 0.4007, G_loss: -3.7647, Num_std: 0.059721, Num_range: 4.0000, Real_score: 0.6989, Fake_score: 0.3032
2025-07-04 10:25:30,107 [INFO] src.gan.trainer: Epoch 27, Batch 500/976, Stage 0, D_loss: 0.4512, G_loss: -3.7697, Num_std: 0.072452, Num_range: 4.0000, Real_score: 0.6994, Fake_score: 0.3022
2025-07-04 10:25:45,585 [INFO] src.gan.trainer: Epoch 27, Batch 600/976, Stage 0, D_loss: 0.4474, G_loss: -3.5383, Num_std: 0.055593, Num_range: 4.0000, Real_score: 0.6991, Fake_score: 0.3035
2025-07-04 10:26:01,064 [INFO] src.gan.trainer: Epoch 27, Batch 700/976, Stage 0, D_loss: 0.4596, G_loss: -3.7626, Num_std: 0.077107, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3031
2025-07-04 10:26:16,426 [INFO] src.gan.trainer: Epoch 27, Batch 800/976, Stage 0, D_loss: 0.4404, G_loss: -3.7634, Num_std: 0.079818, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3032
2025-07-04 10:26:31,925 [INFO] src.gan.trainer: Epoch 27, Batch 900/976, Stage 0, D_loss: 0.4071, G_loss: -3.7192, Num_std: 0.072403, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.3019
2025-07-04 10:26:43,585 [INFO] __main__: 第28个epoch完成. 指标: {'d_loss': 0.4408352812294102, 'd_adv_loss': 0.04176427941906777, 'd_ctr_loss': 0.37848175637668197, 'd_reg_loss': 0.13726163020388032, 'real_d_score': 0.697666933607371, 'fake_d_score': 0.3022334448932131, 'g_loss': -3.732222631349751, 'g_adv_loss': 0.48767606462122925, 'g_feature_loss': 1.0260332612310603, 'g_diversity_loss': -0.998820383325625, 'g_numeric_diversity_loss': -0.4090474971564978, 'fake_d_score_g': 0.30223025291995237, 'temperature': 3.4067183236434393, 'numeric_std': 0.07003653896701982, 'numeric_range': 3.9999985375690916, 'training_stage': 0.0}
2025-07-04 10:26:43,585 [INFO] __main__: 在第28个epoch评估改进的GAN
2025-07-04 10:26:43,601 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 10:26:43,601 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 10:26:43,602 [INFO] __main__: 改进GAN评估指标:
2025-07-04 10:26:43,602 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 10:26:43,603 [INFO] __main__:   特征平均标准差: 0.000002
2025-07-04 10:26:43,603 [INFO] __main__:   零标准差特征数: 3.0
2025-07-04 10:26:43,603 [INFO] __main__: 训练质量: 需要改进
2025-07-04 10:26:43,808 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_28.pt
2025-07-04 10:26:43,808 [INFO] __main__: 开始第29/30个epoch (阶段: 0)
2025-07-04 10:26:44,094 [INFO] src.gan.trainer: Epoch 28, Batch 0/976, Stage 0, D_loss: 0.4056, G_loss: -3.7669, Num_std: 0.079313, Num_range: 4.0000, Real_score: 0.6984, Fake_score: 0.3017
2025-07-04 10:26:59,608 [INFO] src.gan.trainer: Epoch 28, Batch 100/976, Stage 0, D_loss: 0.4208, G_loss: -3.2545, Num_std: 0.053075, Num_range: 4.0000, Real_score: 0.6999, Fake_score: 0.3026
2025-07-04 10:27:15,276 [INFO] src.gan.trainer: Epoch 28, Batch 200/976, Stage 0, D_loss: 0.4254, G_loss: -3.7638, Num_std: 0.068988, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3008
2025-07-04 10:27:30,767 [INFO] src.gan.trainer: Epoch 28, Batch 300/976, Stage 0, D_loss: 0.4255, G_loss: -3.7626, Num_std: 0.086220, Num_range: 4.0000, Real_score: 0.7007, Fake_score: 0.3035
2025-07-04 10:27:46,307 [INFO] src.gan.trainer: Epoch 28, Batch 400/976, Stage 0, D_loss: 0.4072, G_loss: -3.7706, Num_std: 0.073360, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3050
2025-07-04 10:28:01,747 [INFO] src.gan.trainer: Epoch 28, Batch 500/976, Stage 0, D_loss: 0.4530, G_loss: -3.7685, Num_std: 0.073464, Num_range: 4.0000, Real_score: 0.6952, Fake_score: 0.3001
2025-07-04 10:28:17,220 [INFO] src.gan.trainer: Epoch 28, Batch 600/976, Stage 0, D_loss: 0.4379, G_loss: -3.7630, Num_std: 0.074673, Num_range: 4.0000, Real_score: 0.6999, Fake_score: 0.3036
2025-07-04 10:28:32,550 [INFO] src.gan.trainer: Epoch 28, Batch 700/976, Stage 0, D_loss: 0.4022, G_loss: -3.7682, Num_std: 0.074858, Num_range: 4.0000, Real_score: 0.6990, Fake_score: 0.3008
2025-07-04 10:28:47,951 [INFO] src.gan.trainer: Epoch 28, Batch 800/976, Stage 0, D_loss: 0.4545, G_loss: -3.7609, Num_std: 0.074973, Num_range: 4.0000, Real_score: 0.6977, Fake_score: 0.3034
2025-07-04 10:29:03,344 [INFO] src.gan.trainer: Epoch 28, Batch 900/976, Stage 0, D_loss: 0.4380, G_loss: -3.7647, Num_std: 0.071634, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3012
2025-07-04 10:29:15,070 [INFO] __main__: 第29个epoch完成. 指标: {'d_loss': 0.43815149299678274, 'd_adv_loss': 0.04177201780505849, 'd_ctr_loss': 0.3757939848621362, 'd_reg_loss': 0.1372366013219121, 'real_d_score': 0.697645884190427, 'fake_d_score': 0.3022341694927117, 'g_loss': -3.738417317632766, 'g_adv_loss': 0.48764783106337134, 'g_feature_loss': 1.0199326354745635, 'g_diversity_loss': -0.9988116216464119, 'g_numeric_diversity_loss': -0.4097812697851186, 'fake_d_score_g': 0.3022481125621679, 'temperature': 3.4033116053197623, 'numeric_std': 0.06974925633933504, 'numeric_range': 3.99999910951306, 'training_stage': 0.0}
2025-07-04 10:29:15,071 [INFO] __main__: 开始第30/30个epoch (阶段: 0)
2025-07-04 10:29:15,359 [INFO] src.gan.trainer: Epoch 29, Batch 0/976, Stage 0, D_loss: 0.4425, G_loss: -3.7632, Num_std: 0.093387, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3029
2025-07-04 10:29:30,777 [INFO] src.gan.trainer: Epoch 29, Batch 100/976, Stage 0, D_loss: 0.4543, G_loss: -3.7653, Num_std: 0.083445, Num_range: 4.0000, Real_score: 0.6977, Fake_score: 0.3011
2025-07-04 10:29:46,192 [INFO] src.gan.trainer: Epoch 29, Batch 200/976, Stage 0, D_loss: 0.4290, G_loss: -3.7676, Num_std: 0.070575, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3035
2025-07-04 10:30:01,572 [INFO] src.gan.trainer: Epoch 29, Batch 300/976, Stage 0, D_loss: 0.4493, G_loss: -3.7678, Num_std: 0.069901, Num_range: 4.0000, Real_score: 0.6952, Fake_score: 0.3011
2025-07-04 10:30:17,155 [INFO] src.gan.trainer: Epoch 29, Batch 400/976, Stage 0, D_loss: 0.4393, G_loss: -3.7669, Num_std: 0.063555, Num_range: 4.0000, Real_score: 0.6977, Fake_score: 0.3034
2025-07-04 10:30:32,704 [INFO] src.gan.trainer: Epoch 29, Batch 500/976, Stage 0, D_loss: 0.4522, G_loss: -3.7657, Num_std: 0.078365, Num_range: 4.0000, Real_score: 0.6963, Fake_score: 0.3031
2025-07-04 10:30:48,185 [INFO] src.gan.trainer: Epoch 29, Batch 600/976, Stage 0, D_loss: 0.4404, G_loss: -3.7657, Num_std: 0.069404, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3010
2025-07-04 10:31:03,687 [INFO] src.gan.trainer: Epoch 29, Batch 700/976, Stage 0, D_loss: 0.4407, G_loss: -3.7670, Num_std: 0.076903, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.3000
2025-07-04 10:31:19,247 [INFO] src.gan.trainer: Epoch 29, Batch 800/976, Stage 0, D_loss: 0.4522, G_loss: -3.7673, Num_std: 0.059119, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.2987
2025-07-04 10:31:34,710 [INFO] src.gan.trainer: Epoch 29, Batch 900/976, Stage 0, D_loss: 0.4777, G_loss: -3.7627, Num_std: 0.074874, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.3044
2025-07-04 10:31:46,340 [INFO] __main__: 第30个epoch完成. 指标: {'d_loss': 0.43611472909201376, 'd_adv_loss': 0.041728888466557305, 'd_ctr_loss': 0.37378169159542324, 'd_reg_loss': 0.137360987192417, 'real_d_score': 0.6977386077652206, 'fake_d_score': 0.302216694461273, 'g_loss': -3.73802400563584, 'g_adv_loss': 0.48784000911022085, 'g_feature_loss': 1.0176412522222829, 'g_diversity_loss': -0.9987553879415083, 'g_numeric_diversity_loss': -0.4097488363492276, 'fake_d_score_g': 0.3021024108808385, 'temperature': 3.3999082937145353, 'numeric_std': 0.06962212790101592, 'numeric_range': 3.999998953172985, 'training_stage': 0.0}
2025-07-04 10:31:46,341 [INFO] __main__: 在第30个epoch评估改进的GAN
2025-07-04 10:31:46,357 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 10:31:46,357 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 10:31:46,358 [INFO] __main__: 改进GAN评估指标:
2025-07-04 10:31:46,358 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 10:31:46,358 [INFO] __main__:   特征平均标准差: 0.000002
2025-07-04 10:31:46,359 [INFO] __main__:   零标准差特征数: 2.0
2025-07-04 10:31:46,359 [INFO] __main__: 训练质量: 需要改进
2025-07-04 10:31:46,566 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_checkpoint_epoch_30.pt
2025-07-04 10:31:46,765 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan_v2/improved_final_model.pt
2025-07-04 10:31:46,766 [INFO] __main__: 改进的训练成功完成！
2025-07-04 10:31:46,766 [INFO] __main__: 模型保存到: /data/balanced_gan_v2
