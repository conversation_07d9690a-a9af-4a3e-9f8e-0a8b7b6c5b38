2025-07-04 05:59:32,727 [INFO] __main__: 开始改进的GAN训练 - 更好的平衡策略
2025-07-04 05:59:32,727 [INFO] __main__: 参数: {'dataset_name': 'Criteo', 'dataset_path': '/data/Criteo_x4', 'output_dir': '/data/improved_gan', 'noise_dim': 128, 'embedding_dim': 16, 'epochs': 30, 'batch_size': 512, 'generator_lr': 0.0002, 'discriminator_lr': 0.0001, 'initial_temperature': 3.0, 'min_temperature': 1.5, 'temperature_decay': 0.9995, 'max_vocab_size': 10000, 'sample_strategy': 'full', 'max_samples': 500000, 'max_grad_norm': 1.0, 'log_interval': 100, 'save_interval': 5, 'eval_interval': 5, 'seed': 2024, 'num_workers': 2, 'resume': None, 'debug': False}
2025-07-04 05:59:33,327 [INFO] __main__: 加载 Criteo 数据集从 /data/Criteo_x4
2025-07-04 05:59:33,328 [INFO] src.gan.data_prep: Max vocab size per feature: 10000
2025-07-04 05:59:37,289 [INFO] root: Loaded 500000 samples from /data/Criteo_x4/train.csv
2025-07-04 05:59:37,291 [INFO] root: CTR rate in sampled data: 0.2517
2025-07-04 05:59:37,291 [INFO] src.gan.data_prep: Starting GAN preprocessing for 500000 samples
2025-07-04 05:59:37,710 [INFO] src.gan.data_prep: Numeric feature I1: min=0.0000, max=780.0000
2025-07-04 05:59:37,713 [INFO] src.gan.data_prep: Numeric feature I2: min=-3.0000, max=12868.0000
2025-07-04 05:59:37,716 [INFO] src.gan.data_prep: Numeric feature I3: min=0.0000, max=65535.0000
2025-07-04 05:59:37,719 [INFO] src.gan.data_prep: Numeric feature I4: min=0.0000, max=406.0000
2025-07-04 05:59:37,721 [INFO] src.gan.data_prep: Numeric feature I5: min=0.0000, max=2539061.0000
2025-07-04 05:59:37,724 [INFO] src.gan.data_prep: Numeric feature I6: min=0.0000, max=63909.0000
2025-07-04 05:59:37,727 [INFO] src.gan.data_prep: Numeric feature I7: min=0.0000, max=26297.0000
2025-07-04 05:59:37,729 [INFO] src.gan.data_prep: Numeric feature I8: min=0.0000, max=3949.0000
2025-07-04 05:59:37,732 [INFO] src.gan.data_prep: Numeric feature I9: min=0.0000, max=8430.0000
2025-07-04 05:59:37,735 [INFO] src.gan.data_prep: Numeric feature I10: min=0.0000, max=8.0000
2025-07-04 05:59:37,738 [INFO] src.gan.data_prep: Numeric feature I11: min=0.0000, max=125.0000
2025-07-04 05:59:37,740 [INFO] src.gan.data_prep: Numeric feature I12: min=0.0000, max=693.0000
2025-07-04 05:59:37,743 [INFO] src.gan.data_prep: Numeric feature I13: min=0.0000, max=3890.0000
2025-07-04 05:59:37,746 [INFO] src.gan.data_prep: Feature C1: 1031 unique values
2025-07-04 05:59:37,747 [INFO] src.gan.data_prep: Categorical feature C1: final_vocab_size=1032
2025-07-04 05:59:37,749 [INFO] src.gan.data_prep: Feature C2: 528 unique values
2025-07-04 05:59:37,750 [INFO] src.gan.data_prep: Categorical feature C2: final_vocab_size=529
2025-07-04 05:59:37,757 [INFO] src.gan.data_prep: Feature C3: 184172 unique values
2025-07-04 05:59:37,757 [WARNING] src.gan.data_prep: Feature C3 has 184172 categories! Reducing to top 10000
2025-07-04 05:59:37,759 [INFO] src.gan.data_prep: Top 10000 categories cover 62.13% of samples
2025-07-04 05:59:37,762 [INFO] src.gan.data_prep: Categorical feature C3: final_vocab_size=10001
2025-07-04 05:59:37,841 [INFO] src.gan.data_prep: Feature C4: 79226 unique values
2025-07-04 05:59:37,841 [WARNING] src.gan.data_prep: Feature C4 has 79226 categories! Reducing to top 10000
2025-07-04 05:59:37,843 [INFO] src.gan.data_prep: Top 10000 categories cover 82.95% of samples
2025-07-04 05:59:37,846 [INFO] src.gan.data_prep: Categorical feature C4: final_vocab_size=10001
2025-07-04 05:59:37,883 [INFO] src.gan.data_prep: Feature C5: 225 unique values
2025-07-04 05:59:37,883 [INFO] src.gan.data_prep: Categorical feature C5: final_vocab_size=226
2025-07-04 05:59:37,885 [INFO] src.gan.data_prep: Feature C6: 15 unique values
2025-07-04 05:59:37,886 [INFO] src.gan.data_prep: Categorical feature C6: final_vocab_size=16
2025-07-04 05:59:37,888 [INFO] src.gan.data_prep: Feature C7: 10358 unique values
2025-07-04 05:59:37,889 [WARNING] src.gan.data_prep: Feature C7 has 10358 categories! Reducing to top 10000
2025-07-04 05:59:37,891 [INFO] src.gan.data_prep: Top 10000 categories cover 99.93% of samples
2025-07-04 05:59:37,893 [INFO] src.gan.data_prep: Categorical feature C7: final_vocab_size=10001
2025-07-04 05:59:37,901 [INFO] src.gan.data_prep: Feature C8: 447 unique values
2025-07-04 05:59:37,902 [INFO] src.gan.data_prep: Categorical feature C8: final_vocab_size=448
2025-07-04 05:59:37,905 [INFO] src.gan.data_prep: Feature C9: 4 unique values
2025-07-04 05:59:37,905 [INFO] src.gan.data_prep: Categorical feature C9: final_vocab_size=5
2025-07-04 05:59:37,908 [INFO] src.gan.data_prep: Feature C10: 24212 unique values
2025-07-04 05:59:37,908 [WARNING] src.gan.data_prep: Feature C10 has 24212 categories! Reducing to top 10000
2025-07-04 05:59:37,910 [INFO] src.gan.data_prep: Top 10000 categories cover 96.03% of samples
2025-07-04 05:59:37,912 [INFO] src.gan.data_prep: Categorical feature C10: final_vocab_size=10001
2025-07-04 05:59:37,927 [INFO] src.gan.data_prep: Feature C11: 4612 unique values
2025-07-04 05:59:37,928 [INFO] src.gan.data_prep: Categorical feature C11: final_vocab_size=4613
2025-07-04 05:59:37,936 [INFO] src.gan.data_prep: Feature C12: 162318 unique values
2025-07-04 05:59:37,937 [WARNING] src.gan.data_prep: Feature C12 has 162318 categories! Reducing to top 10000
2025-07-04 05:59:37,939 [INFO] src.gan.data_prep: Top 10000 categories cover 66.46% of samples
2025-07-04 05:59:37,941 [INFO] src.gan.data_prep: Categorical feature C12: final_vocab_size=10001
2025-07-04 05:59:38,010 [INFO] src.gan.data_prep: Feature C13: 3072 unique values
2025-07-04 05:59:38,012 [INFO] src.gan.data_prep: Categorical feature C13: final_vocab_size=3073
2025-07-04 05:59:38,015 [INFO] src.gan.data_prep: Feature C14: 27 unique values
2025-07-04 05:59:38,015 [INFO] src.gan.data_prep: Categorical feature C14: final_vocab_size=28
2025-07-04 05:59:38,018 [INFO] src.gan.data_prep: Feature C15: 7961 unique values
2025-07-04 05:59:38,020 [INFO] src.gan.data_prep: Categorical feature C15: final_vocab_size=7962
2025-07-04 05:59:38,029 [INFO] src.gan.data_prep: Feature C16: 128551 unique values
2025-07-04 05:59:38,029 [WARNING] src.gan.data_prep: Feature C16 has 128551 categories! Reducing to top 10000
2025-07-04 05:59:38,032 [INFO] src.gan.data_prep: Top 10000 categories cover 73.14% of samples
2025-07-04 05:59:38,034 [INFO] src.gan.data_prep: Categorical feature C16: final_vocab_size=10001
2025-07-04 05:59:38,089 [INFO] src.gan.data_prep: Feature C17: 11 unique values
2025-07-04 05:59:38,090 [INFO] src.gan.data_prep: Categorical feature C17: final_vocab_size=12
2025-07-04 05:59:38,092 [INFO] src.gan.data_prep: Feature C18: 3555 unique values
2025-07-04 05:59:38,093 [INFO] src.gan.data_prep: Categorical feature C18: final_vocab_size=3556
2025-07-04 05:59:38,097 [INFO] src.gan.data_prep: Feature C19: 1698 unique values
2025-07-04 05:59:38,097 [INFO] src.gan.data_prep: Categorical feature C19: final_vocab_size=1699
2025-07-04 05:59:38,100 [INFO] src.gan.data_prep: Feature C20: 4 unique values
2025-07-04 05:59:38,100 [INFO] src.gan.data_prep: Categorical feature C20: final_vocab_size=5
2025-07-04 05:59:38,106 [INFO] src.gan.data_prep: Feature C21: 147885 unique values
2025-07-04 05:59:38,107 [WARNING] src.gan.data_prep: Feature C21 has 147885 categories! Reducing to top 10000
2025-07-04 05:59:38,109 [INFO] src.gan.data_prep: Top 10000 categories cover 69.30% of samples
2025-07-04 05:59:38,111 [INFO] src.gan.data_prep: Categorical feature C21: final_vocab_size=10001
2025-07-04 05:59:38,176 [INFO] src.gan.data_prep: Feature C22: 15 unique values
2025-07-04 05:59:38,176 [INFO] src.gan.data_prep: Categorical feature C22: final_vocab_size=16
2025-07-04 05:59:38,178 [INFO] src.gan.data_prep: Feature C23: 16 unique values
2025-07-04 05:59:38,179 [INFO] src.gan.data_prep: Categorical feature C23: final_vocab_size=17
2025-07-04 05:59:38,182 [INFO] src.gan.data_prep: Feature C24: 29660 unique values
2025-07-04 05:59:38,182 [WARNING] src.gan.data_prep: Feature C24 has 29660 categories! Reducing to top 10000
2025-07-04 05:59:38,184 [INFO] src.gan.data_prep: Top 10000 categories cover 95.38% of samples
2025-07-04 05:59:38,186 [INFO] src.gan.data_prep: Categorical feature C24: final_vocab_size=10001
2025-07-04 05:59:38,202 [INFO] src.gan.data_prep: Feature C25: 68 unique values
2025-07-04 05:59:38,202 [INFO] src.gan.data_prep: Categorical feature C25: final_vocab_size=69
2025-07-04 05:59:38,205 [INFO] src.gan.data_prep: Feature C26: 22328 unique values
2025-07-04 05:59:38,205 [WARNING] src.gan.data_prep: Feature C26 has 22328 categories! Reducing to top 10000
2025-07-04 05:59:38,207 [INFO] src.gan.data_prep: Top 10000 categories cover 97.38% of samples
2025-07-04 05:59:38,209 [INFO] src.gan.data_prep: Categorical feature C26: final_vocab_size=10001
2025-07-04 05:59:38,221 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 05:59:38,221 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 05:59:38,557 [INFO] src.gan.data_prep: Preprocessing info saved to /data/improved_gan/data/gan_preprocessing_info.pkl
2025-07-04 05:59:38,558 [INFO] src.gan.data_prep: GAN preprocessing completed
2025-07-04 05:59:38,558 [INFO] root: Saving processed data cache to /data/improved_gan/data/processed_Criteo_train_full_500000_10000.pkl
2025-07-04 05:59:38,853 [INFO] __main__: 最终数据集大小: 500000 样本
2025-07-04 05:59:38,853 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 05:59:38,853 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 05:59:38,853 [INFO] __main__: 特征信息: {'numeric_features': ['I1', 'I2', 'I3', 'I4', 'I5', 'I6', 'I7', 'I8', 'I9', 'I10', 'I11', 'I12', 'I13'], 'categorical_features': ['C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10', 'C11', 'C12', 'C13', 'C14', 'C15', 'C16', 'C17', 'C18', 'C19', 'C20', 'C21', 'C22', 'C23', 'C24', 'C25', 'C26'], 'vocab_sizes': [1032, 529, 10001, 10001, 226, 16, 10001, 448, 5, 10001, 4613, 10001, 3073, 28, 7962, 10001, 12, 3556, 1699, 5, 10001, 16, 17, 10001, 69, 10001], 'label_col': 'Label', 'dataset_name': 'Criteo', 'vocab_info_summary': {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}}
2025-07-04 05:59:39,262 [INFO] __main__: 创建数据加载器，共 976 批次
2025-07-04 05:59:39,283 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 05:59:39,283 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 05:59:39,283 [INFO] __main__: 创建改进的Generator和Discriminator模型
2025-07-04 05:59:39,403 [INFO] __main__: Generator参数量: 10,183,879
2025-07-04 05:59:39,403 [INFO] __main__: Discriminator参数量: 1,969,266
2025-07-04 05:59:39,403 [INFO] __main__: 参数比例 (G/D): 5.17
2025-07-04 05:59:39,404 [INFO] __main__: 学习率比例 (G/D): 2.00
2025-07-04 05:59:40,337 [INFO] __main__: 开始改进的训练循环
2025-07-04 05:59:40,337 [INFO] __main__: 开始第1/30个epoch (阶段: 0)
2025-07-04 05:59:41,071 [INFO] src.gan.trainer: Epoch 0, Batch 0/976, Stage 0, D_loss: 0.8884, G_loss: -1.2477, Num_std: 0.457468, Num_range: 3.6253, Real_score: 0.5008, Fake_score: 0.4852
2025-07-04 05:59:51,699 [INFO] src.gan.trainer: Epoch 0, Batch 100/976, Stage 0, D_loss: 0.5738, G_loss: -1.9004, Num_std: 0.080039, Num_range: 3.9517, Real_score: 0.7708, Fake_score: 0.2082
2025-07-04 06:00:02,327 [INFO] src.gan.trainer: Epoch 0, Batch 200/976, Stage 0, D_loss: 0.5488, G_loss: -1.8625, Num_std: 0.069863, Num_range: 3.9827, Real_score: 0.8323, Fake_score: 0.1520
2025-07-04 06:00:12,915 [INFO] src.gan.trainer: Epoch 0, Batch 300/976, Stage 0, D_loss: 0.5351, G_loss: -1.9081, Num_std: 0.081883, Num_range: 3.9915, Real_score: 0.8288, Fake_score: 0.1595
2025-07-04 06:00:23,464 [INFO] src.gan.trainer: Epoch 0, Batch 400/976, Stage 0, D_loss: 0.5365, G_loss: -1.9138, Num_std: 0.079977, Num_range: 3.9947, Real_score: 0.8344, Fake_score: 0.1496
2025-07-04 06:00:34,010 [INFO] src.gan.trainer: 应用温和的平衡调整...
2025-07-04 06:00:34,010 [INFO] src.gan.trainer: 温和调整后 - G学习率: 2.20e-04, D学习率: 9.50e-05, 温度: 3.000
2025-07-04 06:00:34,116 [INFO] src.gan.trainer: Epoch 0, Batch 500/976, Stage 0, D_loss: 0.5503, G_loss: -1.9019, Num_std: 0.080635, Num_range: 3.9964, Real_score: 0.8460, Fake_score: 0.1544
2025-07-04 06:00:44,762 [INFO] src.gan.trainer: Epoch 0, Batch 600/976, Stage 0, D_loss: 0.5328, G_loss: -1.9354, Num_std: 0.101572, Num_range: 3.9974, Real_score: 0.8535, Fake_score: 0.1571
2025-07-04 06:00:55,453 [INFO] src.gan.trainer: Epoch 0, Batch 700/976, Stage 0, D_loss: 0.5225, G_loss: -1.8718, Num_std: 0.076136, Num_range: 3.9981, Real_score: 0.8486, Fake_score: 0.1477
2025-07-04 06:01:06,133 [INFO] src.gan.trainer: Epoch 0, Batch 800/976, Stage 0, D_loss: 0.4957, G_loss: -1.4063, Num_std: 0.051231, Num_range: 3.9986, Real_score: 0.8452, Fake_score: 0.1470
2025-07-04 06:01:16,819 [INFO] src.gan.trainer: Epoch 0, Batch 900/976, Stage 0, D_loss: 0.5286, G_loss: -1.9208, Num_std: 0.082295, Num_range: 3.9989, Real_score: 0.8509, Fake_score: 0.1522
2025-07-04 06:01:24,855 [INFO] __main__: 第1个epoch完成. 指标: {'d_loss': 0.5488692939159323, 'd_adv_loss': 0.012939221827317111, 'd_ctr_loss': 0.5262451052177145, 'd_reg_loss': 0.19369934130325028, 'real_d_score': 0.8195423856988301, 'fake_d_score': 0.17149093188345427, 'g_loss': -1.9112177865549198, 'g_adv_loss': 0.6934406412460031, 'g_feature_loss': 1.4105262466568942, 'g_diversity_loss': -0.997419897802786, 'g_numeric_diversity_loss': -0.38955502642975104, 'fake_d_score_g': 0.17108047124548056, 'temperature': 3.000000000000042, 'numeric_std': 0.09027425175605219, 'numeric_range': 3.9837250428610127, 'training_stage': 0.0}
2025-07-04 06:01:24,855 [INFO] __main__: 开始第2/30个epoch (阶段: 0)
2025-07-04 06:01:25,209 [INFO] src.gan.trainer: Epoch 1, Batch 0/976, Stage 0, D_loss: 0.4881, G_loss: -1.9385, Num_std: 0.082163, Num_range: 3.9990, Real_score: 0.8535, Fake_score: 0.1461
2025-07-04 06:01:36,029 [INFO] src.gan.trainer: Epoch 1, Batch 100/976, Stage 0, D_loss: 0.4673, G_loss: -1.9312, Num_std: 0.090552, Num_range: 3.9993, Real_score: 0.8471, Fake_score: 0.1513
2025-07-04 06:01:46,694 [INFO] src.gan.trainer: Epoch 1, Batch 200/976, Stage 0, D_loss: 0.5285, G_loss: -1.9436, Num_std: 0.062727, Num_range: 3.9994, Real_score: 0.8539, Fake_score: 0.1484
2025-07-04 06:01:57,357 [INFO] src.gan.trainer: Epoch 1, Batch 300/976, Stage 0, D_loss: 0.4894, G_loss: -1.9428, Num_std: 0.084327, Num_range: 3.9995, Real_score: 0.8463, Fake_score: 0.1523
2025-07-04 06:02:08,024 [INFO] src.gan.trainer: Epoch 1, Batch 400/976, Stage 0, D_loss: 0.5039, G_loss: -1.9430, Num_std: 0.081030, Num_range: 3.9995, Real_score: 0.8511, Fake_score: 0.1482
2025-07-04 06:02:18,624 [INFO] src.gan.trainer: Epoch 1, Batch 500/976, Stage 0, D_loss: 0.4646, G_loss: -1.8788, Num_std: 0.049524, Num_range: 3.9996, Real_score: 0.8474, Fake_score: 0.1523
2025-07-04 06:02:29,289 [INFO] src.gan.trainer: Epoch 1, Batch 600/976, Stage 0, D_loss: 0.4747, G_loss: -1.9390, Num_std: 0.077142, Num_range: 3.9997, Real_score: 0.8515, Fake_score: 0.1467
2025-07-04 06:02:39,852 [INFO] src.gan.trainer: Epoch 1, Batch 700/976, Stage 0, D_loss: 0.5128, G_loss: -1.9358, Num_std: 0.088097, Num_range: 3.9997, Real_score: 0.8475, Fake_score: 0.1516
2025-07-04 06:02:50,462 [INFO] src.gan.trainer: Epoch 1, Batch 800/976, Stage 0, D_loss: 0.4925, G_loss: -1.9365, Num_std: 0.070720, Num_range: 3.9997, Real_score: 0.8466, Fake_score: 0.1484
2025-07-04 06:03:01,126 [INFO] src.gan.trainer: Epoch 1, Batch 900/976, Stage 0, D_loss: 0.5087, G_loss: -1.9448, Num_std: 0.058681, Num_range: 3.9997, Real_score: 0.8514, Fake_score: 0.1503
2025-07-04 06:03:09,128 [INFO] __main__: 第2个epoch完成. 指标: {'d_loss': 0.4948575223689196, 'd_adv_loss': 0.004719793070851991, 'd_ctr_loss': 0.47894464085092325, 'd_reg_loss': 0.22386177894888323, 'real_d_score': 0.8493331526024422, 'fake_d_score': 0.15012529092367535, 'g_loss': -1.9112396658566162, 'g_adv_loss': 0.724371769602915, 'g_feature_loss': 1.3395613912554063, 'g_diversity_loss': -0.9800585524835537, 'g_numeric_diversity_loss': -0.39710414869191696, 'fake_d_score_g': 0.15005947679250706, 'temperature': 2.9984999999999435, 'numeric_std': 0.07699136953079527, 'numeric_range': 3.9995328578792666, 'training_stage': 0.0}
2025-07-04 06:03:09,128 [INFO] __main__: 开始第3/30个epoch (阶段: 0)
2025-07-04 06:03:09,348 [INFO] src.gan.trainer: Epoch 2, Batch 0/976, Stage 0, D_loss: 0.5011, G_loss: -1.9390, Num_std: 0.061287, Num_range: 3.9998, Real_score: 0.8519, Fake_score: 0.1555
2025-07-04 06:03:20,018 [INFO] src.gan.trainer: Epoch 2, Batch 100/976, Stage 0, D_loss: 0.4907, G_loss: -1.9265, Num_std: 0.049724, Num_range: 3.9998, Real_score: 0.8469, Fake_score: 0.1524
2025-07-04 06:03:30,787 [INFO] src.gan.trainer: Epoch 2, Batch 200/976, Stage 0, D_loss: 0.4834, G_loss: -1.9291, Num_std: 0.067125, Num_range: 3.9998, Real_score: 0.8515, Fake_score: 0.1526
2025-07-04 06:03:41,532 [INFO] src.gan.trainer: Epoch 2, Batch 300/976, Stage 0, D_loss: 0.4648, G_loss: -1.9347, Num_std: 0.054557, Num_range: 3.9998, Real_score: 0.8516, Fake_score: 0.1506
2025-07-04 06:03:52,267 [INFO] src.gan.trainer: Epoch 2, Batch 400/976, Stage 0, D_loss: 0.4697, G_loss: -1.9391, Num_std: 0.059342, Num_range: 3.9999, Real_score: 0.8497, Fake_score: 0.1517
2025-07-04 06:04:02,981 [INFO] src.gan.trainer: Epoch 2, Batch 500/976, Stage 0, D_loss: 0.5516, G_loss: -1.9363, Num_std: 0.072445, Num_range: 3.9998, Real_score: 0.8478, Fake_score: 0.1527
2025-07-04 06:04:13,636 [INFO] src.gan.trainer: Epoch 2, Batch 600/976, Stage 0, D_loss: 0.4717, G_loss: -1.9443, Num_std: 0.073039, Num_range: 3.9998, Real_score: 0.8496, Fake_score: 0.1505
2025-07-04 06:04:24,302 [INFO] src.gan.trainer: Epoch 2, Batch 700/976, Stage 0, D_loss: 0.4750, G_loss: -1.9345, Num_std: 0.063931, Num_range: 3.9999, Real_score: 0.8511, Fake_score: 0.1512
2025-07-04 06:04:35,060 [INFO] src.gan.trainer: Epoch 2, Batch 800/976, Stage 0, D_loss: 0.4812, G_loss: -1.9347, Num_std: 0.045226, Num_range: 3.9999, Real_score: 0.8483, Fake_score: 0.1493
2025-07-04 06:04:45,706 [INFO] src.gan.trainer: Epoch 2, Batch 900/976, Stage 0, D_loss: 0.4886, G_loss: -1.9385, Num_std: 0.070187, Num_range: 3.9999, Real_score: 0.8480, Fake_score: 0.1474
2025-07-04 06:04:53,724 [INFO] __main__: 第3个epoch完成. 指标: {'d_loss': 0.47756281934800743, 'd_adv_loss': 0.004437437730213463, 'd_ctr_loss': 0.46201393350226017, 'd_reg_loss': 0.22222896369143558, 'real_d_score': 0.8489773802947801, 'fake_d_score': 0.1509975963806518, 'g_loss': -1.9045559485275, 'g_adv_loss': 0.7223908370513418, 'g_feature_loss': 1.4011320621996646, 'g_diversity_loss': -0.9775290930735289, 'g_numeric_diversity_loss': -0.3970073460311181, 'fake_d_score_g': 0.15103442267682682, 'temperature': 2.9970007499999856, 'numeric_std': 0.06829118208108537, 'numeric_range': 3.9998315558081736, 'training_stage': 0.0}
2025-07-04 06:04:53,724 [INFO] __main__: 开始第4/30个epoch (阶段: 0)
2025-07-04 06:04:53,949 [INFO] src.gan.trainer: Epoch 3, Batch 0/976, Stage 0, D_loss: 0.4524, G_loss: -1.9401, Num_std: 0.072074, Num_range: 3.9999, Real_score: 0.8496, Fake_score: 0.1530
2025-07-04 06:05:01,630 [INFO] src.gan.trainer: 应用温和的平衡调整...
2025-07-04 06:05:01,630 [INFO] src.gan.trainer: 温和调整后 - G学习率: 2.42e-04, D学习率: 9.02e-05, 温度: 3.000
2025-07-04 06:05:04,757 [INFO] src.gan.trainer: Epoch 3, Batch 100/976, Stage 0, D_loss: 0.4398, G_loss: -1.9314, Num_std: 0.064583, Num_range: 3.9999, Real_score: 0.8493, Fake_score: 0.1516
2025-07-04 06:05:15,544 [INFO] src.gan.trainer: Epoch 3, Batch 200/976, Stage 0, D_loss: 0.4699, G_loss: -1.9165, Num_std: 0.045528, Num_range: 3.9999, Real_score: 0.8469, Fake_score: 0.1511
2025-07-04 06:05:26,322 [INFO] src.gan.trainer: Epoch 3, Batch 300/976, Stage 0, D_loss: 0.5124, G_loss: -1.9228, Num_std: 0.066703, Num_range: 3.9999, Real_score: 0.8522, Fake_score: 0.1532
2025-07-04 06:05:37,218 [INFO] src.gan.trainer: Epoch 3, Batch 400/976, Stage 0, D_loss: 0.4993, G_loss: -1.7160, Num_std: 0.062710, Num_range: 3.9999, Real_score: 0.8482, Fake_score: 0.1527
2025-07-04 06:05:47,986 [INFO] src.gan.trainer: Epoch 3, Batch 500/976, Stage 0, D_loss: 0.4493, G_loss: -1.7097, Num_std: 0.056947, Num_range: 3.9999, Real_score: 0.8461, Fake_score: 0.1508
2025-07-04 06:05:58,753 [INFO] src.gan.trainer: Epoch 3, Batch 600/976, Stage 0, D_loss: 0.5089, G_loss: -1.7505, Num_std: 0.045222, Num_range: 3.9999, Real_score: 0.8486, Fake_score: 0.1527
2025-07-04 06:06:09,521 [INFO] src.gan.trainer: Epoch 3, Batch 700/976, Stage 0, D_loss: 0.4686, G_loss: -1.7850, Num_std: 0.067225, Num_range: 3.9999, Real_score: 0.8499, Fake_score: 0.1466
2025-07-04 06:06:20,250 [INFO] src.gan.trainer: Epoch 3, Batch 800/976, Stage 0, D_loss: 0.4630, G_loss: -1.8149, Num_std: 0.051822, Num_range: 3.9999, Real_score: 0.8475, Fake_score: 0.1509
2025-07-04 06:06:30,847 [INFO] src.gan.trainer: Epoch 3, Batch 900/976, Stage 0, D_loss: 0.4341, G_loss: -1.8213, Num_std: 0.070965, Num_range: 3.9999, Real_score: 0.8459, Fake_score: 0.1459
2025-07-04 06:06:38,860 [INFO] __main__: 第4个epoch完成. 指标: {'d_loss': 0.4659456959085882, 'd_adv_loss': 0.00471538287152337, 'd_ctr_loss': 0.4501213268605897, 'd_reg_loss': 0.22217971657509675, 'real_d_score': 0.8479937658813163, 'fake_d_score': 0.15031800052670172, 'g_loss': -1.7964042599664114, 'g_adv_loss': 0.7257188249257258, 'g_feature_loss': 1.7973468575443385, 'g_diversity_loss': -0.8965556531167422, 'g_numeric_diversity_loss': -0.39692265086914236, 'fake_d_score_g': 0.14946850861773883, 'temperature': 2.99966819874287, 'numeric_std': 0.06686659334070003, 'numeric_range': 3.99991423135898, 'training_stage': 0.0}
2025-07-04 06:06:38,861 [INFO] __main__: 开始第5/30个epoch (阶段: 0)
2025-07-04 06:06:39,075 [INFO] src.gan.trainer: Epoch 4, Batch 0/976, Stage 0, D_loss: 0.4138, G_loss: -1.8302, Num_std: 0.060477, Num_range: 3.9999, Real_score: 0.8477, Fake_score: 0.1429
2025-07-04 06:06:49,753 [INFO] src.gan.trainer: Epoch 4, Batch 100/976, Stage 0, D_loss: 0.4328, G_loss: -1.8513, Num_std: 0.050018, Num_range: 3.9999, Real_score: 0.8457, Fake_score: 0.1478
2025-07-04 06:07:00,352 [INFO] src.gan.trainer: Epoch 4, Batch 200/976, Stage 0, D_loss: 0.4376, G_loss: -1.8416, Num_std: 0.077431, Num_range: 3.9999, Real_score: 0.8481, Fake_score: 0.1545
2025-07-04 06:07:10,939 [INFO] src.gan.trainer: Epoch 4, Batch 300/976, Stage 0, D_loss: 0.4748, G_loss: -1.8401, Num_std: 0.064943, Num_range: 3.9999, Real_score: 0.8510, Fake_score: 0.1500
2025-07-04 06:07:21,490 [INFO] src.gan.trainer: Epoch 4, Batch 400/976, Stage 0, D_loss: 0.4765, G_loss: -1.8458, Num_std: 0.076727, Num_range: 3.9999, Real_score: 0.8505, Fake_score: 0.1547
2025-07-04 06:07:32,048 [INFO] src.gan.trainer: Epoch 4, Batch 500/976, Stage 0, D_loss: 0.4536, G_loss: -1.8471, Num_std: 0.062833, Num_range: 4.0000, Real_score: 0.8492, Fake_score: 0.1517
2025-07-04 06:07:42,712 [INFO] src.gan.trainer: Epoch 4, Batch 600/976, Stage 0, D_loss: 0.4270, G_loss: -1.8417, Num_std: 0.043648, Num_range: 4.0000, Real_score: 0.8513, Fake_score: 0.1497
2025-07-04 06:07:53,249 [INFO] src.gan.trainer: Epoch 4, Batch 700/976, Stage 0, D_loss: 0.4447, G_loss: -1.7785, Num_std: 0.040460, Num_range: 4.0000, Real_score: 0.8513, Fake_score: 0.1514
2025-07-04 06:08:03,809 [INFO] src.gan.trainer: Epoch 4, Batch 800/976, Stage 0, D_loss: 0.4671, G_loss: -1.8553, Num_std: 0.061256, Num_range: 4.0000, Real_score: 0.8473, Fake_score: 0.1524
2025-07-04 06:08:14,355 [INFO] src.gan.trainer: Epoch 4, Batch 900/976, Stage 0, D_loss: 0.4594, G_loss: -1.8624, Num_std: 0.058128, Num_range: 4.0000, Real_score: 0.8457, Fake_score: 0.1503
2025-07-04 06:08:22,295 [INFO] __main__: 第5个epoch完成. 指标: {'d_loss': 0.4563522679334292, 'd_adv_loss': 0.004416349485180661, 'd_ctr_loss': 0.4408915282883604, 'd_reg_loss': 0.22088780470925287, 'real_d_score': 0.848221913835065, 'fake_d_score': 0.15144788933398784, 'g_loss': -1.8172785784194188, 'g_adv_loss': 0.7215886767953645, 'g_feature_loss': 2.0980537599716036, 'g_diversity_loss': -0.957533563441429, 'g_numeric_diversity_loss': -0.3965291569589589, 'fake_d_score_g': 0.15155340412814855, 'temperature': 2.9984999999999435, 'numeric_std': 0.06439279055472885, 'numeric_range': 3.9999534175044227, 'training_stage': 0.0}
2025-07-04 06:08:22,296 [INFO] __main__: 第5个epoch评估完成
2025-07-04 06:08:22,503 [INFO] src.gan.trainer: 检查点已保存到 /data/improved_gan/improved_checkpoint_epoch_5.pt
2025-07-04 06:08:22,503 [INFO] __main__: 开始第6/30个epoch (阶段: 0)
2025-07-04 06:08:22,727 [INFO] src.gan.trainer: Epoch 5, Batch 0/976, Stage 0, D_loss: 0.4224, G_loss: -1.8539, Num_std: 0.065135, Num_range: 4.0000, Real_score: 0.8494, Fake_score: 0.1539
2025-07-04 06:08:33,421 [INFO] src.gan.trainer: Epoch 5, Batch 100/976, Stage 0, D_loss: 0.4270, G_loss: -1.3150, Num_std: 0.052186, Num_range: 4.0000, Real_score: 0.8484, Fake_score: 0.1463
2025-07-04 06:08:44,011 [INFO] src.gan.trainer: Epoch 5, Batch 200/976, Stage 0, D_loss: 0.4791, G_loss: -1.8122, Num_std: 0.076050, Num_range: 4.0000, Real_score: 0.8483, Fake_score: 0.1501
2025-07-04 06:08:54,673 [INFO] src.gan.trainer: Epoch 5, Batch 300/976, Stage 0, D_loss: 0.4877, G_loss: -1.8470, Num_std: 0.055793, Num_range: 4.0000, Real_score: 0.8506, Fake_score: 0.1512
2025-07-04 06:09:05,337 [INFO] src.gan.trainer: Epoch 5, Batch 400/976, Stage 0, D_loss: 0.3700, G_loss: -1.8774, Num_std: 0.035548, Num_range: 4.0000, Real_score: 0.8490, Fake_score: 0.1519
2025-07-04 06:09:15,980 [INFO] src.gan.trainer: Epoch 5, Batch 500/976, Stage 0, D_loss: 0.4487, G_loss: -1.8881, Num_std: 0.064038, Num_range: 4.0000, Real_score: 0.8467, Fake_score: 0.1469
2025-07-04 06:09:26,643 [INFO] src.gan.trainer: Epoch 5, Batch 600/976, Stage 0, D_loss: 0.4491, G_loss: -1.8964, Num_std: 0.063368, Num_range: 4.0000, Real_score: 0.8463, Fake_score: 0.1517
2025-07-04 06:09:37,420 [INFO] src.gan.trainer: Epoch 5, Batch 700/976, Stage 0, D_loss: 0.4553, G_loss: -1.9162, Num_std: 0.051587, Num_range: 4.0000, Real_score: 0.8501, Fake_score: 0.1554
2025-07-04 06:09:48,089 [INFO] src.gan.trainer: Epoch 5, Batch 800/976, Stage 0, D_loss: 0.4634, G_loss: -1.9169, Num_std: 0.048555, Num_range: 4.0000, Real_score: 0.8452, Fake_score: 0.1514
2025-07-04 06:09:58,710 [INFO] src.gan.trainer: Epoch 5, Batch 900/976, Stage 0, D_loss: 0.4131, G_loss: -1.9039, Num_std: 0.071418, Num_range: 4.0000, Real_score: 0.8500, Fake_score: 0.1541
2025-07-04 06:10:06,720 [INFO] __main__: 第6个epoch完成. 指标: {'d_loss': 0.4482680582670398, 'd_adv_loss': 0.0043788718350220145, 'd_ctr_loss': 0.43283759381194586, 'd_reg_loss': 0.2210318526161498, 'real_d_score': 0.8482633302690554, 'fake_d_score': 0.15137455824640447, 'g_loss': -1.8488501735092646, 'g_adv_loss': 0.7228474316843714, 'g_feature_loss': 1.736903188231048, 'g_diversity_loss': -0.9348062916551957, 'g_numeric_diversity_loss': -0.39950857915320104, 'fake_d_score_g': 0.15079358584995647, 'temperature': 2.9970007499999856, 'numeric_std': 0.0636526032834748, 'numeric_range': 3.999975584935937, 'training_stage': 0.0}
2025-07-04 06:10:06,721 [INFO] __main__: 开始第7/30个epoch (阶段: 0)
2025-07-04 06:10:06,939 [INFO] src.gan.trainer: Epoch 6, Batch 0/976, Stage 0, D_loss: 0.4599, G_loss: -1.9048, Num_std: 0.056660, Num_range: 4.0000, Real_score: 0.8480, Fake_score: 0.1525
2025-07-04 06:10:17,663 [INFO] src.gan.trainer: Epoch 6, Batch 100/976, Stage 0, D_loss: 0.4123, G_loss: -1.9076, Num_std: 0.064095, Num_range: 4.0000, Real_score: 0.8456, Fake_score: 0.1511
2025-07-04 06:10:28,356 [INFO] src.gan.trainer: Epoch 6, Batch 200/976, Stage 0, D_loss: 0.4244, G_loss: -1.8782, Num_std: 0.060452, Num_range: 4.0000, Real_score: 0.8496, Fake_score: 0.1523
2025-07-04 06:10:39,142 [INFO] src.gan.trainer: Epoch 6, Batch 300/976, Stage 0, D_loss: 0.4536, G_loss: -1.8858, Num_std: 0.084415, Num_range: 4.0000, Real_score: 0.8514, Fake_score: 0.1533
2025-07-04 06:10:49,832 [INFO] src.gan.trainer: Epoch 6, Batch 400/976, Stage 0, D_loss: 0.4255, G_loss: -1.9001, Num_std: 0.050505, Num_range: 4.0000, Real_score: 0.8491, Fake_score: 0.1530
2025-07-04 06:11:00,556 [INFO] src.gan.trainer: Epoch 6, Batch 500/976, Stage 0, D_loss: 0.4345, G_loss: -1.9032, Num_std: 0.052194, Num_range: 4.0000, Real_score: 0.8465, Fake_score: 0.1567
2025-07-04 06:11:11,266 [INFO] src.gan.trainer: Epoch 6, Batch 600/976, Stage 0, D_loss: 0.4979, G_loss: -0.9852, Num_std: 0.028165, Num_range: 4.0000, Real_score: 0.8491, Fake_score: 0.1517
2025-07-04 06:11:21,944 [INFO] src.gan.trainer: Epoch 6, Batch 700/976, Stage 0, D_loss: 0.4359, G_loss: -1.8892, Num_std: 0.072935, Num_range: 4.0000, Real_score: 0.8496, Fake_score: 0.1520
2025-07-04 06:11:32,636 [INFO] src.gan.trainer: Epoch 6, Batch 800/976, Stage 0, D_loss: 0.3939, G_loss: -1.8831, Num_std: 0.054065, Num_range: 4.0000, Real_score: 0.8462, Fake_score: 0.1504
2025-07-04 06:11:43,404 [INFO] src.gan.trainer: Epoch 6, Batch 900/976, Stage 0, D_loss: 0.4666, G_loss: -1.8843, Num_std: 0.083145, Num_range: 4.0000, Real_score: 0.8497, Fake_score: 0.1485
2025-07-04 06:11:51,453 [INFO] __main__: 第7个epoch完成. 指标: {'d_loss': 0.44078557739858687, 'd_adv_loss': 0.0043846836025429594, 'd_ctr_loss': 0.42539763975827466, 'd_reg_loss': 0.22006507785838167, 'real_d_score': 0.8478438149465883, 'fake_d_score': 0.15182518082686094, 'g_loss': -1.8610107823595654, 'g_adv_loss': 0.7214527474563635, 'g_feature_loss': 1.5453295049364457, 'g_diversity_loss': -0.9356025479978219, 'g_numeric_diversity_loss': -0.3977028899520757, 'fake_d_score_g': 0.1515993819625468, 'temperature': 2.99550224962497, 'numeric_std': 0.06328939327056006, 'numeric_range': 3.999982184073982, 'training_stage': 0.0}
2025-07-04 06:11:51,453 [INFO] __main__: 开始第8/30个epoch (阶段: 0)
2025-07-04 06:11:51,676 [INFO] src.gan.trainer: Epoch 7, Batch 0/976, Stage 0, D_loss: 0.4382, G_loss: -1.8911, Num_std: 0.093586, Num_range: 4.0000, Real_score: 0.8470, Fake_score: 0.1546
2025-07-04 06:12:02,381 [INFO] src.gan.trainer: Epoch 7, Batch 100/976, Stage 0, D_loss: 0.4545, G_loss: -1.8699, Num_std: 0.036769, Num_range: 4.0000, Real_score: 0.8501, Fake_score: 0.1529
2025-07-04 06:12:13,056 [INFO] src.gan.trainer: Epoch 7, Batch 200/976, Stage 0, D_loss: 0.4181, G_loss: -1.9028, Num_std: 0.085875, Num_range: 4.0000, Real_score: 0.8525, Fake_score: 0.1500
2025-07-04 06:12:23,735 [INFO] src.gan.trainer: Epoch 7, Batch 300/976, Stage 0, D_loss: 0.4269, G_loss: -1.8553, Num_std: 0.051161, Num_range: 4.0000, Real_score: 0.8485, Fake_score: 0.1519
2025-07-04 06:12:34,394 [INFO] src.gan.trainer: Epoch 7, Batch 400/976, Stage 0, D_loss: 0.4821, G_loss: -1.9057, Num_std: 0.075958, Num_range: 4.0000, Real_score: 0.8496, Fake_score: 0.1544
2025-07-04 06:12:45,165 [INFO] src.gan.trainer: Epoch 7, Batch 500/976, Stage 0, D_loss: 0.4456, G_loss: -1.9090, Num_std: 0.070151, Num_range: 4.0000, Real_score: 0.8496, Fake_score: 0.1522
2025-07-04 06:12:55,817 [INFO] src.gan.trainer: Epoch 7, Batch 600/976, Stage 0, D_loss: 0.4061, G_loss: -1.9110, Num_std: 0.067367, Num_range: 4.0000, Real_score: 0.8551, Fake_score: 0.1540
2025-07-04 06:13:06,482 [INFO] src.gan.trainer: Epoch 7, Batch 700/976, Stage 0, D_loss: 0.4259, G_loss: -1.9121, Num_std: 0.063904, Num_range: 4.0000, Real_score: 0.8504, Fake_score: 0.1565
2025-07-04 06:13:17,135 [INFO] src.gan.trainer: Epoch 7, Batch 800/976, Stage 0, D_loss: 0.4296, G_loss: -1.9038, Num_std: 0.109677, Num_range: 4.0000, Real_score: 0.8479, Fake_score: 0.1549
2025-07-04 06:13:27,804 [INFO] src.gan.trainer: Epoch 7, Batch 900/976, Stage 0, D_loss: 0.4404, G_loss: -1.9150, Num_std: 0.057180, Num_range: 4.0000, Real_score: 0.8508, Fake_score: 0.1533
2025-07-04 06:13:35,824 [INFO] __main__: 第8个epoch完成. 指标: {'d_loss': 0.43382620704589325, 'd_adv_loss': 0.004257308083040582, 'd_ctr_loss': 0.41856830188485394, 'd_reg_loss': 0.22001193651593579, 'real_d_score': 0.8480127111810162, 'fake_d_score': 0.15189437287264193, 'g_loss': -1.8868388039380541, 'g_adv_loss': 0.7206519503512828, 'g_feature_loss': 1.4747510522481824, 'g_diversity_loss': -0.947835012262719, 'g_numeric_diversity_loss': -0.3993395719921494, 'fake_d_score_g': 0.15196267463511112, 'temperature': 2.994004498500194, 'numeric_std': 0.06265593935033215, 'numeric_range': 3.9999850214016144, 'training_stage': 0.0}
2025-07-04 06:13:35,824 [INFO] __main__: 开始第9/30个epoch (阶段: 0)
2025-07-04 06:13:36,048 [INFO] src.gan.trainer: Epoch 8, Batch 0/976, Stage 0, D_loss: 0.3714, G_loss: -1.9100, Num_std: 0.037825, Num_range: 4.0000, Real_score: 0.8501, Fake_score: 0.1549
2025-07-04 06:13:46,859 [INFO] src.gan.trainer: Epoch 8, Batch 100/976, Stage 0, D_loss: 0.4139, G_loss: -1.9053, Num_std: 0.083934, Num_range: 4.0000, Real_score: 0.8474, Fake_score: 0.1498
2025-07-04 06:13:57,534 [INFO] src.gan.trainer: Epoch 8, Batch 200/976, Stage 0, D_loss: 0.4568, G_loss: -1.9010, Num_std: 0.060208, Num_range: 4.0000, Real_score: 0.8499, Fake_score: 0.1513
2025-07-04 06:14:08,210 [INFO] src.gan.trainer: Epoch 8, Batch 300/976, Stage 0, D_loss: 0.4109, G_loss: -1.9278, Num_std: 0.059032, Num_range: 4.0000, Real_score: 0.8440, Fake_score: 0.1494
2025-07-04 06:14:18,799 [INFO] src.gan.trainer: Epoch 8, Batch 400/976, Stage 0, D_loss: 0.4448, G_loss: -1.9163, Num_std: 0.059356, Num_range: 4.0000, Real_score: 0.8481, Fake_score: 0.1519
2025-07-04 06:14:29,378 [INFO] src.gan.trainer: Epoch 8, Batch 500/976, Stage 0, D_loss: 0.4410, G_loss: -1.9121, Num_std: 0.064931, Num_range: 4.0000, Real_score: 0.8476, Fake_score: 0.1511
2025-07-04 06:14:39,966 [INFO] src.gan.trainer: Epoch 8, Batch 600/976, Stage 0, D_loss: 0.4392, G_loss: -1.9117, Num_std: 0.056422, Num_range: 4.0000, Real_score: 0.8479, Fake_score: 0.1513
2025-07-04 06:14:50,646 [INFO] src.gan.trainer: Epoch 8, Batch 700/976, Stage 0, D_loss: 0.4567, G_loss: -1.9090, Num_std: 0.065450, Num_range: 4.0000, Real_score: 0.8459, Fake_score: 0.1510
2025-07-04 06:15:01,230 [INFO] src.gan.trainer: Epoch 8, Batch 800/976, Stage 0, D_loss: 0.4577, G_loss: -1.9101, Num_std: 0.055150, Num_range: 4.0000, Real_score: 0.8471, Fake_score: 0.1498
2025-07-04 06:15:11,776 [INFO] src.gan.trainer: Epoch 8, Batch 900/976, Stage 0, D_loss: 0.3691, G_loss: -1.8982, Num_std: 0.055987, Num_range: 4.0000, Real_score: 0.8452, Fake_score: 0.1513
2025-07-04 06:15:19,716 [INFO] __main__: 第9个epoch完成. 指标: {'d_loss': 0.4274297752646638, 'd_adv_loss': 0.004233552665653877, 'd_ctr_loss': 0.41220518164947356, 'd_reg_loss': 0.21982082384104146, 'real_d_score': 0.8479621664422465, 'fake_d_score': 0.15199739179100658, 'g_loss': -1.8936751213871292, 'g_adv_loss': 0.7205322323153253, 'g_feature_loss': 1.430954999672094, 'g_diversity_loss': -0.9471519736908992, 'g_numeric_diversity_loss': -0.3999162566329005, 'fake_d_score_g': 0.15201986416960603, 'temperature': 2.9925074962510005, 'numeric_std': 0.062275524796501974, 'numeric_range': 3.9999872394761047, 'training_stage': 0.0}
2025-07-04 06:15:19,717 [INFO] __main__: 开始第10/30个epoch (阶段: 0)
2025-07-04 06:15:19,934 [INFO] src.gan.trainer: Epoch 9, Batch 0/976, Stage 0, D_loss: 0.4309, G_loss: -1.9013, Num_std: 0.063626, Num_range: 4.0000, Real_score: 0.8506, Fake_score: 0.1545
2025-07-04 06:15:30,692 [INFO] src.gan.trainer: Epoch 9, Batch 100/976, Stage 0, D_loss: 0.4134, G_loss: -1.9070, Num_std: 0.056918, Num_range: 4.0000, Real_score: 0.8510, Fake_score: 0.1560
2025-07-04 06:15:41,295 [INFO] src.gan.trainer: Epoch 9, Batch 200/976, Stage 0, D_loss: 0.4179, G_loss: -1.9108, Num_std: 0.059124, Num_range: 4.0000, Real_score: 0.8504, Fake_score: 0.1507
2025-07-04 06:15:51,994 [INFO] src.gan.trainer: Epoch 9, Batch 300/976, Stage 0, D_loss: 0.4211, G_loss: -1.9177, Num_std: 0.060809, Num_range: 4.0000, Real_score: 0.8470, Fake_score: 0.1523
2025-07-04 06:16:02,623 [INFO] src.gan.trainer: Epoch 9, Batch 400/976, Stage 0, D_loss: 0.4290, G_loss: -1.9164, Num_std: 0.088868, Num_range: 4.0000, Real_score: 0.8447, Fake_score: 0.1521
2025-07-04 06:16:13,214 [INFO] src.gan.trainer: Epoch 9, Batch 500/976, Stage 0, D_loss: 0.4157, G_loss: -1.9148, Num_std: 0.059907, Num_range: 4.0000, Real_score: 0.8492, Fake_score: 0.1506
2025-07-04 06:16:23,805 [INFO] src.gan.trainer: Epoch 9, Batch 600/976, Stage 0, D_loss: 0.4251, G_loss: -1.9141, Num_std: 0.062064, Num_range: 4.0000, Real_score: 0.8459, Fake_score: 0.1516
2025-07-04 06:16:34,462 [INFO] src.gan.trainer: Epoch 9, Batch 700/976, Stage 0, D_loss: 0.4443, G_loss: -1.9115, Num_std: 0.065644, Num_range: 4.0000, Real_score: 0.8452, Fake_score: 0.1529
2025-07-04 06:16:45,150 [INFO] src.gan.trainer: Epoch 9, Batch 800/976, Stage 0, D_loss: 0.4328, G_loss: -1.9151, Num_std: 0.054650, Num_range: 4.0000, Real_score: 0.8508, Fake_score: 0.1529
2025-07-04 06:16:55,828 [INFO] src.gan.trainer: Epoch 9, Batch 900/976, Stage 0, D_loss: 0.4522, G_loss: -1.9121, Num_std: 0.052733, Num_range: 4.0000, Real_score: 0.8442, Fake_score: 0.1480
2025-07-04 06:17:03,830 [INFO] __main__: 第10个epoch完成. 指标: {'d_loss': 0.4217155775146902, 'd_adv_loss': 0.0042929586631887205, 'd_ctr_loss': 0.4064564488095342, 'd_reg_loss': 0.21932339695755568, 'real_d_score': 0.8477525465556832, 'fake_d_score': 0.15229852938810806, 'g_loss': -1.894678304719996, 'g_adv_loss': 0.7200387834769767, 'g_feature_loss': 1.3604115445594314, 'g_diversity_loss': -0.9392587936437519, 'g_numeric_diversity_loss': -0.39987024267921767, 'fake_d_score_g': 0.15233064842120309, 'temperature': 2.9910112425028488, 'numeric_std': 0.06093269744797288, 'numeric_range': 3.9999899054404287, 'training_stage': 0.0}
2025-07-04 06:17:03,830 [INFO] __main__: 第10个epoch评估完成
2025-07-04 06:17:04,019 [INFO] src.gan.trainer: 检查点已保存到 /data/improved_gan/improved_checkpoint_epoch_10.pt
2025-07-04 06:17:04,020 [INFO] __main__: 开始第11/30个epoch (阶段: 0)
2025-07-04 06:17:04,245 [INFO] src.gan.trainer: Epoch 10, Batch 0/976, Stage 0, D_loss: 0.4341, G_loss: -1.9149, Num_std: 0.069375, Num_range: 4.0000, Real_score: 0.8471, Fake_score: 0.1551
2025-07-04 06:17:14,912 [INFO] src.gan.trainer: Epoch 10, Batch 100/976, Stage 0, D_loss: 0.4299, G_loss: -1.9226, Num_std: 0.066827, Num_range: 4.0000, Real_score: 0.8532, Fake_score: 0.1531
2025-07-04 06:17:25,610 [INFO] src.gan.trainer: Epoch 10, Batch 200/976, Stage 0, D_loss: 0.4349, G_loss: -1.9276, Num_std: 0.062540, Num_range: 4.0000, Real_score: 0.8427, Fake_score: 0.1521
2025-07-04 06:17:36,285 [INFO] src.gan.trainer: Epoch 10, Batch 300/976, Stage 0, D_loss: 0.4153, G_loss: -1.9247, Num_std: 0.056050, Num_range: 4.0000, Real_score: 0.8472, Fake_score: 0.1524
2025-07-04 06:17:47,014 [INFO] src.gan.trainer: Epoch 10, Batch 400/976, Stage 0, D_loss: 0.3929, G_loss: -1.9266, Num_std: 0.060870, Num_range: 4.0000, Real_score: 0.8479, Fake_score: 0.1533
2025-07-04 06:17:57,700 [INFO] src.gan.trainer: Epoch 10, Batch 500/976, Stage 0, D_loss: 0.3645, G_loss: -1.9294, Num_std: 0.054189, Num_range: 4.0000, Real_score: 0.8472, Fake_score: 0.1510
2025-07-04 06:18:08,397 [INFO] src.gan.trainer: Epoch 10, Batch 600/976, Stage 0, D_loss: 0.4096, G_loss: -1.9234, Num_std: 0.071929, Num_range: 4.0000, Real_score: 0.8472, Fake_score: 0.1556
