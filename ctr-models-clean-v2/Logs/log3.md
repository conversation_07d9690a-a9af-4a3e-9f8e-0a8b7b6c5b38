2025-07-04 06:24:03,797 [INFO] __main__: 开始改进的GAN训练 - 更好的平衡策略
2025-07-04 06:24:03,797 [INFO] __main__: 参数: {'dataset_name': 'Criteo', 'dataset_path': '/data/Criteo_x4', 'output_dir': '/data/improved_gan_v2', 'noise_dim': 128, 'embedding_dim': 16, 'epochs': 40, 'batch_size': 512, 'generator_lr': 0.0002, 'discriminator_lr': 0.0001, 'initial_temperature': 3.0, 'min_temperature': 1.5, 'temperature_decay': 0.9995, 'max_vocab_size': 10000, 'sample_strategy': 'full', 'max_samples': 500000, 'max_grad_norm': 1.0, 'log_interval': 100, 'save_interval': 3, 'eval_interval': 3, 'seed': 2024, 'num_workers': 2, 'resume': None, 'debug': False}
2025-07-04 06:24:04,319 [INFO] __main__: 加载 Criteo 数据集从 /data/Criteo_x4
2025-07-04 06:24:04,320 [INFO] src.gan.data_prep: Max vocab size per feature: 10000
2025-07-04 06:24:08,286 [INFO] root: Loaded 500000 samples from /data/Criteo_x4/train.csv
2025-07-04 06:24:08,288 [INFO] root: CTR rate in sampled data: 0.2517
2025-07-04 06:24:08,288 [INFO] src.gan.data_prep: Starting GAN preprocessing for 500000 samples
2025-07-04 06:24:08,722 [INFO] src.gan.data_prep: Numeric feature I1: min=0.0000, max=780.0000
2025-07-04 06:24:08,726 [INFO] src.gan.data_prep: Numeric feature I2: min=-3.0000, max=12868.0000
2025-07-04 06:24:08,729 [INFO] src.gan.data_prep: Numeric feature I3: min=0.0000, max=65535.0000
2025-07-04 06:24:08,732 [INFO] src.gan.data_prep: Numeric feature I4: min=0.0000, max=406.0000
2025-07-04 06:24:08,734 [INFO] src.gan.data_prep: Numeric feature I5: min=0.0000, max=2539061.0000
2025-07-04 06:24:08,737 [INFO] src.gan.data_prep: Numeric feature I6: min=0.0000, max=63909.0000
2025-07-04 06:24:08,740 [INFO] src.gan.data_prep: Numeric feature I7: min=0.0000, max=26297.0000
2025-07-04 06:24:08,743 [INFO] src.gan.data_prep: Numeric feature I8: min=0.0000, max=3949.0000
2025-07-04 06:24:08,746 [INFO] src.gan.data_prep: Numeric feature I9: min=0.0000, max=8430.0000
2025-07-04 06:24:08,749 [INFO] src.gan.data_prep: Numeric feature I10: min=0.0000, max=8.0000
2025-07-04 06:24:08,752 [INFO] src.gan.data_prep: Numeric feature I11: min=0.0000, max=125.0000
2025-07-04 06:24:08,754 [INFO] src.gan.data_prep: Numeric feature I12: min=0.0000, max=693.0000
2025-07-04 06:24:08,757 [INFO] src.gan.data_prep: Numeric feature I13: min=0.0000, max=3890.0000
2025-07-04 06:24:08,761 [INFO] src.gan.data_prep: Feature C1: 1031 unique values
2025-07-04 06:24:08,761 [INFO] src.gan.data_prep: Categorical feature C1: final_vocab_size=1032
2025-07-04 06:24:08,764 [INFO] src.gan.data_prep: Feature C2: 528 unique values
2025-07-04 06:24:08,764 [INFO] src.gan.data_prep: Categorical feature C2: final_vocab_size=529
2025-07-04 06:24:08,772 [INFO] src.gan.data_prep: Feature C3: 184172 unique values
2025-07-04 06:24:08,772 [WARNING] src.gan.data_prep: Feature C3 has 184172 categories! Reducing to top 10000
2025-07-04 06:24:08,775 [INFO] src.gan.data_prep: Top 10000 categories cover 62.13% of samples
2025-07-04 06:24:08,777 [INFO] src.gan.data_prep: Categorical feature C3: final_vocab_size=10001
2025-07-04 06:24:08,858 [INFO] src.gan.data_prep: Feature C4: 79226 unique values
2025-07-04 06:24:08,859 [WARNING] src.gan.data_prep: Feature C4 has 79226 categories! Reducing to top 10000
2025-07-04 06:24:08,861 [INFO] src.gan.data_prep: Top 10000 categories cover 82.95% of samples
2025-07-04 06:24:08,864 [INFO] src.gan.data_prep: Categorical feature C4: final_vocab_size=10001
2025-07-04 06:24:08,901 [INFO] src.gan.data_prep: Feature C5: 225 unique values
2025-07-04 06:24:08,901 [INFO] src.gan.data_prep: Categorical feature C5: final_vocab_size=226
2025-07-04 06:24:08,904 [INFO] src.gan.data_prep: Feature C6: 15 unique values
2025-07-04 06:24:08,904 [INFO] src.gan.data_prep: Categorical feature C6: final_vocab_size=16
2025-07-04 06:24:08,906 [INFO] src.gan.data_prep: Feature C7: 10358 unique values
2025-07-04 06:24:08,907 [WARNING] src.gan.data_prep: Feature C7 has 10358 categories! Reducing to top 10000
2025-07-04 06:24:08,909 [INFO] src.gan.data_prep: Top 10000 categories cover 99.93% of samples
2025-07-04 06:24:08,911 [INFO] src.gan.data_prep: Categorical feature C7: final_vocab_size=10001
2025-07-04 06:24:08,920 [INFO] src.gan.data_prep: Feature C8: 447 unique values
2025-07-04 06:24:08,920 [INFO] src.gan.data_prep: Categorical feature C8: final_vocab_size=448
2025-07-04 06:24:08,923 [INFO] src.gan.data_prep: Feature C9: 4 unique values
2025-07-04 06:24:08,924 [INFO] src.gan.data_prep: Categorical feature C9: final_vocab_size=5
2025-07-04 06:24:08,927 [INFO] src.gan.data_prep: Feature C10: 24212 unique values
2025-07-04 06:24:08,927 [WARNING] src.gan.data_prep: Feature C10 has 24212 categories! Reducing to top 10000
2025-07-04 06:24:08,929 [INFO] src.gan.data_prep: Top 10000 categories cover 96.03% of samples
2025-07-04 06:24:08,931 [INFO] src.gan.data_prep: Categorical feature C10: final_vocab_size=10001
2025-07-04 06:24:08,946 [INFO] src.gan.data_prep: Feature C11: 4612 unique values
2025-07-04 06:24:08,947 [INFO] src.gan.data_prep: Categorical feature C11: final_vocab_size=4613
2025-07-04 06:24:08,955 [INFO] src.gan.data_prep: Feature C12: 162318 unique values
2025-07-04 06:24:08,956 [WARNING] src.gan.data_prep: Feature C12 has 162318 categories! Reducing to top 10000
2025-07-04 06:24:08,958 [INFO] src.gan.data_prep: Top 10000 categories cover 66.46% of samples
2025-07-04 06:24:08,960 [INFO] src.gan.data_prep: Categorical feature C12: final_vocab_size=10001
2025-07-04 06:24:09,029 [INFO] src.gan.data_prep: Feature C13: 3072 unique values
2025-07-04 06:24:09,030 [INFO] src.gan.data_prep: Categorical feature C13: final_vocab_size=3073
2025-07-04 06:24:09,034 [INFO] src.gan.data_prep: Feature C14: 27 unique values
2025-07-04 06:24:09,034 [INFO] src.gan.data_prep: Categorical feature C14: final_vocab_size=28
2025-07-04 06:24:09,036 [INFO] src.gan.data_prep: Feature C15: 7961 unique values
2025-07-04 06:24:09,039 [INFO] src.gan.data_prep: Categorical feature C15: final_vocab_size=7962
2025-07-04 06:24:09,048 [INFO] src.gan.data_prep: Feature C16: 128551 unique values
2025-07-04 06:24:09,048 [WARNING] src.gan.data_prep: Feature C16 has 128551 categories! Reducing to top 10000
2025-07-04 06:24:09,051 [INFO] src.gan.data_prep: Top 10000 categories cover 73.14% of samples
2025-07-04 06:24:09,053 [INFO] src.gan.data_prep: Categorical feature C16: final_vocab_size=10001
2025-07-04 06:24:09,110 [INFO] src.gan.data_prep: Feature C17: 11 unique values
2025-07-04 06:24:09,110 [INFO] src.gan.data_prep: Categorical feature C17: final_vocab_size=12
2025-07-04 06:24:09,112 [INFO] src.gan.data_prep: Feature C18: 3555 unique values
2025-07-04 06:24:09,114 [INFO] src.gan.data_prep: Categorical feature C18: final_vocab_size=3556
2025-07-04 06:24:09,117 [INFO] src.gan.data_prep: Feature C19: 1698 unique values
2025-07-04 06:24:09,118 [INFO] src.gan.data_prep: Categorical feature C19: final_vocab_size=1699
2025-07-04 06:24:09,121 [INFO] src.gan.data_prep: Feature C20: 4 unique values
2025-07-04 06:24:09,121 [INFO] src.gan.data_prep: Categorical feature C20: final_vocab_size=5
2025-07-04 06:24:09,127 [INFO] src.gan.data_prep: Feature C21: 147885 unique values
2025-07-04 06:24:09,128 [WARNING] src.gan.data_prep: Feature C21 has 147885 categories! Reducing to top 10000
2025-07-04 06:24:09,130 [INFO] src.gan.data_prep: Top 10000 categories cover 69.30% of samples
2025-07-04 06:24:09,132 [INFO] src.gan.data_prep: Categorical feature C21: final_vocab_size=10001
2025-07-04 06:24:09,198 [INFO] src.gan.data_prep: Feature C22: 15 unique values
2025-07-04 06:24:09,198 [INFO] src.gan.data_prep: Categorical feature C22: final_vocab_size=16
2025-07-04 06:24:09,200 [INFO] src.gan.data_prep: Feature C23: 16 unique values
2025-07-04 06:24:09,200 [INFO] src.gan.data_prep: Categorical feature C23: final_vocab_size=17
2025-07-04 06:24:09,204 [INFO] src.gan.data_prep: Feature C24: 29660 unique values
2025-07-04 06:24:09,204 [WARNING] src.gan.data_prep: Feature C24 has 29660 categories! Reducing to top 10000
2025-07-04 06:24:09,206 [INFO] src.gan.data_prep: Top 10000 categories cover 95.38% of samples
2025-07-04 06:24:09,208 [INFO] src.gan.data_prep: Categorical feature C24: final_vocab_size=10001
2025-07-04 06:24:09,225 [INFO] src.gan.data_prep: Feature C25: 68 unique values
2025-07-04 06:24:09,225 [INFO] src.gan.data_prep: Categorical feature C25: final_vocab_size=69
2025-07-04 06:24:09,228 [INFO] src.gan.data_prep: Feature C26: 22328 unique values
2025-07-04 06:24:09,228 [WARNING] src.gan.data_prep: Feature C26 has 22328 categories! Reducing to top 10000
2025-07-04 06:24:09,230 [INFO] src.gan.data_prep: Top 10000 categories cover 97.38% of samples
2025-07-04 06:24:09,232 [INFO] src.gan.data_prep: Categorical feature C26: final_vocab_size=10001
2025-07-04 06:24:09,244 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 06:24:09,244 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 06:24:09,589 [INFO] src.gan.data_prep: Preprocessing info saved to /data/improved_gan_v2/data/gan_preprocessing_info.pkl
2025-07-04 06:24:09,590 [INFO] src.gan.data_prep: GAN preprocessing completed
2025-07-04 06:24:09,590 [INFO] root: Saving processed data cache to /data/improved_gan_v2/data/processed_Criteo_train_full_500000_10000.pkl
2025-07-04 06:24:09,890 [INFO] __main__: 最终数据集大小: 500000 样本
2025-07-04 06:24:09,890 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 06:24:09,890 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 06:24:09,890 [INFO] __main__: 特征信息: {'numeric_features': ['I1', 'I2', 'I3', 'I4', 'I5', 'I6', 'I7', 'I8', 'I9', 'I10', 'I11', 'I12', 'I13'], 'categorical_features': ['C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10', 'C11', 'C12', 'C13', 'C14', 'C15', 'C16', 'C17', 'C18', 'C19', 'C20', 'C21', 'C22', 'C23', 'C24', 'C25', 'C26'], 'vocab_sizes': [1032, 529, 10001, 10001, 226, 16, 10001, 448, 5, 10001, 4613, 10001, 3073, 28, 7962, 10001, 12, 3556, 1699, 5, 10001, 16, 17, 10001, 69, 10001], 'label_col': 'Label', 'dataset_name': 'Criteo', 'vocab_info_summary': {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}}
2025-07-04 06:24:10,078 [INFO] __main__: 创建数据加载器，共 976 批次
2025-07-04 06:24:10,098 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 06:24:10,099 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 06:24:10,099 [INFO] __main__: 创建改进的Generator和Discriminator模型
2025-07-04 06:24:10,200 [INFO] __main__: Generator参数量: 10,183,879
2025-07-04 06:24:10,200 [INFO] __main__: Discriminator参数量: 1,969,266
2025-07-04 06:24:10,201 [INFO] __main__: 参数比例 (G/D): 5.17
2025-07-04 06:24:10,201 [INFO] __main__: 学习率比例 (G/D): 2.00
2025-07-04 06:24:10,770 [INFO] __main__: 开始改进的训练循环
2025-07-04 06:24:10,771 [INFO] __main__: 开始第1/40个epoch (阶段: 0)
2025-07-04 06:24:11,415 [INFO] src.gan.trainer: Epoch 0, Batch 0/976, Stage 0, D_loss: 0.8887, G_loss: -1.8850, Num_std: 0.457257, Num_range: 3.6264, Real_score: 0.5008, Fake_score: 0.4852
2025-07-04 06:24:22,060 [INFO] src.gan.trainer: Epoch 0, Batch 100/976, Stage 0, D_loss: 0.5778, G_loss: -3.3015, Num_std: 0.089272, Num_range: 3.9533, Real_score: 0.7649, Fake_score: 0.2155
2025-07-04 06:24:32,781 [INFO] src.gan.trainer: Epoch 0, Batch 200/976, Stage 0, D_loss: 0.5545, G_loss: -3.3820, Num_std: 0.074141, Num_range: 3.9848, Real_score: 0.8158, Fake_score: 0.1730
2025-07-04 06:24:43,472 [INFO] src.gan.trainer: Epoch 0, Batch 300/976, Stage 0, D_loss: 0.5410, G_loss: -3.4384, Num_std: 0.099021, Num_range: 3.9933, Real_score: 0.8093, Fake_score: 0.1802
2025-07-04 06:24:54,056 [INFO] src.gan.trainer: Epoch 0, Batch 400/976, Stage 0, D_loss: 0.5421, G_loss: -3.4120, Num_std: 0.087256, Num_range: 3.9962, Real_score: 0.8128, Fake_score: 0.1738
2025-07-04 06:25:04,770 [INFO] src.gan.trainer: 应用温和的平衡调整...
2025-07-04 06:25:04,770 [INFO] src.gan.trainer: 温和调整后 - G学习率: 2.20e-04, D学习率: 9.50e-05, 温度: 3.000
2025-07-04 06:25:04,877 [INFO] src.gan.trainer: Epoch 0, Batch 500/976, Stage 0, D_loss: 0.5562, G_loss: -3.3591, Num_std: 0.092497, Num_range: 3.9971, Real_score: 0.8228, Fake_score: 0.1772
2025-07-04 06:25:15,516 [INFO] src.gan.trainer: Epoch 0, Batch 600/976, Stage 0, D_loss: 0.5391, G_loss: -3.4723, Num_std: 0.074477, Num_range: 3.9980, Real_score: 0.8304, Fake_score: 0.1809
2025-07-04 06:25:26,256 [INFO] src.gan.trainer: Epoch 0, Batch 700/976, Stage 0, D_loss: 0.5284, G_loss: -3.4669, Num_std: 0.093422, Num_range: 3.9986, Real_score: 0.8252, Fake_score: 0.1730
2025-07-04 06:25:37,022 [INFO] src.gan.trainer: Epoch 0, Batch 800/976, Stage 0, D_loss: 0.5020, G_loss: -3.4730, Num_std: 0.087334, Num_range: 3.9989, Real_score: 0.8231, Fake_score: 0.1717
2025-07-04 06:25:47,725 [INFO] src.gan.trainer: Epoch 0, Batch 900/976, Stage 0, D_loss: 0.5346, G_loss: -3.4630, Num_std: 0.096804, Num_range: 3.9991, Real_score: 0.8276, Fake_score: 0.1756
2025-07-04 06:25:55,798 [INFO] __main__: 第1个epoch完成. 指标: {'d_loss': 0.5541258001791651, 'd_adv_loss': 0.015618135572456905, 'd_ctr_loss': 0.5260963942855594, 'd_reg_loss': 0.15514088439450757, 'real_d_score': 0.8001761201952319, 'fake_d_score': 0.1920247363598373, 'g_loss': -3.39346893131733, 'g_adv_loss': 0.6591339367823521, 'g_feature_loss': 1.3111257120760795, 'g_diversity_loss': -0.9998355702970363, 'g_numeric_diversity_loss': -0.3897904481425822, 'fake_d_score_g': 0.19162049156720518, 'temperature': 3.000000000000042, 'numeric_std': 0.08796179705331872, 'numeric_range': 3.9845712369338417, 'training_stage': 0.0}
2025-07-04 06:25:55,798 [INFO] __main__: 开始第2/40个epoch (阶段: 0)
2025-07-04 06:25:56,153 [INFO] src.gan.trainer: Epoch 1, Batch 0/976, Stage 0, D_loss: 0.4937, G_loss: -3.4718, Num_std: 0.078544, Num_range: 3.9992, Real_score: 0.8282, Fake_score: 0.1707
2025-07-04 06:26:06,992 [INFO] src.gan.trainer: Epoch 1, Batch 100/976, Stage 0, D_loss: 0.4746, G_loss: -3.4402, Num_std: 0.053021, Num_range: 3.9994, Real_score: 0.8223, Fake_score: 0.1765
2025-07-04 06:26:17,682 [INFO] src.gan.trainer: Epoch 1, Batch 200/976, Stage 0, D_loss: 0.5344, G_loss: -3.4758, Num_std: 0.070905, Num_range: 3.9995, Real_score: 0.8308, Fake_score: 0.1731
2025-07-04 06:26:28,376 [INFO] src.gan.trainer: Epoch 1, Batch 300/976, Stage 0, D_loss: 0.4950, G_loss: -3.4737, Num_std: 0.092563, Num_range: 3.9996, Real_score: 0.8225, Fake_score: 0.1769
2025-07-04 06:26:38,993 [INFO] src.gan.trainer: Epoch 1, Batch 400/976, Stage 0, D_loss: 0.5095, G_loss: -3.4753, Num_std: 0.081281, Num_range: 3.9996, Real_score: 0.8264, Fake_score: 0.1724
2025-07-04 06:26:49,576 [INFO] src.gan.trainer: Epoch 1, Batch 500/976, Stage 0, D_loss: 0.4711, G_loss: -3.4800, Num_std: 0.064527, Num_range: 3.9997, Real_score: 0.8219, Fake_score: 0.1766
2025-07-04 06:27:00,360 [INFO] src.gan.trainer: Epoch 1, Batch 600/976, Stage 0, D_loss: 0.4809, G_loss: -3.4737, Num_std: 0.081206, Num_range: 3.9997, Real_score: 0.8270, Fake_score: 0.1700
2025-07-04 06:27:11,128 [INFO] src.gan.trainer: Epoch 1, Batch 700/976, Stage 0, D_loss: 0.5192, G_loss: -3.4734, Num_std: 0.064851, Num_range: 3.9998, Real_score: 0.8238, Fake_score: 0.1761
2025-07-04 06:27:21,858 [INFO] src.gan.trainer: Epoch 1, Batch 800/976, Stage 0, D_loss: 0.4998, G_loss: -3.4736, Num_std: 0.057940, Num_range: 3.9998, Real_score: 0.8231, Fake_score: 0.1725
2025-07-04 06:27:32,548 [INFO] src.gan.trainer: Epoch 1, Batch 900/976, Stage 0, D_loss: 0.5153, G_loss: -3.4699, Num_std: 0.041619, Num_range: 3.9998, Real_score: 0.8278, Fake_score: 0.1740
2025-07-04 06:27:40,608 [INFO] __main__: 第2个epoch完成. 指标: {'d_loss': 0.5006778560212407, 'd_adv_loss': 0.007667289614547653, 'd_ctr_loss': 0.47887293658539914, 'd_reg_loss': 0.17672036641628544, 'real_d_score': 0.8254521100736052, 'fake_d_score': 0.17391264162285888, 'g_loss': -3.463152835053993, 'g_adv_loss': 0.6844263674050084, 'g_feature_loss': 1.3007268412802055, 'g_diversity_loss': -0.999912977127024, 'g_numeric_diversity_loss': -0.40158782092487993, 'fake_d_score_g': 0.1738857328128378, 'temperature': 2.9984999999999435, 'numeric_std': 0.07457297803590396, 'numeric_range': 3.999651503611782, 'training_stage': 0.0}
2025-07-04 06:27:40,608 [INFO] __main__: 开始第3/40个epoch (阶段: 0)
2025-07-04 06:27:40,832 [INFO] src.gan.trainer: Epoch 2, Batch 0/976, Stage 0, D_loss: 0.5071, G_loss: -3.4726, Num_std: 0.071696, Num_range: 3.9998, Real_score: 0.8280, Fake_score: 0.1787
2025-07-04 06:27:51,606 [INFO] src.gan.trainer: Epoch 2, Batch 100/976, Stage 0, D_loss: 0.4981, G_loss: -3.4770, Num_std: 0.063582, Num_range: 3.9999, Real_score: 0.8228, Fake_score: 0.1759
2025-07-04 06:28:02,477 [INFO] src.gan.trainer: Epoch 2, Batch 200/976, Stage 0, D_loss: 0.4876, G_loss: -3.4781, Num_std: 0.065836, Num_range: 3.9999, Real_score: 0.8269, Fake_score: 0.1761
2025-07-04 06:28:13,239 [INFO] src.gan.trainer: Epoch 2, Batch 300/976, Stage 0, D_loss: 0.4694, G_loss: -3.4736, Num_std: 0.057460, Num_range: 3.9999, Real_score: 0.8279, Fake_score: 0.1746
2025-07-04 06:28:23,948 [INFO] src.gan.trainer: Epoch 2, Batch 400/976, Stage 0, D_loss: 0.4754, G_loss: -3.4755, Num_std: 0.067081, Num_range: 3.9999, Real_score: 0.8248, Fake_score: 0.1750
2025-07-04 06:28:34,647 [INFO] src.gan.trainer: Epoch 2, Batch 500/976, Stage 0, D_loss: 0.5596, G_loss: -3.4707, Num_std: 0.066680, Num_range: 3.9999, Real_score: 0.8254, Fake_score: 0.1762
2025-07-04 06:28:45,294 [INFO] src.gan.trainer: Epoch 2, Batch 600/976, Stage 0, D_loss: 0.4780, G_loss: -3.4755, Num_std: 0.068845, Num_range: 3.9999, Real_score: 0.8265, Fake_score: 0.1742
2025-07-04 06:28:55,936 [INFO] src.gan.trainer: Epoch 2, Batch 700/976, Stage 0, D_loss: 0.4793, G_loss: -3.4594, Num_std: 0.052554, Num_range: 3.9999, Real_score: 0.8273, Fake_score: 0.1749
2025-07-04 06:29:06,693 [INFO] src.gan.trainer: Epoch 2, Batch 800/976, Stage 0, D_loss: 0.4857, G_loss: -3.4644, Num_std: 0.064880, Num_range: 3.9999, Real_score: 0.8248, Fake_score: 0.1728
2025-07-04 06:29:17,412 [INFO] src.gan.trainer: Epoch 2, Batch 900/976, Stage 0, D_loss: 0.4944, G_loss: -3.4649, Num_std: 0.086869, Num_range: 3.9999, Real_score: 0.8243, Fake_score: 0.1704
2025-07-04 06:29:25,548 [INFO] __main__: 第3个epoch完成. 指标: {'d_loss': 0.48336505074603675, 'd_adv_loss': 0.00733939309739966, 'd_ctr_loss': 0.46196065064458597, 'd_reg_loss': 0.17581258523354268, 'real_d_score': 0.8253473193674787, 'fake_d_score': 0.1743843734997214, 'g_loss': -3.455993567394921, 'g_adv_loss': 0.6832050045066685, 'g_feature_loss': 1.3994410271893787, 'g_diversity_loss': -0.9977722429044401, 'g_numeric_diversity_loss': -0.4014247976182421, 'fake_d_score_g': 0.17440921030785952, 'temperature': 2.9970007499999856, 'numeric_std': 0.07168778944881776, 'numeric_range': 3.999913577539999, 'training_stage': 0.0}
2025-07-04 06:29:25,549 [INFO] __main__: 在第3个epoch评估改进的GAN
2025-07-04 06:29:25,573 [INFO] __main__: 改进GAN评估指标:
2025-07-04 06:29:25,573 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 06:29:25,573 [INFO] __main__:   特征平均标准差: 0.000017
2025-07-04 06:29:25,573 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 06:29:25,573 [INFO] __main__: 训练质量: 需要改进
2025-07-04 06:29:25,575 [INFO] __main__: 新的最佳数值特征质量: 0.400
2025-07-04 06:29:25,766 [INFO] src.gan.trainer: 检查点已保存到 /data/improved_gan_v2/improved_best_model.pt
2025-07-04 06:29:25,767 [INFO] __main__: 保存最佳模型，数值质量评分: 0.400
2025-07-04 06:29:25,952 [INFO] src.gan.trainer: 检查点已保存到 /data/improved_gan_v2/improved_checkpoint_epoch_3.pt
2025-07-04 06:29:25,953 [INFO] __main__: 开始第4/40个epoch (阶段: 0)
2025-07-04 06:29:26,180 [INFO] src.gan.trainer: Epoch 3, Batch 0/976, Stage 0, D_loss: 0.4504, G_loss: -3.4692, Num_std: 0.071133, Num_range: 4.0000, Real_score: 0.8223, Fake_score: 0.1747
2025-07-04 06:29:33,894 [INFO] src.gan.trainer: 应用温和的平衡调整...
2025-07-04 06:29:33,895 [INFO] src.gan.trainer: 温和调整后 - G学习率: 2.42e-04, D学习率: 9.02e-05, 温度: 3.000
2025-07-04 06:29:37,034 [INFO] src.gan.trainer: Epoch 3, Batch 100/976, Stage 0, D_loss: 0.4403, G_loss: -3.4641, Num_std: 0.069400, Num_range: 4.0000, Real_score: 0.8238, Fake_score: 0.1745
2025-07-04 06:29:47,800 [INFO] src.gan.trainer: Epoch 3, Batch 200/976, Stage 0, D_loss: 0.4792, G_loss: -3.4533, Num_std: 0.069486, Num_range: 4.0000, Real_score: 0.8266, Fake_score: 0.1761
2025-07-04 06:29:58,670 [INFO] src.gan.trainer: Epoch 3, Batch 300/976, Stage 0, D_loss: 0.5158, G_loss: -3.3825, Num_std: 0.071819, Num_range: 4.0000, Real_score: 0.8220, Fake_score: 0.1749
2025-07-04 06:30:09,393 [INFO] src.gan.trainer: Epoch 3, Batch 400/976, Stage 0, D_loss: 0.4992, G_loss: -3.3957, Num_std: 0.066763, Num_range: 4.0000, Real_score: 0.8239, Fake_score: 0.1743
2025-07-04 06:30:20,104 [INFO] src.gan.trainer: Epoch 3, Batch 500/976, Stage 0, D_loss: 0.4623, G_loss: -3.4113, Num_std: 0.070169, Num_range: 4.0000, Real_score: 0.8238, Fake_score: 0.1724
2025-07-04 06:30:30,789 [INFO] src.gan.trainer: Epoch 3, Batch 600/976, Stage 0, D_loss: 0.4990, G_loss: -3.4345, Num_std: 0.066009, Num_range: 4.0000, Real_score: 0.8202, Fake_score: 0.1789
2025-07-04 06:30:41,473 [INFO] src.gan.trainer: Epoch 3, Batch 700/976, Stage 0, D_loss: 0.4675, G_loss: -3.4379, Num_std: 0.063064, Num_range: 4.0000, Real_score: 0.8218, Fake_score: 0.1698
2025-07-04 06:30:52,185 [INFO] src.gan.trainer: Epoch 3, Batch 800/976, Stage 0, D_loss: 0.4733, G_loss: -3.4359, Num_std: 0.075493, Num_range: 4.0000, Real_score: 0.8221, Fake_score: 0.1756
2025-07-04 06:31:02,968 [INFO] src.gan.trainer: Epoch 3, Batch 900/976, Stage 0, D_loss: 0.4365, G_loss: -3.4411, Num_std: 0.053769, Num_range: 4.0000, Real_score: 0.8276, Fake_score: 0.1759
2025-07-04 06:31:10,971 [INFO] __main__: 第4个epoch完成. 指标: {'d_loss': 0.4714419633211181, 'd_adv_loss': 0.007602282435266825, 'd_ctr_loss': 0.4497954989554456, 'd_reg_loss': 0.17555227249738592, 'real_d_score': 0.8245923390276115, 'fake_d_score': 0.17415501841450817, 'g_loss': -3.411924332441368, 'g_adv_loss': 0.6850318407609336, 'g_feature_loss': 1.8598983392852262, 'g_diversity_loss': -0.9819633121617486, 'g_numeric_diversity_loss': -0.4009984722666319, 'fake_d_score_g': 0.17368132621813276, 'temperature': 2.99966819874287, 'numeric_std': 0.07162278481091111, 'numeric_range': 3.999967307707325, 'training_stage': 0.0}
2025-07-04 06:31:10,971 [INFO] __main__: 开始第5/40个epoch (阶段: 0)
2025-07-04 06:31:11,201 [INFO] src.gan.trainer: Epoch 4, Batch 0/976, Stage 0, D_loss: 0.4221, G_loss: -3.4390, Num_std: 0.078700, Num_range: 4.0000, Real_score: 0.8222, Fake_score: 0.1753
2025-07-04 06:31:21,827 [INFO] src.gan.trainer: Epoch 4, Batch 100/976, Stage 0, D_loss: 0.4489, G_loss: -3.4427, Num_std: 0.042786, Num_range: 4.0000, Real_score: 0.8255, Fake_score: 0.1738
2025-07-04 06:31:32,495 [INFO] src.gan.trainer: Epoch 4, Batch 200/976, Stage 0, D_loss: 0.4456, G_loss: -3.4419, Num_std: 0.068325, Num_range: 4.0000, Real_score: 0.8272, Fake_score: 0.1710
2025-07-04 06:31:43,127 [INFO] src.gan.trainer: Epoch 4, Batch 300/976, Stage 0, D_loss: 0.4896, G_loss: -3.4376, Num_std: 0.078240, Num_range: 4.0000, Real_score: 0.8274, Fake_score: 0.1750
2025-07-04 06:31:53,841 [INFO] src.gan.trainer: Epoch 4, Batch 400/976, Stage 0, D_loss: 0.4917, G_loss: -3.4352, Num_std: 0.061326, Num_range: 4.0000, Real_score: 0.8286, Fake_score: 0.1749
2025-07-04 06:32:04,702 [INFO] src.gan.trainer: Epoch 4, Batch 500/976, Stage 0, D_loss: 0.4487, G_loss: -3.4294, Num_std: 0.078186, Num_range: 4.0000, Real_score: 0.8242, Fake_score: 0.1758
2025-07-04 06:32:15,428 [INFO] src.gan.trainer: Epoch 4, Batch 600/976, Stage 0, D_loss: 0.4315, G_loss: -3.4302, Num_std: 0.081484, Num_range: 4.0000, Real_score: 0.8258, Fake_score: 0.1758
2025-07-04 06:32:26,253 [INFO] src.gan.trainer: Epoch 4, Batch 700/976, Stage 0, D_loss: 0.4321, G_loss: -3.4370, Num_std: 0.064664, Num_range: 4.0000, Real_score: 0.8289, Fake_score: 0.1740
2025-07-04 06:32:37,159 [INFO] src.gan.trainer: Epoch 4, Batch 800/976, Stage 0, D_loss: 0.4779, G_loss: -3.4346, Num_std: 0.079468, Num_range: 4.0000, Real_score: 0.8267, Fake_score: 0.1714
2025-07-04 06:32:48,005 [INFO] src.gan.trainer: Epoch 4, Batch 900/976, Stage 0, D_loss: 0.4664, G_loss: -3.4360, Num_std: 0.062851, Num_range: 4.0000, Real_score: 0.8236, Fake_score: 0.1755
2025-07-04 06:32:56,194 [INFO] __main__: 第5个epoch完成. 指标: {'d_loss': 0.46220922631929157, 'd_adv_loss': 0.007288536852698954, 'd_ctr_loss': 0.44092500356377073, 'd_reg_loss': 0.17494607411447127, 'real_d_score': 0.825119585531657, 'fake_d_score': 0.17497151271730213, 'g_loss': -3.431346599867594, 'g_adv_loss': 0.6824579282556889, 'g_feature_loss': 2.039260054281984, 'g_diversity_loss': -0.997015721210445, 'g_numeric_diversity_loss': -0.4023439763266537, 'fake_d_score_g': 0.1748844755385987, 'temperature': 2.9984999999999435, 'numeric_std': 0.07118060777126815, 'numeric_range': 3.999984204646978, 'training_stage': 0.0}
2025-07-04 06:32:56,195 [INFO] __main__: 开始第6/40个epoch (阶段: 0)
2025-07-04 06:32:56,410 [INFO] src.gan.trainer: Epoch 5, Batch 0/976, Stage 0, D_loss: 0.4376, G_loss: -3.4282, Num_std: 0.092614, Num_range: 4.0000, Real_score: 0.8235, Fake_score: 0.1733
2025-07-04 06:33:07,195 [INFO] src.gan.trainer: Epoch 5, Batch 100/976, Stage 0, D_loss: 0.4313, G_loss: -3.3780, Num_std: 0.074964, Num_range: 4.0000, Real_score: 0.8235, Fake_score: 0.1752
2025-07-04 06:33:17,862 [INFO] src.gan.trainer: Epoch 5, Batch 200/976, Stage 0, D_loss: 0.4779, G_loss: -3.3974, Num_std: 0.090694, Num_range: 4.0000, Real_score: 0.8275, Fake_score: 0.1752
2025-07-04 06:33:28,616 [INFO] src.gan.trainer: Epoch 5, Batch 300/976, Stage 0, D_loss: 0.4905, G_loss: -1.4213, Num_std: 0.040318, Num_range: 4.0000, Real_score: 0.8250, Fake_score: 0.1742
2025-07-04 06:33:39,278 [INFO] src.gan.trainer: Epoch 5, Batch 400/976, Stage 0, D_loss: 0.3774, G_loss: -3.4353, Num_std: 0.060873, Num_range: 4.0000, Real_score: 0.8259, Fake_score: 0.1722
2025-07-04 06:33:49,914 [INFO] src.gan.trainer: Epoch 5, Batch 500/976, Stage 0, D_loss: 0.4537, G_loss: -3.4390, Num_std: 0.062195, Num_range: 4.0000, Real_score: 0.8253, Fake_score: 0.1743
2025-07-04 06:34:00,572 [INFO] src.gan.trainer: Epoch 5, Batch 600/976, Stage 0, D_loss: 0.4543, G_loss: -3.4349, Num_std: 0.045006, Num_range: 4.0000, Real_score: 0.8248, Fake_score: 0.1743
2025-07-04 06:34:11,286 [INFO] src.gan.trainer: Epoch 5, Batch 700/976, Stage 0, D_loss: 0.4619, G_loss: -3.4298, Num_std: 0.071512, Num_range: 4.0000, Real_score: 0.8271, Fake_score: 0.1721
2025-07-04 06:34:22,006 [INFO] src.gan.trainer: Epoch 5, Batch 800/976, Stage 0, D_loss: 0.4795, G_loss: -3.4072, Num_std: 0.106522, Num_range: 4.0000, Real_score: 0.8242, Fake_score: 0.1784
2025-07-04 06:34:32,743 [INFO] src.gan.trainer: Epoch 5, Batch 900/976, Stage 0, D_loss: 0.4278, G_loss: -3.4180, Num_std: 0.081767, Num_range: 4.0000, Real_score: 0.8296, Fake_score: 0.1771
2025-07-04 06:34:40,836 [INFO] __main__: 第6个epoch完成. 指标: {'d_loss': 0.45399865184406796, 'd_adv_loss': 0.007148434260581453, 'd_ctr_loss': 0.4328588988387682, 'd_reg_loss': 0.17489150094753902, 'real_d_score': 0.8250023088738561, 'fake_d_score': 0.17477059084922067, 'g_loss': -3.4153027912510217, 'g_adv_loss': 0.6831088614818009, 'g_feature_loss': 2.1274001170865815, 'g_diversity_loss': -0.9856846877602781, 'g_numeric_diversity_loss': -0.4023871212953425, 'fake_d_score_g': 0.17442204811625525, 'temperature': 2.9970007499999856, 'numeric_std': 0.07152474022907103, 'numeric_range': 3.999990735630515, 'training_stage': 0.0}
2025-07-04 06:34:40,836 [INFO] __main__: 在第6个epoch评估改进的GAN
2025-07-04 06:34:40,853 [INFO] __main__: 改进GAN评估指标:
2025-07-04 06:34:40,854 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 06:34:40,854 [INFO] __main__:   特征平均标准差: 0.000006
2025-07-04 06:34:40,854 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 06:34:40,854 [INFO] __main__: 训练质量: 需要改进
2025-07-04 06:34:41,046 [INFO] src.gan.trainer: 检查点已保存到 /data/improved_gan_v2/improved_checkpoint_epoch_6.pt
2025-07-04 06:34:41,046 [INFO] __main__: 开始第7/40个epoch (阶段: 0)
2025-07-04 06:34:41,277 [INFO] src.gan.trainer: Epoch 6, Batch 0/976, Stage 0, D_loss: 0.4725, G_loss: -3.4141, Num_std: 0.065643, Num_range: 4.0000, Real_score: 0.8261, Fake_score: 0.1711
2025-07-04 06:34:52,089 [INFO] src.gan.trainer: Epoch 6, Batch 100/976, Stage 0, D_loss: 0.4085, G_loss: -3.4204, Num_std: 0.057819, Num_range: 4.0000, Real_score: 0.8252, Fake_score: 0.1751
