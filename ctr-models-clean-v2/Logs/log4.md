2025-07-04 06:43:31,347 [INFO] __main__: 开始改进的GAN训练 - 更好的平衡策略
2025-07-04 06:43:31,347 [INFO] __main__: 参数: {'dataset_name': 'Criteo', 'dataset_path': '/data/Criteo_x4', 'output_dir': '/data/balanced_gan', 'noise_dim': 128, 'embedding_dim': 16, 'epochs': 30, 'batch_size': 512, 'generator_lr': 0.0003, 'discriminator_lr': 5e-05, 'initial_temperature': 3.5, 'min_temperature': 2.0, 'temperature_decay': 0.999, 'max_vocab_size': 10000, 'sample_strategy': 'full', 'max_samples': 500000, 'max_grad_norm': 1.0, 'log_interval': 100, 'save_interval': 2, 'eval_interval': 2, 'seed': 2024, 'num_workers': 2, 'resume': None, 'debug': False}
2025-07-04 06:43:31,870 [INFO] __main__: 加载 Criteo 数据集从 /data/Criteo_x4
2025-07-04 06:43:31,870 [INFO] src.gan.data_prep: Max vocab size per feature: 10000
2025-07-04 06:43:35,807 [INFO] root: Loaded 500000 samples from /data/Criteo_x4/train.csv
2025-07-04 06:43:35,808 [INFO] root: CTR rate in sampled data: 0.2517
2025-07-04 06:43:35,809 [INFO] src.gan.data_prep: Starting GAN preprocessing for 500000 samples
2025-07-04 06:43:36,238 [INFO] src.gan.data_prep: Numeric feature I1: min=0.0000, max=780.0000
2025-07-04 06:43:36,241 [INFO] src.gan.data_prep: Numeric feature I2: min=-3.0000, max=12868.0000
2025-07-04 06:43:36,244 [INFO] src.gan.data_prep: Numeric feature I3: min=0.0000, max=65535.0000
2025-07-04 06:43:36,246 [INFO] src.gan.data_prep: Numeric feature I4: min=0.0000, max=406.0000
2025-07-04 06:43:36,249 [INFO] src.gan.data_prep: Numeric feature I5: min=0.0000, max=2539061.0000
2025-07-04 06:43:36,252 [INFO] src.gan.data_prep: Numeric feature I6: min=0.0000, max=63909.0000
2025-07-04 06:43:36,254 [INFO] src.gan.data_prep: Numeric feature I7: min=0.0000, max=26297.0000
2025-07-04 06:43:36,257 [INFO] src.gan.data_prep: Numeric feature I8: min=0.0000, max=3949.0000
2025-07-04 06:43:36,260 [INFO] src.gan.data_prep: Numeric feature I9: min=0.0000, max=8430.0000
2025-07-04 06:43:36,262 [INFO] src.gan.data_prep: Numeric feature I10: min=0.0000, max=8.0000
2025-07-04 06:43:36,265 [INFO] src.gan.data_prep: Numeric feature I11: min=0.0000, max=125.0000
2025-07-04 06:43:36,268 [INFO] src.gan.data_prep: Numeric feature I12: min=0.0000, max=693.0000
2025-07-04 06:43:36,270 [INFO] src.gan.data_prep: Numeric feature I13: min=0.0000, max=3890.0000
2025-07-04 06:43:36,274 [INFO] src.gan.data_prep: Feature C1: 1031 unique values
2025-07-04 06:43:36,274 [INFO] src.gan.data_prep: Categorical feature C1: final_vocab_size=1032
2025-07-04 06:43:36,277 [INFO] src.gan.data_prep: Feature C2: 528 unique values
2025-07-04 06:43:36,277 [INFO] src.gan.data_prep: Categorical feature C2: final_vocab_size=529
2025-07-04 06:43:36,284 [INFO] src.gan.data_prep: Feature C3: 184172 unique values
2025-07-04 06:43:36,285 [WARNING] src.gan.data_prep: Feature C3 has 184172 categories! Reducing to top 10000
2025-07-04 06:43:36,287 [INFO] src.gan.data_prep: Top 10000 categories cover 62.13% of samples
2025-07-04 06:43:36,289 [INFO] src.gan.data_prep: Categorical feature C3: final_vocab_size=10001
2025-07-04 06:43:36,370 [INFO] src.gan.data_prep: Feature C4: 79226 unique values
2025-07-04 06:43:36,371 [WARNING] src.gan.data_prep: Feature C4 has 79226 categories! Reducing to top 10000
2025-07-04 06:43:36,373 [INFO] src.gan.data_prep: Top 10000 categories cover 82.95% of samples
2025-07-04 06:43:36,375 [INFO] src.gan.data_prep: Categorical feature C4: final_vocab_size=10001
2025-07-04 06:43:36,412 [INFO] src.gan.data_prep: Feature C5: 225 unique values
2025-07-04 06:43:36,412 [INFO] src.gan.data_prep: Categorical feature C5: final_vocab_size=226
2025-07-04 06:43:36,415 [INFO] src.gan.data_prep: Feature C6: 15 unique values
2025-07-04 06:43:36,415 [INFO] src.gan.data_prep: Categorical feature C6: final_vocab_size=16
2025-07-04 06:43:36,418 [INFO] src.gan.data_prep: Feature C7: 10358 unique values
2025-07-04 06:43:36,418 [WARNING] src.gan.data_prep: Feature C7 has 10358 categories! Reducing to top 10000
2025-07-04 06:43:36,420 [INFO] src.gan.data_prep: Top 10000 categories cover 99.93% of samples
2025-07-04 06:43:36,422 [INFO] src.gan.data_prep: Categorical feature C7: final_vocab_size=10001
2025-07-04 06:43:36,430 [INFO] src.gan.data_prep: Feature C8: 447 unique values
2025-07-04 06:43:36,431 [INFO] src.gan.data_prep: Categorical feature C8: final_vocab_size=448
2025-07-04 06:43:36,434 [INFO] src.gan.data_prep: Feature C9: 4 unique values
2025-07-04 06:43:36,434 [INFO] src.gan.data_prep: Categorical feature C9: final_vocab_size=5
2025-07-04 06:43:36,437 [INFO] src.gan.data_prep: Feature C10: 24212 unique values
2025-07-04 06:43:36,437 [WARNING] src.gan.data_prep: Feature C10 has 24212 categories! Reducing to top 10000
2025-07-04 06:43:36,439 [INFO] src.gan.data_prep: Top 10000 categories cover 96.03% of samples
2025-07-04 06:43:36,441 [INFO] src.gan.data_prep: Categorical feature C10: final_vocab_size=10001
2025-07-04 06:43:36,456 [INFO] src.gan.data_prep: Feature C11: 4612 unique values
2025-07-04 06:43:36,457 [INFO] src.gan.data_prep: Categorical feature C11: final_vocab_size=4613
2025-07-04 06:43:36,466 [INFO] src.gan.data_prep: Feature C12: 162318 unique values
2025-07-04 06:43:36,466 [WARNING] src.gan.data_prep: Feature C12 has 162318 categories! Reducing to top 10000
2025-07-04 06:43:36,468 [INFO] src.gan.data_prep: Top 10000 categories cover 66.46% of samples
2025-07-04 06:43:36,470 [INFO] src.gan.data_prep: Categorical feature C12: final_vocab_size=10001
2025-07-04 06:43:36,539 [INFO] src.gan.data_prep: Feature C13: 3072 unique values
2025-07-04 06:43:36,541 [INFO] src.gan.data_prep: Categorical feature C13: final_vocab_size=3073
2025-07-04 06:43:36,544 [INFO] src.gan.data_prep: Feature C14: 27 unique values
2025-07-04 06:43:36,544 [INFO] src.gan.data_prep: Categorical feature C14: final_vocab_size=28
2025-07-04 06:43:36,547 [INFO] src.gan.data_prep: Feature C15: 7961 unique values
2025-07-04 06:43:36,550 [INFO] src.gan.data_prep: Categorical feature C15: final_vocab_size=7962
2025-07-04 06:43:36,558 [INFO] src.gan.data_prep: Feature C16: 128551 unique values
2025-07-04 06:43:36,559 [WARNING] src.gan.data_prep: Feature C16 has 128551 categories! Reducing to top 10000
2025-07-04 06:43:36,561 [INFO] src.gan.data_prep: Top 10000 categories cover 73.14% of samples
2025-07-04 06:43:36,563 [INFO] src.gan.data_prep: Categorical feature C16: final_vocab_size=10001
2025-07-04 06:43:36,618 [INFO] src.gan.data_prep: Feature C17: 11 unique values
2025-07-04 06:43:36,619 [INFO] src.gan.data_prep: Categorical feature C17: final_vocab_size=12
2025-07-04 06:43:36,621 [INFO] src.gan.data_prep: Feature C18: 3555 unique values
2025-07-04 06:43:36,622 [INFO] src.gan.data_prep: Categorical feature C18: final_vocab_size=3556
2025-07-04 06:43:36,626 [INFO] src.gan.data_prep: Feature C19: 1698 unique values
2025-07-04 06:43:36,626 [INFO] src.gan.data_prep: Categorical feature C19: final_vocab_size=1699
2025-07-04 06:43:36,629 [INFO] src.gan.data_prep: Feature C20: 4 unique values
2025-07-04 06:43:36,630 [INFO] src.gan.data_prep: Categorical feature C20: final_vocab_size=5
2025-07-04 06:43:36,635 [INFO] src.gan.data_prep: Feature C21: 147885 unique values
2025-07-04 06:43:36,636 [WARNING] src.gan.data_prep: Feature C21 has 147885 categories! Reducing to top 10000
2025-07-04 06:43:36,638 [INFO] src.gan.data_prep: Top 10000 categories cover 69.30% of samples
2025-07-04 06:43:36,640 [INFO] src.gan.data_prep: Categorical feature C21: final_vocab_size=10001
2025-07-04 06:43:36,705 [INFO] src.gan.data_prep: Feature C22: 15 unique values
2025-07-04 06:43:36,705 [INFO] src.gan.data_prep: Categorical feature C22: final_vocab_size=16
2025-07-04 06:43:36,707 [INFO] src.gan.data_prep: Feature C23: 16 unique values
2025-07-04 06:43:36,707 [INFO] src.gan.data_prep: Categorical feature C23: final_vocab_size=17
2025-07-04 06:43:36,710 [INFO] src.gan.data_prep: Feature C24: 29660 unique values
2025-07-04 06:43:36,711 [WARNING] src.gan.data_prep: Feature C24 has 29660 categories! Reducing to top 10000
2025-07-04 06:43:36,713 [INFO] src.gan.data_prep: Top 10000 categories cover 95.38% of samples
2025-07-04 06:43:36,715 [INFO] src.gan.data_prep: Categorical feature C24: final_vocab_size=10001
2025-07-04 06:43:36,731 [INFO] src.gan.data_prep: Feature C25: 68 unique values
2025-07-04 06:43:36,731 [INFO] src.gan.data_prep: Categorical feature C25: final_vocab_size=69
2025-07-04 06:43:36,734 [INFO] src.gan.data_prep: Feature C26: 22328 unique values
2025-07-04 06:43:36,734 [WARNING] src.gan.data_prep: Feature C26 has 22328 categories! Reducing to top 10000
2025-07-04 06:43:36,736 [INFO] src.gan.data_prep: Top 10000 categories cover 97.38% of samples
2025-07-04 06:43:36,738 [INFO] src.gan.data_prep: Categorical feature C26: final_vocab_size=10001
2025-07-04 06:43:36,750 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 06:43:36,751 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 06:43:37,095 [INFO] src.gan.data_prep: Preprocessing info saved to /data/balanced_gan/data/gan_preprocessing_info.pkl
2025-07-04 06:43:37,096 [INFO] src.gan.data_prep: GAN preprocessing completed
2025-07-04 06:43:37,096 [INFO] root: Saving processed data cache to /data/balanced_gan/data/processed_Criteo_train_full_500000_10000.pkl
2025-07-04 06:43:37,391 [INFO] __main__: 最终数据集大小: 500000 样本
2025-07-04 06:43:37,392 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 06:43:37,392 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 06:43:37,392 [INFO] __main__: 特征信息: {'numeric_features': ['I1', 'I2', 'I3', 'I4', 'I5', 'I6', 'I7', 'I8', 'I9', 'I10', 'I11', 'I12', 'I13'], 'categorical_features': ['C1', 'C2', 'C3', 'C4', 'C5', 'C6', 'C7', 'C8', 'C9', 'C10', 'C11', 'C12', 'C13', 'C14', 'C15', 'C16', 'C17', 'C18', 'C19', 'C20', 'C21', 'C22', 'C23', 'C24', 'C25', 'C26'], 'vocab_sizes': [1032, 529, 10001, 10001, 226, 16, 10001, 448, 5, 10001, 4613, 10001, 3073, 28, 7962, 10001, 12, 3556, 1699, 5, 10001, 16, 17, 10001, 69, 10001], 'label_col': 'Label', 'dataset_name': 'Criteo', 'vocab_info_summary': {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}}
2025-07-04 06:43:37,518 [INFO] __main__: 创建数据加载器，共 976 批次
2025-07-04 06:43:37,539 [INFO] src.gan.data_prep: Vocab sizes: {'C1': 1032, 'C2': 529, 'C3': 10001, 'C4': 10001, 'C5': 226, 'C6': 16, 'C7': 10001, 'C8': 448, 'C9': 5, 'C10': 10001, 'C11': 4613, 'C12': 10001, 'C13': 3073, 'C14': 28, 'C15': 7962, 'C16': 10001, 'C17': 12, 'C18': 3556, 'C19': 1699, 'C20': 5, 'C21': 10001, 'C22': 16, 'C23': 17, 'C24': 10001, 'C25': 69, 'C26': 10001}
2025-07-04 06:43:37,539 [INFO] src.gan.data_prep: Estimated embedding parameters: 906,520
2025-07-04 06:43:37,539 [INFO] __main__: 创建改进的Generator和Discriminator模型
2025-07-04 06:43:37,638 [INFO] __main__: Generator参数量: 10,183,879
2025-07-04 06:43:37,638 [INFO] __main__: Discriminator参数量: 1,969,266
2025-07-04 06:43:37,638 [INFO] __main__: 参数比例 (G/D): 5.17
2025-07-04 06:43:37,638 [INFO] __main__: 学习率比例 (G/D): 6.00
2025-07-04 06:43:38,209 [INFO] __main__: 开始改进的训练循环
2025-07-04 06:43:38,209 [INFO] __main__: 开始第1/30个epoch (阶段: 0)
2025-07-04 06:43:38,884 [INFO] src.gan.trainer: Epoch 0, Batch 0/976, Stage 0, D_loss: 0.8883, G_loss: -2.1174, Num_std: 0.399869, Num_range: 3.6739, Real_score: 0.5008, Fake_score: 0.4852
2025-07-04 06:43:53,922 [INFO] src.gan.trainer: Epoch 0, Batch 100/976, Stage 0, D_loss: 0.6475, G_loss: -3.6166, Num_std: 0.074571, Num_range: 3.9883, Real_score: 0.6351, Fake_score: 0.3306
2025-07-04 06:44:09,102 [INFO] src.gan.trainer: Epoch 0, Batch 200/976, Stage 0, D_loss: 0.6178, G_loss: -3.6792, Num_std: 0.089358, Num_range: 3.9977, Real_score: 0.6917, Fake_score: 0.3052
2025-07-04 06:44:24,343 [INFO] src.gan.trainer: Epoch 0, Batch 300/976, Stage 0, D_loss: 0.6391, G_loss: -3.6865, Num_std: 0.081332, Num_range: 3.9987, Real_score: 0.6941, Fake_score: 0.3045
2025-07-04 06:44:39,476 [INFO] src.gan.trainer: Epoch 0, Batch 400/976, Stage 0, D_loss: 0.6465, G_loss: -3.6875, Num_std: 0.072291, Num_range: 3.9993, Real_score: 0.6930, Fake_score: 0.3021
2025-07-04 06:44:54,608 [INFO] src.gan.trainer: Epoch 0, Batch 500/976, Stage 0, D_loss: 0.6150, G_loss: -3.6931, Num_std: 0.079237, Num_range: 3.9995, Real_score: 0.6924, Fake_score: 0.3095
2025-07-04 06:45:09,874 [INFO] src.gan.trainer: Epoch 0, Batch 600/976, Stage 0, D_loss: 0.6058, G_loss: -3.6865, Num_std: 0.066755, Num_range: 3.9996, Real_score: 0.6946, Fake_score: 0.3027
2025-07-04 06:45:25,197 [INFO] src.gan.trainer: Epoch 0, Batch 700/976, Stage 0, D_loss: 0.5792, G_loss: -3.6777, Num_std: 0.101438, Num_range: 3.9997, Real_score: 0.6939, Fake_score: 0.3035
2025-07-04 06:45:40,408 [INFO] src.gan.trainer: Epoch 0, Batch 800/976, Stage 0, D_loss: 0.5690, G_loss: -3.6847, Num_std: 0.083183, Num_range: 3.9998, Real_score: 0.6901, Fake_score: 0.3038
2025-07-04 06:45:55,503 [INFO] src.gan.trainer: Epoch 0, Batch 900/976, Stage 0, D_loss: 0.5849, G_loss: -3.6775, Num_std: 0.082987, Num_range: 3.9999, Real_score: 0.6928, Fake_score: 0.3032
2025-07-04 06:46:06,867 [INFO] __main__: 第1个epoch完成. 指标: {'d_loss': 0.6181937716901302, 'd_adv_loss': 0.05176687763515314, 'd_ctr_loss': 0.5490587604094731, 'd_reg_loss': 0.11578755651731397, 'real_d_score': 0.678601463615406, 'fake_d_score': 0.31580641667251697, 'g_loss': -3.6537026780469155, 'g_adv_loss': 0.4719395386309571, 'g_feature_loss': 0.942710879307159, 'g_diversity_loss': -0.9999249626378542, 'g_numeric_diversity_loss': -0.3966066000214647, 'fake_d_score_g': 0.31537098213623177, 'temperature': 3.4999999999999174, 'numeric_std': 0.07979038144872157, 'numeric_range': 3.9930359390589674, 'training_stage': 0.0}
2025-07-04 06:46:06,868 [INFO] __main__: 开始第2/30个epoch (阶段: 0)
2025-07-04 06:46:07,137 [INFO] src.gan.trainer: Epoch 1, Batch 0/976, Stage 0, D_loss: 0.5507, G_loss: -3.6801, Num_std: 0.079834, Num_range: 3.9999, Real_score: 0.7009, Fake_score: 0.3022
2025-07-04 06:46:22,297 [INFO] src.gan.trainer: Epoch 1, Batch 100/976, Stage 0, D_loss: 0.5161, G_loss: -3.6775, Num_std: 0.072636, Num_range: 3.9999, Real_score: 0.6918, Fake_score: 0.2998
2025-07-04 06:46:37,397 [INFO] src.gan.trainer: Epoch 1, Batch 200/976, Stage 0, D_loss: 0.5942, G_loss: -3.6861, Num_std: 0.090036, Num_range: 3.9999, Real_score: 0.6928, Fake_score: 0.3058
2025-07-04 06:46:52,432 [INFO] src.gan.trainer: Epoch 1, Batch 300/976, Stage 0, D_loss: 0.5510, G_loss: -3.6444, Num_std: 0.088915, Num_range: 3.9999, Real_score: 0.6956, Fake_score: 0.3088
2025-07-04 06:47:07,447 [INFO] src.gan.trainer: Epoch 1, Batch 400/976, Stage 0, D_loss: 0.5844, G_loss: -3.6525, Num_std: 0.073628, Num_range: 3.9999, Real_score: 0.6951, Fake_score: 0.3037
2025-07-04 06:47:22,526 [INFO] src.gan.trainer: Epoch 1, Batch 500/976, Stage 0, D_loss: 0.5555, G_loss: -3.6671, Num_std: 0.073320, Num_range: 4.0000, Real_score: 0.6930, Fake_score: 0.3043
2025-07-04 06:47:37,504 [INFO] src.gan.trainer: Epoch 1, Batch 600/976, Stage 0, D_loss: 0.5318, G_loss: -3.6757, Num_std: 0.062841, Num_range: 4.0000, Real_score: 0.6967, Fake_score: 0.3061
2025-07-04 06:47:52,495 [INFO] src.gan.trainer: Epoch 1, Batch 700/976, Stage 0, D_loss: 0.5694, G_loss: -3.6788, Num_std: 0.059626, Num_range: 4.0000, Real_score: 0.7014, Fake_score: 0.3043
2025-07-04 06:48:07,574 [INFO] src.gan.trainer: Epoch 1, Batch 800/976, Stage 0, D_loss: 0.5489, G_loss: -3.6761, Num_std: 0.066943, Num_range: 4.0000, Real_score: 0.6979, Fake_score: 0.2992
2025-07-04 06:48:22,695 [INFO] src.gan.trainer: Epoch 1, Batch 900/976, Stage 0, D_loss: 0.5713, G_loss: -3.6768, Num_std: 0.085397, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3024
2025-07-04 06:48:34,032 [INFO] __main__: 第2个epoch完成. 指标: {'d_loss': 0.5554305866788153, 'd_adv_loss': 0.044347742980834666, 'd_ctr_loss': 0.49111905507743353, 'd_reg_loss': 0.13309191762790337, 'real_d_score': 0.6953973265089953, 'fake_d_score': 0.3044860130633974, 'g_loss': -3.6641258403373858, 'g_adv_loss': 0.4873084348774023, 'g_feature_loss': 0.748945791885013, 'g_diversity_loss': -0.9717334789005138, 'g_numeric_diversity_loss': -0.4021435107710093, 'fake_d_score_g': 0.30378861749758496, 'temperature': 3.4965000000000583, 'numeric_std': 0.07222121703007293, 'numeric_range': 3.999942152226563, 'training_stage': 0.0}
2025-07-04 06:48:34,032 [INFO] __main__: 在第2个epoch评估改进的GAN
2025-07-04 06:48:34,051 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 06:48:34,051 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 06:48:34,052 [INFO] __main__: 改进GAN评估指标:
2025-07-04 06:48:34,052 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 06:48:34,052 [INFO] __main__:   特征平均标准差: 0.000015
2025-07-04 06:48:34,053 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 06:48:34,053 [INFO] __main__: 训练质量: 需要改进
2025-07-04 06:48:34,054 [INFO] __main__: 新的最佳数值特征质量: 0.400
2025-07-04 06:48:34,243 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_best_model.pt
2025-07-04 06:48:34,243 [INFO] __main__: 保存最佳模型，数值质量评分: 0.400
2025-07-04 06:48:34,429 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_2.pt
2025-07-04 06:48:34,429 [INFO] __main__: 开始第3/30个epoch (阶段: 0)
2025-07-04 06:48:34,699 [INFO] src.gan.trainer: Epoch 2, Batch 0/976, Stage 0, D_loss: 0.5707, G_loss: -3.6816, Num_std: 0.069904, Num_range: 4.0000, Real_score: 0.6956, Fake_score: 0.3026
2025-07-04 06:48:49,802 [INFO] src.gan.trainer: Epoch 2, Batch 100/976, Stage 0, D_loss: 0.5584, G_loss: -3.6886, Num_std: 0.069438, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.3010
2025-07-04 06:49:05,069 [INFO] src.gan.trainer: Epoch 2, Batch 200/976, Stage 0, D_loss: 0.5521, G_loss: -3.6854, Num_std: 0.064641, Num_range: 4.0000, Real_score: 0.6934, Fake_score: 0.3028
2025-07-04 06:49:20,396 [INFO] src.gan.trainer: Epoch 2, Batch 300/976, Stage 0, D_loss: 0.5322, G_loss: -3.6914, Num_std: 0.056844, Num_range: 4.0000, Real_score: 0.6948, Fake_score: 0.3063
2025-07-04 06:49:35,616 [INFO] src.gan.trainer: Epoch 2, Batch 400/976, Stage 0, D_loss: 0.5285, G_loss: -3.6912, Num_std: 0.078648, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.2995
2025-07-04 06:49:50,856 [INFO] src.gan.trainer: Epoch 2, Batch 500/976, Stage 0, D_loss: 0.5900, G_loss: -3.6925, Num_std: 0.065505, Num_range: 4.0000, Real_score: 0.6942, Fake_score: 0.3041
2025-07-04 06:50:06,219 [INFO] src.gan.trainer: Epoch 2, Batch 600/976, Stage 0, D_loss: 0.5460, G_loss: -3.6937, Num_std: 0.071315, Num_range: 4.0000, Real_score: 0.6947, Fake_score: 0.3021
2025-07-04 06:50:21,493 [INFO] src.gan.trainer: Epoch 2, Batch 700/976, Stage 0, D_loss: 0.5269, G_loss: -3.6958, Num_std: 0.072439, Num_range: 4.0000, Real_score: 0.6967, Fake_score: 0.3013
2025-07-04 06:50:36,736 [INFO] src.gan.trainer: Epoch 2, Batch 800/976, Stage 0, D_loss: 0.5403, G_loss: -3.6943, Num_std: 0.067425, Num_range: 4.0000, Real_score: 0.6960, Fake_score: 0.3008
2025-07-04 06:50:51,976 [INFO] src.gan.trainer: Epoch 2, Batch 900/976, Stage 0, D_loss: 0.5382, G_loss: -3.6968, Num_std: 0.078646, Num_range: 4.0000, Real_score: 0.6964, Fake_score: 0.3046
2025-07-04 06:51:03,469 [INFO] __main__: 第3个epoch完成. 指标: {'d_loss': 0.5374175265553541, 'd_adv_loss': 0.04311381349126328, 'd_ctr_loss': 0.473930110788492, 'd_reg_loss': 0.1358240036538147, 'real_d_score': 0.696523521828358, 'fake_d_score': 0.3029393809313169, 'g_loss': -3.6856189650264577, 'g_adv_loss': 0.48781084637742916, 'g_feature_loss': 0.7481634082282841, 'g_diversity_loss': -0.9911120483865515, 'g_numeric_diversity_loss': -0.402465741582663, 'fake_d_score_g': 0.30283429360658426, 'temperature': 3.4930035000000896, 'numeric_std': 0.07065574086453649, 'numeric_range': 3.9999863713002615, 'training_stage': 0.0}
2025-07-04 06:51:03,469 [INFO] __main__: 开始第4/30个epoch (阶段: 0)
2025-07-04 06:51:03,731 [INFO] src.gan.trainer: Epoch 3, Batch 0/976, Stage 0, D_loss: 0.5136, G_loss: -3.6917, Num_std: 0.061202, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3016
2025-07-04 06:51:19,030 [INFO] src.gan.trainer: Epoch 3, Batch 100/976, Stage 0, D_loss: 0.4891, G_loss: -3.6984, Num_std: 0.069855, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.3021
2025-07-04 06:51:34,147 [INFO] src.gan.trainer: Epoch 3, Batch 200/976, Stage 0, D_loss: 0.5332, G_loss: -3.6925, Num_std: 0.086222, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.3019
2025-07-04 06:51:49,282 [INFO] src.gan.trainer: Epoch 3, Batch 300/976, Stage 0, D_loss: 0.5685, G_loss: -3.6984, Num_std: 0.077877, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3032
2025-07-04 06:52:04,581 [INFO] src.gan.trainer: Epoch 3, Batch 400/976, Stage 0, D_loss: 0.5578, G_loss: -3.6953, Num_std: 0.068288, Num_range: 4.0000, Real_score: 0.6939, Fake_score: 0.3051
2025-07-04 06:52:19,710 [INFO] src.gan.trainer: Epoch 3, Batch 500/976, Stage 0, D_loss: 0.5069, G_loss: -3.6949, Num_std: 0.079756, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3022
2025-07-04 06:52:34,770 [INFO] src.gan.trainer: Epoch 3, Batch 600/976, Stage 0, D_loss: 0.5560, G_loss: -3.6930, Num_std: 0.068817, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.3004
2025-07-04 06:52:49,862 [INFO] src.gan.trainer: Epoch 3, Batch 700/976, Stage 0, D_loss: 0.5466, G_loss: -3.6945, Num_std: 0.052809, Num_range: 4.0000, Real_score: 0.7002, Fake_score: 0.3037
2025-07-04 06:53:05,052 [INFO] src.gan.trainer: Epoch 3, Batch 800/976, Stage 0, D_loss: 0.5283, G_loss: -3.6882, Num_std: 0.069626, Num_range: 4.0000, Real_score: 0.6948, Fake_score: 0.3016
2025-07-04 06:53:20,234 [INFO] src.gan.trainer: Epoch 3, Batch 900/976, Stage 0, D_loss: 0.4923, G_loss: -3.6917, Num_std: 0.075745, Num_range: 4.0000, Real_score: 0.6955, Fake_score: 0.3047
2025-07-04 06:53:31,630 [INFO] __main__: 第4个epoch完成. 指标: {'d_loss': 0.5256803460174899, 'd_adv_loss': 0.042571610500120045, 'd_ctr_loss': 0.46265817723679775, 'd_reg_loss': 0.13633705703846982, 'real_d_score': 0.696863747888901, 'fake_d_score': 0.30257609162907145, 'g_loss': -3.692542632604072, 'g_adv_loss': 0.48766483639270247, 'g_feature_loss': 0.853879498857293, 'g_diversity_loss': -0.9988165503886886, 'g_numeric_diversity_loss': -0.4030106117836928, 'fake_d_score_g': 0.3026197942004343, 'temperature': 3.489510496499961, 'numeric_std': 0.06993419804754794, 'numeric_range': 3.9999937862971966, 'training_stage': 0.0}
2025-07-04 06:53:31,631 [INFO] __main__: 在第4个epoch评估改进的GAN
2025-07-04 06:53:31,646 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 06:53:31,647 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 06:53:31,647 [INFO] __main__: 改进GAN评估指标:
2025-07-04 06:53:31,648 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 06:53:31,648 [INFO] __main__:   特征平均标准差: 0.000003
2025-07-04 06:53:31,648 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 06:53:31,648 [INFO] __main__: 训练质量: 需要改进
2025-07-04 06:53:31,842 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_4.pt
2025-07-04 06:53:31,842 [INFO] __main__: 开始第5/30个epoch (阶段: 0)
2025-07-04 06:53:32,117 [INFO] src.gan.trainer: Epoch 4, Batch 0/976, Stage 0, D_loss: 0.4769, G_loss: -3.6913, Num_std: 0.067072, Num_range: 4.0000, Real_score: 0.7003, Fake_score: 0.3018
2025-07-04 06:53:47,300 [INFO] src.gan.trainer: Epoch 4, Batch 100/976, Stage 0, D_loss: 0.5055, G_loss: -3.6876, Num_std: 0.083757, Num_range: 4.0000, Real_score: 0.6954, Fake_score: 0.3025
2025-07-04 06:54:02,606 [INFO] src.gan.trainer: Epoch 4, Batch 200/976, Stage 0, D_loss: 0.5066, G_loss: -3.6908, Num_std: 0.067299, Num_range: 4.0000, Real_score: 0.6989, Fake_score: 0.3033
2025-07-04 06:54:17,780 [INFO] src.gan.trainer: Epoch 4, Batch 300/976, Stage 0, D_loss: 0.5277, G_loss: -3.6922, Num_std: 0.062163, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.3033
2025-07-04 06:54:32,916 [INFO] src.gan.trainer: Epoch 4, Batch 400/976, Stage 0, D_loss: 0.5391, G_loss: -3.6903, Num_std: 0.070587, Num_range: 4.0000, Real_score: 0.7009, Fake_score: 0.3022
2025-07-04 06:54:48,041 [INFO] src.gan.trainer: Epoch 4, Batch 500/976, Stage 0, D_loss: 0.5157, G_loss: -3.6869, Num_std: 0.081831, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3024
2025-07-04 06:55:03,249 [INFO] src.gan.trainer: Epoch 4, Batch 600/976, Stage 0, D_loss: 0.4980, G_loss: -3.6808, Num_std: 0.063991, Num_range: 4.0000, Real_score: 0.6967, Fake_score: 0.3082
2025-07-04 06:55:18,262 [INFO] src.gan.trainer: Epoch 4, Batch 700/976, Stage 0, D_loss: 0.5025, G_loss: -3.6288, Num_std: 0.081488, Num_range: 4.0000, Real_score: 0.6955, Fake_score: 0.3028
2025-07-04 06:55:33,318 [INFO] src.gan.trainer: Epoch 4, Batch 800/976, Stage 0, D_loss: 0.5311, G_loss: -3.6361, Num_std: 0.075032, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3054
2025-07-04 06:55:48,331 [INFO] src.gan.trainer: Epoch 4, Batch 900/976, Stage 0, D_loss: 0.5236, G_loss: -3.6505, Num_std: 0.070695, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3026
2025-07-04 06:55:59,705 [INFO] __main__: 第5个epoch完成. 指标: {'d_loss': 0.5170306854072158, 'd_adv_loss': 0.042694831018900994, 'd_ctr_loss': 0.4539731911032418, 'd_reg_loss': 0.13575108721088922, 'real_d_score': 0.6969445617106124, 'fake_d_score': 0.3031299209802366, 'g_loss': -3.666104272150435, 'g_adv_loss': 0.48754099839942067, 'g_feature_loss': 1.1234129429922068, 'g_diversity_loss': -0.9913349218585117, 'g_numeric_diversity_loss': -0.40231012456281867, 'fake_d_score_g': 0.3028494049820421, 'temperature': 3.48602098600344, 'numeric_std': 0.07013819819923374, 'numeric_range': 3.9999956156386753, 'training_stage': 0.0}
2025-07-04 06:55:59,706 [INFO] __main__: 开始第6/30个epoch (阶段: 0)
2025-07-04 06:55:59,971 [INFO] src.gan.trainer: Epoch 5, Batch 0/976, Stage 0, D_loss: 0.4821, G_loss: -3.6513, Num_std: 0.068642, Num_range: 4.0000, Real_score: 0.6990, Fake_score: 0.2975
2025-07-04 06:56:15,138 [INFO] src.gan.trainer: Epoch 5, Batch 100/976, Stage 0, D_loss: 0.4913, G_loss: -3.6537, Num_std: 0.074460, Num_range: 4.0000, Real_score: 0.6932, Fake_score: 0.2988
2025-07-04 06:56:30,333 [INFO] src.gan.trainer: Epoch 5, Batch 200/976, Stage 0, D_loss: 0.5462, G_loss: -3.6540, Num_std: 0.080424, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3050
2025-07-04 06:56:45,494 [INFO] src.gan.trainer: Epoch 5, Batch 300/976, Stage 0, D_loss: 0.5477, G_loss: -3.6546, Num_std: 0.074697, Num_range: 4.0000, Real_score: 0.6952, Fake_score: 0.3008
2025-07-04 06:57:00,780 [INFO] src.gan.trainer: Epoch 5, Batch 400/976, Stage 0, D_loss: 0.4407, G_loss: -3.6501, Num_std: 0.074066, Num_range: 4.0000, Real_score: 0.6955, Fake_score: 0.3041
2025-07-04 06:57:15,954 [INFO] src.gan.trainer: Epoch 5, Batch 500/976, Stage 0, D_loss: 0.5303, G_loss: -3.6527, Num_std: 0.079564, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3020
2025-07-04 06:57:31,142 [INFO] src.gan.trainer: Epoch 5, Batch 600/976, Stage 0, D_loss: 0.5081, G_loss: -3.6561, Num_std: 0.059887, Num_range: 4.0000, Real_score: 0.6953, Fake_score: 0.3017
2025-07-04 06:57:46,310 [INFO] src.gan.trainer: Epoch 5, Batch 700/976, Stage 0, D_loss: 0.5361, G_loss: -3.6620, Num_std: 0.079408, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3018
2025-07-04 06:58:01,462 [INFO] src.gan.trainer: Epoch 5, Batch 800/976, Stage 0, D_loss: 0.5184, G_loss: -3.6522, Num_std: 0.077125, Num_range: 4.0000, Real_score: 0.6983, Fake_score: 0.3063
2025-07-04 06:58:16,502 [INFO] src.gan.trainer: Epoch 5, Batch 900/976, Stage 0, D_loss: 0.4892, G_loss: -3.6480, Num_std: 0.067839, Num_range: 4.0000, Real_score: 0.6956, Fake_score: 0.3025
2025-07-04 06:58:27,885 [INFO] __main__: 第6个epoch完成. 指标: {'d_loss': 0.5097079126194849, 'd_adv_loss': 0.042446600170195345, 'd_ctr_loss': 0.44684402250730526, 'd_reg_loss': 0.13611526205586114, 'real_d_score': 0.6968923409576304, 'fake_d_score': 0.3026697506486881, 'g_loss': -3.646047094826656, 'g_adv_loss': 0.48761287040479173, 'g_feature_loss': 1.5707469783682628, 'g_diversity_loss': -0.9950430967280112, 'g_numeric_diversity_loss': -0.40214427714261547, 'fake_d_score_g': 0.3026292294447056, 'temperature': 3.48253496501745, 'numeric_std': 0.0693682256811143, 'numeric_range': 3.9999967031791503, 'training_stage': 0.0}
2025-07-04 06:58:27,885 [INFO] __main__: 在第6个epoch评估改进的GAN
2025-07-04 06:58:27,901 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 06:58:27,901 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 06:58:27,902 [INFO] __main__: 改进GAN评估指标:
2025-07-04 06:58:27,902 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 06:58:27,902 [INFO] __main__:   特征平均标准差: 0.000003
2025-07-04 06:58:27,902 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 06:58:27,902 [INFO] __main__: 训练质量: 需要改进
2025-07-04 06:58:28,095 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_6.pt
2025-07-04 06:58:28,095 [INFO] __main__: 开始第7/30个epoch (阶段: 0)
2025-07-04 06:58:28,375 [INFO] src.gan.trainer: Epoch 6, Batch 0/976, Stage 0, D_loss: 0.5395, G_loss: -3.6656, Num_std: 0.073843, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3025
2025-07-04 06:58:43,697 [INFO] src.gan.trainer: Epoch 6, Batch 100/976, Stage 0, D_loss: 0.4798, G_loss: -3.6797, Num_std: 0.043254, Num_range: 4.0000, Real_score: 0.6943, Fake_score: 0.3003
2025-07-04 06:58:59,016 [INFO] src.gan.trainer: Epoch 6, Batch 200/976, Stage 0, D_loss: 0.4793, G_loss: -3.6769, Num_std: 0.073716, Num_range: 4.0000, Real_score: 0.6953, Fake_score: 0.3040
2025-07-04 06:59:14,329 [INFO] src.gan.trainer: Epoch 6, Batch 300/976, Stage 0, D_loss: 0.5140, G_loss: -3.6808, Num_std: 0.080946, Num_range: 4.0000, Real_score: 0.6976, Fake_score: 0.3031
2025-07-04 06:59:29,630 [INFO] src.gan.trainer: Epoch 6, Batch 400/976, Stage 0, D_loss: 0.4848, G_loss: -3.6821, Num_std: 0.061382, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3026
2025-07-04 06:59:44,971 [INFO] src.gan.trainer: Epoch 6, Batch 500/976, Stage 0, D_loss: 0.5052, G_loss: -3.6835, Num_std: 0.069773, Num_range: 4.0000, Real_score: 0.6990, Fake_score: 0.3041
2025-07-04 07:00:00,687 [INFO] src.gan.trainer: Epoch 6, Batch 600/976, Stage 0, D_loss: 0.5569, G_loss: -3.6820, Num_std: 0.079293, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3024
2025-07-04 07:00:15,865 [INFO] src.gan.trainer: Epoch 6, Batch 700/976, Stage 0, D_loss: 0.5014, G_loss: -3.6782, Num_std: 0.063445, Num_range: 4.0000, Real_score: 0.6989, Fake_score: 0.3053
2025-07-04 07:00:30,872 [INFO] src.gan.trainer: Epoch 6, Batch 800/976, Stage 0, D_loss: 0.4553, G_loss: -3.6817, Num_std: 0.068924, Num_range: 4.0000, Real_score: 0.6990, Fake_score: 0.3026
2025-07-04 07:00:45,812 [INFO] src.gan.trainer: Epoch 6, Batch 900/976, Stage 0, D_loss: 0.5289, G_loss: -3.6794, Num_std: 0.062678, Num_range: 4.0000, Real_score: 0.6955, Fake_score: 0.3066
2025-07-04 07:00:57,141 [INFO] __main__: 第7个epoch完成. 指标: {'d_loss': 0.5034752620353554, 'd_adv_loss': 0.042268728734314726, 'd_ctr_loss': 0.4407226129389197, 'd_reg_loss': 0.13655946318243378, 'real_d_score': 0.6971712963136485, 'fake_d_score': 0.30253482829840467, 'g_loss': -3.6663009985019217, 'g_adv_loss': 0.48783871345222013, 'g_feature_loss': 1.0737981396253964, 'g_diversity_loss': -0.9973779214250909, 'g_numeric_diversity_loss': -0.40130646233675943, 'fake_d_score_g': 0.3023842695408341, 'temperature': 3.4790524300524415, 'numeric_std': 0.06963600939739091, 'numeric_range': 3.9999957261352708, 'training_stage': 0.0}
2025-07-04 07:00:57,141 [INFO] __main__: 开始第8/30个epoch (阶段: 0)
2025-07-04 07:00:57,415 [INFO] src.gan.trainer: Epoch 7, Batch 0/976, Stage 0, D_loss: 0.4990, G_loss: -3.6722, Num_std: 0.067368, Num_range: 4.0000, Real_score: 0.6947, Fake_score: 0.3020
2025-07-04 07:01:12,825 [INFO] src.gan.trainer: Epoch 7, Batch 100/976, Stage 0, D_loss: 0.5200, G_loss: -1.7875, Num_std: 0.038309, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3008
2025-07-04 07:01:28,200 [INFO] src.gan.trainer: Epoch 7, Batch 200/976, Stage 0, D_loss: 0.4838, G_loss: -3.6676, Num_std: 0.088906, Num_range: 4.0000, Real_score: 0.6955, Fake_score: 0.3045
2025-07-04 07:01:43,588 [INFO] src.gan.trainer: Epoch 7, Batch 300/976, Stage 0, D_loss: 0.4900, G_loss: -3.6721, Num_std: 0.075561, Num_range: 4.0000, Real_score: 0.6960, Fake_score: 0.3041
2025-07-04 07:01:59,113 [INFO] src.gan.trainer: Epoch 7, Batch 400/976, Stage 0, D_loss: 0.5450, G_loss: -3.6825, Num_std: 0.068857, Num_range: 4.0000, Real_score: 0.6966, Fake_score: 0.3003
2025-07-04 07:02:14,387 [INFO] src.gan.trainer: Epoch 7, Batch 500/976, Stage 0, D_loss: 0.5114, G_loss: -3.6783, Num_std: 0.056894, Num_range: 4.0000, Real_score: 0.6981, Fake_score: 0.3031
2025-07-04 07:02:29,546 [INFO] src.gan.trainer: Epoch 7, Batch 600/976, Stage 0, D_loss: 0.4758, G_loss: -3.6778, Num_std: 0.080730, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3001
2025-07-04 07:02:44,588 [INFO] src.gan.trainer: Epoch 7, Batch 700/976, Stage 0, D_loss: 0.4898, G_loss: -3.6828, Num_std: 0.081950, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3026
2025-07-04 07:02:59,727 [INFO] src.gan.trainer: Epoch 7, Batch 800/976, Stage 0, D_loss: 0.5059, G_loss: -3.6816, Num_std: 0.057635, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.3019
2025-07-04 07:03:14,735 [INFO] src.gan.trainer: Epoch 7, Batch 900/976, Stage 0, D_loss: 0.4925, G_loss: -3.6774, Num_std: 0.047061, Num_range: 4.0000, Real_score: 0.6954, Fake_score: 0.3023
2025-07-04 07:03:26,018 [INFO] __main__: 第8个epoch完成. 指标: {'d_loss': 0.4988281655690224, 'd_adv_loss': 0.042142242761939494, 'd_ctr_loss': 0.43618414722016535, 'd_reg_loss': 0.13667850323082512, 'real_d_score': 0.6971332711152366, 'fake_d_score': 0.302334763811993, 'g_loss': -3.6295691913838635, 'g_adv_loss': 0.4879515189575698, 'g_feature_loss': 1.1578490230473668, 'g_diversity_loss': -0.9965664488696009, 'g_numeric_diversity_loss': -0.39735583901975297, 'fake_d_score_g': 0.3022362051602923, 'temperature': 3.4755733776224775, 'numeric_std': 0.07142324469098882, 'numeric_range': 3.999994436736961, 'training_stage': 0.0}
2025-07-04 07:03:26,018 [INFO] __main__: 在第8个epoch评估改进的GAN
2025-07-04 07:03:26,033 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:03:26,034 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:03:26,034 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:03:26,035 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 07:03:26,035 [INFO] __main__:   特征平均标准差: 0.000016
2025-07-04 07:03:26,035 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 07:03:26,035 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:03:26,227 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_8.pt
2025-07-04 07:03:26,227 [INFO] __main__: 开始第9/30个epoch (阶段: 0)
2025-07-04 07:03:26,499 [INFO] src.gan.trainer: Epoch 8, Batch 0/976, Stage 0, D_loss: 0.4332, G_loss: -3.6837, Num_std: 0.080826, Num_range: 4.0000, Real_score: 0.6968, Fake_score: 0.3039
2025-07-04 07:03:41,716 [INFO] src.gan.trainer: Epoch 8, Batch 100/976, Stage 0, D_loss: 0.4592, G_loss: -3.6692, Num_std: 0.112086, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3037
2025-07-04 07:03:56,959 [INFO] src.gan.trainer: Epoch 8, Batch 200/976, Stage 0, D_loss: 0.5347, G_loss: -3.6782, Num_std: 0.072790, Num_range: 4.0000, Real_score: 0.6995, Fake_score: 0.3012
2025-07-04 07:04:12,141 [INFO] src.gan.trainer: Epoch 8, Batch 300/976, Stage 0, D_loss: 0.4825, G_loss: -3.6778, Num_std: 0.078302, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3050
2025-07-04 07:04:27,299 [INFO] src.gan.trainer: Epoch 8, Batch 400/976, Stage 0, D_loss: 0.5199, G_loss: -3.6725, Num_std: 0.112253, Num_range: 4.0000, Real_score: 0.6984, Fake_score: 0.3009
2025-07-04 07:04:42,392 [INFO] src.gan.trainer: Epoch 8, Batch 500/976, Stage 0, D_loss: 0.5043, G_loss: -3.6778, Num_std: 0.060676, Num_range: 4.0000, Real_score: 0.6968, Fake_score: 0.3017
2025-07-04 07:04:57,527 [INFO] src.gan.trainer: Epoch 8, Batch 600/976, Stage 0, D_loss: 0.4996, G_loss: -3.6732, Num_std: 0.060184, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3023
2025-07-04 07:05:12,588 [INFO] src.gan.trainer: Epoch 8, Batch 700/976, Stage 0, D_loss: 0.5185, G_loss: -2.9291, Num_std: 0.039630, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.3022
2025-07-04 07:05:27,585 [INFO] src.gan.trainer: Epoch 8, Batch 800/976, Stage 0, D_loss: 0.5156, G_loss: -3.6714, Num_std: 0.072475, Num_range: 4.0000, Real_score: 0.6960, Fake_score: 0.3028
2025-07-04 07:05:42,600 [INFO] src.gan.trainer: Epoch 8, Batch 900/976, Stage 0, D_loss: 0.4402, G_loss: -3.6680, Num_std: 0.091707, Num_range: 4.0000, Real_score: 0.6999, Fake_score: 0.3036
2025-07-04 07:05:54,026 [INFO] __main__: 第9个epoch完成. 指标: {'d_loss': 0.49398164906096276, 'd_adv_loss': 0.042096533488146376, 'd_ctr_loss': 0.4313756449178596, 'd_reg_loss': 0.13672979350094913, 'real_d_score': 0.6973368363668684, 'fake_d_score': 0.3024738679838479, 'g_loss': -3.6156377349191344, 'g_adv_loss': 0.4876327352608494, 'g_feature_loss': 1.1477581483255954, 'g_diversity_loss': -0.9953344749704085, 'g_numeric_diversity_loss': -0.3956654877376439, 'fake_d_score_g': 0.30244906789170256, 'temperature': 3.4720978042448434, 'numeric_std': 0.07533907294552575, 'numeric_range': 3.999985242150516, 'training_stage': 0.0}
2025-07-04 07:05:54,026 [INFO] __main__: 开始第10/30个epoch (阶段: 0)
2025-07-04 07:05:54,296 [INFO] src.gan.trainer: Epoch 9, Batch 0/976, Stage 0, D_loss: 0.4928, G_loss: -3.6695, Num_std: 0.066126, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.3032
2025-07-04 07:06:09,362 [INFO] src.gan.trainer: Epoch 9, Batch 100/976, Stage 0, D_loss: 0.4976, G_loss: -2.5534, Num_std: 0.039459, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.3029
2025-07-04 07:06:24,413 [INFO] src.gan.trainer: Epoch 9, Batch 200/976, Stage 0, D_loss: 0.4916, G_loss: -3.1820, Num_std: 0.057934, Num_range: 4.0000, Real_score: 0.6998, Fake_score: 0.3027
2025-07-04 07:06:39,405 [INFO] src.gan.trainer: Epoch 9, Batch 300/976, Stage 0, D_loss: 0.4793, G_loss: -3.6774, Num_std: 0.085503, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3025
2025-07-04 07:06:54,503 [INFO] src.gan.trainer: Epoch 9, Batch 400/976, Stage 0, D_loss: 0.4747, G_loss: -3.6740, Num_std: 0.041487, Num_range: 4.0000, Real_score: 0.6984, Fake_score: 0.3002
2025-07-04 07:07:09,535 [INFO] src.gan.trainer: Epoch 9, Batch 500/976, Stage 0, D_loss: 0.4854, G_loss: -3.6822, Num_std: 0.062705, Num_range: 4.0000, Real_score: 0.6966, Fake_score: 0.3018
2025-07-04 07:07:24,556 [INFO] src.gan.trainer: Epoch 9, Batch 600/976, Stage 0, D_loss: 0.4939, G_loss: -3.6820, Num_std: 0.073100, Num_range: 4.0000, Real_score: 0.6967, Fake_score: 0.3015
2025-07-04 07:07:39,574 [INFO] src.gan.trainer: Epoch 9, Batch 700/976, Stage 0, D_loss: 0.4957, G_loss: -3.6750, Num_std: 0.076987, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3026
2025-07-04 07:07:54,669 [INFO] src.gan.trainer: Epoch 9, Batch 800/976, Stage 0, D_loss: 0.5026, G_loss: -3.6762, Num_std: 0.065058, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.2998
2025-07-04 07:08:09,722 [INFO] src.gan.trainer: Epoch 9, Batch 900/976, Stage 0, D_loss: 0.4968, G_loss: -3.6845, Num_std: 0.069344, Num_range: 4.0000, Real_score: 0.6998, Fake_score: 0.3051
2025-07-04 07:08:21,409 [INFO] __main__: 第10个epoch完成. 指标: {'d_loss': 0.4898052414054756, 'd_adv_loss': 0.04203693832835699, 'd_ctr_loss': 0.4272513219811881, 'd_reg_loss': 0.1367798681050296, 'real_d_score': 0.6973014084897086, 'fake_d_score': 0.3023658977424509, 'g_loss': -3.62820591280858, 'g_adv_loss': 0.48769024232290453, 'g_feature_loss': 1.1513187877548836, 'g_diversity_loss': -0.9960707326322961, 'g_numeric_diversity_loss': -0.3971739197862357, 'fake_d_score_g': 0.30238035084079223, 'temperature': 3.4686257064405845, 'numeric_std': 0.07160587694167576, 'numeric_range': 3.9999876471002245, 'training_stage': 0.0}
2025-07-04 07:08:21,410 [INFO] __main__: 在第10个epoch评估改进的GAN
2025-07-04 07:08:21,425 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:08:21,425 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:08:21,426 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:08:21,426 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 07:08:21,426 [INFO] __main__:   特征平均标准差: 0.000010
2025-07-04 07:08:21,426 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 07:08:21,427 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:08:21,622 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_10.pt
2025-07-04 07:08:21,623 [INFO] __main__: 开始第11/30个epoch (阶段: 0)
2025-07-04 07:08:21,894 [INFO] src.gan.trainer: Epoch 10, Batch 0/976, Stage 0, D_loss: 0.5115, G_loss: -3.6786, Num_std: 0.069039, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.3050
2025-07-04 07:08:37,021 [INFO] src.gan.trainer: Epoch 10, Batch 100/976, Stage 0, D_loss: 0.4923, G_loss: -3.6848, Num_std: 0.075158, Num_range: 4.0000, Real_score: 0.6991, Fake_score: 0.3016
2025-07-04 07:08:52,338 [INFO] src.gan.trainer: Epoch 10, Batch 200/976, Stage 0, D_loss: 0.4931, G_loss: -3.6785, Num_std: 0.076122, Num_range: 4.0000, Real_score: 0.6952, Fake_score: 0.3046
2025-07-04 07:09:07,477 [INFO] src.gan.trainer: Epoch 10, Batch 300/976, Stage 0, D_loss: 0.4611, G_loss: -3.6770, Num_std: 0.080045, Num_range: 4.0000, Real_score: 0.6989, Fake_score: 0.3017
2025-07-04 07:09:22,662 [INFO] src.gan.trainer: Epoch 10, Batch 400/976, Stage 0, D_loss: 0.4610, G_loss: -3.6725, Num_std: 0.067588, Num_range: 4.0000, Real_score: 0.6957, Fake_score: 0.3015
2025-07-04 07:09:37,899 [INFO] src.gan.trainer: Epoch 10, Batch 500/976, Stage 0, D_loss: 0.4291, G_loss: -3.6757, Num_std: 0.067194, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3027
2025-07-04 07:09:53,080 [INFO] src.gan.trainer: Epoch 10, Batch 600/976, Stage 0, D_loss: 0.4715, G_loss: -3.6764, Num_std: 0.064815, Num_range: 4.0000, Real_score: 0.6998, Fake_score: 0.3025
2025-07-04 07:10:08,317 [INFO] src.gan.trainer: Epoch 10, Batch 700/976, Stage 0, D_loss: 0.4802, G_loss: -3.6785, Num_std: 0.073616, Num_range: 4.0000, Real_score: 0.6967, Fake_score: 0.3021
2025-07-04 07:10:23,534 [INFO] src.gan.trainer: Epoch 10, Batch 800/976, Stage 0, D_loss: 0.4945, G_loss: -3.6817, Num_std: 0.072381, Num_range: 4.0000, Real_score: 0.6957, Fake_score: 0.3053
2025-07-04 07:10:38,849 [INFO] src.gan.trainer: Epoch 10, Batch 900/976, Stage 0, D_loss: 0.5004, G_loss: -3.6741, Num_std: 0.074301, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.2995
2025-07-04 07:10:50,281 [INFO] __main__: 第11个epoch完成. 指标: {'d_loss': 0.48601064489024615, 'd_adv_loss': 0.04204671420767662, 'd_ctr_loss': 0.42345657327868874, 'd_reg_loss': 0.13671570621644608, 'real_d_score': 0.6972658029589494, 'fake_d_score': 0.30237988974960095, 'g_loss': -3.6408302159216612, 'g_adv_loss': 0.487785216766954, 'g_feature_loss': 1.16228395154893, 'g_diversity_loss': -0.9962418247116075, 'g_numeric_diversity_loss': -0.3988109754164361, 'fake_d_score_g': 0.30230535972924527, 'temperature': 3.4651570807341785, 'numeric_std': 0.07095033456336981, 'numeric_range': 3.99999306037452, 'training_stage': 0.0}
2025-07-04 07:10:50,281 [INFO] __main__: 开始第12/30个epoch (阶段: 0)
2025-07-04 07:10:50,557 [INFO] src.gan.trainer: Epoch 11, Batch 0/976, Stage 0, D_loss: 0.4846, G_loss: -3.6722, Num_std: 0.051814, Num_range: 4.0000, Real_score: 0.6976, Fake_score: 0.3002
2025-07-04 07:11:05,854 [INFO] src.gan.trainer: Epoch 11, Batch 100/976, Stage 0, D_loss: 0.4462, G_loss: -3.6724, Num_std: 0.068402, Num_range: 4.0000, Real_score: 0.6949, Fake_score: 0.3040
2025-07-04 07:11:21,086 [INFO] src.gan.trainer: Epoch 11, Batch 200/976, Stage 0, D_loss: 0.4797, G_loss: -3.6774, Num_std: 0.064936, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3023
2025-07-04 07:11:36,401 [INFO] src.gan.trainer: Epoch 11, Batch 300/976, Stage 0, D_loss: 0.4867, G_loss: -3.6806, Num_std: 0.057462, Num_range: 4.0000, Real_score: 0.6984, Fake_score: 0.3035
2025-07-04 07:11:51,651 [INFO] src.gan.trainer: Epoch 11, Batch 400/976, Stage 0, D_loss: 0.5326, G_loss: -3.6800, Num_std: 0.085597, Num_range: 4.0000, Real_score: 0.6996, Fake_score: 0.3045
2025-07-04 07:12:06,782 [INFO] src.gan.trainer: Epoch 11, Batch 500/976, Stage 0, D_loss: 0.4909, G_loss: -3.6742, Num_std: 0.092278, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.2981
2025-07-04 07:12:21,803 [INFO] src.gan.trainer: Epoch 11, Batch 600/976, Stage 0, D_loss: 0.4613, G_loss: -3.6761, Num_std: 0.080699, Num_range: 4.0000, Real_score: 0.6966, Fake_score: 0.3054
2025-07-04 07:12:37,004 [INFO] src.gan.trainer: Epoch 11, Batch 700/976, Stage 0, D_loss: 0.4701, G_loss: -3.6788, Num_std: 0.082924, Num_range: 4.0000, Real_score: 0.7011, Fake_score: 0.2994
2025-07-04 07:12:52,066 [INFO] src.gan.trainer: Epoch 11, Batch 800/976, Stage 0, D_loss: 0.4910, G_loss: -2.3944, Num_std: 0.049671, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3033
2025-07-04 07:13:07,134 [INFO] src.gan.trainer: Epoch 11, Batch 900/976, Stage 0, D_loss: 0.5016, G_loss: -3.6693, Num_std: 0.088457, Num_range: 4.0000, Real_score: 0.6983, Fake_score: 0.3033
2025-07-04 07:13:18,485 [INFO] __main__: 第12个epoch完成. 指标: {'d_loss': 0.48177381021688204, 'd_adv_loss': 0.041977436571824796, 'd_ctr_loss': 0.4192604460127531, 'd_reg_loss': 0.13690617953839357, 'real_d_score': 0.6973273070742849, 'fake_d_score': 0.302269820734614, 'g_loss': -3.6363622328174863, 'g_adv_loss': 0.48790558581265947, 'g_feature_loss': 1.1713542698429575, 'g_diversity_loss': -0.996700890952609, 'g_numeric_diversity_loss': -0.3982668303136452, 'fake_d_score_g': 0.30218036764917217, 'temperature': 3.461691923653452, 'numeric_std': 0.07186419130631, 'numeric_range': 3.9999926659416047, 'training_stage': 0.0}
2025-07-04 07:13:18,485 [INFO] __main__: 在第12个epoch评估改进的GAN
2025-07-04 07:13:18,501 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:13:18,501 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:13:18,502 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:13:18,502 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 07:13:18,502 [INFO] __main__:   特征平均标准差: 0.000008
2025-07-04 07:13:18,502 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 07:13:18,502 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:13:18,694 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_12.pt
2025-07-04 07:13:18,694 [INFO] __main__: 开始第13/30个epoch (阶段: 0)
2025-07-04 07:13:18,967 [INFO] src.gan.trainer: Epoch 12, Batch 0/976, Stage 0, D_loss: 0.4624, G_loss: -3.6773, Num_std: 0.092294, Num_range: 4.0000, Real_score: 0.6988, Fake_score: 0.3027
2025-07-04 07:13:34,166 [INFO] src.gan.trainer: Epoch 12, Batch 100/976, Stage 0, D_loss: 0.4840, G_loss: -3.6826, Num_std: 0.046252, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3021
2025-07-04 07:13:49,304 [INFO] src.gan.trainer: Epoch 12, Batch 200/976, Stage 0, D_loss: 0.4574, G_loss: -3.6779, Num_std: 0.083764, Num_range: 4.0000, Real_score: 0.6935, Fake_score: 0.3013
2025-07-04 07:14:04,465 [INFO] src.gan.trainer: Epoch 12, Batch 300/976, Stage 0, D_loss: 0.4911, G_loss: -3.6774, Num_std: 0.068686, Num_range: 4.0000, Real_score: 0.6945, Fake_score: 0.3026
2025-07-04 07:14:19,547 [INFO] src.gan.trainer: Epoch 12, Batch 400/976, Stage 0, D_loss: 0.4670, G_loss: -3.6815, Num_std: 0.075367, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.3013
2025-07-04 07:14:34,620 [INFO] src.gan.trainer: Epoch 12, Batch 500/976, Stage 0, D_loss: 0.4793, G_loss: -3.6778, Num_std: 0.087436, Num_range: 4.0000, Real_score: 0.6954, Fake_score: 0.3068
2025-07-04 07:14:49,625 [INFO] src.gan.trainer: Epoch 12, Batch 600/976, Stage 0, D_loss: 0.4647, G_loss: -3.6808, Num_std: 0.087960, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3036
2025-07-04 07:15:04,667 [INFO] src.gan.trainer: Epoch 12, Batch 700/976, Stage 0, D_loss: 0.4416, G_loss: -3.6826, Num_std: 0.063734, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3019
2025-07-04 07:15:19,774 [INFO] src.gan.trainer: Epoch 12, Batch 800/976, Stage 0, D_loss: 0.4638, G_loss: -3.6836, Num_std: 0.073077, Num_range: 4.0000, Real_score: 0.6967, Fake_score: 0.3001
2025-07-04 07:15:35,125 [INFO] src.gan.trainer: Epoch 12, Batch 900/976, Stage 0, D_loss: 0.4348, G_loss: -2.4087, Num_std: 0.057912, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3005
2025-07-04 07:15:46,842 [INFO] __main__: 第13个epoch完成. 指标: {'d_loss': 0.477938371817352, 'd_adv_loss': 0.042033098828902514, 'd_ctr_loss': 0.4153930241333655, 'd_reg_loss': 0.13674832275900678, 'real_d_score': 0.6972891239479906, 'fake_d_score': 0.3023728690430769, 'g_loss': -3.6301608811554953, 'g_adv_loss': 0.4875808131214375, 'g_feature_loss': 1.090010049177812, 'g_diversity_loss': -0.9963427498895947, 'g_numeric_diversity_loss': -0.3969874310006936, 'fake_d_score_g': 0.30244937914683173, 'temperature': 3.458230231729817, 'numeric_std': 0.0708990117735108, 'numeric_range': 3.999991116302263, 'training_stage': 0.0}
2025-07-04 07:15:46,842 [INFO] __main__: 开始第14/30个epoch (阶段: 0)
2025-07-04 07:15:47,134 [INFO] src.gan.trainer: Epoch 13, Batch 0/976, Stage 0, D_loss: 0.5038, G_loss: -3.6796, Num_std: 0.073976, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3026
2025-07-04 07:16:02,432 [INFO] src.gan.trainer: Epoch 13, Batch 100/976, Stage 0, D_loss: 0.4671, G_loss: -3.6761, Num_std: 0.088475, Num_range: 4.0000, Real_score: 0.6989, Fake_score: 0.3063
2025-07-04 07:16:17,596 [INFO] src.gan.trainer: Epoch 13, Batch 200/976, Stage 0, D_loss: 0.4513, G_loss: -3.6828, Num_std: 0.079484, Num_range: 4.0000, Real_score: 0.6960, Fake_score: 0.3036
2025-07-04 07:16:32,836 [INFO] src.gan.trainer: Epoch 13, Batch 300/976, Stage 0, D_loss: 0.4873, G_loss: -3.6848, Num_std: 0.070632, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3017
2025-07-04 07:16:47,932 [INFO] src.gan.trainer: Epoch 13, Batch 400/976, Stage 0, D_loss: 0.4617, G_loss: -3.6810, Num_std: 0.088671, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3018
2025-07-04 07:17:03,064 [INFO] src.gan.trainer: Epoch 13, Batch 500/976, Stage 0, D_loss: 0.4848, G_loss: -3.6872, Num_std: 0.061195, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3022
2025-07-04 07:17:18,194 [INFO] src.gan.trainer: Epoch 13, Batch 600/976, Stage 0, D_loss: 0.4662, G_loss: -3.6831, Num_std: 0.074711, Num_range: 4.0000, Real_score: 0.6994, Fake_score: 0.3032
2025-07-04 07:17:33,336 [INFO] src.gan.trainer: Epoch 13, Batch 700/976, Stage 0, D_loss: 0.4356, G_loss: -3.6845, Num_std: 0.071392, Num_range: 4.0000, Real_score: 0.7003, Fake_score: 0.3014
2025-07-04 07:17:48,346 [INFO] src.gan.trainer: Epoch 13, Batch 800/976, Stage 0, D_loss: 0.4864, G_loss: -3.6834, Num_std: 0.071622, Num_range: 4.0000, Real_score: 0.6939, Fake_score: 0.3017
2025-07-04 07:18:03,382 [INFO] src.gan.trainer: Epoch 13, Batch 900/976, Stage 0, D_loss: 0.4712, G_loss: -3.6849, Num_std: 0.083760, Num_range: 4.0000, Real_score: 0.6984, Fake_score: 0.3039
2025-07-04 07:18:14,740 [INFO] __main__: 第14个epoch完成. 指标: {'d_loss': 0.47442453156118564, 'd_adv_loss': 0.04198068031491564, 'd_ctr_loss': 0.4119139839818729, 'd_reg_loss': 0.136865777070405, 'real_d_score': 0.6973130800074248, 'fake_d_score': 0.30228478650822027, 'g_loss': -3.646578081094665, 'g_adv_loss': 0.48766311148151054, 'g_feature_loss': 1.0552627089245066, 'g_diversity_loss': -0.9973018967999447, 'g_numeric_diversity_loss': -0.398712804042251, 'fake_d_score_g': 0.3023411221496883, 'temperature': 3.4547720014980263, 'numeric_std': 0.07114084311137833, 'numeric_range': 3.99999171642007, 'training_stage': 0.0}
2025-07-04 07:18:14,740 [INFO] __main__: 在第14个epoch评估改进的GAN
2025-07-04 07:18:14,756 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:18:14,756 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:18:14,757 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:18:14,757 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 07:18:14,757 [INFO] __main__:   特征平均标准差: 0.000008
2025-07-04 07:18:14,757 [INFO] __main__:   零标准差特征数: 1.0
2025-07-04 07:18:14,757 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:18:14,948 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_14.pt
2025-07-04 07:18:14,949 [INFO] __main__: 开始第15/30个epoch (阶段: 0)
2025-07-04 07:18:15,215 [INFO] src.gan.trainer: Epoch 14, Batch 0/976, Stage 0, D_loss: 0.4687, G_loss: -3.5722, Num_std: 0.052261, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.3025
2025-07-04 07:18:30,424 [INFO] src.gan.trainer: Epoch 14, Batch 100/976, Stage 0, D_loss: 0.4756, G_loss: -3.6885, Num_std: 0.065517, Num_range: 4.0000, Real_score: 0.6963, Fake_score: 0.3058
2025-07-04 07:18:45,568 [INFO] src.gan.trainer: Epoch 14, Batch 200/976, Stage 0, D_loss: 0.4356, G_loss: -3.6847, Num_std: 0.069677, Num_range: 4.0000, Real_score: 0.6988, Fake_score: 0.3020
2025-07-04 07:19:00,733 [INFO] src.gan.trainer: Epoch 14, Batch 300/976, Stage 0, D_loss: 0.4594, G_loss: -3.6870, Num_std: 0.076372, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3035
2025-07-04 07:19:15,985 [INFO] src.gan.trainer: Epoch 14, Batch 400/976, Stage 0, D_loss: 0.4971, G_loss: -3.1411, Num_std: 0.064065, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.3021
2025-07-04 07:19:31,188 [INFO] src.gan.trainer: Epoch 14, Batch 500/976, Stage 0, D_loss: 0.4767, G_loss: -3.6848, Num_std: 0.072406, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.3027
2025-07-04 07:19:46,204 [INFO] src.gan.trainer: Epoch 14, Batch 600/976, Stage 0, D_loss: 0.4472, G_loss: -3.6825, Num_std: 0.087429, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3015
2025-07-04 07:20:01,228 [INFO] src.gan.trainer: Epoch 14, Batch 700/976, Stage 0, D_loss: 0.4751, G_loss: -3.6847, Num_std: 0.059758, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3001
2025-07-04 07:20:16,242 [INFO] src.gan.trainer: Epoch 14, Batch 800/976, Stage 0, D_loss: 0.4577, G_loss: -3.6834, Num_std: 0.075487, Num_range: 4.0000, Real_score: 0.6983, Fake_score: 0.3041
2025-07-04 07:20:31,354 [INFO] src.gan.trainer: Epoch 14, Batch 900/976, Stage 0, D_loss: 0.4788, G_loss: -3.6831, Num_std: 0.093391, Num_range: 4.0000, Real_score: 0.6966, Fake_score: 0.3031
2025-07-04 07:20:42,649 [INFO] __main__: 第15个epoch完成. 指标: {'d_loss': 0.4702373280754833, 'd_adv_loss': 0.04194470595751624, 'd_ctr_loss': 0.4077603347965932, 'd_reg_loss': 0.1368819128294461, 'real_d_score': 0.6974393360194607, 'fake_d_score': 0.30237572460023115, 'g_loss': -3.634929304097682, 'g_adv_loss': 0.48763920071590794, 'g_feature_loss': 1.063330643978276, 'g_diversity_loss': -0.9980047304855015, 'g_numeric_diversity_loss': -0.39721628845430074, 'fake_d_score_g': 0.30232709185143936, 'temperature': 3.4513172294964614, 'numeric_std': 0.07080018476308714, 'numeric_range': 3.9999917004603525, 'training_stage': 0.0}
2025-07-04 07:20:42,650 [INFO] __main__: 开始第16/30个epoch (阶段: 0)
2025-07-04 07:20:42,919 [INFO] src.gan.trainer: Epoch 15, Batch 0/976, Stage 0, D_loss: 0.4854, G_loss: -3.6795, Num_std: 0.053047, Num_range: 4.0000, Real_score: 0.6976, Fake_score: 0.3033
2025-07-04 07:20:57,935 [INFO] src.gan.trainer: Epoch 15, Batch 100/976, Stage 0, D_loss: 0.5038, G_loss: -3.6868, Num_std: 0.058914, Num_range: 4.0000, Real_score: 0.6959, Fake_score: 0.3039
2025-07-04 07:21:12,936 [INFO] src.gan.trainer: Epoch 15, Batch 200/976, Stage 0, D_loss: 0.4583, G_loss: -3.6834, Num_std: 0.078634, Num_range: 4.0000, Real_score: 0.6959, Fake_score: 0.3011
2025-07-04 07:21:28,144 [INFO] src.gan.trainer: Epoch 15, Batch 300/976, Stage 0, D_loss: 0.4582, G_loss: -3.6756, Num_std: 0.106218, Num_range: 3.9999, Real_score: 0.6978, Fake_score: 0.3038
2025-07-04 07:21:43,204 [INFO] src.gan.trainer: Epoch 15, Batch 400/976, Stage 0, D_loss: 0.4906, G_loss: -3.6860, Num_std: 0.068360, Num_range: 4.0000, Real_score: 0.6979, Fake_score: 0.3039
2025-07-04 07:21:58,298 [INFO] src.gan.trainer: Epoch 15, Batch 500/976, Stage 0, D_loss: 0.4612, G_loss: -3.6836, Num_std: 0.066402, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3030
2025-07-04 07:22:13,960 [INFO] src.gan.trainer: Epoch 15, Batch 600/976, Stage 0, D_loss: 0.4902, G_loss: -3.6837, Num_std: 0.075565, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.3021
2025-07-04 07:22:29,405 [INFO] src.gan.trainer: Epoch 15, Batch 700/976, Stage 0, D_loss: 0.4932, G_loss: -3.6789, Num_std: 0.084137, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3015
2025-07-04 07:22:44,577 [INFO] src.gan.trainer: Epoch 15, Batch 800/976, Stage 0, D_loss: 0.4472, G_loss: -3.6825, Num_std: 0.098327, Num_range: 4.0000, Real_score: 0.6992, Fake_score: 0.3033
2025-07-04 07:22:59,662 [INFO] src.gan.trainer: Epoch 15, Batch 900/976, Stage 0, D_loss: 0.4358, G_loss: -3.6807, Num_std: 0.077575, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.3018
2025-07-04 07:23:11,133 [INFO] __main__: 第16个epoch完成. 指标: {'d_loss': 0.46727122735903903, 'd_adv_loss': 0.04190944516115259, 'd_ctr_loss': 0.40482458569964397, 'd_reg_loss': 0.1369146380390301, 'real_d_score': 0.697455027064337, 'fake_d_score': 0.30234599119571404, 'g_loss': -3.6280951359680764, 'g_adv_loss': 0.4875761589106641, 'g_feature_loss': 1.0542074975257374, 'g_diversity_loss': -0.998188495269564, 'g_numeric_diversity_loss': -0.39627414675622874, 'fake_d_score_g': 0.3023608224415552, 'temperature': 3.4478659122669706, 'numeric_std': 0.07219759593239163, 'numeric_range': 3.999964686527934, 'training_stage': 0.0}
2025-07-04 07:23:11,133 [INFO] __main__: 在第16个epoch评估改进的GAN
2025-07-04 07:23:11,149 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:23:11,149 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:23:11,150 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:23:11,150 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 07:23:11,150 [INFO] __main__:   特征平均标准差: 0.000013
2025-07-04 07:23:11,150 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 07:23:11,150 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:23:11,340 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_16.pt
2025-07-04 07:23:11,341 [INFO] __main__: 开始第17/30个epoch (阶段: 0)
2025-07-04 07:23:11,612 [INFO] src.gan.trainer: Epoch 16, Batch 0/976, Stage 0, D_loss: 0.4525, G_loss: -3.6843, Num_std: 0.061927, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3051
2025-07-04 07:23:26,916 [INFO] src.gan.trainer: Epoch 16, Batch 100/976, Stage 0, D_loss: 0.4659, G_loss: -3.6844, Num_std: 0.075717, Num_range: 4.0000, Real_score: 0.6963, Fake_score: 0.3058
2025-07-04 07:23:42,125 [INFO] src.gan.trainer: Epoch 16, Batch 200/976, Stage 0, D_loss: 0.4512, G_loss: -3.6807, Num_std: 0.086424, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3014
2025-07-04 07:23:57,237 [INFO] src.gan.trainer: Epoch 16, Batch 300/976, Stage 0, D_loss: 0.4266, G_loss: -3.6813, Num_std: 0.066155, Num_range: 4.0000, Real_score: 0.6997, Fake_score: 0.3041
2025-07-04 07:24:12,419 [INFO] src.gan.trainer: Epoch 16, Batch 400/976, Stage 0, D_loss: 0.4516, G_loss: -3.6874, Num_std: 0.066032, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3006
2025-07-04 07:24:27,594 [INFO] src.gan.trainer: Epoch 16, Batch 500/976, Stage 0, D_loss: 0.4694, G_loss: -3.6802, Num_std: 0.080180, Num_range: 4.0000, Real_score: 0.6998, Fake_score: 0.2995
2025-07-04 07:24:42,746 [INFO] src.gan.trainer: Epoch 16, Batch 600/976, Stage 0, D_loss: 0.4527, G_loss: -3.6856, Num_std: 0.066979, Num_range: 4.0000, Real_score: 0.6950, Fake_score: 0.3056
2025-07-04 07:24:57,862 [INFO] src.gan.trainer: Epoch 16, Batch 700/976, Stage 0, D_loss: 0.4642, G_loss: -3.6744, Num_std: 0.098327, Num_range: 4.0000, Real_score: 0.6992, Fake_score: 0.3039
2025-07-04 07:25:13,084 [INFO] src.gan.trainer: Epoch 16, Batch 800/976, Stage 0, D_loss: 0.4502, G_loss: -3.6771, Num_std: 0.064656, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3017
2025-07-04 07:25:28,331 [INFO] src.gan.trainer: Epoch 16, Batch 900/976, Stage 0, D_loss: 0.4775, G_loss: -3.6669, Num_std: 0.100095, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.2999
2025-07-04 07:25:39,739 [INFO] __main__: 第17个epoch完成. 指标: {'d_loss': 0.46346297838770983, 'd_adv_loss': 0.04197557258694508, 'd_ctr_loss': 0.4009665984538247, 'd_reg_loss': 0.13680537725936207, 'real_d_score': 0.6973681201944588, 'fake_d_score': 0.3023771838758318, 'g_loss': -3.6157505714380322, 'g_adv_loss': 0.48766319108668965, 'g_feature_loss': 1.0861095605122904, 'g_diversity_loss': -0.9978992992330112, 'g_numeric_diversity_loss': -0.39497749281467, 'fake_d_score_g': 0.3023435889636391, 'temperature': 3.4444180463548033, 'numeric_std': 0.07102833876105438, 'numeric_range': 3.999976241197746, 'training_stage': 0.0}
2025-07-04 07:25:39,739 [INFO] __main__: 开始第18/30个epoch (阶段: 0)
2025-07-04 07:25:40,008 [INFO] src.gan.trainer: Epoch 17, Batch 0/976, Stage 0, D_loss: 0.4410, G_loss: -3.6741, Num_std: 0.075406, Num_range: 4.0000, Real_score: 0.7008, Fake_score: 0.3025
2025-07-04 07:25:55,236 [INFO] src.gan.trainer: Epoch 17, Batch 100/976, Stage 0, D_loss: 0.4343, G_loss: -3.6768, Num_std: 0.074432, Num_range: 4.0000, Real_score: 0.7012, Fake_score: 0.3026
2025-07-04 07:26:10,454 [INFO] src.gan.trainer: Epoch 17, Batch 200/976, Stage 0, D_loss: 0.4129, G_loss: -3.6849, Num_std: 0.082020, Num_range: 4.0000, Real_score: 0.6939, Fake_score: 0.3031
2025-07-04 07:26:25,737 [INFO] src.gan.trainer: Epoch 17, Batch 300/976, Stage 0, D_loss: 0.4338, G_loss: -3.6823, Num_std: 0.061258, Num_range: 4.0000, Real_score: 0.6991, Fake_score: 0.3007
2025-07-04 07:26:40,951 [INFO] src.gan.trainer: Epoch 17, Batch 400/976, Stage 0, D_loss: 0.5015, G_loss: -3.6809, Num_std: 0.076889, Num_range: 4.0000, Real_score: 0.6979, Fake_score: 0.2995
2025-07-04 07:26:56,084 [INFO] src.gan.trainer: Epoch 17, Batch 500/976, Stage 0, D_loss: 0.4410, G_loss: -3.6852, Num_std: 0.045697, Num_range: 4.0000, Real_score: 0.6964, Fake_score: 0.3019
2025-07-04 07:27:11,355 [INFO] src.gan.trainer: Epoch 17, Batch 600/976, Stage 0, D_loss: 0.4619, G_loss: -3.6950, Num_std: 0.068422, Num_range: 4.0000, Real_score: 0.6952, Fake_score: 0.3056
2025-07-04 07:27:26,752 [INFO] src.gan.trainer: Epoch 17, Batch 700/976, Stage 0, D_loss: 0.4941, G_loss: -3.6859, Num_std: 0.072358, Num_range: 4.0000, Real_score: 0.6956, Fake_score: 0.3015
2025-07-04 07:27:42,065 [INFO] src.gan.trainer: Epoch 17, Batch 800/976, Stage 0, D_loss: 0.4930, G_loss: -3.6834, Num_std: 0.061797, Num_range: 4.0000, Real_score: 0.6990, Fake_score: 0.3006
2025-07-04 07:27:57,207 [INFO] src.gan.trainer: Epoch 17, Batch 900/976, Stage 0, D_loss: 0.4726, G_loss: -3.6826, Num_std: 0.065932, Num_range: 4.0000, Real_score: 0.7001, Fake_score: 0.3020
2025-07-04 07:28:08,528 [INFO] __main__: 第18个epoch完成. 指标: {'d_loss': 0.45993449053437013, 'd_adv_loss': 0.04186913224899021, 'd_ctr_loss': 0.39752036186515344, 'd_reg_loss': 0.13696663753419616, 'real_d_score': 0.6975189587254016, 'fake_d_score': 0.302349198029423, 'g_loss': -3.637067849864886, 'g_adv_loss': 0.4876842808507672, 'g_feature_loss': 1.0743689016118405, 'g_diversity_loss': -0.9982681430143411, 'g_numeric_diversity_loss': -0.39752530428718363, 'fake_d_score_g': 0.3022547396195068, 'temperature': 3.4409736283084036, 'numeric_std': 0.07067006560955541, 'numeric_range': 3.999980849972189, 'training_stage': 0.0}
2025-07-04 07:28:08,528 [INFO] __main__: 在第18个epoch评估改进的GAN
2025-07-04 07:28:08,543 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:28:08,544 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:28:08,545 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:28:08,545 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 07:28:08,545 [INFO] __main__:   特征平均标准差: 0.000007
2025-07-04 07:28:08,545 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 07:28:08,545 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:28:08,733 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_18.pt
2025-07-04 07:28:08,734 [INFO] __main__: 开始第19/30个epoch (阶段: 0)
2025-07-04 07:28:09,022 [INFO] src.gan.trainer: Epoch 18, Batch 0/976, Stage 0, D_loss: 0.4537, G_loss: -3.6866, Num_std: 0.082320, Num_range: 4.0000, Real_score: 0.6984, Fake_score: 0.3026
2025-07-04 07:28:24,273 [INFO] src.gan.trainer: Epoch 18, Batch 100/976, Stage 0, D_loss: 0.4503, G_loss: -3.6803, Num_std: 0.078444, Num_range: 4.0000, Real_score: 0.6993, Fake_score: 0.3019
2025-07-04 07:28:39,326 [INFO] src.gan.trainer: Epoch 18, Batch 200/976, Stage 0, D_loss: 0.4672, G_loss: -3.6900, Num_std: 0.060033, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3009
2025-07-04 07:28:54,366 [INFO] src.gan.trainer: Epoch 18, Batch 300/976, Stage 0, D_loss: 0.4578, G_loss: -3.6841, Num_std: 0.063475, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3019
2025-07-04 07:29:09,496 [INFO] src.gan.trainer: Epoch 18, Batch 400/976, Stage 0, D_loss: 0.4659, G_loss: -3.6857, Num_std: 0.054016, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3036
2025-07-04 07:29:24,540 [INFO] src.gan.trainer: Epoch 18, Batch 500/976, Stage 0, D_loss: 0.4587, G_loss: -3.6867, Num_std: 0.079358, Num_range: 4.0000, Real_score: 0.6968, Fake_score: 0.3034
2025-07-04 07:29:39,587 [INFO] src.gan.trainer: Epoch 18, Batch 600/976, Stage 0, D_loss: 0.4606, G_loss: -3.6844, Num_std: 0.067433, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3030
2025-07-04 07:29:54,591 [INFO] src.gan.trainer: Epoch 18, Batch 700/976, Stage 0, D_loss: 0.5131, G_loss: -3.6808, Num_std: 0.078873, Num_range: 4.0000, Real_score: 0.6997, Fake_score: 0.3036
2025-07-04 07:30:09,782 [INFO] src.gan.trainer: Epoch 18, Batch 800/976, Stage 0, D_loss: 0.4715, G_loss: -3.6868, Num_std: 0.081326, Num_range: 4.0000, Real_score: 0.7005, Fake_score: 0.3051
2025-07-04 07:30:24,860 [INFO] src.gan.trainer: Epoch 18, Batch 900/976, Stage 0, D_loss: 0.4283, G_loss: -3.6866, Num_std: 0.073290, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3027
2025-07-04 07:30:36,286 [INFO] __main__: 第19个epoch完成. 指标: {'d_loss': 0.4567517409192736, 'd_adv_loss': 0.04186114223796081, 'd_ctr_loss': 0.3943423319485829, 'd_reg_loss': 0.1369884406201176, 'real_d_score': 0.697499006253774, 'fake_d_score': 0.3023098784147715, 'g_loss': -3.6390251338156188, 'g_adv_loss': 0.4876857405335032, 'g_feature_loss': 1.0480387972596585, 'g_diversity_loss': -0.9983623878918383, 'g_numeric_diversity_loss': -0.39759380326611404, 'fake_d_score_g': 0.30226610497128775, 'temperature': 3.4375326546800222, 'numeric_std': 0.07058856999161456, 'numeric_range': 3.999991450071995, 'training_stage': 0.0}
2025-07-04 07:30:36,287 [INFO] __main__: 开始第20/30个epoch (阶段: 0)
2025-07-04 07:30:36,555 [INFO] src.gan.trainer: Epoch 19, Batch 0/976, Stage 0, D_loss: 0.4212, G_loss: -3.6818, Num_std: 0.091734, Num_range: 4.0000, Real_score: 0.6991, Fake_score: 0.3030
2025-07-04 07:30:51,747 [INFO] src.gan.trainer: Epoch 19, Batch 100/976, Stage 0, D_loss: 0.4468, G_loss: -3.6865, Num_std: 0.051072, Num_range: 4.0000, Real_score: 0.7000, Fake_score: 0.3042
2025-07-04 07:31:06,937 [INFO] src.gan.trainer: Epoch 19, Batch 200/976, Stage 0, D_loss: 0.4235, G_loss: -3.6891, Num_std: 0.068988, Num_range: 4.0000, Real_score: 0.6949, Fake_score: 0.3014
2025-07-04 07:31:22,216 [INFO] src.gan.trainer: Epoch 19, Batch 300/976, Stage 0, D_loss: 0.4569, G_loss: -3.6818, Num_std: 0.096795, Num_range: 4.0000, Real_score: 0.6991, Fake_score: 0.3028
2025-07-04 07:31:37,331 [INFO] src.gan.trainer: Epoch 19, Batch 400/976, Stage 0, D_loss: 0.4032, G_loss: -3.6905, Num_std: 0.068681, Num_range: 4.0000, Real_score: 0.6991, Fake_score: 0.3049
2025-07-04 07:31:52,486 [INFO] src.gan.trainer: Epoch 19, Batch 500/976, Stage 0, D_loss: 0.4248, G_loss: -3.6861, Num_std: 0.059235, Num_range: 4.0000, Real_score: 0.6948, Fake_score: 0.3027
2025-07-04 07:32:07,794 [INFO] src.gan.trainer: Epoch 19, Batch 600/976, Stage 0, D_loss: 0.4134, G_loss: -3.6859, Num_std: 0.072073, Num_range: 4.0000, Real_score: 0.6959, Fake_score: 0.3019
2025-07-04 07:32:22,997 [INFO] src.gan.trainer: Epoch 19, Batch 700/976, Stage 0, D_loss: 0.4611, G_loss: -3.6865, Num_std: 0.083181, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.2997
2025-07-04 07:32:38,260 [INFO] src.gan.trainer: Epoch 19, Batch 800/976, Stage 0, D_loss: 0.4103, G_loss: -3.6854, Num_std: 0.080611, Num_range: 4.0000, Real_score: 0.6979, Fake_score: 0.3040
2025-07-04 07:32:53,354 [INFO] src.gan.trainer: Epoch 19, Batch 900/976, Stage 0, D_loss: 0.4742, G_loss: -3.6840, Num_std: 0.055760, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3005
2025-07-04 07:33:04,754 [INFO] __main__: 第20个epoch完成. 指标: {'d_loss': 0.45320767582562127, 'd_adv_loss': 0.041851305337928135, 'd_ctr_loss': 0.3908033928238468, 'd_reg_loss': 0.1370198458402617, 'real_d_score': 0.6975326789695699, 'fake_d_score': 0.30231589176615753, 'g_loss': -3.6227740675129496, 'g_adv_loss': 0.48762252198053835, 'g_feature_loss': 1.0311360232369173, 'g_diversity_loss': -0.9983780659898057, 'g_numeric_diversity_loss': -0.3954469157698374, 'fake_d_score_g': 0.30231106877734104, 'temperature': 3.434095122025459, 'numeric_std': 0.07103350584905162, 'numeric_range': 3.999988876161029, 'training_stage': 0.0}
2025-07-04 07:33:04,754 [INFO] __main__: 在第20个epoch评估改进的GAN
2025-07-04 07:33:04,770 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:33:04,770 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:33:04,771 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:33:04,771 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 07:33:04,772 [INFO] __main__:   特征平均标准差: 0.032960
2025-07-04 07:33:04,772 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 07:33:04,772 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:33:05,039 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_20.pt
2025-07-04 07:33:05,040 [INFO] __main__: 开始第21/30个epoch (阶段: 0)
2025-07-04 07:33:05,324 [INFO] src.gan.trainer: Epoch 20, Batch 0/976, Stage 0, D_loss: 0.4117, G_loss: -3.6901, Num_std: 0.079059, Num_range: 4.0000, Real_score: 0.6998, Fake_score: 0.3021
2025-07-04 07:33:20,509 [INFO] src.gan.trainer: Epoch 20, Batch 100/976, Stage 0, D_loss: 0.4165, G_loss: -3.6859, Num_std: 0.067981, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3065
2025-07-04 07:33:35,585 [INFO] src.gan.trainer: Epoch 20, Batch 200/976, Stage 0, D_loss: 0.4862, G_loss: -3.6826, Num_std: 0.077447, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3030
2025-07-04 07:33:50,645 [INFO] src.gan.trainer: Epoch 20, Batch 300/976, Stage 0, D_loss: 0.4189, G_loss: -3.6858, Num_std: 0.060163, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3014
2025-07-04 07:34:05,970 [INFO] src.gan.trainer: Epoch 20, Batch 400/976, Stage 0, D_loss: 0.4230, G_loss: -3.6715, Num_std: 0.054157, Num_range: 4.0000, Real_score: 0.6988, Fake_score: 0.3025
2025-07-04 07:34:21,220 [INFO] src.gan.trainer: Epoch 20, Batch 500/976, Stage 0, D_loss: 0.4218, G_loss: -3.6865, Num_std: 0.073958, Num_range: 4.0000, Real_score: 0.6996, Fake_score: 0.3021
2025-07-04 07:34:36,221 [INFO] src.gan.trainer: Epoch 20, Batch 600/976, Stage 0, D_loss: 0.4517, G_loss: -3.6835, Num_std: 0.083011, Num_range: 4.0000, Real_score: 0.6981, Fake_score: 0.3022
2025-07-04 07:34:51,234 [INFO] src.gan.trainer: Epoch 20, Batch 700/976, Stage 0, D_loss: 0.4602, G_loss: -3.6860, Num_std: 0.064643, Num_range: 4.0000, Real_score: 0.6979, Fake_score: 0.3047
2025-07-04 07:35:06,348 [INFO] src.gan.trainer: Epoch 20, Batch 800/976, Stage 0, D_loss: 0.4417, G_loss: -3.6836, Num_std: 0.077895, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.3018
2025-07-04 07:35:21,367 [INFO] src.gan.trainer: Epoch 20, Batch 900/976, Stage 0, D_loss: 0.4640, G_loss: -3.6834, Num_std: 0.051726, Num_range: 4.0000, Real_score: 0.6989, Fake_score: 0.3015
2025-07-04 07:35:32,742 [INFO] __main__: 第21个epoch完成. 指标: {'d_loss': 0.4494368665408897, 'd_adv_loss': 0.04182661990787769, 'd_ctr_loss': 0.38704762772702767, 'd_reg_loss': 0.13708412042651008, 'real_d_score': 0.6975859118411781, 'fake_d_score': 0.30231100162033137, 'g_loss': -3.6457392426177124, 'g_adv_loss': 0.4875323250279072, 'g_feature_loss': 1.02518497226261, 'g_diversity_loss': -0.9980698099838262, 'g_numeric_diversity_loss': -0.39830762602877434, 'fake_d_score_g': 0.30236797213880107, 'temperature': 3.4306610269033375, 'numeric_std': 0.07056657752458895, 'numeric_range': 3.999989261555541, 'training_stage': 0.0}
2025-07-04 07:35:32,742 [INFO] __main__: 开始第22/30个epoch (阶段: 0)
2025-07-04 07:35:33,012 [INFO] src.gan.trainer: Epoch 21, Batch 0/976, Stage 0, D_loss: 0.4503, G_loss: -3.6909, Num_std: 0.070271, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.3028
2025-07-04 07:35:48,166 [INFO] src.gan.trainer: Epoch 21, Batch 100/976, Stage 0, D_loss: 0.4542, G_loss: -3.6858, Num_std: 0.089670, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3009
2025-07-04 07:36:03,491 [INFO] src.gan.trainer: Epoch 21, Batch 200/976, Stage 0, D_loss: 0.4099, G_loss: -3.6883, Num_std: 0.071875, Num_range: 4.0000, Real_score: 0.6988, Fake_score: 0.3016
2025-07-04 07:36:18,545 [INFO] src.gan.trainer: Epoch 21, Batch 300/976, Stage 0, D_loss: 0.4272, G_loss: -3.2467, Num_std: 0.068826, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3021
2025-07-04 07:36:33,887 [INFO] src.gan.trainer: Epoch 21, Batch 400/976, Stage 0, D_loss: 0.4521, G_loss: -3.6840, Num_std: 0.084966, Num_range: 4.0000, Real_score: 0.6954, Fake_score: 0.3014
2025-07-04 07:36:48,925 [INFO] src.gan.trainer: Epoch 21, Batch 500/976, Stage 0, D_loss: 0.4431, G_loss: -3.6843, Num_std: 0.082471, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3044
2025-07-04 07:37:04,137 [INFO] src.gan.trainer: Epoch 21, Batch 600/976, Stage 0, D_loss: 0.4249, G_loss: -3.6839, Num_std: 0.079476, Num_range: 4.0000, Real_score: 0.6983, Fake_score: 0.3008
2025-07-04 07:37:19,304 [INFO] src.gan.trainer: Epoch 21, Batch 700/976, Stage 0, D_loss: 0.4634, G_loss: -3.6893, Num_std: 0.066731, Num_range: 4.0000, Real_score: 0.6954, Fake_score: 0.3010
2025-07-04 07:37:34,321 [INFO] src.gan.trainer: Epoch 21, Batch 800/976, Stage 0, D_loss: 0.4277, G_loss: -3.6862, Num_std: 0.075844, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3031
2025-07-04 07:37:49,771 [INFO] src.gan.trainer: Epoch 21, Batch 900/976, Stage 0, D_loss: 0.4181, G_loss: -3.6880, Num_std: 0.069194, Num_range: 4.0000, Real_score: 0.6997, Fake_score: 0.3011
2025-07-04 07:38:01,125 [INFO] __main__: 第22个epoch完成. 指标: {'d_loss': 0.44705073074361373, 'd_adv_loss': 0.041789499283233895, 'd_ctr_loss': 0.38468673126008646, 'd_reg_loss': 0.1371633330604336, 'real_d_score': 0.6976204502289418, 'fake_d_score': 0.3022690050181795, 'g_loss': -3.633393241387738, 'g_adv_loss': 0.48772377257179306, 'g_feature_loss': 1.0263994040954967, 'g_diversity_loss': -0.9982402431753163, 'g_numeric_diversity_loss': -0.3967745926007226, 'fake_d_score_g': 0.30220879806724715, 'temperature': 3.4272303658764303, 'numeric_std': 0.07086903535578505, 'numeric_range': 3.999991450886262, 'training_stage': 0.0}
2025-07-04 07:38:01,126 [INFO] __main__: 在第22个epoch评估改进的GAN
2025-07-04 07:38:01,142 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:38:01,142 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:38:01,143 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:38:01,143 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 07:38:01,143 [INFO] __main__:   特征平均标准差: 0.000009
2025-07-04 07:38:01,143 [INFO] __main__:   零标准差特征数: 2.0
2025-07-04 07:38:01,143 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:38:01,409 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_22.pt
2025-07-04 07:38:01,410 [INFO] __main__: 开始第23/30个epoch (阶段: 0)
2025-07-04 07:38:01,688 [INFO] src.gan.trainer: Epoch 22, Batch 0/976, Stage 0, D_loss: 0.4071, G_loss: -3.6854, Num_std: 0.071465, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.3006
2025-07-04 07:38:16,735 [INFO] src.gan.trainer: Epoch 22, Batch 100/976, Stage 0, D_loss: 0.4361, G_loss: -3.6905, Num_std: 0.073199, Num_range: 4.0000, Real_score: 0.6993, Fake_score: 0.3005
2025-07-04 07:38:31,808 [INFO] src.gan.trainer: Epoch 22, Batch 200/976, Stage 0, D_loss: 0.4121, G_loss: -3.6849, Num_std: 0.077232, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.2995
2025-07-04 07:38:46,987 [INFO] src.gan.trainer: Epoch 22, Batch 300/976, Stage 0, D_loss: 0.4697, G_loss: -3.6865, Num_std: 0.065974, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3019
2025-07-04 07:39:02,285 [INFO] src.gan.trainer: Epoch 22, Batch 400/976, Stage 0, D_loss: 0.4465, G_loss: -3.6855, Num_std: 0.087075, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3047
2025-07-04 07:39:17,514 [INFO] src.gan.trainer: Epoch 22, Batch 500/976, Stage 0, D_loss: 0.4147, G_loss: -3.6856, Num_std: 0.055273, Num_range: 4.0000, Real_score: 0.6981, Fake_score: 0.3001
2025-07-04 07:39:32,779 [INFO] src.gan.trainer: Epoch 22, Batch 600/976, Stage 0, D_loss: 0.4189, G_loss: -3.6844, Num_std: 0.084718, Num_range: 4.0000, Real_score: 0.6979, Fake_score: 0.3045
2025-07-04 07:39:47,993 [INFO] src.gan.trainer: Epoch 22, Batch 700/976, Stage 0, D_loss: 0.4335, G_loss: -3.6873, Num_std: 0.078124, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3017
2025-07-04 07:40:03,245 [INFO] src.gan.trainer: Epoch 22, Batch 800/976, Stage 0, D_loss: 0.4403, G_loss: -3.6869, Num_std: 0.066270, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3015
2025-07-04 07:40:18,467 [INFO] src.gan.trainer: Epoch 22, Batch 900/976, Stage 0, D_loss: 0.4939, G_loss: -3.6854, Num_std: 0.087198, Num_range: 4.0000, Real_score: 0.6957, Fake_score: 0.3039
2025-07-04 07:40:29,930 [INFO] __main__: 第23个epoch完成. 指标: {'d_loss': 0.4438502664570915, 'd_adv_loss': 0.0417508739275766, 'd_ctr_loss': 0.38150803155464214, 'd_reg_loss': 0.13727573345064142, 'real_d_score': 0.6976847493501963, 'fake_d_score': 0.30223441520919553, 'g_loss': -3.631295847823548, 'g_adv_loss': 0.4877521470290881, 'g_feature_loss': 1.0209986507851887, 'g_diversity_loss': -0.9984662519574499, 'g_numeric_diversity_loss': -0.39645395926683347, 'fake_d_score_g': 0.3021787589039306, 'temperature': 3.423803135510544, 'numeric_std': 0.06992694347216064, 'numeric_range': 3.9999863731730794, 'training_stage': 0.0}
2025-07-04 07:40:29,930 [INFO] __main__: 开始第24/30个epoch (阶段: 0)
2025-07-04 07:40:30,208 [INFO] src.gan.trainer: Epoch 23, Batch 0/976, Stage 0, D_loss: 0.4580, G_loss: -3.6901, Num_std: 0.066732, Num_range: 4.0000, Real_score: 0.6981, Fake_score: 0.3051
2025-07-04 07:40:45,738 [INFO] src.gan.trainer: Epoch 23, Batch 100/976, Stage 0, D_loss: 0.4414, G_loss: -3.6910, Num_std: 0.075792, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3034
2025-07-04 07:41:01,074 [INFO] src.gan.trainer: Epoch 23, Batch 200/976, Stage 0, D_loss: 0.4403, G_loss: -3.6916, Num_std: 0.068960, Num_range: 4.0000, Real_score: 0.6996, Fake_score: 0.3008
2025-07-04 07:41:16,294 [INFO] src.gan.trainer: Epoch 23, Batch 300/976, Stage 0, D_loss: 0.4561, G_loss: -3.6895, Num_std: 0.065456, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3019
2025-07-04 07:41:31,512 [INFO] src.gan.trainer: Epoch 23, Batch 400/976, Stage 0, D_loss: 0.4584, G_loss: -3.6858, Num_std: 0.067438, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3013
2025-07-04 07:41:46,727 [INFO] src.gan.trainer: Epoch 23, Batch 500/976, Stage 0, D_loss: 0.4033, G_loss: -3.6940, Num_std: 0.060912, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3014
2025-07-04 07:42:02,050 [INFO] src.gan.trainer: Epoch 23, Batch 600/976, Stage 0, D_loss: 0.4403, G_loss: -3.6905, Num_std: 0.068375, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3026
2025-07-04 07:42:17,294 [INFO] src.gan.trainer: Epoch 23, Batch 700/976, Stage 0, D_loss: 0.4121, G_loss: -3.6873, Num_std: 0.062646, Num_range: 4.0000, Real_score: 0.6961, Fake_score: 0.3007
2025-07-04 07:42:32,567 [INFO] src.gan.trainer: Epoch 23, Batch 800/976, Stage 0, D_loss: 0.4566, G_loss: -3.6896, Num_std: 0.080790, Num_range: 4.0000, Real_score: 0.6957, Fake_score: 0.3014
2025-07-04 07:42:47,834 [INFO] src.gan.trainer: Epoch 23, Batch 900/976, Stage 0, D_loss: 0.4510, G_loss: -3.6828, Num_std: 0.080657, Num_range: 4.0000, Real_score: 0.6995, Fake_score: 0.3036
2025-07-04 07:42:59,306 [INFO] __main__: 第24个epoch完成. 指标: {'d_loss': 0.44131388493859414, 'd_adv_loss': 0.041747769913407194, 'd_ctr_loss': 0.3789742449145827, 'd_reg_loss': 0.13727913297651742, 'real_d_score': 0.697713029128118, 'fake_d_score': 0.30225822360056304, 'g_loss': -3.640288447319968, 'g_adv_loss': 0.48775030348877413, 'g_feature_loss': 0.9965850747714597, 'g_diversity_loss': -0.998501513295812, 'g_numeric_diversity_loss': -0.39742081134473267, 'fake_d_score_g': 0.3021855545968147, 'temperature': 3.420379332375029, 'numeric_std': 0.07055274697319723, 'numeric_range': 3.9999813673600446, 'training_stage': 0.0}
2025-07-04 07:42:59,306 [INFO] __main__: 在第24个epoch评估改进的GAN
2025-07-04 07:42:59,322 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:42:59,322 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:42:59,323 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:42:59,323 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 07:42:59,323 [INFO] __main__:   特征平均标准差: 0.000012
2025-07-04 07:42:59,323 [INFO] __main__:   零标准差特征数: 4.0
2025-07-04 07:42:59,323 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:42:59,590 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_24.pt
2025-07-04 07:42:59,590 [INFO] __main__: 开始第25/30个epoch (阶段: 0)
2025-07-04 07:42:59,872 [INFO] src.gan.trainer: Epoch 24, Batch 0/976, Stage 0, D_loss: 0.4373, G_loss: -3.6906, Num_std: 0.066706, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3026
2025-07-04 07:43:15,149 [INFO] src.gan.trainer: Epoch 24, Batch 100/976, Stage 0, D_loss: 0.4330, G_loss: -3.6916, Num_std: 0.052173, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3006
2025-07-04 07:43:30,362 [INFO] src.gan.trainer: Epoch 24, Batch 200/976, Stage 0, D_loss: 0.4347, G_loss: -3.6876, Num_std: 0.075987, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3008
2025-07-04 07:43:45,599 [INFO] src.gan.trainer: Epoch 24, Batch 300/976, Stage 0, D_loss: 0.4471, G_loss: -3.6898, Num_std: 0.067516, Num_range: 4.0000, Real_score: 0.6952, Fake_score: 0.3044
2025-07-04 07:44:00,850 [INFO] src.gan.trainer: Epoch 24, Batch 400/976, Stage 0, D_loss: 0.3876, G_loss: -3.6795, Num_std: 0.086074, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3023
2025-07-04 07:44:15,959 [INFO] src.gan.trainer: Epoch 24, Batch 500/976, Stage 0, D_loss: 0.4936, G_loss: -3.6835, Num_std: 0.084528, Num_range: 4.0000, Real_score: 0.6963, Fake_score: 0.3032
2025-07-04 07:44:31,024 [INFO] src.gan.trainer: Epoch 24, Batch 600/976, Stage 0, D_loss: 0.4486, G_loss: -3.6891, Num_std: 0.080727, Num_range: 4.0000, Real_score: 0.6952, Fake_score: 0.3044
2025-07-04 07:44:46,103 [INFO] src.gan.trainer: Epoch 24, Batch 700/976, Stage 0, D_loss: 0.4059, G_loss: -3.6844, Num_std: 0.091712, Num_range: 4.0000, Real_score: 0.6982, Fake_score: 0.3025
2025-07-04 07:45:01,406 [INFO] src.gan.trainer: Epoch 24, Batch 800/976, Stage 0, D_loss: 0.4364, G_loss: -3.6915, Num_std: 0.072321, Num_range: 4.0000, Real_score: 0.6954, Fake_score: 0.3045
2025-07-04 07:45:16,514 [INFO] src.gan.trainer: Epoch 24, Batch 900/976, Stage 0, D_loss: 0.4107, G_loss: -3.6754, Num_std: 0.081268, Num_range: 4.0000, Real_score: 0.6999, Fake_score: 0.2994
2025-07-04 07:45:27,845 [INFO] __main__: 第25个epoch完成. 指标: {'d_loss': 0.4381833245031174, 'd_adv_loss': 0.04173912304896304, 'd_ctr_loss': 0.37585269232265306, 'd_reg_loss': 0.13727672184344195, 'real_d_score': 0.6977128205118619, 'fake_d_score': 0.30225465022271747, 'g_loss': -3.664585674313902, 'g_adv_loss': 0.48762983903085266, 'g_feature_loss': 0.9941494718112586, 'g_diversity_loss': -0.9984115913428911, 'g_numeric_diversity_loss': -0.4004389242945955, 'fake_d_score_g': 0.3022674626869253, 'temperature': 3.4169589530427884, 'numeric_std': 0.0702026951536633, 'numeric_range': 3.9999882381796192, 'training_stage': 0.0}
2025-07-04 07:45:27,846 [INFO] __main__: 开始第26/30个epoch (阶段: 0)
2025-07-04 07:45:28,113 [INFO] src.gan.trainer: Epoch 25, Batch 0/976, Stage 0, D_loss: 0.4677, G_loss: -3.6850, Num_std: 0.085618, Num_range: 4.0000, Real_score: 0.6983, Fake_score: 0.3015
2025-07-04 07:45:43,250 [INFO] src.gan.trainer: Epoch 25, Batch 100/976, Stage 0, D_loss: 0.3936, G_loss: -3.6911, Num_std: 0.065165, Num_range: 4.0000, Real_score: 0.6991, Fake_score: 0.3032
2025-07-04 07:45:58,471 [INFO] src.gan.trainer: Epoch 25, Batch 200/976, Stage 0, D_loss: 0.4082, G_loss: -3.6918, Num_std: 0.061884, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3011
2025-07-04 07:46:13,790 [INFO] src.gan.trainer: Epoch 25, Batch 300/976, Stage 0, D_loss: 0.3985, G_loss: -3.6897, Num_std: 0.075989, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3005
2025-07-04 07:46:29,109 [INFO] src.gan.trainer: Epoch 25, Batch 400/976, Stage 0, D_loss: 0.4458, G_loss: -3.6875, Num_std: 0.068721, Num_range: 4.0000, Real_score: 0.6955, Fake_score: 0.2992
2025-07-04 07:46:44,317 [INFO] src.gan.trainer: Epoch 25, Batch 500/976, Stage 0, D_loss: 0.4363, G_loss: -3.6858, Num_std: 0.087184, Num_range: 4.0000, Real_score: 0.7000, Fake_score: 0.3023
2025-07-04 07:46:59,594 [INFO] src.gan.trainer: Epoch 25, Batch 600/976, Stage 0, D_loss: 0.4929, G_loss: -3.6872, Num_std: 0.068068, Num_range: 4.0000, Real_score: 0.6968, Fake_score: 0.3040
2025-07-04 07:47:14,834 [INFO] src.gan.trainer: Epoch 25, Batch 700/976, Stage 0, D_loss: 0.5097, G_loss: -3.6886, Num_std: 0.061083, Num_range: 4.0000, Real_score: 0.6973, Fake_score: 0.3014
2025-07-04 07:47:30,032 [INFO] src.gan.trainer: Epoch 25, Batch 800/976, Stage 0, D_loss: 0.4069, G_loss: -3.6848, Num_std: 0.053511, Num_range: 4.0000, Real_score: 0.6971, Fake_score: 0.3004
2025-07-04 07:47:45,202 [INFO] src.gan.trainer: Epoch 25, Batch 900/976, Stage 0, D_loss: 0.4544, G_loss: -3.6827, Num_std: 0.081541, Num_range: 4.0000, Real_score: 0.6977, Fake_score: 0.3021
2025-07-04 07:47:56,617 [INFO] __main__: 第26个epoch完成. 指标: {'d_loss': 0.43568195988897374, 'd_adv_loss': 0.041710864466263854, 'd_ctr_loss': 0.3733607062184418, 'd_reg_loss': 0.137402589509233, 'real_d_score': 0.6977703835754114, 'fake_d_score': 0.30220936397548576, 'g_loss': -3.658274273077648, 'g_adv_loss': 0.48760882516702003, 'g_feature_loss': 0.9949914008134696, 'g_diversity_loss': -0.9983323432830847, 'g_numeric_diversity_loss': -0.3996625408845769, 'fake_d_score_g': 0.30227231827712137, 'temperature': 3.4135419940896403, 'numeric_std': 0.07035818667191544, 'numeric_range': 3.9999947367144437, 'training_stage': 0.0}
2025-07-04 07:47:56,617 [INFO] __main__: 在第26个epoch评估改进的GAN
2025-07-04 07:47:56,632 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:47:56,633 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:47:56,634 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:47:56,634 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 07:47:56,634 [INFO] __main__:   特征平均标准差: 0.000006
2025-07-04 07:47:56,634 [INFO] __main__:   零标准差特征数: 5.0
2025-07-04 07:47:56,634 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:47:56,902 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_26.pt
2025-07-04 07:47:56,903 [INFO] __main__: 开始第27/30个epoch (阶段: 0)
2025-07-04 07:47:57,173 [INFO] src.gan.trainer: Epoch 26, Batch 0/976, Stage 0, D_loss: 0.4130, G_loss: -3.6837, Num_std: 0.067796, Num_range: 4.0000, Real_score: 0.6975, Fake_score: 0.3035
2025-07-04 07:48:12,442 [INFO] src.gan.trainer: Epoch 26, Batch 100/976, Stage 0, D_loss: 0.4492, G_loss: -3.6932, Num_std: 0.072089, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3034
2025-07-04 07:48:27,760 [INFO] src.gan.trainer: Epoch 26, Batch 200/976, Stage 0, D_loss: 0.4349, G_loss: -3.6918, Num_std: 0.080966, Num_range: 4.0000, Real_score: 0.6983, Fake_score: 0.3021
2025-07-04 07:48:43,034 [INFO] src.gan.trainer: Epoch 26, Batch 300/976, Stage 0, D_loss: 0.4622, G_loss: -3.6860, Num_std: 0.070707, Num_range: 4.0000, Real_score: 0.6981, Fake_score: 0.3010
2025-07-04 07:48:58,440 [INFO] src.gan.trainer: Epoch 26, Batch 400/976, Stage 0, D_loss: 0.4615, G_loss: -3.6909, Num_std: 0.075106, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.3049
2025-07-04 07:49:13,669 [INFO] src.gan.trainer: Epoch 26, Batch 500/976, Stage 0, D_loss: 0.4497, G_loss: -3.6801, Num_std: 0.075048, Num_range: 4.0000, Real_score: 0.7003, Fake_score: 0.3015
2025-07-04 07:49:28,906 [INFO] src.gan.trainer: Epoch 26, Batch 600/976, Stage 0, D_loss: 0.4972, G_loss: -3.6865, Num_std: 0.064414, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3004
2025-07-04 07:49:44,138 [INFO] src.gan.trainer: Epoch 26, Batch 700/976, Stage 0, D_loss: 0.4304, G_loss: -3.6857, Num_std: 0.071072, Num_range: 4.0000, Real_score: 0.7000, Fake_score: 0.3030
2025-07-04 07:49:59,382 [INFO] src.gan.trainer: Epoch 26, Batch 800/976, Stage 0, D_loss: 0.4596, G_loss: -3.6885, Num_std: 0.063120, Num_range: 4.0000, Real_score: 0.6997, Fake_score: 0.3004
2025-07-04 07:50:14,652 [INFO] src.gan.trainer: Epoch 26, Batch 900/976, Stage 0, D_loss: 0.4633, G_loss: -3.6367, Num_std: 0.070351, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3038
2025-07-04 07:50:26,138 [INFO] __main__: 第27个epoch完成. 指标: {'d_loss': 0.43270656649695044, 'd_adv_loss': 0.041721952579091066, 'd_ctr_loss': 0.3703845263626733, 'd_reg_loss': 0.13733391111075396, 'real_d_score': 0.6977297263677976, 'fake_d_score': 0.30222202226763895, 'g_loss': -3.6562714857237575, 'g_adv_loss': 0.4875959865952447, 'g_feature_loss': 0.9805880112807606, 'g_diversity_loss': -0.9983189625696083, 'g_numeric_diversity_loss': -0.3993222390815533, 'fake_d_score_g': 0.3022659830443684, 'temperature': 3.4101284520955066, 'numeric_std': 0.07042428582294415, 'numeric_range': 3.99999289638032, 'training_stage': 0.0}
2025-07-04 07:50:26,139 [INFO] __main__: 开始第28/30个epoch (阶段: 0)
2025-07-04 07:50:26,411 [INFO] src.gan.trainer: Epoch 27, Batch 0/976, Stage 0, D_loss: 0.4353, G_loss: -3.6868, Num_std: 0.077614, Num_range: 4.0000, Real_score: 0.6959, Fake_score: 0.3026
2025-07-04 07:50:41,686 [INFO] src.gan.trainer: Epoch 27, Batch 100/976, Stage 0, D_loss: 0.4105, G_loss: -3.6907, Num_std: 0.072499, Num_range: 4.0000, Real_score: 0.6969, Fake_score: 0.3032
2025-07-04 07:50:57,055 [INFO] src.gan.trainer: Epoch 27, Batch 200/976, Stage 0, D_loss: 0.4192, G_loss: -2.4632, Num_std: 0.054799, Num_range: 4.0000, Real_score: 0.6964, Fake_score: 0.3030
2025-07-04 07:51:12,315 [INFO] src.gan.trainer: Epoch 27, Batch 300/976, Stage 0, D_loss: 0.4239, G_loss: -3.6838, Num_std: 0.087801, Num_range: 4.0000, Real_score: 0.6958, Fake_score: 0.3012
2025-07-04 07:51:27,489 [INFO] src.gan.trainer: Epoch 27, Batch 400/976, Stage 0, D_loss: 0.3883, G_loss: -3.6861, Num_std: 0.083490, Num_range: 4.0000, Real_score: 0.6979, Fake_score: 0.3018
2025-07-04 07:51:42,572 [INFO] src.gan.trainer: Epoch 27, Batch 500/976, Stage 0, D_loss: 0.4364, G_loss: -3.6935, Num_std: 0.071580, Num_range: 4.0000, Real_score: 0.6994, Fake_score: 0.3025
2025-07-04 07:51:57,708 [INFO] src.gan.trainer: Epoch 27, Batch 600/976, Stage 0, D_loss: 0.4429, G_loss: -3.6842, Num_std: 0.049687, Num_range: 4.0000, Real_score: 0.6992, Fake_score: 0.3010
2025-07-04 07:52:12,887 [INFO] src.gan.trainer: Epoch 27, Batch 700/976, Stage 0, D_loss: 0.4553, G_loss: -3.6894, Num_std: 0.058064, Num_range: 4.0000, Real_score: 0.6988, Fake_score: 0.3046
2025-07-04 07:52:27,991 [INFO] src.gan.trainer: Epoch 27, Batch 800/976, Stage 0, D_loss: 0.4318, G_loss: -3.6901, Num_std: 0.056869, Num_range: 4.0000, Real_score: 0.6949, Fake_score: 0.3043
2025-07-04 07:52:43,131 [INFO] src.gan.trainer: Epoch 27, Batch 900/976, Stage 0, D_loss: 0.4008, G_loss: -3.6898, Num_std: 0.063673, Num_range: 4.0000, Real_score: 0.6974, Fake_score: 0.3012
2025-07-04 07:52:54,536 [INFO] __main__: 第28个epoch完成. 指标: {'d_loss': 0.43035001008481266, 'd_adv_loss': 0.0416740632051083, 'd_ctr_loss': 0.3680523000962909, 'd_reg_loss': 0.13749097156353665, 'real_d_score': 0.6978361387966103, 'fake_d_score': 0.3021926693312942, 'g_loss': -3.655988896418297, 'g_adv_loss': 0.48770110124895144, 'g_feature_loss': 0.975603797905595, 'g_diversity_loss': -0.9982527425217489, 'g_numeric_diversity_loss': -0.39927718040782784, 'fake_d_score_g': 0.3021781775143629, 'temperature': 3.4067183236434393, 'numeric_std': 0.06954791648877777, 'numeric_range': 3.9999915980250464, 'training_stage': 0.0}
2025-07-04 07:52:54,536 [INFO] __main__: 在第28个epoch评估改进的GAN
2025-07-04 07:52:54,552 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:52:54,552 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:52:54,553 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:52:54,553 [INFO] __main__:   数值特征质量评分: 0.200
2025-07-04 07:52:54,553 [INFO] __main__:   特征平均标准差: 0.000009
2025-07-04 07:52:54,553 [INFO] __main__:   零标准差特征数: 5.0
2025-07-04 07:52:54,554 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:52:54,824 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_28.pt
2025-07-04 07:52:54,825 [INFO] __main__: 开始第29/30个epoch (阶段: 0)
2025-07-04 07:52:55,098 [INFO] src.gan.trainer: Epoch 28, Batch 0/976, Stage 0, D_loss: 0.3929, G_loss: -3.6868, Num_std: 0.076732, Num_range: 4.0000, Real_score: 0.6990, Fake_score: 0.3001
2025-07-04 07:53:10,418 [INFO] src.gan.trainer: Epoch 28, Batch 100/976, Stage 0, D_loss: 0.4070, G_loss: -3.6891, Num_std: 0.058143, Num_range: 4.0000, Real_score: 0.7012, Fake_score: 0.3026
2025-07-04 07:53:25,689 [INFO] src.gan.trainer: Epoch 28, Batch 200/976, Stage 0, D_loss: 0.4195, G_loss: -2.4076, Num_std: 0.060403, Num_range: 4.0000, Real_score: 0.6990, Fake_score: 0.3022
2025-07-04 07:53:40,925 [INFO] src.gan.trainer: Epoch 28, Batch 300/976, Stage 0, D_loss: 0.4133, G_loss: -3.6868, Num_std: 0.050498, Num_range: 4.0000, Real_score: 0.7002, Fake_score: 0.3038
2025-07-04 07:53:56,252 [INFO] src.gan.trainer: Epoch 28, Batch 400/976, Stage 0, D_loss: 0.3971, G_loss: -3.6923, Num_std: 0.073742, Num_range: 4.0000, Real_score: 0.6953, Fake_score: 0.3045
2025-07-04 07:54:11,423 [INFO] src.gan.trainer: Epoch 28, Batch 500/976, Stage 0, D_loss: 0.4440, G_loss: -3.6939, Num_std: 0.074815, Num_range: 4.0000, Real_score: 0.6957, Fake_score: 0.3001
2025-07-04 07:54:26,497 [INFO] src.gan.trainer: Epoch 28, Batch 600/976, Stage 0, D_loss: 0.4208, G_loss: -2.7071, Num_std: 0.046901, Num_range: 4.0000, Real_score: 0.6999, Fake_score: 0.3019
2025-07-04 07:54:41,598 [INFO] src.gan.trainer: Epoch 28, Batch 700/976, Stage 0, D_loss: 0.3913, G_loss: -3.6887, Num_std: 0.083950, Num_range: 4.0000, Real_score: 0.6983, Fake_score: 0.3014
2025-07-04 07:54:56,750 [INFO] src.gan.trainer: Epoch 28, Batch 800/976, Stage 0, D_loss: 0.4393, G_loss: -3.6836, Num_std: 0.063719, Num_range: 4.0000, Real_score: 0.6972, Fake_score: 0.3004
2025-07-04 07:55:11,861 [INFO] src.gan.trainer: Epoch 28, Batch 900/976, Stage 0, D_loss: 0.4300, G_loss: -3.6879, Num_std: 0.086146, Num_range: 4.0000, Real_score: 0.6980, Fake_score: 0.2998
2025-07-04 07:55:23,198 [INFO] __main__: 第29个epoch完成. 指标: {'d_loss': 0.42774869953511174, 'd_adv_loss': 0.0416659566566165, 'd_ctr_loss': 0.3654589804469561, 'd_reg_loss': 0.13749174421011914, 'real_d_score': 0.6978299090730369, 'fake_d_score': 0.30218105054781136, 'g_loss': -3.6513429208775614, 'g_adv_loss': 0.48760766470855704, 'g_feature_loss': 0.9622780819693225, 'g_diversity_loss': -0.998180443879033, 'g_numeric_diversity_loss': -0.39861050561131545, 'fake_d_score_g': 0.3022394765351638, 'temperature': 3.4033116053197623, 'numeric_std': 0.07019215191375935, 'numeric_range': 3.999989070771823, 'training_stage': 0.0}
2025-07-04 07:55:23,198 [INFO] __main__: 开始第30/30个epoch (阶段: 0)
2025-07-04 07:55:23,469 [INFO] src.gan.trainer: Epoch 29, Batch 0/976, Stage 0, D_loss: 0.4365, G_loss: -3.6834, Num_std: 0.072697, Num_range: 4.0000, Real_score: 0.6986, Fake_score: 0.3023
2025-07-04 07:55:38,545 [INFO] src.gan.trainer: Epoch 29, Batch 100/976, Stage 0, D_loss: 0.4404, G_loss: -2.8368, Num_std: 0.053459, Num_range: 4.0000, Real_score: 0.6959, Fake_score: 0.3016
2025-07-04 07:55:53,724 [INFO] src.gan.trainer: Epoch 29, Batch 200/976, Stage 0, D_loss: 0.4185, G_loss: -3.6883, Num_std: 0.076310, Num_range: 4.0000, Real_score: 0.6984, Fake_score: 0.3030
2025-07-04 07:56:08,802 [INFO] src.gan.trainer: Epoch 29, Batch 300/976, Stage 0, D_loss: 0.4290, G_loss: -3.6891, Num_std: 0.071689, Num_range: 4.0000, Real_score: 0.6962, Fake_score: 0.3015
2025-07-04 07:56:23,894 [INFO] src.gan.trainer: Epoch 29, Batch 400/976, Stage 0, D_loss: 0.4264, G_loss: -3.6877, Num_std: 0.075091, Num_range: 4.0000, Real_score: 0.6985, Fake_score: 0.3037
2025-07-04 07:56:38,978 [INFO] src.gan.trainer: Epoch 29, Batch 500/976, Stage 0, D_loss: 0.4328, G_loss: -3.6931, Num_std: 0.072767, Num_range: 4.0000, Real_score: 0.6970, Fake_score: 0.3024
2025-07-04 07:56:54,125 [INFO] src.gan.trainer: Epoch 29, Batch 600/976, Stage 0, D_loss: 0.4245, G_loss: -3.6841, Num_std: 0.094147, Num_range: 4.0000, Real_score: 0.6996, Fake_score: 0.3006
2025-07-04 07:57:09,266 [INFO] src.gan.trainer: Epoch 29, Batch 700/976, Stage 0, D_loss: 0.4271, G_loss: -3.6908, Num_std: 0.066705, Num_range: 4.0000, Real_score: 0.6978, Fake_score: 0.3001
2025-07-04 07:57:24,467 [INFO] src.gan.trainer: Epoch 29, Batch 800/976, Stage 0, D_loss: 0.4471, G_loss: -3.6943, Num_std: 0.061826, Num_range: 4.0000, Real_score: 0.6964, Fake_score: 0.2979
2025-07-04 07:57:39,553 [INFO] src.gan.trainer: Epoch 29, Batch 900/976, Stage 0, D_loss: 0.4706, G_loss: -3.4050, Num_std: 0.041799, Num_range: 4.0000, Real_score: 0.6987, Fake_score: 0.3021
2025-07-04 07:57:50,865 [INFO] __main__: 第30个epoch完成. 指标: {'d_loss': 0.4253108829076665, 'd_adv_loss': 0.04162262385085105, 'd_ctr_loss': 0.36304684792507846, 'd_reg_loss': 0.13760940255749904, 'real_d_score': 0.6978904543230756, 'fake_d_score': 0.30213606644605084, 'g_loss': -3.656825455241517, 'g_adv_loss': 0.487889043335381, 'g_feature_loss': 0.9547627717663678, 'g_diversity_loss': -0.9981562015625953, 'g_numeric_diversity_loss': -0.3992870544729745, 'fake_d_score_g': 0.302027331298736, 'temperature': 3.3999082937145353, 'numeric_std': 0.06954148440073342, 'numeric_range': 3.9999941010944173, 'training_stage': 0.0}
2025-07-04 07:57:50,865 [INFO] __main__: 在第30个epoch评估改进的GAN
2025-07-04 07:57:50,881 [INFO] __main__: 生成的数值数据形状: (1000, 13)
2025-07-04 07:57:50,881 [INFO] __main__: 数值数据范围: [-2.0000, 2.0000]
2025-07-04 07:57:50,882 [INFO] __main__: 改进GAN评估指标:
2025-07-04 07:57:50,882 [INFO] __main__:   数值特征质量评分: 0.400
2025-07-04 07:57:50,882 [INFO] __main__:   特征平均标准差: 0.000231
2025-07-04 07:57:50,882 [INFO] __main__:   零标准差特征数: 0.0
2025-07-04 07:57:50,882 [INFO] __main__: 训练质量: 需要改进
2025-07-04 07:57:51,145 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_checkpoint_epoch_30.pt
2025-07-04 07:57:51,333 [INFO] src.gan.trainer: 检查点已保存到 /data/balanced_gan/improved_final_model.pt
2025-07-04 07:57:51,334 [INFO] __main__: 改进的训练成功完成！
2025-07-04 07:57:51,334 [INFO] __main__: 模型保存到: /data/balanced_gan
