from synthetic_ctr_generator import FeatureConfig, InteractionGroup, TargetConfig, SyntheticCTRGenerator
import pandas as pd
from sklearn.utils import shuffle

def create_feature_configs():
    """
    Create feature configurations for CTR data generation
    """
    user_categorical_features = [
        FeatureConfig(
            name='user_gender',
            feature_type='categorical',
            distribution='categorical',
            params={'categories': [f'gender_{i}' for i in range(3)],
                   'probabilities': [0.6, 0.3, 0.1]}
        ),
        FeatureConfig(
            name='user_region',
            feature_type='categorical',
            distribution='categorical',
            params={'categories': [f'region_{i}' for i in range(333)],
                   'probabilities': [0.8/10 if i < 10 else 0.2/323 for i in range(333)]}
        )
    ]

    device_categorical_features = [
        FeatureConfig(
            name='device_type',
            feature_type='categorical',
            distribution='categorical',
            params={'categories': [f'device_{i}' for i in range(4)]}
        ),
    ]

    ad_categorical_features = [
        FeatureConfig(
            name='ad_category',
            feature_type='categorical',
            distribution='categorical',
            params={'categories': [f'category_{i}' for i in range(44)]}
        ),
        FeatureConfig(
            name='ad_placement',
            feature_type='categorical',
            distribution='categorical',
            params={'categories': [f'place_{i}' for i in range(7)]}
        )
    ]

    context_categorical_features = [
        FeatureConfig(
            name='hour_of_day',
            feature_type='categorical',
            distribution='categorical',
            params={'categories': [str(i).zfill(2) for i in range(24)]}
        ),
        FeatureConfig(
            name='day_of_week',
            feature_type='categorical',
            distribution='categorical',
            params={'categories': ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun']}
        ),
        FeatureConfig(
            name='scene_type',
            feature_type='categorical',
            distribution='categorical',
            params={'categories': [f'scene_{i}' for i in range(5)]}
        )
    ]

    numerical_features = [
        FeatureConfig(
            name='user_age',
            feature_type='numerical',
            distribution='normal',
            params={'mean': 30, 'std': 15} 
        ),
        FeatureConfig(
            name='item_price',
            feature_type='numerical',
            distribution='lognormal',
            params={'mean': 4, 'std': 0.5} 
        )
    ]

    return (user_categorical_features + device_categorical_features + 
            ad_categorical_features + context_categorical_features + numerical_features)

def create_target_configs():
    """Create target configurations for features"""
    target_configs = {
        'user_age': TargetConfig(
            value=(0, 100, 30), 
            is_categorical=False
        ),
        'item_price': TargetConfig(
            value=(1, 30000, 100),
            is_categorical=False
        ),

        'user_gender': TargetConfig(
            value={
                'gender_0': 0.8,
                'gender_1': 0.3,
                'gender_2': 0.01
            },
            is_categorical=True
        ),
        'user_region': TargetConfig(
            value={
                **{f'region_{i}': 0.8 for i in range(5)}, 
                **{f'region_{i}': 0.3 for i in range(5, 15)}, 
                **{f'region_{i}': 0.05 for i in range(15, 333)} 
            },
            is_categorical=True
        ),
        'device_type': TargetConfig(
            value={
                'device_0': 0.5,   
                'device_1': 0.1,    
                'device_2': 0.1,    
                'device_3': 0.05  
            },
            is_categorical=True
        ),
        'ad_category': TargetConfig(
            value={str(f'category_{i}'): (0.6 if i < 5 else 0.1) for i in range(44)}, 
            is_categorical=True
        ),
        'ad_placement': TargetConfig(
            value={f'place_{i}': (0.6 if i < 3 else 0.1) for i in range(7)},
            is_categorical=True
        ),
        'hour_of_day': TargetConfig(
            value={'00': 0.1, '01': 0.01, '02': 0.01, '03': 0.01, '04': 0.01, 
                    '05': 0.01, '06': 0.01, '07': 0.01, '08': 0.01, '09': 0.01, 
                    '10': 0.01, '11': 0.01, '12': 0.2, '13': 0.01, '14': 0.01, 
                    '15': 0.01, '16': 0.01, '17': 0.01, '18': 0.01, '19': 0.2, 
                    '20': 0.4, '21': 0.4, '22': 0.4, '23': 0.3
            },
            is_categorical=True
        ),
        'day_of_week': TargetConfig(
            value={
                'sat': 0.8,
                'sun': 0.7,
                'fri': 0.7,
                'mon': 0.2,
                'tue': 0.2,
                'wed': 0.1,
                'thu': 0.1
            },
            is_categorical=True
        ),
        'scene_type': TargetConfig(
            value={f'scene_{i}': (0.3 if i < 2 else 0.01) for i in range(5)},
            is_categorical=True
        )
    }
    return target_configs

def create_interaction_groups():
    """
    Create feature interaction groups with flexible target values
    """
    # use target_configs
    target_configs = create_target_configs()

    def get_high_weight_value(feature: str) -> str:
        """
        get the max value
        """
        config = target_configs[feature]
        if config.is_categorical:
            return max(config.value.items(), key=lambda x: x[1])[0]
        else:
            return config.value[2]

    # get all the features
    available_features = list(target_configs.keys())
    
    # First-order interactions
    first_order_groups = [InteractionGroup(
            features=['user_gender'],
            weight=0.8,
            target_values={'user_gender': 'gender_0'}  
        ),
        InteractionGroup(
            features=['user_region'],
            weight=0.8,
            target_values={'user_region': 'region_0'}  
        ),
        InteractionGroup(
            features=['ad_placement'],
            weight=0.8,
            target_values={'ad_placement': 'place_0'}  
        ),
        InteractionGroup(
            features=['day_of_week'],
            weight=0.8,
            target_values={'day_of_week': 'sat'} 
        ),
        
    ]

    # Second-order interactions
    second_order_groups = []
    business_combinations = [
        (['user_age', 'ad_category'], 0.9),
        (['user_gender', 'hour_of_day'], 0.9),
        (['user_region', 'ad_category'], 0.9),
        (['item_price', 'ad_placement'], 0.9),
        ]

    for features, weight in business_combinations:
        if all(f in target_configs for f in features):
            second_order_groups.append(
                InteractionGroup(
                    features=features,
                    weight=weight,
                    target_values={f: get_high_weight_value(f) for f in features}
                )
            )

    # Third-order interactions
    third_order_combinations = [
        (['user_age', 'user_gender', 'item_price'], 0.9),
        (['user_age', 'user_region', 'ad_placement'], 0.9),
    ]

    third_order_groups = [
        InteractionGroup(
            features=features,
            weight=weight,
            target_values={f: get_high_weight_value(f) for f in features}
        )
        for features, weight in third_order_combinations
        if all(f in target_configs for f in features)
    ]

    return first_order_groups, second_order_groups, third_order_groups

def create_generator():
    """
    Create the synthetic CTR data generator with all configurations
    """
    feature_configs = create_feature_configs()
    target_configs = create_target_configs()
    first_groups, second_groups, third_groups = create_interaction_groups()
    
    return SyntheticCTRGenerator(
        feature_configs=feature_configs,
        first_groups=first_groups,
        second_groups=second_groups,
        third_groups=third_groups,
        target_configs=target_configs,
        base_ctr=0.01, 
        random_seed=2024
    )

def downsample_positive(data, target_col, target_positive_ratio):
    positive_samples = data[data[target_col] == 1]
    negative_samples = data[data[target_col] == 0]
    
    n_negative = len(negative_samples)
    n_positive_target = int((n_negative * target_positive_ratio) / (1 - target_positive_ratio))
    positive_downsampled = positive_samples.sample(n=n_positive_target, random_state=2024)
    
    balanced_data = pd.concat([positive_downsampled, negative_samples])
    
    balanced_data = shuffle(balanced_data, random_state=2024)
    
    return balanced_data