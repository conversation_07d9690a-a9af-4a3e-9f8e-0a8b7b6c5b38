from synthetic_configs import create_generator, downsample_positive
from sklearn.model_selection import train_test_split
import logging
import os
import sys
from pathlib import Path

if __name__ == "__main__":
    training_data_num = 30000000
    valid_data_num = 4000000
    test_data_num = 4000000
    ratio = 0.25
    log_dir = './data/logs'
    save_path = '/data/ctr/Synthetic'
    total_num = training_data_num + valid_data_num + test_data_num

    # Logging
    os.makedirs(log_dir, exist_ok=True)
    log_filename = f"synthetic.log"
    log_path = Path(log_dir) / log_filename
    logging.basicConfig(level=logging.INFO,
                        format='%(asctime)s %(levelname)s %(message)s',
                        datefmt='%Y-%m-%d %H:%M:%S',
                        handlers=[logging.StreamHandler(sys.stdout),
                                  logging.FileHandler(log_path)]
    )
    logger = logging.getLogger(__name__)

    logging.info(f"Total number of data points: {total_num}")
    
    # Create generator
    generator = create_generator()
    # Generate dataset
    data = generator.generate_batch(total_num)

    # check ctr 
    logging.info("Train samples: total/{:d}, pos/{:.0f}, neg/{:.0f}, ratio/{:.2f}%" \
                    .format(len(data), data['click'].sum(), len(data)-data['click'].sum(),
                    100 * data['click'].sum() / len(data)))

    if 100 * data['click'].sum() / len(data) >= ratio:
        logging.info("adjust ratio")
        data_balance = downsample_positive(data, target_col='click', target_positive_ratio=ratio)



    # Split data
    train_data, remaining = train_test_split(data_balance,
                                                   train_size=0.8,
                                                   stratify=data_balance['click'],
                                                   random_state=2024
                                                   )
    
    valid_data, test_data = train_test_split(remaining,
                                              test_size=0.5,
                                              stratify=remaining['click'],
                                              random_state=2024
                                              )
    
    logging.info("Train samples: total/{:d}, pos/{:.0f}, neg/{:.0f}, ratio/{:.2f}%" \
                    .format(len(train_data), train_data['click'].sum(), len(train_data)-train_data['click'].sum(),
                    100 * train_data['click'].sum() / len(train_data)))
    logging.info("Validation samples: total/{:d}, pos/{:.0f}, neg/{:.0f}, ratio/{:.2f}%" \
                    .format(len(valid_data), valid_data['click'].sum(), len(valid_data)-valid_data['click'].sum(),
                    100 * valid_data['click'].sum() / len(valid_data)))
    logging.info("Test samples: total/{:d}, pos/{:.0f}, neg/{:.0f}, ratio/{:.2f}%" \
                   .format(len(test_data), test_data['click'].sum(), len(test_data)-test_data['click'].sum(),
                    100 * test_data['click'].sum() / len(test_data)))

    # Save data
    train_path = os.path.join(save_path, 'train.csv')
    valid_path = os.path.join(save_path, 'valid.csv')
    test_path = os.path.join(save_path, 'test.csv')
    
    train_data.to_csv(train_path, index=False)
    valid_data.to_csv(valid_path, index=False)
    test_data.to_csv(test_path, index=False)