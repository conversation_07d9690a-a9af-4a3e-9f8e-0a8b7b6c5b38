import torch
import torch.nn as nn
from .base_model import BaseModel
from ..layers.ShallowComponent import LinearRegressionComponent


class LinearRegressionCTR(BaseModel):
    def __init__(self,
                 feature_mapping,
                 model_id="LinearCTR",
                 gpu=-1,
                 task="binary_classification",
                 learning_rate=1e-3,
                 embedding_initializer="torch.nn.init.normal_(std=1e-4)",
                 regularizer=None,
                 **kwargs):
        """Initialize the Linear Regression CTR model."""

        # Initialize parent class with standard parameter names
        super(LinearRegressionCTR, self).__init__(
            feature_mapping,
            model_id=model_id,
            gpu=gpu,
            embedding_regularizer=regularizer,
            net_regularizer=regularizer,
            **kwargs
        )

        self.prediction_task = task
        self.regularization_factor = regularizer

        self.linear_transformation = LinearRegressionComponent(
            feature_mapping,
            output_activation=self._get_task_activation(task),
            include_bias=True
        )

        self._setup_optimization(kwargs["optimizer"],
                               kwargs["loss"],
                               learning_rate)
        self._initialize_parameters(embedding_initializer)

    def _get_task_activation(self, task_type):
        return self.get_final_activation(task_type)
    
    def _setup_optimization(self, optimizer_config, loss_config, learning_rate):
        self.compile(optimizer_config, loss=loss_config, lr=learning_rate)
    
    def _initialize_parameters(self, initializer_method):
        self.init_weights(embedding_initializer=initializer_method)

    def compute_prediction(self, input_batch):
        """
        Args:
            input_batch (tuple): Tuple containing (features, labels)
                features (torch.Tensor): Input feature tensor
                labels (torch.Tensor): Target label tensor
        
        Returns:
            dict: Dictionary containing:
                - "prediction_loss": Computed loss value with regularization
                - "predicted_scores": Model predictions
        """
        feature_tensor, target_labels = self._prepare_input_data(input_batch)
        
        # Compute linear transformation
        prediction_scores = self._compute_linear_scores(feature_tensor)
        
        # Calculate loss with regularization
        total_loss = self._compute_loss_with_regularization(prediction_scores, target_labels)
        
        output_dict = {
            "prediction_loss": total_loss, 
            "predicted_scores": prediction_scores
        }
        return output_dict

    def forward(self, inputs):
        """
        Args:
            inputs (tuple): Tuple of (X, y) where:
                X (torch.Tensor): Input features
                y (torch.Tensor): Target labels

        Returns:
            dict: Dictionary containing:
                - "y_pred": Predicted probabilities
                - "loss": Computed loss value
        """
        result = self.compute_prediction(inputs)
        return {
            "y_pred": result["predicted_scores"],
            "loss": result["prediction_loss"]
        }

    def _prepare_input_data(self, input_batch):

        return self.inputs_to_device(input_batch)
    
    def _compute_linear_scores(self, feature_tensor):

        return self.linear_transformation(feature_tensor)
    
    def _compute_loss_with_regularization(self, predictions, targets):

        return self.loss_with_reg(predictions, targets)
    
    def get_model_summary(self):
        """        
        Returns:
            dict: Model summary including parameter counts and configuration
        """
        total_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        return {
            "model_type": "Linear Regression CTR",
            "model_class": "LinearRegressionCTR",
            "total_parameters": total_params,
            "prediction_task": self.prediction_task,
            "regularization_factor": self.regularization_factor,
            "has_bias": True,
            "activation_function": "sigmoid" if self.prediction_task == "binary_classification" else "none"
        }
    
    def predict_batch(self, feature_batch):
        """
        Make predictions on a batch of features without computing loss.
        
        Args:
            feature_batch (torch.Tensor): Input features
            
        Returns:
            torch.Tensor: Predicted scores
        """
        self.eval()
        with torch.no_grad():
            scores = self._compute_linear_scores(feature_batch)
        return scores
        
LR_Model = LinearRegressionCTR
