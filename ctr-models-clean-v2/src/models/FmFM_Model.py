import torch
import torch.nn as nn
from .base_model import BaseModel
from ..layers.embedding import Embedding<PERSON>ayer_v3
from ..layers.ShallowComponent import LinearRegressionComponent, LR_Layer


class FieldMatrixedFactorizationModel(BaseModel):
    """
    Args:
        feature_map: Feature specification object
        model_id (str): Unique model identifier
        gpu (int): GPU device ID (-1 for CPU)
        task (str): Type of prediction task
        learning_rate (float): Learning rate for training
        embedding_initializer (str): Weight initialization method
        embedding_dim (int): Size of embedding vectors
        regularizer (float): L2 regularization strength
        field_interaction_mode (str): Type of field interaction ("vectorized" or "matrixed")
        field_interaction_type (str): Compatibility parameter for original FmFM
        **kwargs: Additional parameters
    """
    
    def __init__(self,
                 feature_map,
                 model_id="FieldMatrixFM",
                 gpu=-1,
                 task="binary_classification",
                 learning_rate=1e-3,
                 embedding_initializer="torch.nn.init.normal_(std=1e-4)",
                 embedding_dim=16,
                 regularizer=None,
                 field_interaction_mode="matrixed",
                 field_interaction_type=None,
                 **kwargs):

        if field_interaction_type is not None:
            field_interaction_mode = field_interaction_type

        super(FieldMatrixedFactorizationModel, self).__init__(
            feature_map,
            model_id=model_id,
            gpu=gpu,
            embedding_regularizer=regularizer,
            net_regularizer=regularizer,
            **kwargs
        )

        self.embedding_dimension = embedding_dim
        self.field_count = feature_map.num_fields
        self.interaction_pair_count = int(self.field_count * (self.field_count - 1) / 2)
        self.field_interaction_mode = field_interaction_mode
        self.prediction_task = task

        self._setup_embedding_component(feature_map, embedding_dim)
        self._setup_field_interaction_weights(field_interaction_mode, embedding_dim)
        self._setup_linear_component(feature_map)
        self._setup_triangular_masks()
        self._setup_output_activation(task)

        self._configure_training_process(kwargs, learning_rate)
        self._initialize_model_parameters(embedding_initializer)

    def _setup_embedding_component(self, feature_mapping, dimension):

        self.feature_embedding_processor = EmbeddingLayer_v3(feature_mapping, dimension)
    
    def _setup_field_interaction_weights(self, interaction_mode, embedding_dim):

        if interaction_mode == "vectorized":
            # Vectorized interaction: element-wise multiplication weights
            self.field_interaction_weights = nn.Parameter(
                torch.Tensor(self.interaction_pair_count, embedding_dim)
            )
        elif interaction_mode == "matrixed":
            # Matrixed interaction: matrix multiplication weights
            self.field_interaction_weights = nn.Parameter(
                torch.Tensor(self.interaction_pair_count, embedding_dim, embedding_dim)
            )
        else:
            raise ValueError(f"Unsupported field interaction mode: {interaction_mode}")
        
        nn.init.xavier_normal_(self.field_interaction_weights)
    
    def _setup_linear_component(self, feature_mapping):
        self.linear_transformation_component = LinearRegressionComponent(
            feature_mapping,
            output_activation=None,
            include_bias=False
        )
    
    def _setup_triangular_masks(self):
        # Upper triangular mask for field pair selection
        self.upper_triangular_selector = torch.triu(
            torch.ones(self.field_count, self.field_count - 1), 0
        ).bool().to(self.device)
        
        # Lower triangular mask for field pair selection
        self.lower_triangular_selector = torch.tril(
            torch.ones(self.field_count, self.field_count - 1), -1
        ).bool().to(self.device)
    
    def _setup_output_activation(self, task_type):

        self.output_activation_function = self.get_final_activation(task_type)
    
    def _configure_training_process(self, params, learning_rate):

        self.compile(params["optimizer"], loss=params["loss"], lr=learning_rate)
    
    def _initialize_model_parameters(self, initializer):

        self.init_weights(embedding_initializer=initializer)
    
    def compute_field_interactions(self, input_data):
        """
        Args:
            input_data (tuple): Tuple containing (features, labels)
        
        Returns:
            dict: Dictionary with prediction results
                - "computed_loss": Loss value with regularization
                - "prediction_output": Model prediction scores
        """
        feature_tensor, target_tensor = self._extract_input_data(input_data)
        
        embedding_matrix = self._generate_feature_embeddings(feature_tensor)
        
        field_interaction_tensors = self._prepare_field_interaction_tensors(embedding_matrix)
        
        upper_field_components, lower_field_components = self._extract_triangular_components(
            field_interaction_tensors
        )
        
        transformed_upper_components = self._apply_field_interaction_transformation(
            upper_field_components
        )
        
        interaction_prediction = self._compute_interaction_prediction(
            transformed_upper_components, lower_field_components
        )
        
        linear_prediction = self._compute_linear_prediction(feature_tensor)
        combined_prediction = self._combine_predictions(interaction_prediction, linear_prediction)
        
        final_prediction = self._apply_final_activation(combined_prediction)
        
        computed_loss = self._calculate_prediction_loss(final_prediction, target_tensor)
        
        result_package = {
            "computed_loss": computed_loss, 
            "prediction_output": final_prediction
        }
        return result_package

    def forward(self, inputs):
        """
        Args:
            inputs (tuple): Tuple of (X, y) where:
                X (torch.Tensor): Input features
                y (torch.Tensor): Target labels

        Returns:
            dict: Dictionary containing:
                - "y_pred": Predicted probabilities
                - "loss": Computed loss value
        """
        result = self.compute_field_interactions(inputs)
        return {
            "y_pred": result["prediction_output"],
            "loss": result["computed_loss"]
        }

    def _extract_input_data(self, input_data):

        return self.inputs_to_device(input_data)
    
    def _generate_feature_embeddings(self, feature_tensor):

        return self.feature_embedding_processor(feature_tensor)
    
    def _prepare_field_interaction_tensors(self, embedding_matrix):
        """Prepare tensors for field interaction computation."""
        field_wise_embedding_matrix = embedding_matrix.unsqueeze(2).expand(
            -1, -1, self.field_count - 1, -1
        )
        return field_wise_embedding_matrix
    
    def _extract_triangular_components(self, field_tensors):
        """Extract upper and lower triangular components for field pairs."""
        upper_components = torch.masked_select(
            field_tensors, 
            self.upper_triangular_selector.unsqueeze(-1)
        ).view(-1, self.interaction_pair_count, self.embedding_dimension)
        
        # Extract lower triangular components
        lower_components = torch.masked_select(
            field_tensors.transpose(1, 2), 
            self.lower_triangular_selector.t().unsqueeze(-1)
        ).view(-1, self.interaction_pair_count, self.embedding_dimension)
        
        return upper_components, lower_components
    
    def _apply_field_interaction_transformation(self, upper_components):
        if self.field_interaction_mode == "vectorized":
            return upper_components * self.field_interaction_weights
        elif self.field_interaction_mode == "matrixed":
            # Matrix multiplication for matrixed interaction
            return torch.matmul(
                upper_components.unsqueeze(2), 
                self.field_interaction_weights
            ).squeeze(2)
        else:
            raise ValueError(f"Unknown interaction mode: {self.field_interaction_mode}")
    
    def _compute_interaction_prediction(self, transformed_upper, lower_components):
        # Element-wise multiplication and aggregation
        interaction_products = transformed_upper * lower_components
        flattened_products = interaction_products.flatten(start_dim=1)
        interaction_score = flattened_products.sum(dim=-1, keepdim=True)
        return interaction_score
    
    def _compute_linear_prediction(self, feature_tensor):

        return self.linear_transformation_component(feature_tensor)
    
    def _combine_predictions(self, interaction_pred, linear_pred):

        return interaction_pred + linear_pred
    
    def _apply_final_activation(self, combined_prediction):
        if self.output_activation_function is not None:
            return self.output_activation_function(combined_prediction)
        return combined_prediction
    
    def _calculate_prediction_loss(self, predictions, targets):

        return self.loss_with_reg(predictions, targets)
    
    def get_field_interaction_architecture_info(self):
        total_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        embedding_params = sum(p.numel() for p in self.feature_embedding_processor.parameters())
        interaction_params = self.field_interaction_weights.numel()
        linear_params = sum(p.numel() for p in self.linear_transformation_component.parameters())
        
        return {
            "model_type": "Field-matrixed Factorization Machine",
            "model_class": "FieldMatrixedFactorizationModel",
            "total_parameters": total_params,
            "component_parameters": {
                "embedding": embedding_params,
                "field_interactions": interaction_params,
                "linear": linear_params
            },
            "architecture_config": {
                "embedding_dimension": self.embedding_dimension,
                "field_count": self.field_count,
                "interaction_pair_count": self.interaction_pair_count,
                "field_interaction_mode": self.field_interaction_mode,
                "prediction_task": self.prediction_task
            },
            "interaction_details": {
                "interaction_weight_shape": list(self.field_interaction_weights.shape),
                "triangular_mask_size": f"{self.field_count}x{self.field_count-1}",
                "computation_mode": self.field_interaction_mode
            }
        }
    
    def analyze_field_interaction_patterns(self, feature_batch):
        """
        Analyze field interaction patterns for interpretability.
        
        Args:
            feature_batch (torch.Tensor): Input features
            
        Returns:
            dict: Analysis of field interaction patterns
        """
        self.eval()
        with torch.no_grad():
            embeddings = self._generate_feature_embeddings(feature_batch)
            
            field_tensors = self._prepare_field_interaction_tensors(embeddings)
            
            upper_comp, lower_comp = self._extract_triangular_components(field_tensors)
            
            # Apply transformations
            transformed_upper = self._apply_field_interaction_transformation(upper_comp)
            
            # Compute interaction strengths
            interaction_products = transformed_upper * lower_comp
            interaction_strengths = interaction_products.sum(dim=-1).abs().mean(dim=0)
            
            top_interactions = torch.topk(interaction_strengths, k=min(5, len(interaction_strengths)))
            
            return {
                "interaction_strengths": interaction_strengths.cpu().numpy(),
                "top_interaction_indices": top_interactions.indices.cpu().numpy(),
                "top_interaction_values": top_interactions.values.cpu().numpy(),
                "average_interaction_strength": interaction_strengths.mean().item(),
                "interaction_variance": interaction_strengths.var().item()
            }

FmFM_Model = FieldMatrixedFactorizationModel
