import torch
import torch.nn as nn
from .base_model import BaseModel
from ..layers.embedding import EmbeddingLayer

# Configure CUDA settings for stability
torch.backends.cudnn.enabled = False
torch.backends.cudnn.benchmark = False


def create_activation_function(activation_spec):
    """
    Args:
        activation_spec: Activation function specification
        
    Returns:
        nn.Module: Activation function module
    """
    if isinstance(activation_spec, str):
        activation_name = activation_spec.lower()
        
        if activation_name == "relu":
            return nn.ReLU()
        elif activation_name == "sigmoid":
            return nn.Sigmoid()
        elif activation_name == "tanh":
            return nn.Tanh()
        elif activation_name == "leakyrelu":
            return nn.LeakyReLU()
        elif activation_name == "elu":
            return nn.ELU()
        else:
            return getattr(nn, activation_spec)()
    else:
        return activation_spec


class ConvolutionalClickPredictionModel(BaseModel):
    """
    Args:
        feature_mapping: Feature specification object
        model_identifier (str): Unique model identifier
        device_id (int): GPU device ID (-1 for CPU)
        prediction_task (str): Type of prediction task
        optimization_rate (float): Learning rate for training
        weight_initializer (str): Weight initialization method
        embedding_dimension (int): Size of embedding vectors
        channel_configurations (list): Channel sizes for convolutional layers
        kernel_height_specs (list): Kernel heights for each layer
        activation_function (str): Activation function type
        embedding_regularization (float): L2 regularization for embeddings
        network_regularization (float): L2 regularization for network
        embedding_dropout_rate (float): Dropout rate for embeddings
        **extra_params: Additional parameters
    """
    
    def __init__(self,
                 feature_mapping,
                 model_identifier="ConvClickModel",
                 device_id=-1,
                 prediction_task="binary_classification",
                 optimization_rate=1e-3,
                 weight_initializer="torch.nn.init.normal_(std=1e-4)",
                 embedding_dim=10,
                 channel_configurations=[64, 128, 256],
                 kernel_height_specs=[7, 5, 3],
                 activation_function="Tanh",
                 embedding_regularization=1.0e-05,
                 network_regularization=0,
                 embedding_dropout_rate=0,
                 **extra_params):
        
        super(ConvolutionalClickPredictionModel, self).__init__(
            feature_mapping,
            gpu=device_id,
            embedding_regularizer=embedding_regularization,
            net_regularizer=network_regularization,
            **extra_params
        )
        
        self.embedding_dimension = embedding_dim
        self.channel_configurations = channel_configurations
        self.kernel_height_specs = kernel_height_specs
        self.prediction_task = prediction_task

        self._setup_embedding_component(feature_mapping, embedding_dim, embedding_dropout_rate)
        self._setup_convolutional_processor(feature_mapping.num_fields, channel_configurations,
                                          kernel_height_specs, activation_function)
        self._setup_prediction_component(embedding_dim, channel_configurations)
        self._setup_output_activation(prediction_task)
        
        self._configure_training_process(extra_params, optimization_rate)
        self._initialize_model_parameters(weight_initializer)
    
    def _setup_embedding_component(self, feature_mapping, dimension, dropout_rate):
        self.feature_embedding_processor = EmbeddingLayer(
            feature_mapping, 
            dimension, 
            embedding_dropout=dropout_rate
        )
    
    def _setup_convolutional_processor(self, field_count, channels, kernel_heights, activation):
        self.convolutional_feature_extractor = ConvolutionalFeatureExtractor(
            field_count, 
            channel_configurations=channels, 
            kernel_height_specs=kernel_heights, 
            activation_function=activation
        )
    
    def _setup_prediction_component(self, embedding_dim, channels):
        # Calculate output dimension from convolutional layers
        conv_output_dimension = 3 * embedding_dim * channels[-1]
        
        self.final_prediction_layer = nn.Linear(conv_output_dimension, 1)
    
    def _setup_output_activation(self, task_type):
        self.output_activation_function = self.get_final_activation(task_type)
    
    def _configure_training_process(self, params, learning_rate):
        self.compile(params["optimizer"], loss=params["loss"], lr=learning_rate)
    
    def _initialize_model_parameters(self, initializer):
        self.init_weights(embedding_initializer=initializer)
    
    def execute_convolutional_prediction(self, input_data):
        """
        Args:
            input_data (tuple): Tuple containing (features, labels)
        
        Returns:
            dict: Dictionary with prediction results
                - "prediction_output": Model prediction scores
                - "computed_loss": Loss value with regularization
        """
        feature_tensor, target_tensor = self._extract_input_data(input_data)
        
        embedding_collection = self._generate_feature_embeddings(feature_tensor)
        
        convolutional_input = self._prepare_convolutional_input(embedding_collection)
        
        extracted_features = self._extract_convolutional_features(convolutional_input)
        
        flattened_features = self._flatten_extracted_features(extracted_features)
        
        prediction_scores = self._generate_final_prediction(flattened_features)
        
        final_prediction = self._apply_output_activation(prediction_scores)
        
        computed_loss = self._calculate_prediction_loss(final_prediction, target_tensor)
        
        # Return structured results
        result_package = {
            "prediction_output": final_prediction, 
            "computed_loss": computed_loss
        }
        return result_package

    def forward(self, inputs):
        """
        Args:
            inputs (tuple): Tuple of (X, y) where:
                X (torch.Tensor): Input features
                y (torch.Tensor): Target labels

        Returns:
            dict: Dictionary containing:
                - "y_pred": Predicted probabilities
                - "loss": Computed loss value
        """
        result = self.execute_convolutional_prediction(inputs)
        return {
            "y_pred": result["prediction_output"],
            "loss": result["computed_loss"]
        }

    def _extract_input_data(self, input_data):
        """Extract and prepare input tensors."""
        return self.inputs_to_device(input_data)
    
    def _generate_feature_embeddings(self, feature_tensor):
        """Generate embeddings for input features."""
        return self.feature_embedding_processor(feature_tensor)
    
    def _prepare_convolutional_input(self, embedding_collection):
        """Prepare embeddings for convolutional processing."""
        embedding_tensor = torch.stack(embedding_collection, dim=1)
        
        convolutional_input = torch.unsqueeze(embedding_tensor, 1)
        
        return convolutional_input
    
    def _extract_convolutional_features(self, conv_input):
        return self.convolutional_feature_extractor.extract_features(conv_input)
    
    def _flatten_extracted_features(self, extracted_features):
        return torch.flatten(extracted_features, start_dim=1)
    
    def _generate_final_prediction(self, flattened_features):
        return self.final_prediction_layer(flattened_features)
    
    def _apply_output_activation(self, prediction):
        if self.output_activation_function is not None:
            return self.output_activation_function(prediction)
        return prediction
    
    def _calculate_prediction_loss(self, predictions, targets):
        return self.loss_with_reg(predictions, targets)
    
    def get_convolutional_architecture_info(self):
        total_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        embedding_params = sum(p.numel() for p in self.feature_embedding_processor.parameters())
        conv_params = sum(p.numel() for p in self.convolutional_feature_extractor.parameters())
        prediction_params = sum(p.numel() for p in self.final_prediction_layer.parameters())
        
        return {
            "model_type": "Convolutional Click Prediction Model",
            "model_class": "ConvolutionalClickPredictionModel",
            "total_parameters": total_params,
            "component_parameters": {
                "embedding": embedding_params,
                "convolutional": conv_params,
                "prediction": prediction_params
            },
            "architecture_config": {
                "embedding_dimension": self.embedding_dimension,
                "channel_configurations": self.channel_configurations,
                "kernel_height_specs": self.kernel_height_specs,
                "prediction_task": self.prediction_task
            },
            "convolutional_components": [
                "Feature Embedding",
                "Convolutional Layers", 
                "K-Max Pooling",
                "Hierarchical Feature Extraction"
            ]
        }


class ConvolutionalFeatureExtractor(nn.Module):
    """
    Args:
        field_count (int): Number of feature fields
        channel_configurations (list): Channel sizes for each layer
        kernel_height_specs (list): Kernel heights for each layer
        activation_function (str): Activation function type
    """
    
    def __init__(self, field_count, channel_configurations=[3], kernel_height_specs=[3], activation_function="Tanh"):
        """Initialize the convolutional feature extractor."""
        super(ConvolutionalFeatureExtractor, self).__init__()
        
        self.field_count = field_count
        self.channel_configurations = channel_configurations
        self.kernel_height_specs = kernel_height_specs
        
        processed_kernel_heights = self._process_kernel_configuration(kernel_height_specs, channel_configurations)
        
        self.feature_extraction_pipeline = self._build_convolutional_pipeline(
            field_count, channel_configurations, processed_kernel_heights, activation_function
        )
    
    def _process_kernel_configuration(self, kernel_heights, channels):
        if not isinstance(kernel_heights, list):
            return [kernel_heights] * len(channels)
        elif len(kernel_heights) != len(channels):
            raise ValueError(f"channels={channels} and kernel_heights={kernel_heights} should have the same length.")
        return kernel_heights
    
    def _build_convolutional_pipeline(self, field_count, channels, kernel_heights, activation):
        pipeline_modules = []
        
        channel_sequence = [1] + channels
        layer_count = len(kernel_heights)
        
        for layer_index in range(1, len(channel_sequence)):
            conv_block = self._build_convolutional_block(
                channel_sequence[layer_index - 1], 
                channel_sequence[layer_index],
                kernel_heights[layer_index - 1],
                field_count, layer_index, layer_count, activation
            )
            pipeline_modules.extend(conv_block)
        
        return nn.Sequential(*pipeline_modules)
    
    def _build_convolutional_block(self, in_channels, out_channels, kernel_height,
                                 field_count, layer_index, total_layers, activation):
        block_modules = []
        
        # Add zero padding
        padding_layer = nn.ZeroPad2d((0, 0, kernel_height - 1, kernel_height - 1))
        block_modules.append(padding_layer)
        
        # Add convolutional layer
        conv_layer = nn.Conv2d(in_channels, out_channels, kernel_size=(kernel_height, 1))
        block_modules.append(conv_layer)
        
        if layer_index < total_layers:
            k_value = max(3, int((1 - pow(float(layer_index) / total_layers, total_layers - layer_index)) * field_count))
        else:
            k_value = 3
        
        kmax_pooling = TopKFeatureSelector(k_value, 2)
        block_modules.append(kmax_pooling)
        
        activation_layer = create_activation_function(activation)
        block_modules.append(activation_layer)
        
        return block_modules
    
    def extract_features(self, input_tensor):
        """
        Args:
            input_tensor (torch.Tensor): Input tensor for feature extraction
            
        Returns:
            torch.Tensor: Extracted features
        """
        contiguous_input = input_tensor.contiguous()
        
        try:
            extracted_features = self.feature_extraction_pipeline(contiguous_input)
            return extracted_features
        except RuntimeError as e:
            original_device = contiguous_input.device
            cpu_input = contiguous_input.cpu()
            cpu_result = self.feature_extraction_pipeline(cpu_input)
            return cpu_result.to(original_device)
    
    def forward(self, input_tensor):

        return self.extract_features(input_tensor)


class TopKFeatureSelector(nn.Module):
    """
    Args:
        k_value (int): Number of top features to select
        selection_dimension (int): Dimension along which to select top-k features
    """
    
    def __init__(self, k_value, selection_dimension):
        super(TopKFeatureSelector, self).__init__()
        
        self.k_value = k_value
        self.selection_dimension = selection_dimension
    
    def select_top_features(self, input_tensor):
        """
        Args:
            input_tensor (torch.Tensor): Input tensor for feature selection
            
        Returns:
            torch.Tensor: Selected top-k features
        """
        top_k_indices = input_tensor.topk(self.k_value, dim=self.selection_dimension)[1]
        sorted_indices = top_k_indices.sort(dim=self.selection_dimension)[0]
        
        selected_features = input_tensor.gather(self.selection_dimension, sorted_indices)
        
        return selected_features
    
    def forward(self, input_tensor):
        return self.select_top_features(input_tensor)

CCPM_Model = ConvolutionalClickPredictionModel
CCPM_ConvLayer = ConvolutionalFeatureExtractor
KMaxPooling = TopKFeatureSelector
