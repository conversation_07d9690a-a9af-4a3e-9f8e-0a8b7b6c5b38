import torch
import torch.nn as nn
from .base_model import BaseModel
from ..layers.DeepComponent import DNN_Layer
from ..layers.embedding import EmbeddingLayer
from ..layers.ShallowComponent import LinearRegressionComponent
from ..layers.interaction import InnerProductLayer


class NeuralFactorizationMachine(BaseModel):
    """
    Args:
        feature_mapping: Feature specification object
        model_identifier (str): Unique model identifier
        device_id (int): GPU device ID (-1 for CPU)
        prediction_task (str): Type of prediction task
        optimization_rate (float): Learning rate for training
        weight_initializer (str): Weight initialization method
        embedding_dimension (int): Size of embedding vectors
        network_architecture (list): Hidden layer sizes for DNN
        activation_functions (str): Activation function type
        embedding_dropout_rate (float): Dropout rate for embeddings
        network_dropout_rate (float): Dropout rate for DNN
        use_batch_normalization (bool): Whether to use batch normalization
        embedding_regularization (float): L2 regularization for embeddings
        network_regularization (float): L2 regularization for network
        **extra_params: Additional parameters
    """
    
    def __init__(self,
                 feature_mapping,
                 model_identifier="NeuralFM",
                 device_id=-1,
                 prediction_task="binary_classification",
                 optimization_rate=1e-3,
                 weight_initializer="torch.nn.init.normal_(std=1e-4)",
                 embedding_dimension=10,
                 network_architecture=[1000, 1000, 1000],
                 activation_functions="ReLU",
                 embedding_dropout_rate=0,
                 network_dropout_rate=0,
                 use_batch_normalization=False,
                 embedding_regularization=1e-06,
                 network_regularization=0,
                 **extra_params):
        """Initialize the Neural Factorization Machine model."""

        if 'embedding_dim' in extra_params:
            embedding_dimension = extra_params.pop('embedding_dim')
        if 'hidden_units' in extra_params:
            network_architecture = extra_params.pop('hidden_units')
        if 'learning_rate' in extra_params:
            optimization_rate = extra_params.pop('learning_rate')
        if 'task' in extra_params:
            prediction_task = extra_params.pop('task')
        if 'gpu' in extra_params:
            device_id = extra_params.pop('gpu')
        if 'embedding_dropout' in extra_params:
            embedding_dropout_rate = extra_params.pop('embedding_dropout')
        if 'net_dropout' in extra_params:
            network_dropout_rate = extra_params.pop('net_dropout')
        if 'batch_norm' in extra_params:
            use_batch_normalization = extra_params.pop('batch_norm')
        if 'hidden_activations' in extra_params:
            activation_functions = extra_params.pop('hidden_activations')

        if 'model_id' in extra_params:
            actual_model_id = extra_params.pop('model_id')
        else:
            actual_model_id = model_identifier

        super(NeuralFactorizationMachine, self).__init__(
            feature_mapping,
            model_id=actual_model_id,
            gpu=device_id,
            embedding_regularizer=embedding_regularization,
            net_regularizer=network_regularization,
            **extra_params
        )
        
        self.embedding_dimension = embedding_dimension
        self.network_architecture = network_architecture
        self.prediction_task = prediction_task
        
        self._setup_linear_component(feature_mapping)
        self._setup_embedding_component(feature_mapping, embedding_dimension, embedding_dropout_rate)
        self._setup_interaction_component()
        self._setup_neural_network(embedding_dimension, network_architecture, activation_functions,
                                 network_dropout_rate, use_batch_normalization)
        self._setup_output_activation(prediction_task)
        
        self._configure_training(extra_params, optimization_rate)
        self._initialize_model_parameters(weight_initializer)
    
    def _setup_linear_component(self, feature_mapping):
        self.linear_transformation_component = LinearRegressionComponent(
            feature_mapping,
            output_activation=None,
            include_bias=False
        )
    
    def _setup_embedding_component(self, feature_mapping, dimension, dropout_rate):
        self.feature_embedding_component = EmbeddingLayer(
            feature_mapping, 
            dimension, 
            embedding_dropout=dropout_rate
        )
    
    def _setup_interaction_component(self):
        self.bi_interaction_component = InnerProductLayer(output="bi_vector")
    
    def _setup_neural_network(self, input_dimension, hidden_layers, activations,
                            dropout_rate, batch_norm):
        self.deep_processing_network = DNN_Layer(
            input_dimension=input_dimension,
            output_dimension=1,
            hidden_layer_sizes=hidden_layers,
            activation_functions=activations,
            output_activation=None,
            dropout_probabilities=dropout_rate,
            use_batch_normalization=batch_norm,
            include_bias=True
        )
    
    def _setup_output_activation(self, task_type):

        self.output_activation_function = self.get_final_activation(task_type)
    
    def _configure_training(self, params, learning_rate):

        optimizer = params.get("optimizer", "adam")
        loss = params.get("loss", "binary_crossentropy")
        self.compile(optimizer, loss=loss, lr=learning_rate)
    
    def _initialize_model_parameters(self, initializer):

        self.init_weights(embedding_initializer=initializer)
    
    def execute_prediction_pipeline(self, input_data):
        """
        Args:
            input_data (tuple): Tuple containing (features, labels)
        
        Returns:
            dict: Dictionary with prediction results
                - "total_loss": Loss value with regularization
                - "prediction_output": Model prediction scores
        """

        feature_tensor, target_tensor = self._extract_and_prepare_inputs(input_data)
        
        linear_prediction = self._compute_linear_prediction(feature_tensor)
        
        embedding_collection = self._generate_feature_embeddings(feature_tensor)
        
        pooled_interactions = self._perform_bi_interaction_pooling(embedding_collection)
        
        deep_prediction = self._process_through_deep_network(pooled_interactions)
        
        combined_prediction = self._combine_prediction_components(linear_prediction, deep_prediction)
        
        final_prediction = self._apply_output_activation(combined_prediction)
        
        total_loss = self._calculate_prediction_loss(final_prediction, target_tensor)
        
        # Return structured results
        result_package = {
            "total_loss": total_loss, 
            "prediction_output": final_prediction
        }
        return result_package

    def forward(self, inputs):
        """
        Args:
            inputs (tuple): Tuple of (X, y) where:
                X (torch.Tensor): Input features
                y (torch.Tensor): Target labels

        Returns:
            dict: Dictionary containing:
                - "y_pred": Predicted probabilities
                - "loss": Computed loss value
        """
        try:
            result = self.execute_prediction_pipeline(inputs)
            return_dict = {
                "y_pred": result["prediction_output"],
                "loss": result["total_loss"]
            }
            return return_dict
        except Exception as e:
            print(f"ERROR in NFM forward: {e}")
            import traceback
            traceback.print_exc()
            raise



    def _extract_and_prepare_inputs(self, input_data):
        """Extract and prepare input tensors."""
        return self.inputs_to_device(input_data)
    
    def _compute_linear_prediction(self, feature_tensor):
        """Compute linear component prediction."""
        return self.linear_transformation_component(feature_tensor)
    
    def _generate_feature_embeddings(self, feature_tensor):
        """Generate embeddings for input features."""
        return self.feature_embedding_component(feature_tensor)
    
    def _perform_bi_interaction_pooling(self, embedding_collection):
        # Compute bi-interaction tensor
        bi_interaction_tensor = self.bi_interaction_component(embedding_collection)
        
        # Reshape for neural network processing
        batch_size = bi_interaction_tensor.size(0)
        pooled_tensor = bi_interaction_tensor.view(batch_size, -1)
        
        return pooled_tensor
    
    def _process_through_deep_network(self, pooled_features):

        return self.deep_processing_network(pooled_features)
    
    def _combine_prediction_components(self, linear_component, deep_component):

        return linear_component + deep_component
    
    def _apply_output_activation(self, combined_prediction):

        if self.output_activation_function is not None:
            return self.output_activation_function(combined_prediction)
        return combined_prediction
    
    def _calculate_prediction_loss(self, predictions, targets):

        return self.loss_with_reg(predictions, targets)
    
    def get_model_architecture_summary(self):
        """
        Returns:
            dict: Detailed architecture information
        """
        total_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        embedding_params = sum(p.numel() for p in self.feature_embedding_component.parameters())
        linear_params = sum(p.numel() for p in self.linear_transformation_component.parameters())
        network_params = sum(p.numel() for p in self.deep_processing_network.parameters())
        
        return {
            "model_type": "Neural Factorization Machine",
            "model_class": "NeuralFactorizationMachine",
            "total_parameters": total_params,
            "component_parameters": {
                "embedding": embedding_params,
                "linear": linear_params,
                "deep_network": network_params
            },
            "architecture_config": {
                "embedding_dimension": self.embedding_dimension,
                "network_layers": self.network_architecture,
                "prediction_task": self.prediction_task
            },
            "components": [
                "Linear Transformation",
                "Feature Embedding", 
                "Bi-interaction Pooling",
                "Deep Neural Network"
            ]
        }
    
    def predict_batch_scores(self, feature_batch):
        """
        Args:
            feature_batch (torch.Tensor): Input features
            
        Returns:
            torch.Tensor: Prediction scores
        """
        self.eval()
        with torch.no_grad():
            linear_pred = self._compute_linear_prediction(feature_batch)
            embeddings = self._generate_feature_embeddings(feature_batch)
            pooled = self._perform_bi_interaction_pooling(embeddings)
            deep_pred = self._process_through_deep_network(pooled)
            combined = self._combine_prediction_components(linear_pred, deep_pred)
            final_scores = self._apply_output_activation(combined)
        return final_scores

NFM_Model = NeuralFactorizationMachine
