import torch
import torch.nn as nn
from .base_model import BaseModel
from ..layers.embedding import EmbeddingLayer_v3
from ..layers.DeepComponent import DNN_Layer
from ..layers.ShallowComponent import LinearRegressionComponent


class WideAndDeepLearningModel(BaseModel):
    """
    Args:
        feature_mapping: Feature specification object
        model_identifier (str): Unique model identifier
        device_id (int): GPU device ID (-1 for CPU)
        prediction_task (str): Type of prediction task
        optimization_rate (float): Learning rate for training
        weight_initializer (str): Weight initialization method
        embedding_dimension (int): Size of embedding vectors
        deep_network_layers (list): Hidden layer sizes for deep component
        deep_activation_functions (str): Activation functions for deep network
        network_dropout_rate (float): Dropout rate for deep network
        use_batch_normalization (bool): Whether to use batch normalization
        embedding_regularization (float): L2 regularization for embeddings
        network_regularization (float): L2 regularization for network
        **extra_params: Additional parameters
    """
    
    def __init__(self, 
                 feature_mapping, 
                 model_identifier="WideDeepModel", 
                 device_id=-1, 
                 prediction_task="binary_classification", 
                 optimization_rate=1e-3, 
                 weight_initializer="torch.nn.init.normal_(std=1e-4)", 
                 embedding_dim=10,
                 deep_network_layers=[1000, 1000, 1000, 1000, 1000], 
                 deep_activation_functions="ReLU", 
                 network_dropout_rate=0.2, 
                 use_batch_normalization=False, 
                 embedding_regularization=1.0e-06, 
                 network_regularization=0,
                 **extra_params):
        
        super(WideAndDeepLearningModel, self).__init__(
            feature_mapping,
            gpu=device_id,
            embedding_regularizer=embedding_regularization,
            net_regularizer=network_regularization,
            **extra_params
        )
        
        self.embedding_dimension = embedding_dim
        self.deep_network_layers = deep_network_layers
        self.prediction_task = prediction_task

        self._setup_embedding_component(feature_mapping, embedding_dim)
        self._setup_wide_component(feature_mapping)
        self._setup_deep_component(feature_mapping, embedding_dim, deep_network_layers,
                                 deep_activation_functions, network_dropout_rate, use_batch_normalization)
        self._setup_output_activation(prediction_task)
        
        self._configure_training_process(extra_params, optimization_rate)
        self._initialize_model_parameters(weight_initializer)
    
    def _setup_embedding_component(self, feature_mapping, dimension):

        self.feature_embedding_processor = EmbeddingLayer_v3(feature_mapping, dimension)
    
    def _setup_wide_component(self, feature_mapping):

        self.wide_memorization_component = LinearRegressionComponent(
            feature_mapping,
            output_activation=None,
            include_bias=False
        )
    
    def _setup_deep_component(self, feature_mapping, embedding_dim, hidden_layers,
                            activations, dropout_rate, batch_norm):
        # Calculate input dimension for deep network
        deep_input_dimension = embedding_dim * feature_mapping.num_fields
        
        self.deep_generalization_component = DNN_Layer(
            input_dimension=deep_input_dimension,
            output_dimension=1,
            hidden_layer_sizes=hidden_layers,
            activation_functions=activations,
            output_activation=None,
            dropout_probabilities=dropout_rate,
            use_batch_normalization=batch_norm,
            include_bias=True
        )
    
    def _setup_output_activation(self, task_type):

        self.output_activation_function = self.get_final_activation(task_type)
    
    def _configure_training_process(self, params, learning_rate):

        self.compile(params["optimizer"], loss=params["loss"], lr=learning_rate)
    
    def _initialize_model_parameters(self, initializer):

        self.init_weights(embedding_initializer=initializer)
    
    def execute_wide_deep_prediction(self, input_data):
        """        
        Args:
            input_data (tuple): Tuple containing (features, labels)
        
        Returns:
            dict: Dictionary with prediction results
                - "computed_loss": Loss value with regularization
                - "prediction_output": Model prediction scores
        """
        feature_tensor, target_tensor = self._extract_input_data(input_data)
        
        wide_pathway_output = self._process_wide_pathway(feature_tensor)
        
        deep_pathway_output = self._process_deep_pathway(feature_tensor)
        
        #Combine wide and deep outputs
        combined_prediction = self._combine_pathway_outputs(wide_pathway_output, deep_pathway_output)
        
        final_prediction = self._apply_final_activation(combined_prediction)
        
        computed_loss = self._calculate_prediction_loss(final_prediction, target_tensor)
        
        # Return structured results
        result_package = {
            "computed_loss": computed_loss, 
            "prediction_output": final_prediction
        }
        return result_package

    def forward(self, inputs):
        """
        Args:
            inputs (tuple): Tuple of (X, y) where:
                X (torch.Tensor): Input features
                y (torch.Tensor): Target labels

        Returns:
            dict: Dictionary containing:
                - "y_pred": Predicted probabilities
                - "loss": Computed loss value
        """
        result = self.execute_wide_deep_prediction(inputs)
        return {
            "y_pred": result["prediction_output"],
            "loss": result["computed_loss"]
        }

    def _extract_input_data(self, input_data):

        return self.inputs_to_device(input_data)
    
    def _process_wide_pathway(self, feature_tensor):

        return self.wide_memorization_component(feature_tensor)
    
    def _process_deep_pathway(self, feature_tensor):
        embedding_representations = self._generate_dense_embeddings(feature_tensor)
        
        # Flatten embeddings for deep network
        flattened_embeddings = self._flatten_embedding_representations(embedding_representations)
        
        deep_output = self._process_through_deep_network(flattened_embeddings)
        
        return deep_output
    
    def _generate_dense_embeddings(self, feature_tensor):

        return self.feature_embedding_processor(feature_tensor)
    
    def _flatten_embedding_representations(self, embeddings):

        return embeddings.flatten(start_dim=1)
    
    def _process_through_deep_network(self, flattened_features):

        return self.deep_generalization_component(flattened_features)
    
    def _combine_pathway_outputs(self, wide_output, deep_output):

        return wide_output + deep_output
    
    def _apply_final_activation(self, combined_output):
        if self.output_activation_function is not None:
            return self.output_activation_function(combined_output)
        return combined_output
    
    def _calculate_prediction_loss(self, predictions, targets):

        return self.loss_with_reg(predictions, targets)
    
    def get_dual_pathway_architecture_info(self):
        """
        Returns:
            dict: Architecture details for both wide and deep components
        """
        total_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        embedding_params = sum(p.numel() for p in self.feature_embedding_processor.parameters())
        wide_params = sum(p.numel() for p in self.wide_memorization_component.parameters())
        deep_params = sum(p.numel() for p in self.deep_generalization_component.parameters())
        
        return {
            "model_type": "Wide & Deep Learning",
            "model_class": "WideAndDeepLearningModel",
            "total_parameters": total_params,
            "pathway_parameters": {
                "embedding": embedding_params,
                "wide_memorization": wide_params,
                "deep_generalization": deep_params
            },
            "architecture_config": {
                "embedding_dimension": self.embedding_dimension,
                "deep_network_layers": self.deep_network_layers,
                "prediction_task": self.prediction_task
            },
            "pathway_components": {
                "wide": ["Linear Transformation", "Direct Feature Mapping"],
                "deep": ["Feature Embedding", "Deep Neural Network", "Non-linear Transformation"]
            }
        }
    
    def predict_with_pathway_breakdown(self, feature_batch):
        """
        Args:
            feature_batch (torch.Tensor): Input features
            
        Returns:
            dict: Predictions broken down by pathway
        """
        self.eval()
        with torch.no_grad():
            wide_output = self._process_wide_pathway(feature_batch)
            deep_output = self._process_deep_pathway(feature_batch)
            combined_output = self._combine_pathway_outputs(wide_output, deep_output)
            final_output = self._apply_final_activation(combined_output)
            
            return {
                "wide_component": wide_output,
                "deep_component": deep_output,
                "combined_output": combined_output,
                "final_prediction": final_output
            }
    
    def analyze_pathway_contributions(self, feature_batch):
        """
        Args:
            feature_batch (torch.Tensor): Input features
            
        Returns:
            dict: Analysis of pathway contributions
        """
        pathway_breakdown = self.predict_with_pathway_breakdown(feature_batch)
        
        wide_magnitude = torch.abs(pathway_breakdown["wide_component"]).mean().item()
        deep_magnitude = torch.abs(pathway_breakdown["deep_component"]).mean().item()
        total_magnitude = wide_magnitude + deep_magnitude
        
        if total_magnitude > 0:
            wide_contribution = wide_magnitude / total_magnitude
            deep_contribution = deep_magnitude / total_magnitude
        else:
            wide_contribution = deep_contribution = 0.5
        
        return {
            "wide_contribution_ratio": wide_contribution,
            "deep_contribution_ratio": deep_contribution,
            "wide_average_magnitude": wide_magnitude,
            "deep_average_magnitude": deep_magnitude,
            "pathway_balance": "wide_dominant" if wide_contribution > 0.6 else 
                             "deep_dominant" if deep_contribution > 0.6 else "balanced"
        }

WideDeep_Model = WideAndDeepLearningModel
