import torch
import torch.nn as nn
from .base_model import BaseModel
from ..layers.embedding import EmbeddingLayer
from ..layers.GraphComponent import FiGNN_Layer


class FieldAwareGraphNetwork(BaseModel):
    """
    Args:
        feature_mapping: Feature specification object
        model_identifier (str): Unique model identifier
        device_id (int): GPU device ID (-1 for CPU)
        prediction_task (str): Type of prediction task
        optimization_rate (float): Learning rate for training
        weight_initializer (str): Weight initialization method
        embedding_dimension (int): Size of embedding vectors
        embedding_dropout_rate (float): Dropout rate for embeddings
        graph_layer_count (int): Number of graph neural network layers
        initial_batch_size (int): Initial batch size for graph processing
        use_residual_connections (bool): Whether to use residual connections
        use_gru_updates (bool): Whether to use GRU for state updates
        reuse_graph_layers (bool): Whether to reuse graph layer parameters
        embedding_regularization (float): L2 regularization for embeddings
        network_regularization (float): L2 regularization for network
        **extra_params: Additional parameters
    """
    
    def __init__(self, 
                 feature_mapping, 
                 model_identifier="FieldGraphNet", 
                 device_id=-1, 
                 prediction_task="binary_classification", 
                 optimization_rate=1e-3, 
                 weight_initializer="torch.nn.init.normal_(std=1e-4)", 
                 embedding_dim=10,
                 embedding_dropout_rate=0,
                 graph_layer_count=4,
                 initial_batch_size=32,
                 use_residual_connections=True,
                 use_gru_updates=True,
                 reuse_graph_layers=False,
                 embedding_regularization=1e-06,
                 network_regularization=0,
                 **extra_params):
        
        super(FieldAwareGraphNetwork, self).__init__(
            feature_mapping,
            gpu=device_id,
            embedding_regularizer=embedding_regularization,
            net_regularizer=network_regularization,
            **extra_params
        )
        
        self.embedding_dimension = embedding_dim
        self.feature_field_count = feature_mapping.num_fields
        self.initial_batch_size = initial_batch_size
        self.prediction_task = prediction_task

        self._setup_embedding_component(feature_mapping, embedding_dim, embedding_dropout_rate)
        self._setup_graph_processing_component(graph_layer_count, use_residual_connections,
                                             use_gru_updates, reuse_graph_layers)
        self._setup_prediction_component()
        self._setup_output_activation(prediction_task)
        
        self._configure_training_process(extra_params, optimization_rate)
        self._initialize_model_parameters(weight_initializer)
    
    def _setup_embedding_component(self, feature_mapping, dimension, dropout_rate):
        self.feature_embedding_processor = EmbeddingLayer(
            feature_mapping, 
            dimension, 
            embedding_dropout=dropout_rate
        )
    
    def _setup_graph_processing_component(self, layer_count, use_residual, use_gru, reuse_layers):
        self.graph_neural_processor = FiGNN_Layer(
            self.feature_field_count,
            self.embedding_dimension,
            graph_layer_count=layer_count,
            reuse_graph_layers=reuse_layers,
            use_gru_updates=use_gru,
            use_residual_connections=use_residual,
            processing_device=self.device
        )
    
    def _setup_prediction_component(self):
        self.prediction_aggregator = WeightedPredictionAggregator(
            self.feature_field_count, 
            self.embedding_dimension
        )
    
    def _setup_output_activation(self, task_type):
        self.output_activation_function = self.get_final_activation(task_type)
    
    def _configure_training_process(self, params, learning_rate):

        self.compile(params["optimizer"], loss=params["loss"], lr=learning_rate)
    
    def _initialize_model_parameters(self, initializer):

        self.init_weights(embedding_initializer=initializer)
    
    def execute_graph_processing(self, input_data):
        """
        Args:
            input_data (tuple): Tuple containing (features, labels)
        
        Returns:
            dict: Dictionary with prediction results
                - "predicted_output": Model prediction scores
                - "computed_loss": Loss value with regularization
        """
        feature_tensor, target_tensor = self._extract_input_data(input_data)
        
        embedding_collection = self._generate_feature_embeddings(feature_tensor)
        
        graph_input_tensor = self._prepare_graph_input(embedding_collection)
        
        graph_output_representations = self._process_through_graph_network(graph_input_tensor)
        
        prediction_scores = self._generate_final_prediction(graph_output_representations)
        
        final_prediction = self._apply_output_activation(prediction_scores)
        
        computed_loss = self._calculate_prediction_loss(final_prediction, target_tensor)
        
        result_package = {
            "predicted_output": final_prediction, 
            "computed_loss": computed_loss
        }
        return result_package
    
    def _extract_input_data(self, input_data):

        return self.inputs_to_device(input_data)
    
    def _generate_feature_embeddings(self, feature_tensor):

        return self.feature_embedding_processor(feature_tensor)
    
    def _prepare_graph_input(self, embedding_collection):

        # Stack embeddings to create tensor suitable for graph processing
        return torch.stack(embedding_collection, dim=1)
    
    def _process_through_graph_network(self, graph_input):

        return self.graph_neural_processor(graph_input)
    
    def _generate_final_prediction(self, graph_representations):

        return self.prediction_aggregator.compute_prediction(graph_representations)
    
    def _apply_output_activation(self, prediction):
        if self.output_activation_function is not None:
            return self.output_activation_function(prediction)
        return prediction
    
    def _calculate_prediction_loss(self, predictions, targets):

        return self.loss_with_reg(predictions, targets)

    def forward(self, inputs):
        """
        Args:
            inputs (tuple): Tuple of (X, y) where:
                X (torch.Tensor): Input features
                y (torch.Tensor): Target labels

        Returns:
            dict: Dictionary containing:
                - "y_pred": Predicted probabilities
                - "loss": Computed loss value
        """
        result = self.execute_graph_processing(inputs)
        return {
            "y_pred": result["predicted_output"],
            "loss": result["computed_loss"]
        }

    def get_graph_architecture_info(self):
        """        
        Returns:
            dict: Graph architecture details and parameter counts
        """
        total_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        embedding_params = sum(p.numel() for p in self.feature_embedding_processor.parameters())
        graph_params = sum(p.numel() for p in self.graph_neural_processor.parameters())
        prediction_params = sum(p.numel() for p in self.prediction_aggregator.parameters())
        
        return {
            "model_type": "Field-aware Graph Neural Network",
            "model_class": "FieldAwareGraphNetwork",
            "total_parameters": total_params,
            "component_parameters": {
                "embedding": embedding_params,
                "graph_processing": graph_params,
                "prediction": prediction_params
            },
            "architecture_config": {
                "embedding_dimension": self.embedding_dimension,
                "feature_field_count": self.feature_field_count,
                "prediction_task": self.prediction_task
            },
            "graph_components": [
                "Feature Embedding",
                "Graph Neural Network", 
                "Message Passing",
                "Weighted Aggregation"
            ]
        }


class WeightedPredictionAggregator(nn.Module):
    """
    Args:
        field_count (int): Number of feature fields
        embedding_dimension (int): Dimension of embeddings
    """
    
    def __init__(self, field_count, embedding_dimension):
        super(WeightedPredictionAggregator, self).__init__()
        
        self.field_count = field_count
        self.embedding_dimension = embedding_dimension
        
        self.feature_score_generator = self._create_score_generator(embedding_dimension)
        self.importance_weight_generator = self._create_weight_generator(field_count, embedding_dimension)
    
    def _create_score_generator(self, dimension):

        return nn.Linear(dimension, 1, bias=False)
    
    def _create_weight_generator(self, field_count, dimension):

        return nn.Sequential(
            nn.Linear(field_count * dimension, field_count, bias=False),
            nn.Sigmoid()
        )
    
    def compute_prediction(self, graph_representations):
        """
        Args:
            graph_representations (torch.Tensor): Output from graph neural network
            
        Returns:
            torch.Tensor: Final prediction scores
        """
        # Generate feature scores
        feature_scores = self._generate_feature_scores(graph_representations)
        
        # Generate importance weights
        importance_weights = self._generate_importance_weights(graph_representations)
        
        # Combine scores and weights
        final_prediction = self._combine_scores_and_weights(feature_scores, importance_weights)
        
        return final_prediction
    
    def _generate_feature_scores(self, representations):
        """Generate scores for each feature."""
        return self.feature_score_generator(representations).squeeze(-1)
    
    def _generate_importance_weights(self, representations):
        """Generate importance weights for features."""
        flattened_representations = representations.flatten(start_dim=1)
        return self.importance_weight_generator(flattened_representations)
    
    def _combine_scores_and_weights(self, scores, weights):
        """Combine feature scores with importance weights."""
        weighted_scores = weights * scores
        final_score = weighted_scores.sum(dim=1).unsqueeze(-1)
        return final_score
    
    def forward(self, graph_representations):

        return self.compute_prediction(graph_representations)

FiGNN_Model = FieldAwareGraphNetwork
PredictionLayer = WeightedPredictionAggregator
