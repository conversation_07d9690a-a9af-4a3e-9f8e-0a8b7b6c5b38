import torch
import torch.nn as nn
from .base_model import BaseModel
from ..layers.DeepComponent import DNN_Layer
from ..layers.embedding import EmbeddingLayer
from ..layers.interaction import InnerProductLayer


class ProductNeuralNetwork(BaseModel):
    """
    Args:
        feature_mapping: Feature specification object
        model_identifier (str): Unique identifier for the model
        device_id (int): GPU device ID (-1 for CPU)
        prediction_task (str): Type of prediction task
        optimization_rate (float): Learning rate for training
        weight_initializer (str): Method for weight initialization
        embedding_dimension (int): Size of embedding vectors
        network_layers (list): Hidden layer sizes for DNN
        activation_functions (str): Activation function type
        embedding_dropout_rate (float): Dropout rate for embeddings
        network_dropout_rate (float): Dropout rate for DNN layers
        use_batch_normalization (bool): Whether to use batch normalization
        product_operation_type (str): Type of product operation
        embedding_regularization (float): L2 regularization for embeddings
        network_regularization (float): L2 regularization for network
        **extra_params: Additional parameters
    """
    
    def __init__(self, 
                 feature_mapping, 
                 model_identifier="ProductNN", 
                 device_id=-1, 
                 prediction_task="binary_classification", 
                 optimization_rate=1e-3, 
                 weight_initializer="torch.nn.init.normal_(std=1e-4)", 
                 embedding_dim=10,
                 network_layers=[1000, 1000],
                 activation_functions="ReLU", 
                 embedding_dropout_rate=0,
                 network_dropout_rate=0.2, 
                 use_batch_normalization=False, 
                 product_operation_type="inner", 
                 embedding_regularization=1e-06, 
                 network_regularization=0, 
                 **extra_params):
        
        super(ProductNeuralNetwork, self).__init__(
            feature_mapping,
            gpu=device_id,
            embedding_regularizer=embedding_regularization,
            net_regularizer=network_regularization,
            **extra_params
        )
        
        self.embedding_dimension = embedding_dim
        self.network_layers = network_layers
        self.product_operation_type = product_operation_type
        self.prediction_task = prediction_task

        self._validate_product_type(product_operation_type)

        self._setup_embedding_component(feature_mapping, embedding_dim, embedding_dropout_rate)
        self._setup_product_component()
        self._setup_neural_network(feature_mapping, network_layers, activation_functions, 
                                 network_dropout_rate, use_batch_normalization)
        
        self._configure_optimization(extra_params, optimization_rate)
        self._initialize_model_weights(weight_initializer)
    
    def _validate_product_type(self, operation_type):
        if operation_type != "inner":
            raise NotImplementedError(f"Product operation type '{operation_type}' is not implemented.")
    
    def _setup_embedding_component(self, feature_mapping, dimension, dropout_rate):
        self.feature_embedding_component = EmbeddingLayer(
            feature_mapping, 
            dimension, 
            embedding_dropout=dropout_rate
        )
    
    def _setup_product_component(self):
        self.interaction_product_component = InnerProductLayer(output="dot_vector")
    
    def _setup_neural_network(self, feature_mapping, hidden_layers, activations, 
                            dropout_rate, batch_norm):
        """Initialize the deep neural network component."""
        num_feature_fields = feature_mapping.num_fields
        interaction_dimension = int(num_feature_fields * (num_feature_fields - 1) / 2)
        embedding_dimension = num_feature_fields * self.embedding_dimension
        total_input_dimension = interaction_dimension + embedding_dimension
        
        self.deep_neural_network = DNN_Layer(
            input_dimension=total_input_dimension,
            output_dimension=1,
            hidden_layer_sizes=hidden_layers,
            activation_functions=activations,
            output_activation=self._get_output_activation(),
            dropout_probabilities=dropout_rate,
            use_batch_normalization=batch_norm,
            include_bias=True
        )
    
    def _get_output_activation(self):

        return self.get_final_activation(self.prediction_task)
    
    def _configure_optimization(self, params, learning_rate):

        self.compile(params["optimizer"], loss=params["loss"], lr=learning_rate)
    
    def _initialize_model_weights(self, initializer):

        self.init_weights(embedding_initializer=initializer)
    
    def compute_ctr_prediction(self, input_data):
        """
        Args:
            input_data (tuple): Tuple containing (features, labels)
        
        Returns:
            dict: Dictionary with prediction results
                - "computed_loss": Loss value with regularization
                - "prediction_scores": Model output scores
        """
        feature_tensor, target_tensor = self._extract_input_tensors(input_data)
        
        # Generate feature embeddings
        embedding_vectors = self._generate_feature_embeddings(feature_tensor)
        
        # Compute feature interactions
        interaction_vectors = self._compute_feature_interactions(embedding_vectors)
        
        # Combine embeddings and interactions
        combined_features = self._combine_feature_representations(embedding_vectors, interaction_vectors)
        
        # Generate final prediction
        prediction_scores = self._generate_final_prediction(combined_features)
        
        computed_loss = self._calculate_total_loss(prediction_scores, target_tensor)
        
        # Return results with different structure
        result_dictionary = {
            "computed_loss": computed_loss, 
            "prediction_scores": prediction_scores
        }
        return result_dictionary

    def forward(self, inputs):
        """
        Args:
            inputs (tuple): Tuple of (X, y) where:
                X (torch.Tensor): Input features
                y (torch.Tensor): Target labels

        Returns:
            dict: Dictionary containing:
                - "y_pred": Predicted probabilities
                - "loss": Computed loss value
        """
        result = self.compute_ctr_prediction(inputs)
        return {
            "y_pred": result["prediction_scores"],
            "loss": result["computed_loss"]
        }

    def _extract_input_tensors(self, input_data):

        return self.inputs_to_device(input_data)
    
    def _generate_feature_embeddings(self, feature_tensor):

        return self.feature_embedding_component(feature_tensor)
    
    def _compute_feature_interactions(self, embedding_vectors):

        return self.interaction_product_component(embedding_vectors)
    
    def _combine_feature_representations(self, embeddings, interactions):

        return torch.cat(embeddings + [interactions], dim=1)
    
    def _generate_final_prediction(self, combined_features):

        return self.deep_neural_network(combined_features)
    
    def _calculate_total_loss(self, predictions, targets):

        return self.loss_with_reg(predictions, targets)
    
    def get_architecture_info(self):
        """
        Returns:
            dict: Architecture details and parameter counts
        """
        total_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        embedding_params = sum(p.numel() for p in self.feature_embedding_component.parameters())
        network_params = sum(p.numel() for p in self.deep_neural_network.parameters())
        
        return {
            "model_type": "Product-based Neural Network",
            "model_class": "ProductNeuralNetwork", 
            "total_parameters": total_params,
            "embedding_parameters": embedding_params,
            "network_parameters": network_params,
            "embedding_dimension": self.embedding_dimension,
            "network_layers": self.network_layers,
            "product_operation": self.product_operation_type,
            "prediction_task": self.prediction_task
        }

PNN_Model = ProductNeuralNetwork
