import torch
from torch import nn
from .base_model import BaseModel
from ..layers import Embedding<PERSON>ayer, DNN_Layer

torch.backends.cudnn.enabled = False

class LSTM(BaseModel):
    def __init__(self,
                 feature_map,
                 model_id="LSTM",
                 gpu=-1,
                 task="binary_classification",
                 learning_rate=1e-3,
                 embedding_dim=16,
                 hidden_dim=128,
                 num_layers=2,
                 dropout=0.2,
                 embedding_regularizer=None,
                 net_regularizer=None,
                 **kwargs):
        super(LSTM, self).__init__(feature_map,
                                  model_id=model_id,
                                  gpu=gpu,
                                  embedding_regularizer=embedding_regularizer,
                                  net_regularizer=net_regularizer,
                                  **kwargs)
        self.num_layers = num_layers
        self.hidden_dim = hidden_dim
        self.embedding_layer = EmbeddingLayer(feature_map, embedding_dim)
        self.lstm = nn.LSTM(input_size=embedding_dim,
                           hidden_size=hidden_dim,
                           num_layers=num_layers,
                           batch_first=True,
                           dropout=dropout if num_layers > 1 else 0)
        
        # Calculate input dimension for final DNN layer
        num_fields = feature_map.num_fields
        # LSTM output + other features
        self.dnn_input_dim = hidden_dim + (num_fields - 1) * embedding_dim
        
        # DNN layer for final prediction
        self.dnn = DNN_Layer(input_dimension=self.dnn_input_dim,
                            output_dimension=1,
                            hidden_layer_sizes=[64, 32],
                            activation_functions="ReLU",
                            output_activation=self.get_final_activation(task),
                            dropout_probabilities=dropout,
                            use_batch_normalization=True)
        
        self.compile(kwargs["optimizer"], loss=kwargs["loss"], lr=learning_rate)
    def _init_hidden(self, batch_size):
        """Initialize hidden state and cell state."""
        device = next(self.parameters()).device
        # Change hidden state shape to match input requirements
        return (torch.zeros(self.num_layers, batch_size, self.hidden_dim, device=device).contiguous(),
                torch.zeros(self.num_layers, batch_size, self.hidden_dim, device=device).contiguous())
        
    def forward(self, inputs):
        """
        Inputs: [X, y]
        """
        X, y = self.inputs_to_device(inputs)
        feature_emb_list = self.embedding_layer(X)
        
        # Separate sequence feature and other features
        sequence_emb = feature_emb_list[0]
        other_emb = torch.cat(feature_emb_list[1:], dim=1) if len(feature_emb_list) > 1 else None

        # Ensure sequence_emb has the correct shape [batch_size, seq_length, embedding_dim]
        if len(sequence_emb.shape) == 2:
            # Add sequence length dimension
            sequence_emb = sequence_emb.unsqueeze(1)
        
        # Initialize hidden states
        hidden = self._init_hidden(sequence_emb.size(0))
        
        # Process sequence with LSTM
        lstm_out, _ = self.lstm(sequence_emb, hidden)
        # Take the last output
        lstm_last = lstm_out[:, -1, :]
        
        if other_emb is not None:
            # Concatenate LSTM output with other feature embeddings
            combined_features = torch.cat([lstm_last, other_emb.view(other_emb.size(0), -1)], dim=1)
        else:
            combined_features = lstm_last
            
        # Final prediction
        y_pred = self.dnn(combined_features)
        loss = self.loss_with_reg(y_pred, y)
        
        return_dict = {"loss": loss, "y_pred": y_pred}
        return return_dict