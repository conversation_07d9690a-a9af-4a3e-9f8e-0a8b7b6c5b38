import torch
import torch.nn as nn
from .base_model import BaseModel
from ..layers.DeepComponent import DNN_Layer
from ..layers.embedding import EmbeddingLayer
from ..layers.attention import MultiHeadSelfAttention
from ..layers.ShallowComponent import LinearRegressionComponent


class AutomaticInteractionNetwork(BaseModel):
    """
    Args:
        feature_mapping: Feature specification object
        model_identifier (str): Unique model identifier
        device_id (int): GPU device ID (-1 for CPU)
        prediction_task (str): Type of prediction task
        optimization_rate (float): Learning rate for training
        weight_initializer (str): Weight initialization method
        embedding_dimension (int): Size of embedding vectors
        deep_network_layers (list): Hidden layer sizes for DNN
        deep_activation_functions (str): Activation function for DNN
        attention_layer_count (int): Number of attention layers
        attention_head_count (int): Number of attention heads
        attention_dimension (int): Dimension of attention mechanism
        embedding_dropout_rate (float): Dropout rate for embeddings
        network_dropout_rate (float): Dropout rate for networks
        use_batch_normalization (bool): Whether to use batch normalization
        use_layer_normalization (bool): Whether to use layer normalization
        use_attention_scaling (bool): Whether to use attention scaling
        include_wide_component (bool): Whether to include wide component
        use_residual_connections (bool): Whether to use residual connections
        embedding_regularization (float): L2 regularization for embeddings
        network_regularization (float): L2 regularization for networks
        **extra_params: Additional parameters
    """
    
    def __init__(self, 
                 feature_mapping, 
                 model_identifier="AutoInteractionNet", 
                 device_id=-1, 
                 prediction_task="binary_classification", 
                 optimization_rate=1e-3, 
                 weight_initializer="torch.nn.init.normal_(std=1e-4)", 
                 embedding_dim=10,
                 deep_network_layers=[], 
                 deep_activation_functions="ReLU", 
                 attention_layer_count=5,
                 attention_head_count=1,
                 attention_dimension=64,
                 embedding_dropout_rate=0,
                 network_dropout_rate=0, 
                 use_batch_normalization=False,
                 use_layer_normalization=False,
                 use_attention_scaling=False,
                 include_wide_component=False,
                 use_residual_connections=True,
                 embedding_regularization=1.0e-06, 
                 network_regularization=0, 
                 **extra_params):
        """Initialize the Automatic Interaction Network model."""
        
        super(AutomaticInteractionNetwork, self).__init__(
            feature_mapping,
            gpu=device_id,
            embedding_regularizer=embedding_regularization,
            net_regularizer=network_regularization,
            **extra_params
        )
        
        self.embedding_dimension = embedding_dim
        self.deep_network_layers = deep_network_layers
        self.attention_layer_count = attention_layer_count
        self.attention_head_count = attention_head_count
        self.attention_dimension = attention_dimension
        self.include_wide_component = include_wide_component
        self.prediction_task = prediction_task

        self._setup_embedding_component(feature_mapping, embedding_dim, embedding_dropout_rate)
        self._setup_wide_component(feature_mapping, include_wide_component)
        self._setup_deep_component(feature_mapping, deep_network_layers, deep_activation_functions,
                                 network_dropout_rate, use_batch_normalization)
        self._setup_attention_mechanism(embedding_dim, attention_layer_count, attention_head_count,
                                      attention_dimension, network_dropout_rate, use_residual_connections,
                                      use_attention_scaling, use_layer_normalization)
        self._setup_output_components(feature_mapping, attention_dimension, attention_head_count, prediction_task)

        self._configure_training_process(extra_params, optimization_rate)
        self._initialize_model_parameters(weight_initializer)
    
    def _setup_embedding_component(self, feature_mapping, dimension, dropout_rate):
        """Initialize the feature embedding component."""
        self.feature_embedding_processor = EmbeddingLayer(
            feature_mapping, 
            dimension, 
            embedding_dropout=dropout_rate
        )
    
    def _setup_wide_component(self, feature_mapping, include_wide):
        self.wide_interaction_component = LinearRegressionComponent(
            feature_mapping,
            output_activation=None,
            include_bias=False
        ) if include_wide else None
    
    def _setup_deep_component(self, feature_mapping, hidden_layers, activations,
                            dropout_rate, batch_norm):
        if hidden_layers:
            input_dimension = self.embedding_dimension * feature_mapping.num_fields
            self.deep_processing_network = DNN_Layer(
                input_dimension=input_dimension,
                output_dimension=1,
                hidden_layer_sizes=hidden_layers,
                activation_functions=activations,
                output_activation=None,
                dropout_probabilities=dropout_rate,
                use_batch_normalization=batch_norm,
                include_bias=True
            )
        else:
            self.deep_processing_network = None
    
    def _setup_attention_mechanism(self, embedding_dim, layer_count, head_count,
                                 attention_dim, dropout_rate, use_residual,
                                 use_scaling, use_layer_norm):
        attention_layers = []
        
        for layer_index in range(layer_count):
            # Determine input dimension for each layer
            input_dim = embedding_dim if layer_index == 0 else head_count * attention_dim
            
            # Create attention layer
            attention_layer = MultiHeadSelfAttention(
                input_dim,
                attention_dim=attention_dim, 
                num_heads=head_count, 
                dropout_rate=dropout_rate, 
                use_residual=use_residual, 
                use_scale=use_scaling, 
                layer_norm=use_layer_norm,
                align_to="output"
            )
            attention_layers.append(attention_layer)
        
        self.multi_head_attention_stack = nn.Sequential(*attention_layers)
    
    def _setup_output_components(self, feature_mapping, attention_dim, head_count, task):
        # Calculate output dimension from attention mechanism
        attention_output_dim = feature_mapping.num_fields * attention_dim * head_count
        
        # Create final combination layer
        self.attention_combination_layer = nn.Linear(attention_output_dim, 1)
        
        # Setup final activation
        self.output_activation_function = self.get_final_activation(task)
    
    def _configure_training_process(self, params, learning_rate):

        self.compile(params["optimizer"], loss=params["loss"], lr=learning_rate)
    
    def _initialize_model_parameters(self, initializer):

        self.init_weights(embedding_initializer=initializer)
    
    def process_feature_interactions(self, input_data):
        """
        Returns:
            dict: Dictionary with prediction results
                - "prediction_output": Model prediction scores
                - "total_loss": Loss value with regularization
        """
        feature_tensor, target_tensor = self._extract_input_data(input_data)
        
        embedding_sequence = self._generate_embedding_sequence(feature_tensor)
        
        attention_output = self._process_attention_interactions(embedding_sequence)
        
        attention_prediction = self._compute_attention_prediction(attention_output)
        
        final_prediction = self._integrate_deep_component(attention_prediction, embedding_sequence)
        
        final_prediction = self._integrate_wide_component(final_prediction, feature_tensor)
        
        final_prediction = self._apply_final_activation(final_prediction)
        
        total_loss = self._calculate_prediction_loss(final_prediction, target_tensor)
        
        # Return structured results
        result_package = {
            "prediction_output": final_prediction, 
            "total_loss": total_loss
        }
        return result_package
    
    def _extract_input_data(self, input_data):
        """Extract and prepare input tensors."""
        return self.inputs_to_device(input_data)
    
    def _generate_embedding_sequence(self, feature_tensor):
        """Generate embedding sequence for features."""
        return self.feature_embedding_processor(feature_tensor)
    
    def _process_attention_interactions(self, embedding_sequence):
        """Process embeddings through multi-head self-attention."""
        embedding_tensor = torch.stack(embedding_sequence, dim=1)
        
        # Process through attention layers
        attention_output = self.multi_head_attention_stack(embedding_tensor)
        
        return attention_output
    
    def _compute_attention_prediction(self, attention_output):
        """Compute prediction from attention output."""
        flattened_attention = torch.flatten(attention_output, start_dim=1)
        
        # Generate prediction from attention features
        return self.attention_combination_layer(flattened_attention)
    
    def _integrate_deep_component(self, current_prediction, embedding_sequence):
        if self.deep_processing_network is not None:
            # Concatenate embeddings for deep processing
            concatenated_embeddings = torch.cat(embedding_sequence, dim=1)
            
            # Process through deep network
            deep_prediction = self.deep_processing_network(concatenated_embeddings)
            
            current_prediction = current_prediction + deep_prediction
        
        return current_prediction
    
    def _integrate_wide_component(self, current_prediction, feature_tensor):
        if self.wide_interaction_component is not None:
            # Compute linear prediction
            wide_prediction = self.wide_interaction_component(feature_tensor)
            
            current_prediction = current_prediction + wide_prediction
        
        return current_prediction
    
    def _apply_final_activation(self, prediction):
        if self.output_activation_function is not None:
            return self.output_activation_function(prediction)
        return prediction
    
    def _calculate_prediction_loss(self, predictions, targets):
        return self.loss_with_reg(predictions, targets)

    def forward(self, inputs):
        """
        Args:
            inputs (tuple): Tuple of (X, y) where:
                X (torch.Tensor): Input features
                y (torch.Tensor): Target labels

        Returns:
            dict: Dictionary containing:
                - "y_pred": Predicted probabilities
                - "loss": Computed loss value
        """
        result = self.process_feature_interactions(inputs)
        return {
            "y_pred": result["prediction_output"],
            "loss": result["total_loss"]
        }

AutoInt_Model = AutomaticInteractionNetwork
