import torch
import torch.nn as nn
from .base_model import BaseModel
from ..layers import EmbeddingLayer


class GatedCrossLayer(nn.Module):
    """
    Gated Cross Network layer with information gating mechanism.
    """
    
    def __init__(self, input_dim, num_layers=3):
        """
        Initialize gated cross layer.
        
        Args:
            input_dim (int): Input dimension
            num_layers (int): Number of cross layers
        """
        super(GatedCross<PERSON><PERSON>er, self).__init__()
        self.num_layers = num_layers
        
        # Cross transformation weights
        self.cross_weights = nn.ModuleList([
            nn.Linear(input_dim, input_dim, bias=False) for _ in range(num_layers)
        ])
        
        # Gating weights
        self.gate_weights = nn.ModuleList([
            nn.Linear(input_dim, input_dim, bias=False) for _ in range(num_layers)
        ])
        
        # Bias parameters
        self.biases = nn.ParameterList([
            nn.Parameter(torch.zeros(input_dim)) for _ in range(num_layers)
        ])
        
        # Initialize biases
        for i in range(num_layers):
            nn.init.uniform_(self.biases[i].data)
        
        self.activation = nn.Sigmoid()

    def forward(self, x):
        """
        Forward pass through gated cross layers.
        
        Args:
            x (torch.Tensor): Input tensor
            
        Returns:
            torch.Tensor: Output after gated cross transformations
        """
        x0 = x  # Keep original input
        
        for i in range(self.num_layers):
            # Feature crossing
            cross_output = self.cross_weights[i](x)
            
            # Information gate
            gate_output = self.activation(self.gate_weights[i](x))
            
            # Gated cross transformation: x0 * (cross + bias) * gate + x
            x = x0 * (cross_output + self.biases[i]) * gate_output + x
        
        return x


class FeaturesEmbedding(nn.Module):
    """
    Feature embedding layer for GDCN that handles categorical features.
    """
    
    def __init__(self, feature_map, embedding_dim):
        """
        Initialize features embedding.
        
        Args:
            feature_map: Feature mapping object
            embedding_dim (int): Embedding dimension
        """
        super(FeaturesEmbedding, self).__init__()
        self.embed_dict = nn.ModuleDict()
        self.embedding_dim = embedding_dim
        self.feature_map = feature_map
        
        for feature_name, feature_spec in feature_map.feature_specs.items():
            if feature_spec["type"] == "categorical":
                vocab_size = feature_spec["vocab_size"]
                embed = nn.Embedding(vocab_size, embedding_dim)
                nn.init.xavier_uniform_(embed.weight)
                self.embed_dict[feature_name] = embed

    def forward(self, X):
        """
        Forward pass through feature embedding.
        
        Args:
            X (torch.Tensor): Input features
            
        Returns:
            torch.Tensor: Concatenated embeddings
        """
        feature_emb = []
        for feature_name, embedding_layer in self.embed_dict.items():
            feature_idx = X[:, self.feature_map.feature_specs[feature_name]["index"]].long()
            emb = embedding_layer(feature_idx)
            feature_emb.append(emb)
        
        if len(feature_emb) > 0:
            feature_emb = torch.cat(feature_emb, dim=1)
        else:
            feature_emb = torch.zeros((X.size(0), 0), device=X.device)
        
        return feature_emb


class GDCN(BaseModel):
    """
    Args:
        feature_map: Feature mapping object containing feature specifications
        model_id (str): Model identifier
        gpu (int): GPU device ID (-1 for CPU)
        task (str): Task type ("binary_classification", "regression", etc.)
        learning_rate (float): Learning rate for optimization
        embedding_dim (int): Dimension of feature embeddings
        embedding_dropout (float): Dropout rate for embeddings
        cn_layers (int): Number of cross network layers
        mlp_layers (list): List of hidden layer sizes for deep network
        dropout (float): Dropout rate for deep network
        batch_norm (bool): Whether to use batch normalization
        embedding_regularizer (float): Regularization for embedding layers
        net_regularizer (float): Regularization for network layers
        **kwargs: Additional arguments passed to base model
    """
    
    def __init__(self, 
                 feature_map,
                 model_id="GDCN",
                 gpu=-1,
                 task="binary_classification",
                 learning_rate=1e-3,
                 embedding_dim=10,
                 embedding_dropout=0,
                 cn_layers=3,
                 mlp_layers=[400, 400, 400],
                 dropout=0.5,
                 batch_norm=True,
                 embedding_regularizer=None,
                 net_regularizer=None,
                 **kwargs):
        """
        Initialize GDCN model.
        """
        super(GDCN, self).__init__(feature_map,
                                   model_id=model_id,
                                   gpu=gpu,
                                   embedding_regularizer=embedding_regularizer,
                                   net_regularizer=net_regularizer,
                                   **kwargs)
        
        self.categorical_feature_count = sum(1 for _, spec in feature_map.feature_specs.items() 
                                             if spec["type"] == "categorical")
        
        # Feature embedding layer
        self.embedding = FeaturesEmbedding(feature_map, embedding_dim)
        self.embedding_dropout = nn.Dropout(embedding_dropout)
        self.embed_output_dim = self.categorical_feature_count * embedding_dim
        
        # Gated cross network
        self.cross_net = GatedCrossLayer(self.embed_output_dim, cn_layers)
        
        layers = []
        input_dim = self.embed_output_dim
        for i in range(len(mlp_layers)):
            layers.append(nn.Linear(input_dim, mlp_layers[i]))
            if batch_norm:
                layers.append(nn.BatchNorm1d(mlp_layers[i]))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(p=dropout))
            input_dim = mlp_layers[i]
        self.mlp = nn.Sequential(*layers)
        
        # Final output layer
        self.fc = nn.Linear(mlp_layers[-1] + self.embed_output_dim, 1)
        self.final_activation = self.get_final_activation(task)
        
        # Compile model
        self.compile(kwargs["optimizer"], loss=kwargs["loss"], lr=learning_rate)

    def forward(self, inputs):
        """
        Args:
            inputs (tuple): Tuple of (X, y) where:
                X (torch.Tensor): Input features of shape (batch_size, num_features)
                y (torch.Tensor): Target labels of shape (batch_size, 1)
        
        Returns:
            dict: Dictionary containing:
                - "y_pred": Predicted probabilities
                - "loss": Computed loss value
        """
        X, y = self.inputs_to_device(inputs)
        
        if not isinstance(X, torch.Tensor):
            X = torch.tensor(X, device=self.device)
        
        # Feature embedding
        feature_emb = self.embedding(X)
        feature_emb = self.embedding_dropout(feature_emb)
        
        # Gated cross network
        cross_out = self.cross_net(feature_emb)
        
        # Deep network
        mlp_out = self.mlp(feature_emb)
        
        # Combine cross and deep outputs
        concat_out = torch.cat([cross_out, mlp_out], dim=1)
        y_pred = self.fc(concat_out)
        
        if self.final_activation is not None:
            y_pred = self.final_activation(y_pred)
        
        # Compute loss with regularization
        loss = self.loss_with_reg(y_pred, y)
        
        return {"loss": loss, "y_pred": y_pred}

    def count_parameters(self, count_embedding=True):
        """        
        Args:
            count_embedding (bool): Whether to count embedding parameters
            
        Returns:
            int: Total number of trainable parameters
        """
        return super().count_parameters(count_embedding)

    def get_model_info(self):
        """
        Returns:
            dict: Model information including parameter counts and architecture details
        """
        total_params = self.count_parameters(count_embedding=True)
        embedding_params = self.count_parameters(count_embedding=True) - self.count_parameters(count_embedding=False)
        
        # Count cross network parameters
        cross_params = sum(p.numel() for p in self.cross_net.parameters() if p.requires_grad)
        
        # Count MLP parameters
        mlp_params = sum(p.numel() for p in self.mlp.parameters() if p.requires_grad)
        
        # Count final layer parameters
        fc_params = sum(p.numel() for p in self.fc.parameters() if p.requires_grad)
        
        return {
            "model_type": "Gated Deep Cross Network (GDCN)",
            "total_parameters": total_params,
            "embedding_parameters": embedding_params,
            "cross_parameters": cross_params,
            "mlp_parameters": mlp_params,
            "fc_parameters": fc_params,
            "num_features": self._feature_map.num_fields,
            "embedding_dim": self.embedding.embedding_dim,
            "cross_layers": self.cross_net.num_layers,
            "task": "binary_classification"
        }
