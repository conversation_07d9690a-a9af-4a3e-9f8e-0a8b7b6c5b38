import torch
import torch.nn as nn


class MaskedAverageAggregator(nn.Module):    
    def __init__(self, epsilon=1e-16, mask_value=0.0):
        """Initialize the masked average aggregator."""
        super(MaskedAverageAggregator, self).__init__()
        
        self.epsilon = epsilon
        self.mask_value = mask_value
    
    def aggregate_sequence(self, sequence_embeddings):
        # Compute sum pooling across sequence dimension
        sum_pooled_embeddings = self._compute_sum_pooling(sequence_embeddings)
        
        valid_sequence_lengths = self._calculate_valid_lengths(sequence_embeddings)
        
        # Compute average with numerical stability
        averaged_embeddings = self._compute_stable_average(
            sum_pooled_embeddings, valid_sequence_lengths
        )
        
        return averaged_embeddings
    
    def _compute_sum_pooling(self, embeddings):

        return torch.sum(embeddings, dim=1)
    
    def _calculate_valid_lengths(self, embeddings):
        non_padding_mask = (embeddings != self.mask_value)
        
        # Count valid positions per sequence
        valid_lengths = non_padding_mask.sum(dim=1)
        
        return valid_lengths
    
    def _compute_stable_average(self, sum_pooled, valid_lengths):
        """Compute average with numerical stability."""
        stable_lengths = valid_lengths.float() + self.epsilon
        
        # Compute average
        averaged_result = sum_pooled / stable_lengths
        
        return averaged_result
    
    def forward(self, sequence_embeddings):

        return self.aggregate_sequence(sequence_embeddings)
    
    def get_aggregator_info(self):

        return {
            "aggregator_type": "Masked Average Pooling",
            "aggregator_class": "MaskedAverageAggregator",
            "epsilon": self.epsilon,
            "mask_value": self.mask_value,
            "aggregation_method": "average",
            "handles_padding": True
        }


class MaskedSumAggregator(nn.Module):    
    def __init__(self, mask_value=0.0):
        super(MaskedSumAggregator, self).__init__()
        
        self.mask_value = mask_value
    
    def aggregate_sequence(self, sequence_embeddings):

        sum_aggregated_embeddings = self._compute_sum_aggregation(sequence_embeddings)
        
        return sum_aggregated_embeddings
    
    def _compute_sum_aggregation(self, embeddings):

        return torch.sum(embeddings, dim=1)
    
    def forward(self, sequence_embeddings):

        return self.aggregate_sequence(sequence_embeddings)
    
    def get_aggregator_info(self):
        return {
            "aggregator_type": "Masked Sum Pooling",
            "aggregator_class": "MaskedSumAggregator",
            "mask_value": self.mask_value,
            "aggregation_method": "sum",
            "handles_padding": True
        }


class MaskedMaxAggregator(nn.Module):    
    def __init__(self, mask_value=0.0, mask_fill_value=-1e9):
        """Initialize the masked max aggregator."""
        super(MaskedMaxAggregator, self).__init__()
        
        # Store configuration
        self.mask_value = mask_value
        self.mask_fill_value = mask_fill_value
    
    def aggregate_sequence(self, sequence_embeddings):
        # Create mask for padding positions
        padding_mask = self._create_padding_mask(sequence_embeddings)
        
        # Apply mask by filling padding positions with very negative values
        masked_embeddings = self._apply_max_pooling_mask(sequence_embeddings, padding_mask)
        
        # Compute max pooling
        max_aggregated_embeddings = self._compute_max_aggregation(masked_embeddings)
        
        return max_aggregated_embeddings
    
    def _create_padding_mask(self, embeddings):
        """Create mask identifying padding positions."""
        return (embeddings == self.mask_value).all(dim=-1, keepdim=True)
    
    def _apply_max_pooling_mask(self, embeddings, padding_mask):
        masked_embeddings = embeddings.clone()
        masked_embeddings[padding_mask.expand_as(embeddings)] = self.mask_fill_value
        return masked_embeddings
    
    def _compute_max_aggregation(self, masked_embeddings):
        max_values, _ = torch.max(masked_embeddings, dim=1)
        return max_values
    
    def forward(self, sequence_embeddings):
        return self.aggregate_sequence(sequence_embeddings)


class AdaptiveSequenceAggregator(nn.Module):
    """
    Args:
        aggregation_method (str): Aggregation method ("average", "sum", "max")
        epsilon (float): Numerical stability constant for average pooling
        mask_value (float): Value used for padding
    """
    
    def __init__(self, aggregation_method="average", epsilon=1e-16, mask_value=0.0):
        """Initialize the adaptive sequence aggregator."""
        super(AdaptiveSequenceAggregator, self).__init__()
        
        self.aggregation_method = aggregation_method
        self.epsilon = epsilon
        self.mask_value = mask_value
        
        self.aggregator = self._create_aggregator(aggregation_method, epsilon, mask_value)
    
    def _create_aggregator(self, method, epsilon, mask_value):
        if method == "average":
            return MaskedAverageAggregator(epsilon=epsilon, mask_value=mask_value)
        elif method == "sum":
            return MaskedSumAggregator(mask_value=mask_value)
        elif method == "max":
            return MaskedMaxAggregator(mask_value=mask_value)
        else:
            raise ValueError(f"Unsupported aggregation method: {method}")
    
    def aggregate_sequence(self, sequence_embeddings):
        return self.aggregator.aggregate_sequence(sequence_embeddings)
    
    def forward(self, sequence_embeddings):
        return self.aggregate_sequence(sequence_embeddings)
    
    def switch_aggregation_method(self, new_method):
        self.aggregation_method = new_method
        self.aggregator = self._create_aggregator(new_method, self.epsilon, self.mask_value)
    
    def get_aggregator_info(self):
        base_info = {
            "aggregator_type": "Adaptive Sequence Aggregator",
            "aggregator_class": "AdaptiveSequenceAggregator",
            "current_method": self.aggregation_method,
            "epsilon": self.epsilon,
            "mask_value": self.mask_value
        }
        
        # Add method-specific info
        method_info = self.aggregator.get_aggregator_info()
        base_info.update(method_info)
        
        return base_info
