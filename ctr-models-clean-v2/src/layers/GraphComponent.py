import torch
import torch.nn as nn
import torch.nn.functional as F
from itertools import product


class FieldAwareGraphProcessor(nn.Module):
    """
    Args:
        field_count (int): Number of feature fields
        embedding_dimension (int): Dimension of feature embeddings
        graph_layer_count (int): Number of graph convolution layers
        reuse_graph_layers (bool): Whether to reuse graph layer parameters
        use_gru_updates (bool): Whether to use GRU for state updates
        use_residual_connections (bool): Whether to use residual connections
        processing_device: Device for computation
    """
    
    def __init__(self, 
                 field_count, 
                 embedding_dimension,
                 graph_layer_count=3,
                 reuse_graph_layers=False,
                 use_gru_updates=True,
                 use_residual_connections=True,
                 processing_device=None):
        super(FieldAwareGraphProcessor, self).__init__()
        
        self.field_count = field_count
        self.embedding_dimension = embedding_dimension
        self.graph_layer_count = graph_layer_count
        self.use_residual_connections = use_residual_connections
        self.reuse_graph_layers = reuse_graph_layers
        self.processing_device = processing_device
        
        self._setup_graph_convolution_layers(field_count, embedding_dimension, 
                                           graph_layer_count, reuse_graph_layers)
        self._setup_state_update_mechanism(embedding_dimension, use_gru_updates)
        self._setup_attention_mechanism(embedding_dimension)
        self._setup_graph_structure(field_count)
    
    def _setup_graph_convolution_layers(self, field_count, embedding_dim, layer_count, reuse_layers):
        if reuse_layers:
            # Single shared graph layer
            self.graph_convolution_processor = GraphConvolutionUnit(field_count, embedding_dim)
        else:
            # Multiple independent graph layers
            self.graph_convolution_processor = nn.ModuleList([
                GraphConvolutionUnit(field_count, embedding_dim)
                for _ in range(layer_count)
            ])
    
    def _setup_state_update_mechanism(self, embedding_dim, use_gru):

        self.state_update_unit = nn.GRUCell(embedding_dim, embedding_dim) if use_gru else None
    
    def _setup_attention_mechanism(self, embedding_dim):

        # Leaky ReLU activation for attention computation
        self.attention_activation = nn.LeakyReLU(negative_slope=0.01)
        
        self.attention_weight_generator = nn.Linear(embedding_dim * 2, 1, bias=False)
    
    def _setup_graph_structure(self, field_count):
        node_pairs = list(product(range(field_count), repeat=2))
        self.source_node_indices, self.destination_node_indices = zip(*node_pairs)
    
    def construct_attention_graph(self, node_embeddings):
        # Extract source and destination node embeddings
        source_embeddings = self._extract_source_embeddings(node_embeddings)
        destination_embeddings = self._extract_destination_embeddings(node_embeddings)
        
        # Concatenate embeddings for attention computation
        concatenated_embeddings = self._concatenate_node_embeddings(source_embeddings, destination_embeddings)
        
        # Compute attention weights
        attention_scores = self._compute_attention_scores(concatenated_embeddings)
        
        # Reshape and mask self-loops
        adjacency_matrix = self._create_adjacency_matrix(attention_scores)
        
        return adjacency_matrix
    
    def _extract_source_embeddings(self, embeddings):

        return embeddings[:, self.source_node_indices, :]
    
    def _extract_destination_embeddings(self, embeddings):

        return embeddings[:, self.destination_node_indices, :]
    
    def _concatenate_node_embeddings(self, source_emb, dest_emb):

        return torch.cat([source_emb, dest_emb], dim=-1)
    
    def _compute_attention_scores(self, concatenated_embeddings):
        attention_logits = self.attention_weight_generator(concatenated_embeddings)
        attention_scores = self.attention_activation(attention_logits)
        return attention_scores
    
    def _create_adjacency_matrix(self, attention_scores):
        adjacency_matrix = attention_scores.view(-1, self.field_count, self.field_count)
        
        self_loop_mask = torch.eye(self.field_count).to(self.processing_device)
        
        masked_matrix = adjacency_matrix.masked_fill(self_loop_mask.bool(), float('-inf'))
        
        normalized_adjacency = F.softmax(masked_matrix, dim=-1)
        
        return normalized_adjacency
    
    def process_graph_features(self, node_embeddings):
        adjacency_matrix = self.construct_attention_graph(node_embeddings)
        
        current_node_states = node_embeddings
        
        for layer_index in range(self.graph_layer_count):
            # Apply graph convolution
            updated_states = self._apply_graph_convolution(adjacency_matrix, current_node_states, layer_index)
            
            # Update node states
            current_node_states = self._update_node_states(updated_states, current_node_states)
            
            if self.use_residual_connections:
                current_node_states = self._apply_residual_connection(current_node_states, node_embeddings)
        
        return current_node_states
    
    def _apply_graph_convolution(self, adjacency_matrix, node_states, layer_index):
        """Apply graph convolution operation."""
        if self.reuse_graph_layers:
            return self.graph_convolution_processor(adjacency_matrix, node_states)
        else:
            return self.graph_convolution_processor[layer_index](adjacency_matrix, node_states)
    
    def _update_node_states(self, new_states, current_states):
        if self.state_update_unit is not None:
            return self._apply_gru_update(new_states, current_states)
        else:
            return new_states + current_states
    
    def _apply_gru_update(self, new_states, current_states):
        # Reshape for GRU processing
        batch_size = new_states.size(0)
        flattened_new = new_states.view(-1, self.embedding_dimension)
        flattened_current = current_states.view(-1, self.embedding_dimension)
        
        updated_states = self.state_update_unit(flattened_new, flattened_current)
        
        reshaped_states = updated_states.view(batch_size, self.field_count, self.embedding_dimension)
        
        return reshaped_states
    
    def _apply_residual_connection(self, current_states, original_embeddings):

        return current_states + original_embeddings
    
    def forward(self, node_embeddings):

        return self.process_graph_features(node_embeddings)
    
    def get_graph_processor_info(self):
        """        
        Returns:
            dict: Graph processor configuration details
        """
        return {
            "processor_type": "Field-aware Graph Neural Network",
            "processor_class": "FieldAwareGraphProcessor",
            "field_count": self.field_count,
            "embedding_dimension": self.embedding_dimension,
            "graph_layer_count": self.graph_layer_count,
            "reuse_graph_layers": self.reuse_graph_layers,
            "use_gru_updates": self.state_update_unit is not None,
            "use_residual_connections": self.use_residual_connections,
            "total_parameters": sum(p.numel() for p in self.parameters() if p.requires_grad)
        }


class GraphConvolutionUnit(nn.Module):    
    def __init__(self, field_count, embedding_dimension):

        super(GraphConvolutionUnit, self).__init__()
        
        self.field_count = field_count
        self.embedding_dimension = embedding_dimension
        
        self._setup_transformation_matrices(field_count, embedding_dimension)
        self._setup_bias_parameters(embedding_dimension)
    
    def _setup_transformation_matrices(self, field_count, embedding_dim):
        self.input_transformation_weights = nn.Parameter(
            torch.Tensor(field_count, embedding_dim, embedding_dim)
        )
        
        self.output_transformation_weights = nn.Parameter(
            torch.Tensor(field_count, embedding_dim, embedding_dim)
        )
        
        nn.init.xavier_normal_(self.input_transformation_weights)
        nn.init.xavier_normal_(self.output_transformation_weights)
    
    def _setup_bias_parameters(self, embedding_dim):
        self.transformation_bias = nn.Parameter(torch.zeros(embedding_dim))
    
    def perform_graph_convolution(self, adjacency_matrix, node_features):
        transformed_features = self._apply_output_transformation(node_features)
        
        aggregated_features = self._aggregate_neighbor_features(adjacency_matrix, transformed_features)
        
        final_features = self._apply_input_transformation_and_bias(aggregated_features)
        
        return final_features
    
    def _apply_output_transformation(self, node_features):
        expanded_features = node_features.unsqueeze(-1)
        transformed = torch.matmul(self.output_transformation_weights, expanded_features)
        return transformed.squeeze(-1)
    
    def _aggregate_neighbor_features(self, adjacency_matrix, transformed_features):
        return torch.bmm(adjacency_matrix, transformed_features)
    
    def _apply_input_transformation_and_bias(self, aggregated_features):
        expanded_aggregated = aggregated_features.unsqueeze(-1)
        transformed = torch.matmul(self.input_transformation_weights, expanded_aggregated)
        squeezed_transformed = transformed.squeeze(-1)
        
        final_features = squeezed_transformed + self.transformation_bias
        
        return final_features
    
    def forward(self, adjacency_matrix, node_features):

        return self.perform_graph_convolution(adjacency_matrix, node_features)
    
    def get_convolution_unit_info(self):
        input_params = self.input_transformation_weights.numel()
        output_params = self.output_transformation_weights.numel()
        bias_params = self.transformation_bias.numel()
        
        return {
            "unit_type": "Graph Convolution Unit",
            "unit_class": "GraphConvolutionUnit",
            "field_count": self.field_count,
            "embedding_dimension": self.embedding_dimension,
            "parameter_counts": {
                "input_transformation": input_params,
                "output_transformation": output_params,
                "bias": bias_params,
                "total": input_params + output_params + bias_params
            },
            "transformation_matrix_shapes": {
                "input": list(self.input_transformation_weights.shape),
                "output": list(self.output_transformation_weights.shape)
            }
        }

class GraphLayer(nn.Module):
    def __init__(self, num_fields, embedding_dim):
        super(GraphLayer, self).__init__()
        self.W_in = torch.nn.Parameter(torch.Tensor(num_fields, embedding_dim, embedding_dim))
        self.W_out = torch.nn.Parameter(torch.Tensor(num_fields, embedding_dim, embedding_dim))
        nn.init.xavier_normal_(self.W_in)
        nn.init.xavier_normal_(self.W_out)
        self.num_fields = num_fields
        self.embedding_dim = embedding_dim

    def forward(self, g, h):
        h_in = torch.matmul(h.unsqueeze(1), self.W_in).squeeze(1)
        h_out = torch.matmul(h.unsqueeze(1), self.W_out).squeeze(1)

        return torch.matmul(g, h_in) + h_out

FiGNN_Layer = FieldAwareGraphProcessor
GraphComponent = FieldAwareGraphProcessor
