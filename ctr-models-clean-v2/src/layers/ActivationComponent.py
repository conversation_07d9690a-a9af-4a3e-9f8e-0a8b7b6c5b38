import torch
import torch.nn as nn


class AdaptiveActivationFunction(nn.Module):
    """
    Args:
        feature_dimension (int): Input feature dimension
        scaling_factor (float): Scaling factor for negative part
        epsilon (float): Small constant for numerical stability
    """
    
    def __init__(self, feature_dimension, scaling_factor=0.0, epsilon=1e-8):
        """
        Initialize the adaptive activation function
        """
        super(AdaptiveActivationFunction, self).__init__()
        
        self.feature_dimension = feature_dimension
        self.epsilon = epsilon
        
        self.normalization_component = self._create_normalization_layer(feature_dimension, epsilon)
        
        self.adaptive_scaling = self._create_scaling_parameter(scaling_factor)
    
    def _create_normalization_layer(self, dimension, eps):
        return nn.BatchNorm1d(dimension, eps=eps)
    
    def _create_scaling_parameter(self, initial_value):
        return nn.Parameter(torch.tensor(initial_value), requires_grad=False)
    
    def compute_activation(self, input_tensor):
        """        
        Args:
            input_tensor (torch.Tensor): Input tensor to activate
            
        Returns:
            torch.Tensor: Activated output tensor
        """
        normalized_features = self._apply_normalization(input_tensor)
        
        probability_weights = self._compute_probability_weights(normalized_features)
        
        activated_output = self._generate_adaptive_output(
            input_tensor, probability_weights, self.adaptive_scaling
        )
        
        return activated_output
    
    def _apply_normalization(self, tensor):
        return self.normalization_component(tensor)
    
    def _compute_probability_weights(self, normalized_tensor):
        return torch.sigmoid(normalized_tensor)
    
    def _generate_adaptive_output(self, original_input, probabilities, scaling):
        positive_component = probabilities * original_input
        negative_component = (1 - probabilities) * scaling * original_input
        return positive_component + negative_component
    
    def forward(self, input_tensor):

        return self.compute_activation(input_tensor)
    
    def get_component_info(self):

        return {
            "component_type": "Adaptive Activation Function",
            "component_class": "AdaptiveActivationFunction",
            "feature_dimension": self.feature_dimension,
            "scaling_factor": self.adaptive_scaling.item(),
            "epsilon": self.epsilon,
            "trainable_parameters": sum(p.numel() for p in self.parameters() if p.requires_grad),
            "total_parameters": sum(p.numel() for p in self.parameters())
        }


class LinearActivationComponent(nn.Module):    
    def __init__(self):
        super(LinearActivationComponent, self).__init__()
    
    def compute_activation(self, input_tensor):
        return input_tensor
    
    def forward(self, input_tensor):
        return self.compute_activation(input_tensor)


class ReLUActivationComponent(nn.Module):
    def __init__(self, negative_slope=0.0, inplace_operation=False):
        """        
        Args:
            negative_slope (float): Slope for negative values (LeakyReLU behavior)
            inplace_operation (bool): Whether to perform inplace operations
        """
        super(ReLUActivationComponent, self).__init__()
        self.negative_slope = negative_slope
        self.inplace_operation = inplace_operation
        
        if negative_slope > 0:
            self.activation_function = nn.LeakyReLU(negative_slope, inplace=inplace_operation)
        else:
            self.activation_function = nn.ReLU(inplace=inplace_operation)
    
    def compute_activation(self, input_tensor):
        return self.activation_function(input_tensor)
    
    def forward(self, input_tensor):
        return self.compute_activation(input_tensor)
