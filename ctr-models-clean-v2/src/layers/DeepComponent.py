import numpy as np
import torch
import torch.nn as nn


def create_activation_function(activation_spec):
    if isinstance(activation_spec, str):
        activation_name = activation_spec.lower()
        
        if activation_name == "relu":
            return nn.ReLU()
        elif activation_name == "sigmoid":
            return nn.Sigmoid()
        elif activation_name == "tanh":
            return nn.Tanh()
        elif activation_name == "leakyrelu":
            return nn.LeakyReLU()
        elif activation_name == "elu":
            return nn.ELU()
        elif activation_name == "gelu":
            return nn.GELU()
        else:
            return getattr(nn, activation_spec)()
    else:
        return activation_spec


class DeepNeuralNetworkComponent(nn.Module):
    """    
    Args:
        input_dimension (int): Input feature dimension
        output_dimension (int): Output dimension
        hidden_layer_sizes (list): List of hidden layer sizes
        activation_functions: Activation functions for hidden layers
        output_activation: Final activation function
        dropout_probabilities: Dropout rates for each layer
        use_batch_normalization (bool): Whether to use batch normalization
        include_bias (bool): Whether to include bias terms
    """
    
    def __init__(self, 
                 input_dimension, 
                 output_dimension=None, 
                 hidden_layer_sizes=[], 
                 activation_functions="ReLU",
                 output_activation=None, 
                 dropout_probabilities=[], 
                 use_batch_normalization=False, 
                 include_bias=True):
        super(DeepNeuralNetworkComponent, self).__init__()
        
        self.input_dimension = input_dimension
        self.output_dimension = output_dimension
        self.hidden_layer_sizes = hidden_layer_sizes
        self.use_batch_normalization = use_batch_normalization
        self.include_bias = include_bias
        
        processed_dropout_rates = self._process_dropout_configuration(dropout_probabilities, hidden_layer_sizes)
        processed_activations = self._process_activation_configuration(activation_functions, hidden_layer_sizes)
        
        self.neural_network_stack = self._build_network_architecture(
            input_dimension, output_dimension, hidden_layer_sizes,
            processed_activations, processed_dropout_rates, 
            output_activation, use_batch_normalization, include_bias
        )
    
    def _process_dropout_configuration(self, dropout_spec, layer_sizes):
        if not isinstance(dropout_spec, list):
            return [dropout_spec] * len(layer_sizes)
        return dropout_spec
    
    def _process_activation_configuration(self, activation_spec, layer_sizes):
        if not isinstance(activation_spec, list):
            activation_list = [activation_spec] * len(layer_sizes)
        else:
            activation_list = activation_spec
        
        return [create_activation_function(act) for act in activation_list]
    
    def _build_network_architecture(self, input_dim, output_dim, hidden_sizes,
                                  activations, dropout_rates, final_activation,
                                  batch_norm, use_bias):
        network_layers = []
        
        layer_dimensions = [input_dim] + hidden_sizes
        
        # Build hidden layers
        network_layers.extend(
            self._build_hidden_layers(layer_dimensions, activations, 
                                    dropout_rates, batch_norm, use_bias)
        )
        
        if output_dim is not None:
            network_layers.extend(
                self._build_output_layer(layer_dimensions[-1], output_dim, 
                                       final_activation, use_bias)
            )
        
        return nn.Sequential(*network_layers)
    
    def _build_hidden_layers(self, layer_dims, activations, dropout_rates, batch_norm, use_bias):
        hidden_layers = []
        
        for layer_index in range(len(layer_dims) - 1):
            linear_layer = nn.Linear(
                layer_dims[layer_index], 
                layer_dims[layer_index + 1], 
                bias=use_bias
            )
            hidden_layers.append(linear_layer)
            
            if batch_norm:
                batch_norm_layer = nn.BatchNorm1d(layer_dims[layer_index + 1])
                hidden_layers.append(batch_norm_layer)
            
            if activations[layer_index] is not None:
                hidden_layers.append(activations[layer_index])
            
            if dropout_rates[layer_index] > 0:
                dropout_layer = nn.Dropout(p=dropout_rates[layer_index])
                hidden_layers.append(dropout_layer)
        
        return hidden_layers
    
    def _build_output_layer(self, input_dim, output_dim, final_activation, use_bias):
        output_layers = []
        
        output_linear = nn.Linear(input_dim, output_dim, bias=use_bias)
        output_layers.append(output_linear)
        
        if final_activation is not None:
            final_activation_func = create_activation_function(final_activation)
            output_layers.append(final_activation_func)
        
        return output_layers
    
    def process_features(self, input_features):

        return self.neural_network_stack(input_features)
    
    def forward(self, input_features):

        return self.process_features(input_features)
    
    def get_network_architecture_info(self):

        total_params = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        # Count parameters by layer type
        linear_params = sum(
            p.numel() for module in self.neural_network_stack 
            for p in module.parameters() 
            if isinstance(module, nn.Linear) and p.requires_grad
        )
        
        return {
            "component_type": "Deep Neural Network",
            "component_class": "DeepNeuralNetworkComponent",
            "total_parameters": total_params,
            "linear_parameters": linear_params,
            "architecture_config": {
                "input_dimension": self.input_dimension,
                "output_dimension": self.output_dimension,
                "hidden_layer_sizes": self.hidden_layer_sizes,
                "use_batch_normalization": self.use_batch_normalization,
                "include_bias": self.include_bias
            },
            "layer_count": len(self.hidden_layer_sizes) + (1 if self.output_dimension else 0)
        }
    
    def get_layer_outputs(self, input_features):

        layer_outputs = []
        current_output = input_features
        
        for layer in self.neural_network_stack:
            current_output = layer(current_output)
            layer_outputs.append(current_output.clone())
        
        return layer_outputs


class ConvolutionalFeatureGenerator(nn.Module):
    """
    Args:
        field_count (int): Number of feature fields
        embedding_dimension (int): Dimension of embeddings
        channel_configurations (list): Channel sizes for each conv layer
        kernel_height_specs (list): Kernel heights for each conv layer
        pooling_size_specs (list): Pooling sizes for each conv layer
        recombination_channels (list): Recombination channel sizes
        activation_function (str): Activation function type
        use_batch_normalization (bool): Whether to use batch normalization
    """
    
    def __init__(self, 
                 field_count, 
                 embedding_dimension,
                 channel_configurations=[3], 
                 kernel_height_specs=[3], 
                 pooling_size_specs=[2],
                 recombination_channels=[2],
                 activation_function="Tanh",
                 use_batch_normalization=True):
        super(ConvolutionalFeatureGenerator, self).__init__()
        
        self.embedding_dimension = embedding_dimension
        self.field_count = field_count
        
        self.convolution_processors, self.feature_recombiners = self._build_conv_architecture(
            field_count, channel_configurations, kernel_height_specs,
            pooling_size_specs, recombination_channels, activation_function,
            use_batch_normalization
        )
    
    def _build_conv_architecture(self, field_count, channels, kernel_heights,
                               pooling_sizes, recomb_channels, activation, batch_norm):
        conv_processors = nn.ModuleList()
        feature_recombiners = nn.ModuleList()
        
        channel_sequence = [1] + channels
        current_height = field_count
        
        for layer_idx in range(1, len(channel_sequence)):
            conv_processor = self._build_conv_processor(
                channel_sequence[layer_idx - 1], channel_sequence[layer_idx],
                kernel_heights[layer_idx - 1], activation, batch_norm
            )
            conv_processors.append(conv_processor)
            
            feature_recombiner = self._build_feature_recombiner(
                current_height, channel_sequence[layer_idx],
                recomb_channels[layer_idx - 1], pooling_sizes[layer_idx - 1]
            )
            feature_recombiners.append(feature_recombiner)
            
            current_height = int(np.ceil(current_height / pooling_sizes[layer_idx - 1]))
        
        return conv_processors, feature_recombiners
    
    def _build_conv_processor(self, in_channels, out_channels, kernel_height, activation, batch_norm):
        layers = []
        
        conv_layer = nn.Conv2d(
            in_channels, out_channels, 
            kernel_size=(kernel_height, 1), 
            padding=(int((kernel_height - 1) / 2), 0)
        )
        layers.append(conv_layer)
        
        if batch_norm:
            layers.append(nn.BatchNorm2d(out_channels))
        
        layers.append(create_activation_function(activation))
        
        return nn.Sequential(*layers)
    
    def _build_feature_recombiner(self, height, channels, recomb_channels, pooling_size):
        return nn.Sequential(
            nn.Conv2d(channels, recomb_channels, kernel_size=(height, 1)),
            nn.AvgPool2d(kernel_size=(pooling_size, 1), stride=(pooling_size, 1))
        )

DNN_Layer = DeepNeuralNetworkComponent
FGCNN_Layer = ConvolutionalFeatureGenerator
DeepComponent = DeepNeuralNetworkComponent
