import torch
import torch.nn as nn
from .embedding import Embedding<PERSON>ayer
from .interaction import InnerProduct<PERSON><PERSON><PERSON>, InnerProductLayer_v2


class LinearRegressionComponent(nn.Module):
    """
    Args:
        feature_mapping: Feature specification object
        output_activation: Activation function
        include_bias (bool): Whether to include bias
    """
    
    def __init__(self, feature_mapping, output_activation=None, include_bias=True):
        super(LinearRegressionComponent, self).__init__()
        
        self.output_activation = output_activation
        self.include_bias = include_bias
        
        self.bias_parameter = self._create_bias_parameter(include_bias)
        
        self.feature_weight_embedder = self._create_weight_embedder(feature_mapping)
    
    def _create_bias_parameter(self, use_bias):
        if use_bias:
            return nn.Parameter(torch.zeros(1), requires_grad=True)
        return None
    
    def _create_weight_embedder(self, feature_mapping):

        return EmbeddingLayer(feature_mapping, 1)
    
    def compute_output(self, input_features):
        # Generate feature weight embeddings
        embedding_weights = self._generate_feature_weights(input_features)
        
        # Compute weighted sum
        linear_output = self._compute_weighted_sum(embedding_weights)
        
        linear_output = self._add_bias_term(linear_output)
        
        final_output = self._apply_output_activation(linear_output)
        
        return final_output
    
    def _generate_feature_weights(self, features):

        return self.feature_weight_embedder(features)
    
    def _compute_weighted_sum(self, weight_embeddings):

        return torch.stack(weight_embeddings).sum(dim=0)
    
    def _add_bias_term(self, linear_output):
        if self.bias_parameter is not None:
            return linear_output + self.bias_parameter
        return linear_output
    
    def _apply_output_activation(self, output):
        if self.output_activation is not None:
            return self.output_activation(output)
        return output
    
    def forward(self, input_features):

        return self.compute_output(input_features)


class FactorizationComponent(nn.Module):    
    def __init__(self, feature_mapping, output_activation=None, include_bias=True):
        super(FactorizationComponent, self).__init__()
        
        self.output_activation = output_activation
        
        # Initialize
        self.interaction_processor = self._create_interaction_processor()
        self.linear_processor = self._create_linear_processor(feature_mapping, include_bias)
    
    def _create_interaction_processor(self):

        return InnerProductLayer(output="sum")
    
    def _create_linear_processor(self, feature_mapping, include_bias):

        return LinearRegressionComponent(
            feature_mapping, 
            output_activation=None, 
            include_bias=include_bias
        )
    
    def compute_output(self, input_features, feature_embedding_list):
        # Compute linear component
        linear_component = self._compute_linear_component(input_features)
        
        # Compute interaction component
        interaction_component = self._compute_interaction_component(feature_embedding_list)
        
        # Combine components
        combined_output = self._combine_components(linear_component, interaction_component)
        
        final_output = self._apply_final_activation(combined_output)
        
        return final_output
    
    def _compute_linear_component(self, features):

        return self.linear_processor.compute_output(features)
    
    def _compute_interaction_component(self, embedding_list):

        return self.interaction_processor(embedding_list)
    
    def _combine_components(self, linear_part, interaction_part):

        return interaction_part + linear_part
    
    def _apply_final_activation(self, output):
        if self.output_activation is not None:
            return self.output_activation(output)
        return output
    
    def forward(self, input_features, feature_embedding_list):
        return self.compute_output(input_features, feature_embedding_list)


class AdvancedFactorizationComponent(nn.Module):    
    def __init__(self, feature_mapping, output_activation=None, include_bias=True):

        super(AdvancedFactorizationComponent, self).__init__()
        
        self.output_activation = output_activation
        
        self.optimized_interaction_processor = self._create_optimized_interaction_processor(feature_mapping)
        self.linear_computation_component = self._create_linear_component(feature_mapping, include_bias)
    
    def _create_optimized_interaction_processor(self, feature_mapping):
        """Create optimized interaction processing component."""
        return InnerProductLayer_v2(feature_mapping.num_fields, output="sum")
    
    def _create_linear_component(self, feature_mapping, include_bias):
        """Create linear computation component."""
        return LinearRegressionComponent(
            feature_mapping, 
            output_activation=None, 
            include_bias=include_bias
        )
    
    def compute_output(self, input_features, feature_embedding_tensor):

        linear_result = self._process_linear_features(input_features)
        
        interaction_result = self._process_feature_interactions(feature_embedding_tensor)
        
        combined_result = self._merge_computation_results(interaction_result, linear_result)
        
        # Apply transformation
        final_result = self._apply_final_transformation(combined_result)
        
        return final_result
    
    def _process_linear_features(self, features):
        return self.linear_computation_component.compute_output(features)
    
    def _process_feature_interactions(self, embedding_tensor):
        return self.optimized_interaction_processor(embedding_tensor)
    
    def _merge_computation_results(self, interaction_result, linear_result):

        return interaction_result + linear_result
    
    def _apply_final_transformation(self, combined_result):
        if self.output_activation is not None:
            return self.output_activation(combined_result)
        return combined_result
    
    def forward(self, input_features, feature_embedding_tensor):
        return self.compute_output(input_features, feature_embedding_tensor)

LR_Layer = LinearRegressionComponent
FM_Layer = FactorizationComponent
FM_Layer_v2 = AdvancedFactorizationComponent
ShallowComponent = FactorizationComponent
