import torch
import torch.optim as optim
import torch.nn.functional as F
import numpy as np
import logging
import wandb
from torch.utils.data import DataLoader, TensorDataset
import math


class RefinedStableLosses:
    """
    精调的稳定损失函数 - 更温和的平衡策略
    """
    
    @staticmethod
    def stable_adversarial_loss(real_scores, fake_scores, mode='lsgan', label_smoothing=0.1):
        """稳定的对抗损失 - 增加标签平滑"""
        if mode == 'lsgan':
            if real_scores is not None:
                real_target = 1.0 - label_smoothing
                fake_target = 0.0 + label_smoothing
                
                real_loss = F.mse_loss(real_scores, torch.full_like(real_scores, real_target))
                fake_loss = F.mse_loss(fake_scores, torch.full_like(fake_scores, fake_target))
                d_loss = (real_loss + fake_loss) / 2
            else:
                d_loss = None
                
            g_loss = F.mse_loss(fake_scores, torch.ones_like(fake_scores))
            
        elif mode == 'vanilla':
            if real_scores is not None:
                real_labels = torch.ones_like(real_scores) * (1.0 - label_smoothing)
                fake_labels = torch.zeros_like(fake_scores) + label_smoothing
                
                real_loss = F.binary_cross_entropy(real_scores, real_labels)
                fake_loss = F.binary_cross_entropy(fake_scores, fake_labels)
                d_loss = (real_loss + fake_loss) / 2
            else:
                d_loss = None
                
            g_loss = F.binary_cross_entropy(fake_scores, torch.ones_like(fake_scores))
            
        else:
            raise ValueError(f"Unknown adversarial loss mode: {mode}")
            
        return d_loss, g_loss
    
    @staticmethod
    def enhanced_feature_matching_loss(real_features, fake_features):
        """增强的特征匹配损失"""
        # 1. 均值匹配
        mean_real = torch.mean(real_features, dim=0)
        mean_fake = torch.mean(fake_features, dim=0)
        mean_loss = F.mse_loss(mean_fake, mean_real)
        
        # 2. 标准差匹配
        std_real = torch.std(real_features, dim=0)
        std_fake = torch.std(fake_features, dim=0)
        std_loss = F.mse_loss(std_fake, std_real)
        
        # 3. 协方差匹配（降低权重）
        def compute_covariance(features):
            mean = torch.mean(features, dim=0, keepdim=True)
            centered = features - mean
            cov = torch.mm(centered.t(), centered) / (features.size(0) - 1)
            return cov
        
        try:
            cov_real = compute_covariance(real_features)
            cov_fake = compute_covariance(fake_features)
            cov_loss = F.mse_loss(cov_fake, cov_real)
        except:
            cov_loss = 0.0
        
        # 温和的权重组合
        total_loss = mean_loss + 1.5 * std_loss + 0.05 * cov_loss
        
        return total_loss
    
    @staticmethod
    def gentle_numeric_diversity_loss(numeric_features):
        """
        改进的数值特征多样性损失 - 更强的多样性要求
        """
        if numeric_features is None:
            return torch.tensor(0.0)

        # 计算每个特征的标准差
        feature_stds = torch.std(numeric_features, dim=0)

        # 进一步提高标准差要求
        min_std = 0.08  # 进一步提高最小标准差要求
        std_penalty = torch.mean(F.relu(min_std - feature_stds) ** 2)  # 平方惩罚

        # 强烈鼓励特征间的差异
        feature_means = torch.mean(numeric_features, dim=0)
        mean_diff = torch.std(feature_means)
        diversity_reward = -0.2 * mean_diff  # 增加权重

        # 惩罚总体方差过小
        total_var = torch.var(numeric_features)
        var_penalty = 2.0 * F.relu(0.02 - total_var)  # 提高阈值和权重

        # 添加特征范围惩罚
        feature_ranges = torch.max(numeric_features, dim=0)[0] - torch.min(numeric_features, dim=0)[0]
        min_range = 0.5  # 要求每个特征至少有0.5的范围
        range_penalty = torch.mean(F.relu(min_range - feature_ranges))

        return std_penalty + diversity_reward + var_penalty + range_penalty
    
    @staticmethod
    def enhanced_diversity_loss(categorical_probs, vocab_sizes):
        """增强的多样性损失"""
        if categorical_probs is None or len(categorical_probs) == 0:
            return torch.tensor(0.0)
        
        total_diversity_loss = 0.0
        total_weight = 0.0
        
        for i, (probs, vocab_size) in enumerate(zip(categorical_probs, vocab_sizes)):
            if probs is None:
                continue
                
            # 计算特征权重
            feature_weight = min(math.log(vocab_size + 1), 5.0)
            
            # 计算熵
            avg_probs = torch.mean(probs, dim=0) + 1e-8
            entropy = -torch.sum(avg_probs * torch.log(avg_probs))
            max_entropy = torch.log(torch.tensor(vocab_size, dtype=torch.float, device=probs.device))
            normalized_entropy = entropy / max_entropy
            
            # 多样性损失 = 负熵
            diversity_loss = -normalized_entropy
            
            # 加权累加
            total_diversity_loss += feature_weight * diversity_loss
            total_weight += feature_weight
        
        # 平均化
        if total_weight > 0:
            return total_diversity_loss / total_weight
        else:
            return torch.tensor(0.0)
    
    @staticmethod
    def discriminator_regularization_loss(real_scores, fake_scores):
        """判别器正则化损失 - 强力防止过度自信，促进平衡"""
        # 非常强的正则化，强制平衡
        real_reg = torch.mean((real_scores - 0.55) ** 2)  # 希望real_score接近0.55
        fake_reg = torch.mean((fake_scores - 0.45) ** 2)  # 希望fake_score接近0.45

        # 强力分数差距惩罚
        score_gap = torch.mean(real_scores) - torch.mean(fake_scores)
        gap_penalty = 3.0 * F.relu(score_gap - 0.2) ** 2  # 大幅惩罚分数差距过大

        # 添加极端分数惩罚
        extreme_real_penalty = torch.mean(F.relu(real_scores - 0.8) ** 2)  # 惩罚过高的real_score
        extreme_fake_penalty = torch.mean(F.relu(0.2 - fake_scores) ** 2)  # 惩罚过低的fake_score

        return (real_reg + fake_reg) / 2 + gap_penalty + extreme_real_penalty + extreme_fake_penalty


class RefinedProgressiveTrainer:
    """
    精调的渐进式训练器 - 温和平衡策略
    """
    
    def __init__(self, generator, discriminator, config):
        self.generator = generator
        self.discriminator = discriminator
        self.config = config
        
        # 设备设置
        if torch.backends.mps.is_available():
            self.device = torch.device("mps")
        elif torch.cuda.is_available():
            self.device = torch.device("cuda")
        else:
            self.device = torch.device("cpu")
            
        self.generator.to(self.device)
        self.discriminator.to(self.device)
        
        # 优化器 - 精调的学习率比例
        self.g_optimizer = optim.Adam(
            self.generator.parameters(),
            lr=config.generator_lr,  # 5e-5
            betas=(0.5, 0.999),
            weight_decay=1e-6
        )
        
        self.d_optimizer = optim.Adam(
            self.discriminator.parameters(), 
            lr=config.discriminator_lr,  # 2e-5
            betas=(0.5, 0.999),
            weight_decay=2e-6  # 增加判别器权重衰减
        )
        
        # 学习率调度器 - 更保守
        self.g_scheduler = optim.lr_scheduler.ExponentialLR(self.g_optimizer, gamma=0.9998)
        self.d_scheduler = optim.lr_scheduler.ExponentialLR(self.d_optimizer, gamma=0.9999)
        
        # 损失函数
        self.losses = RefinedStableLosses()
        
        # 训练状态
        self.current_epoch = 0
        self.total_steps = 0
        
        # 温度控制 - 更保守
        self.current_temperature = config.initial_temperature
        self.temperature_decay = config.temperature_decay
        self.min_temperature = config.min_temperature
        
        # 训练阶段控制
        self.training_stage = 0
        self.stage_switch_epochs = [30, 50]  # 更晚切换阶段
        
        # 统计信息
        self.d_loss_history = []
        self.g_loss_history = []
        self.real_score_history = []
        self.fake_score_history = []
        
        # 数值特征质量监控
        self.numeric_std_history = []
        self.numeric_range_history = []
        
        # 温和的稳定性监控
        self.stability_monitor = {
            'consecutive_bad_epochs': 0,
            'best_g_loss': float('inf'),
            'best_numeric_quality': 0.0,
            'entropy_history': [],
            'score_gap_history': [],
            'gentle_intervention_count': 0,
            'last_gentle_intervention_epoch': -10
        }
        
        self.logger = logging.getLogger(__name__)
        
    def get_current_training_config(self):
        """根据训练阶段获取当前配置 - 更温和的权重"""
        if self.training_stage == 0:
            # 基础训练阶段：强化生成器训练
            return {
                'd_steps': 1,
                'g_steps': 3,  # 增加生成器训练频率以对抗强判别器
                'lambda_feature': 0.05,  # 进一步降低特征匹配权重
                'lambda_diversity': 1.0,  # 进一步增加多样性权重
                'lambda_mode_seeking': 0.0,
                'lambda_numeric_diversity': 8.0,  # 大幅增加数值多样性权重
                'lambda_d_reg': 0.15,  # 大幅增加判别器正则化以平衡训练
                'loss_mode': 'lsgan'
            }
        elif self.training_stage == 1:
            # 稳定训练阶段
            return {
                'd_steps': 1,
                'g_steps': 2,
                'lambda_feature': 0.5,
                'lambda_diversity': 0.3,
                'lambda_mode_seeking': 0.1,
                'lambda_numeric_diversity': 1.5,
                'lambda_d_reg': 0.01,
                'loss_mode': 'lsgan'
            }
        else:
            # 精细调优阶段
            return {
                'd_steps': 1,
                'g_steps': 1,
                'lambda_feature': 0.8,
                'lambda_diversity': 0.2,
                'lambda_mode_seeking': 0.2,
                'lambda_numeric_diversity': 1.0,
                'lambda_d_reg': 0.005,
                'loss_mode': 'lsgan'
            }
    
    def update_training_stage(self):
        """更新训练阶段"""
        if (self.current_epoch >= self.stage_switch_epochs[0] and 
            self.training_stage == 0):
            self.training_stage = 1
            self.logger.info(f"切换到稳定训练阶段 (Stage 1)")
            
        elif (self.current_epoch >= self.stage_switch_epochs[1] and 
              self.training_stage == 1):
            self.training_stage = 2
            self.logger.info(f"切换到精细调优阶段 (Stage 2)")
    
    def prepare_real_data(self, batch):
        """准备真实数据"""
        numeric_data = None
        categorical_data = None
        labels = None
        
        if isinstance(batch, (list, tuple)) and len(batch) == 2:
            features, labels = batch
            
            feature_idx = 0
            
            # 数值特征
            if self.generator.num_numeric > 0:
                numeric_data = features[:, feature_idx:feature_idx + self.generator.num_numeric].float()
                feature_idx += self.generator.num_numeric
            
            # 类别特征
            if self.generator.num_categorical > 0:
                categorical_data = features[:, feature_idx:feature_idx + self.generator.num_categorical].long()
                
        # 移动到设备
        if numeric_data is not None:
            numeric_data = numeric_data.to(self.device)
        if categorical_data is not None:
            categorical_data = categorical_data.to(self.device)
        if labels is not None:
            labels = labels.float().to(self.device)
            
        return numeric_data, categorical_data, labels
    
    def generate_fake_data(self, batch_size):
        """生成假数据"""
        noise = torch.randn(batch_size, self.generator.noise_dim, device=self.device)
        
        gen_output = self.generator(
            noise, 
            temperature=self.current_temperature, 
            hard=False
        )
            
        return gen_output, noise
    
    def check_gentle_intervention_needed(self):
        """温和检查是否需要干预"""
        if len(self.real_score_history) < 10:
            return False
            
        recent_real = np.mean(self.real_score_history[-10:])
        recent_fake = np.mean(self.fake_score_history[-10:])
        recent_std = np.mean(self.numeric_std_history[-5:]) if self.numeric_std_history else 0.1
        
        # 更温和的干预条件
        score_gap = recent_real - recent_fake
        
        # 只在极端情况下干预
        severe_imbalance = score_gap > 0.4  # 提高阈值
        severe_collapse = recent_std < 0.02  # 降低阈值
        
        return severe_imbalance or severe_collapse
    
    def train_discriminator(self, real_batch, train_config):
        """训练判别器 - 增加正则化"""
        self.d_optimizer.zero_grad()
        
        real_numeric, real_categorical, real_labels = self.prepare_real_data(real_batch)
        batch_size = real_labels.size(0)
        
        # 验证数据
        if real_numeric is not None and (torch.isnan(real_numeric).any() or torch.isinf(real_numeric).any()):
            self.logger.warning("检测到NaN或Inf在真实数值数据中")
            return {'d_loss': 0.0, 'd_adv_loss': 0.0, 'd_ctr_loss': 0.0, 'd_reg_loss': 0.0}
        
        # 真实数据通过判别器
        real_d_score, real_ctr_score, real_features = self.discriminator(
            numeric_data=real_numeric,
            categorical_data=real_categorical
        )
        
        # 生成假数据
        with torch.no_grad():
            gen_output, _ = self.generate_fake_data(batch_size)
        
        # 假数据通过判别器
        fake_d_score, fake_ctr_score, fake_features = self.discriminator(
            numeric_data=gen_output['numeric'],
            categorical_embeddings=gen_output['categorical_embeddings']
        )
        
        # 计算损失
        d_adv_loss, _ = self.losses.stable_adversarial_loss(
            real_d_score, fake_d_score, mode=train_config['loss_mode']
        )
        
        d_ctr_loss = F.binary_cross_entropy(real_ctr_score.squeeze(), real_labels.squeeze())
        
        # 新增：判别器正则化损失
        d_reg_loss = self.losses.discriminator_regularization_loss(real_d_score, fake_d_score)
        
        # 总损失
        d_loss = (d_adv_loss + 
                 self.config.lambda_ctr * d_ctr_loss + 
                 train_config['lambda_d_reg'] * d_reg_loss)
        
        # 检查损失有效性
        if torch.isnan(d_loss) or torch.isinf(d_loss):
            self.logger.warning("检测到无效的D损失")
            return {'d_loss': 0.0, 'd_adv_loss': 0.0, 'd_ctr_loss': 0.0, 'd_reg_loss': 0.0}
        
        # 反向传播
        d_loss.backward()
        
        # 梯度裁剪
        if self.config.max_grad_norm > 0:
            torch.nn.utils.clip_grad_norm_(self.discriminator.parameters(), self.config.max_grad_norm)
            
        self.d_optimizer.step()
        
        return {
            'd_loss': d_loss.item(),
            'd_adv_loss': d_adv_loss.item(),
            'd_ctr_loss': d_ctr_loss.item(),
            'd_reg_loss': d_reg_loss.item() if isinstance(d_reg_loss, torch.Tensor) else d_reg_loss,
            'real_d_score': real_d_score.mean().item(),
            'fake_d_score': fake_d_score.mean().item()
        }
    
    def train_generator(self, real_batch, train_config):
        """训练生成器 - 温和的损失组合"""
        self.g_optimizer.zero_grad()
        
        real_numeric, real_categorical, real_labels = self.prepare_real_data(real_batch)
        batch_size = real_labels.size(0)
        
        # 生成数据
        gen_output, noise = self.generate_fake_data(batch_size)
        
        # 通过判别器
        fake_d_score, fake_ctr_score, fake_features = self.discriminator(
            numeric_data=gen_output['numeric'],
            categorical_embeddings=gen_output['categorical_embeddings']
        )
        
        # 获取真实数据特征用于特征匹配
        real_features = None
        if train_config['lambda_feature'] > 0:
            with torch.no_grad():
                _, _, real_features = self.discriminator(
                    numeric_data=real_numeric,
                    categorical_data=real_categorical
                )
        
        # 计算损失
        _, g_adv_loss = self.losses.stable_adversarial_loss(
            None, fake_d_score, mode=train_config['loss_mode']
        )
        
        # 温和的特征匹配损失
        g_feature_loss = 0
        if real_features is not None and train_config['lambda_feature'] > 0:
            g_feature_loss = self.losses.enhanced_feature_matching_loss(real_features, fake_features)
        
        # 多样性损失
        g_diversity_loss = 0
        if (train_config['lambda_diversity'] > 0 and 
            gen_output['categorical_probs'] is not None):
            vocab_sizes = [self.discriminator.embeddings[i].num_embeddings 
                          for i in range(len(self.discriminator.embeddings))]
            g_diversity_loss = self.losses.enhanced_diversity_loss(
                gen_output['categorical_probs'], vocab_sizes
            )
        
        # 温和的数值特征多样性损失
        g_numeric_diversity_loss = 0
        if (train_config['lambda_numeric_diversity'] > 0 and 
            gen_output['numeric'] is not None):
            g_numeric_diversity_loss = self.losses.gentle_numeric_diversity_loss(gen_output['numeric'])
        
        # 总损失
        g_loss = (g_adv_loss + 
                 train_config['lambda_feature'] * g_feature_loss + 
                 train_config['lambda_diversity'] * g_diversity_loss +
                 train_config['lambda_numeric_diversity'] * g_numeric_diversity_loss)
        
        # 检查损失有效性
        if torch.isnan(g_loss) or torch.isinf(g_loss):
            self.logger.warning("检测到无效的G损失")
            return {'g_loss': 0.0, 'g_adv_loss': 0.0, 'g_feature_loss': 0.0}
        
        # 反向传播
        g_loss.backward()
        
        # 梯度裁剪
        if self.config.max_grad_norm > 0:
            torch.nn.utils.clip_grad_norm_(self.generator.parameters(), self.config.max_grad_norm)
            
        self.g_optimizer.step()
        
        # 记录数值特征质量
        if gen_output['numeric'] is not None:
            with torch.no_grad():
                numeric_std = gen_output['numeric'].std(dim=0).mean().item()
                numeric_range = (gen_output['numeric'].max() - gen_output['numeric'].min()).item()
                self.numeric_std_history.append(numeric_std)
                self.numeric_range_history.append(numeric_range)
                
                # 保持历史记录长度
                if len(self.numeric_std_history) > 100:
                    self.numeric_std_history.pop(0)
                    self.numeric_range_history.pop(0)
        
        return {
            'g_loss': g_loss.item(),
            'g_adv_loss': g_adv_loss.item(),
            'g_feature_loss': g_feature_loss.item() if isinstance(g_feature_loss, torch.Tensor) else g_feature_loss,
            'g_diversity_loss': g_diversity_loss.item() if isinstance(g_diversity_loss, torch.Tensor) else g_diversity_loss,
            'g_numeric_diversity_loss': g_numeric_diversity_loss.item() if isinstance(g_numeric_diversity_loss, torch.Tensor) else g_numeric_diversity_loss,
            'fake_d_score_g': fake_d_score.mean().item(),
            'temperature': self.current_temperature,
            'numeric_std': self.numeric_std_history[-1] if self.numeric_std_history else 0.0,
            'numeric_range': self.numeric_range_history[-1] if self.numeric_range_history else 0.0
        }
    
    def apply_gentle_intervention(self):
        """温和的干预措施 - 避免破坏平衡"""
        if (self.current_epoch - self.stability_monitor['last_gentle_intervention_epoch'] < 3 or
            self.stability_monitor['gentle_intervention_count'] >= 2):
            return  # 减少干预频率
            
        self.logger.info("应用温和的平衡调整...")
        
        # 1. 温和调整学习率（小幅度）
        for param_group in self.g_optimizer.param_groups:
            param_group['lr'] *= 1.1  # 小幅增加生成器学习率
        
        for param_group in self.d_optimizer.param_groups:
            param_group['lr'] *= 0.95  # 小幅降低判别器学习率
        
        # 2. 轻微提高温度（而非重置）
        self.current_temperature = min(self.current_temperature * 1.2, self.config.initial_temperature)
        
        self.stability_monitor['gentle_intervention_count'] += 1
        self.stability_monitor['last_gentle_intervention_epoch'] = self.current_epoch
        
        self.logger.info(f"温和调整后 - G学习率: {self.g_optimizer.param_groups[0]['lr']:.2e}, "
                        f"D学习率: {self.d_optimizer.param_groups[0]['lr']:.2e}, "
                        f"温度: {self.current_temperature:.3f}")
    
    def update_temperature(self):
        """更新温度 - 更保守的退火"""
        if self.current_temperature > self.min_temperature:
            # 始终缓慢退火，不暂停
            self.current_temperature = max(
                self.min_temperature,
                self.current_temperature * self.temperature_decay
            )
    
    def train_step(self, real_batch):
        """单步训练"""
        # 更新训练阶段
        self.update_training_stage()
        
        # 获取当前训练配置
        train_config = self.get_current_training_config()
        
        batch_size = real_batch[1].size(0)
        
        # 温和检查是否需要干预
        if self.check_gentle_intervention_needed() and self.total_steps % 500 == 0:
            self.apply_gentle_intervention()
        
        # 训练判别器
        d_metrics = {}
        for _ in range(train_config['d_steps']):
            d_step_metrics = self.train_discriminator(real_batch, train_config)
            for k, v in d_step_metrics.items():
                d_metrics[k] = d_metrics.get(k, 0) + v / train_config['d_steps']
        
        # 训练生成器
        g_metrics = {}
        for _ in range(train_config['g_steps']):
            g_step_metrics = self.train_generator(real_batch, train_config)
            for k, v in g_step_metrics.items():
                g_metrics[k] = g_metrics.get(k, 0) + v / train_config['g_steps']
        
        # 记录损失历史
        self.d_loss_history.append(d_metrics['d_loss'])
        self.g_loss_history.append(g_metrics['g_loss'])
        self.real_score_history.append(d_metrics['real_d_score'])
        self.fake_score_history.append(d_metrics['fake_d_score'])
        
        # 保持历史记录长度
        max_history = 100
        for history in [self.d_loss_history, self.g_loss_history, 
                       self.real_score_history, self.fake_score_history]:
            if len(history) > max_history:
                history.pop(0)
        
        # 合并指标
        metrics = {**d_metrics, **g_metrics, 'training_stage': self.training_stage}
        
        self.total_steps += 1
        return metrics
    
    def train_epoch(self, dataloader):
        """训练一个epoch"""
        self.generator.train()
        self.discriminator.train()
        
        epoch_metrics = {}
        num_batches = len(dataloader)
        
        for batch_idx, batch in enumerate(dataloader):
            step_metrics = self.train_step(batch)
            
            # 累积指标
            for k, v in step_metrics.items():
                epoch_metrics[k] = epoch_metrics.get(k, 0) + v / num_batches
            
            # 记录到wandb
            if batch_idx % self.config.log_interval == 0:
                wandb.log({
                    **{f"batch_{k}": v for k, v in step_metrics.items()},
                    "epoch": self.current_epoch,
                    "batch": batch_idx,
                    "total_steps": self.total_steps
                })
                
                # 日志
                numeric_std = step_metrics.get('numeric_std', 0.0)
                numeric_range = step_metrics.get('numeric_range', 0.0)
                
                self.logger.info(
                    f"Epoch {self.current_epoch}, Batch {batch_idx}/{num_batches}, "
                    f"Stage {self.training_stage}, "
                    f"D_loss: {step_metrics['d_loss']:.4f}, G_loss: {step_metrics['g_loss']:.4f}, "
                    f"Num_std: {numeric_std:.6f}, Num_range: {numeric_range:.4f}, "
                    f"Real_score: {step_metrics['real_d_score']:.4f}, Fake_score: {step_metrics['fake_d_score']:.4f}"
                )
        
        # 更新温度和学习率
        self.update_temperature()
        self.g_scheduler.step()
        self.d_scheduler.step()
        
        # 记录epoch指标
        wandb.log({
            **{f"epoch_{k}": v for k, v in epoch_metrics.items()},
            "epoch": self.current_epoch,
            "generator_lr": self.g_scheduler.get_last_lr()[0],
            "discriminator_lr": self.d_scheduler.get_last_lr()[0],
            "temperature": self.current_temperature
        })
        
        self.current_epoch += 1
        return epoch_metrics
    
    def save_checkpoint(self, path, epoch, metrics=None):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'generator_state_dict': self.generator.state_dict(),
            'discriminator_state_dict': self.discriminator.state_dict(),
            'g_optimizer_state_dict': self.g_optimizer.state_dict(),
            'd_optimizer_state_dict': self.d_optimizer.state_dict(),
            'g_scheduler_state_dict': self.g_scheduler.state_dict(),
            'd_scheduler_state_dict': self.d_scheduler.state_dict(),
            'current_temperature': self.current_temperature,
            'total_steps': self.total_steps,
            'training_stage': self.training_stage,
            'stability_monitor': self.stability_monitor,
            'numeric_std_history': self.numeric_std_history,
            'numeric_range_history': self.numeric_range_history,
            'config': self.config,
            'metrics': metrics
        }
        
        torch.save(checkpoint, path)
        self.logger.info(f"检查点已保存到 {path}")
    
    def load_checkpoint(self, path):
        """加载检查点"""
        checkpoint = torch.load(path, map_location=self.device)
        
        self.generator.load_state_dict(checkpoint['generator_state_dict'])
        self.discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
        self.g_optimizer.load_state_dict(checkpoint['g_optimizer_state_dict'])
        self.d_optimizer.load_state_dict(checkpoint['d_optimizer_state_dict'])
        self.g_scheduler.load_state_dict(checkpoint['g_scheduler_state_dict'])
        self.d_scheduler.load_state_dict(checkpoint['d_scheduler_state_dict'])
        
        self.current_temperature = checkpoint['current_temperature']
        self.total_steps = checkpoint['total_steps']
        self.current_epoch = checkpoint['epoch']
        self.training_stage = checkpoint.get('training_stage', 0)
        self.stability_monitor = checkpoint.get('stability_monitor', {})
        self.numeric_std_history = checkpoint.get('numeric_std_history', [])
        self.numeric_range_history = checkpoint.get('numeric_range_history', [])
        
        self.logger.info(f"检查点已从 {path} 加载")
        return checkpoint.get('metrics', {})


class RefinedGANConfig:
    """
    精调的GAN配置 - 温和平衡的超参数
    """
    
    def __init__(self):
        # 模型参数
        self.noise_dim = 128
        self.embedding_dim = 16
        
        # 训练参数 - 精调的学习率比例
        self.generator_lr = 5e-5      # 降低生成器学习率
        self.discriminator_lr = 2e-5  # 保持判别器学习率
        self.batch_size = 512
        self.epochs = 50
        
        # 损失权重
        self.lambda_ctr = 1.0
        
        # Gumbel-Softmax参数 - 更稳定的温度控制
        self.initial_temperature = 2.5   # 适中的初始温度
        self.min_temperature = 1.2      # 适中的最小温度
        self.temperature_decay = 0.9998  # 很慢的退火
        
        # 梯度相关
        self.max_grad_norm = 1.0
        
        # 日志和保存
        self.log_interval = 100
        self.save_interval = 5
        
        # 数据集配置
        self.dataset_name = "Criteo"
        self.dataset_path = "../data/Criteo"


def create_refined_dataloader(processed_data, config):
    """创建数据加载器"""
    # 提取特征
    features = []
    
    # 数值特征
    numeric_cols = [f'I{i}' for i in range(1, 14)]
    if all(col in processed_data.columns for col in numeric_cols):
        numeric_data = processed_data[numeric_cols].values
        features.append(numeric_data)
    
    # 类别特征（索引形式）
    categorical_cols = [f'C{i}_idx' for i in range(1, 27)]
    if all(col in processed_data.columns for col in categorical_cols):
        categorical_data = processed_data[categorical_cols].values
        features.append(categorical_data)
    
    # 拼接特征
    X = np.concatenate(features, axis=1)
    
    # 标签
    y = processed_data['Label'].values if 'Label' in processed_data.columns else processed_data['click'].values
    
    # 转换为tensor
    X_tensor = torch.FloatTensor(X)
    y_tensor = torch.FloatTensor(y)
    
    # 创建数据集和加载器
    dataset = TensorDataset(X_tensor, y_tensor)
    
    use_pin_memory = torch.cuda.is_available()
    num_workers = 0 if torch.backends.mps.is_available() else min(4, config.batch_size // 128)
    
    dataloader = DataLoader(
        dataset,
        batch_size=config.batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=use_pin_memory,
        drop_last=True
    )
    
    return dataloader