import os
import logging
import pickle
import json
import numpy as np
import pandas as pd
from collections import OrderedDict
from pathlib import Path
import sys

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent.parent.parent
sys.path.append(str(ROOT_DIR))

from src.data_process.utils import get_column


class GANDataProcessor:
    """
    GAN专用数据预处理器
    只做轻度预处理，保留更多原始数据分布信息
    """
    
    def __init__(self, dataset_name, data_dir, output_dir, max_vocab_size=10000):
        self.dataset_name = dataset_name
        self.data_dir = data_dir
        self.output_dir = output_dir
        self.max_vocab_size = max_vocab_size
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取特征定义（复用现有代码）
        self.numeric_features, self.categorical_features, self.label_col = get_column(dataset_name)
        
        # 存储预处理信息
        self.numeric_stats = {}  # 存储数值特征的归一化参数
        self.vocab_info = {}     # 存储类别特征的词汇表信息
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Max vocab size per feature: {max_vocab_size}")
        
    def data_clean(self, df):
        """
        基础数据清洗，复用现有逻辑
        """
        # 数值特征填充
        if len(self.numeric_features) > 0:
            df[self.numeric_features] = df[self.numeric_features].fillna(0)
            
        # 类别特征填充
        if len(self.categorical_features) > 0:
            for col in self.categorical_features:
                if col in df.columns and df[col].dtype.name == 'category':
                    if '' not in df[col].cat.categories:
                        df[col] = df[col].cat.add_categories([''])
            df[self.categorical_features] = df[self.categorical_features].fillna('')
            
        return df
        
    def normalize_numeric_features(self, df, is_training=True):
        """
        数值特征归一化到[-1, 1]区间，配合Generator的Tanh激活
        """
        for col in self.numeric_features:
            if is_training:
                # 训练时计算并保存统计信息
                min_val = df[col].min()
                max_val = df[col].max()
                
                # 避免除零错误
                if max_val == min_val:
                    max_val = min_val + 1e-8
                    
                self.numeric_stats[col] = {
                    'min': min_val,
                    'max': max_val
                }
                self.logger.info(f"Numeric feature {col}: min={min_val:.4f}, max={max_val:.4f}")
                
            else:
                # 推理时使用保存的统计信息
                min_val = self.numeric_stats[col]['min']
                max_val = self.numeric_stats[col]['max']
                
            # MinMax归一化到[-1, 1]
            df[col] = 2 * (df[col] - min_val) / (max_val - min_val) - 1
            
        return df
        
    def build_categorical_vocab(self, df, is_training=True):
        """
        构建类别特征的完整词汇表（带频率过滤）
        """
        for col in self.categorical_features:
            if is_training:
                # 训练时构建词汇表
                value_counts = df[col].value_counts()
                total_unique = len(value_counts)
                
                self.logger.info(f"Feature {col}: {total_unique} unique values")
                
                # 🔧 频率过滤 + vocab_size限制
                if total_unique > self.max_vocab_size:
                    self.logger.warning(f"Feature {col} has {total_unique} categories! Reducing to top {self.max_vocab_size}")
                    # 保留top-K最频繁的类别
                    top_values = value_counts.head(self.max_vocab_size).index.tolist()
                    unique_values = [str(val) for val in top_values]
                    
                    # 统计覆盖率
                    covered_samples = value_counts.head(self.max_vocab_size).sum()
                    coverage = covered_samples / len(df) * 100
                    self.logger.info(f"Top {self.max_vocab_size} categories cover {coverage:.2f}% of samples")
                else:
                    unique_values = [str(val) for val in value_counts.index.tolist()]
                
                # 构建 value -> index 映射，预留0给unknown
                value_to_idx = {'__UNKNOWN__': 0}
                value_to_idx.update({val: idx + 1 for idx, val in enumerate(unique_values)})
                
                # 构建 index -> value 映射
                idx_to_value = {idx: val for val, idx in value_to_idx.items()}
                
                self.vocab_info[col] = {
                    'value_to_idx': value_to_idx,
                    'idx_to_value': idx_to_value,
                    'vocab_size': len(value_to_idx),
                    'original_unique_count': total_unique,
                    'coverage': coverage if total_unique > self.max_vocab_size else 100.0
                }
                
                self.logger.info(f"Categorical feature {col}: final_vocab_size={len(value_to_idx)}")
                
            # 将类别值转换为索引（用于Discriminator输入真实数据）
            value_to_idx = self.vocab_info[col]['value_to_idx']
            df[f"{col}_idx"] = df[col].map(lambda x: value_to_idx.get(str(x), 0))
            
        return df
        
    def preprocess_for_gan(self, df, is_training=True):
        """
        GAN训练专用的数据预处理
        
        Args:
            df: 原始数据
            is_training: 是否是训练模式
            
        Returns:
            处理后的数据
        """
        self.logger.info(f"Starting GAN preprocessing for {len(df)} samples")
        
        # 1. 基础清洗
        df = self.data_clean(df.copy())
        
        # 2. 数值特征归一化
        df = self.normalize_numeric_features(df, is_training)
        
        # 3. 类别特征词汇表构建
        df = self.build_categorical_vocab(df, is_training)
        
        # 4. 如果是训练模式，保存预处理信息
        if is_training:
            self.save_preprocessing_info()
            
        self.logger.info("GAN preprocessing completed")
        return df
        
    def reverse_preprocessing(self, synthetic_df):
        """
        将GAN生成的合成数据转换回原始格式
        用于后续的CTR模型训练
        """
        result_df = synthetic_df.copy()
        
        # 1. 数值特征反归一化
        for col in self.numeric_features:
            if col in result_df.columns:
                min_val = self.numeric_stats[col]['min']
                max_val = self.numeric_stats[col]['max']
                
                # 从[-1, 1]反归一化到原始范围
                result_df[col] = (result_df[col] + 1) / 2 * (max_val - min_val) + min_val
                
        # 2. 类别特征：从索引转换回原始值
        for col in self.categorical_features:
            if f"{col}_idx" in result_df.columns:
                idx_to_value = self.vocab_info[col]['idx_to_value']
                result_df[col] = result_df[f"{col}_idx"].map(
                    lambda x: idx_to_value.get(int(x), '__UNKNOWN__')
                )
                # 删除索引列
                result_df = result_df.drop(columns=[f"{col}_idx"])
                
        return result_df
        
    def get_vocab_sizes(self):
        """
        获取所有类别特征的词汇表大小，用于初始化Generator
        """
        vocab_sizes = [self.vocab_info[col]['vocab_size'] for col in self.categorical_features]
        self.logger.info(f"Vocab sizes: {dict(zip(self.categorical_features, vocab_sizes))}")
        return vocab_sizes
        
    def get_feature_info(self):
        """
        获取特征信息，用于初始化GAN模型
        """
        vocab_sizes = self.get_vocab_sizes()
        total_params_estimate = sum(vocab_size * 8 for vocab_size in vocab_sizes)  # embedding_dim=8
        self.logger.info(f"Estimated embedding parameters: {total_params_estimate:,}")
        
        return {
            'numeric_features': self.numeric_features,
            'categorical_features': self.categorical_features,
            'vocab_sizes': vocab_sizes,
            'label_col': self.label_col,
            'dataset_name': self.dataset_name,
            'vocab_info_summary': {col: self.vocab_info[col]['vocab_size'] for col in self.categorical_features}
        }
        
    def save_preprocessing_info(self):
        """
        保存预处理信息，用于后续的数据生成和反处理
        """
        info = {
            'numeric_stats': self.numeric_stats,
            'vocab_info': self.vocab_info,
            'feature_info': self.get_feature_info()
        }
        
        # 保存为pickle文件
        save_path = os.path.join(self.output_dir, 'gan_preprocessing_info.pkl')
        with open(save_path, 'wb') as f:
            pickle.dump(info, f)
            
        # 保存为json文件（方便查看）
        json_path = os.path.join(self.output_dir, 'gan_preprocessing_info.json')
        # 需要处理numpy类型以便json序列化
        json_info = self._make_json_serializable(info)
        with open(json_path, 'w') as f:
            json.dump(json_info, f, indent=2)
            
        self.logger.info(f"Preprocessing info saved to {save_path}")
        
    def load_preprocessing_info(self):
        """
        加载预处理信息
        """
        save_path = os.path.join(self.output_dir, 'gan_preprocessing_info.pkl')
        
        if not os.path.exists(save_path):
            raise FileNotFoundError(f"Preprocessing info not found at {save_path}")
            
        with open(save_path, 'rb') as f:
            info = pickle.load(f)
            
        self.numeric_stats = info['numeric_stats']
        self.vocab_info = info['vocab_info']
        
        self.logger.info(f"Preprocessing info loaded from {save_path}")
        return info['feature_info']
        
    def _make_json_serializable(self, obj):
        """
        递归地将numpy类型转换为Python原生类型以便json序列化
        """
        if isinstance(obj, dict):
            return {k: self._make_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(v) for v in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj


def prepare_gan_data(dataset_name, data_dir, output_dir, train_file='train.csv', 
                    use_cache=True, sample_strategy='progressive', max_samples=None,
                    max_vocab_size=10000):
    """
    准备GAN训练数据，支持vocab_size控制
    
    Args:
        dataset_name: 数据集名称
        data_dir: 原始数据目录
        output_dir: 输出目录
        train_file: 训练文件名
        use_cache: 是否使用缓存
        sample_strategy: 采样策略
        max_samples: 最大样本数
        max_vocab_size: 每个类别特征的最大词汇表大小
        
    Returns:
        处理后的训练数据和预处理器
    """
    # 缓存文件名包含vocab_size信息
    cache_suffix = f"_{sample_strategy}_{max_samples}_{max_vocab_size}" if max_samples else f"_{sample_strategy}_full_{max_vocab_size}"
    cache_file = os.path.join(output_dir, f'processed_{dataset_name}_train{cache_suffix}.pkl')
    
    # 如果缓存存在且使用缓存，直接加载
    if use_cache and os.path.exists(cache_file):
        logging.info(f"Loading cached processed data from {cache_file}")
        
        with open(cache_file, 'rb') as f:
            cache_data = pickle.load(f)
        
        processed_df = cache_data['processed_data']
        
        # 重建预处理器
        processor = GANDataProcessor(dataset_name, data_dir, output_dir, max_vocab_size=max_vocab_size)
        processor.numeric_stats = cache_data['numeric_stats']
        processor.vocab_info = cache_data['vocab_info']
        
        logging.info(f"Loaded {len(processed_df)} cached samples with max_vocab_size={max_vocab_size}")
        return processed_df, processor
    
    # 原始处理逻辑
    processor = GANDataProcessor(dataset_name, data_dir, output_dir, max_vocab_size=max_vocab_size)
    
    # 读取训练数据
    train_path = os.path.join(data_dir, train_file)
    
    # 根据采样策略决定如何读取数据
    if sample_strategy == 'progressive' and max_samples is None:
        progressive_sizes = [50000, 200000, 500000, 1000000]
        max_samples = 500000
        logging.info(f"Progressive sampling: using {max_samples} samples")
    elif max_samples is None:
        max_samples = 3000000
        logging.info(f"Using default {max_samples} samples")
    
    # 确定dtype以优化内存使用
    use_cols = []
    dtype_dict = {}
    
    if len(processor.numeric_features) > 0:
        dtype_dict.update({feat: 'float32' for feat in processor.numeric_features})
        use_cols.extend(processor.numeric_features)
        
    if len(processor.categorical_features) > 0:
        dtype_dict.update({feat: 'category' for feat in processor.categorical_features})
        use_cols.extend(processor.categorical_features)
        
    dtype_dict[processor.label_col] = 'float32'
    use_cols.append(processor.label_col)
    
    # 读取指定数量的数据
    df = pd.read_csv(train_path, usecols=use_cols, dtype=dtype_dict, nrows=max_samples)
    logging.info(f"Loaded {len(df)} samples from {train_path}")
    
    # 数据质量检查
    ctr_rate = df[processor.label_col].mean()
    logging.info(f"CTR rate in sampled data: {ctr_rate:.4f}")
    
    # 预处理
    processed_df = processor.preprocess_for_gan(df, is_training=True)
    
    # 保存缓存
    if use_cache:
        logging.info(f"Saving processed data cache to {cache_file}")
        cache_data = {
            'processed_data': processed_df,
            'numeric_stats': processor.numeric_stats,
            'vocab_info': processor.vocab_info,
            'sample_info': {
                'strategy': sample_strategy,
                'max_samples': max_samples,
                'max_vocab_size': max_vocab_size,
                'actual_samples': len(processed_df),
                'ctr_rate': ctr_rate
            }
        }
        
        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)
    
    return processed_df, processor


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    # 测试Criteo数据集
    dataset_name = "Criteo"
    data_dir = "../data/public_data/Criteo_x4"
    output_dir = "./gan_preprocessing_test"
    
    try:
        processed_data, processor = prepare_gan_data(
            dataset_name, data_dir, output_dir, 
            max_samples=10000, max_vocab_size=1000
        )
        print("GAN数据预处理测试成功！")
        print(f"处理后数据形状: {processed_data.shape}")
        print(f"特征信息: {processor.get_feature_info()}")
        
        # 测试反处理
        synthetic_sample = processed_data.head(10)
        reversed_data = processor.reverse_preprocessing(synthetic_sample)
        print(f"反处理测试成功，数据形状: {reversed_data.shape}")
        
    except Exception as e:
        print(f"测试失败: {e}")