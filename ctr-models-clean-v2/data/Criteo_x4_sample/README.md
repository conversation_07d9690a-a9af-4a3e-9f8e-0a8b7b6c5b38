# Sampled Criteo Dataset for CTR Prediction

This is a sampled version of the Criteo dataset for testing and development purposes.

## Dataset Structure
This follows the exact structure expected by the CTR training pipeline:

### Data Files
- `train.h5`: 5,000 training samples
- `valid.h5`: 5,000 validation samples
- `test.h5`: 5,000 test samples
- `train.csv`, `valid.csv`, `test.csv`: Placeholder CSV files (data is in .h5 files)

### Metadata Files
- `feature_encoder.pkl`: Feature encoders for preprocessing
- `feature_map.json`: Feature specifications and vocabulary sizes

## Sampling Details
- Original dataset: /data/Criteo_x4
- Sample size per file: 5,000
- Random seed: 42
- Total samples: 15,000

## Features
- **Numerical features**: I1-I13 (13 features)
- **Categorical features**: C1-C26 (26 features)
- **Label**: Binary classification target (0/1)
- **Total dimensions**: 40 (39 features + 1 label)

## Usage with Training Script

```bash
# Train a model using the sampled dataset
python scripts/train.py \
    --dataset_name Criteo \
    --dataset_path ./data/Criteo_x4_sample \
    --model_name FM \
    --epochs 10 \
    --batch_size 1000
```

## Data Loading
The training pipeline automatically:
1. Looks for CSV files (train.csv, valid.csv, test.csv)
2. Checks for corresponding .h5 files (train.h5, valid.h5, test.h5)
3. Loads .h5 files if available (much faster)
4. Uses feature_encoder.pkl and feature_map.json for preprocessing

## Important Notes
- Results on this sampled dataset may not be representative of full dataset performance
- The sample maintains the original data distribution and feature encoding
- All preprocessing and feature engineering is preserved from the original dataset
- Compatible with all existing model training scripts
