import os
import torch

# LLM
seed = 2012

dataset = "antm2c"
data_path = "/data/antm2c/process_data.csv"

'''
Scenarios
0: Advertising
'''

all_scenarios = [0]
train_scenarios = [0]
test_scenarios = [0]

num_workers = 0
weight_decay = 0.001

# epoch 0
lr = 1e-5
max_lr = 5e-5

text_encoder_models = [
    # Name, num_hidden_layers, text_embedding_dim, max_length
    ["Qwen2.5-1.5B", 28, 1536, 1024]
]

text_encoder_model_name, layer_num, text_embedding_dim, max_length = text_encoder_models[0]

if text_encoder_model_name == "tiny-bert-4l-en":
    nlp_finetune_batch_size = 50 * torch.cuda.device_count()
    ladder_frequency = 1
elif text_encoder_model_name == "deberta-v3-base":
    nlp_finetune_batch_size = 60 * torch.cuda.device_count()
    ladder_frequency = 6
elif text_encoder_model_name == "deberta-v3-large":
    nlp_finetune_batch_size = 30 * torch.cuda.device_count()
    ladder_frequency = 8
elif text_encoder_model_name == "chatglm2-6b-32k":
    nlp_finetune_batch_size = 2 * torch.cuda.device_count()
    ladder_frequency = 7
elif text_encoder_model_name == "Qwen2.5-1.5B-Instruct":
    nlp_finetune_batch_size = 2 * torch.cuda.device_count()
    ladder_frequency = 7
elif text_encoder_model_name == "Qwen2.5-1.5B":
    nlp_finetune_batch_size = 2 * torch.cuda.device_count()
    ladder_frequency = 8
else:
    nlp_finetune_batch_size = 2 * torch.cuda.device_count()
    ladder_frequency = 8

text_encoder_model = os.path.join("/data/antm2c", text_encoder_model_name)
text_tokenizer = os.path.join("/data/antm2c", text_encoder_model_name)

ladder_block = ["wo_block", "w_lora", "w_self_attention", "w_transformer_block"]
ladder_block = ladder_block[3]

r = 4
num_heads = 2
narrowed_ratio = 0.5

use_peft = True
pretrained = False
load_path = ""
save_path = os.path.join("/data/antm2c/saved_models", str(dataset), "llm_based", text_encoder_model_name, ladder_block)
save_every_n_epoch = 1

max_epochs = 10
lr_step = "epoch"
dropout = 0.2

mixed_precision = False
accumulation_steps = 16
focal_loss = False
clip_grad = True
clip_value = 1.0

use_special_token = False

# Early stopping parameters
patience = 2