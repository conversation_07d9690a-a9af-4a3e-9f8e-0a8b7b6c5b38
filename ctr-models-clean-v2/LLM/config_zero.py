import os
import torch

seed = 2012
dataset = "antm2c"
data_path = "/data/antm2c/process_data.csv"
all_scenarios = [0]
train_scenarios = [0]
test_scenarios = [0]

num_workers = 0

# Model settings
text_encoder_models = [
    # Name, num_hidden_layers, text_embedding_dim, max_length
    ["Qwen2.5-1.5B", 28, 1536, 1024]
]
text_encoder_model_name, layer_num, text_embedding_dim, max_length = text_encoder_models[0]

# Zero-shot specific settings
use_peft = False  # Disable LoRA
pretrained = False  # Don't load fine-tuned weights
nlp_finetune_batch_size = 2 * torch.cuda.device_count()
ladder_frequency = 8

# Set paths
text_encoder_model = os.path.join("/data/antm2c", text_encoder_model_name)
text_tokenizer = os.path.join("/data/antm2c", text_encoder_model_name)

# Disable ladder blocks for zero-shot, Use simplest architecture
ladder_block = "wo_block"

mixed_precision = False
# Disable dropout for inference
dropout = 0.0  
# Only need one pass for evaluation
max_epochs = 1