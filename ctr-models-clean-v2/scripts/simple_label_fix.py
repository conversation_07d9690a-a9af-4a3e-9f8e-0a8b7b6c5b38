#!/usr/bin/env python3
"""
简单的标签修复方案 - 使用训练好的CTR预测器直接修复现有合成数据的标签
避免GAN模型加载的兼容性问题
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from pathlib import Path
import argparse
from sklearn.metrics import roc_auc_score

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))

from scripts.train_ctr_predictor import CTRPredictor


def load_ctr_predictor(ctr_predictor_path, device):
    """加载训练好的CTR预测器"""
    
    print(f"Loading CTR predictor from {ctr_predictor_path}")
    checkpoint = torch.load(ctr_predictor_path, map_location=device, weights_only=False)
    
    # 重建CTR预测器
    ctr_predictor = CTRPredictor(
        numeric_features=checkpoint['numeric_features'],
        categorical_features=checkpoint['categorical_features'],
        vocab_sizes=checkpoint['vocab_sizes'],
        embedding_dim=checkpoint['embedding_dim']
    ).to(device)
    
    ctr_predictor.load_state_dict(checkpoint['model_state_dict'])
    ctr_predictor.eval()
    
    print(f"✅ CTR predictor loaded successfully")
    print(f"Validation AUC: {checkpoint['val_auc']:.4f}")
    
    return ctr_predictor, checkpoint


def prepare_synthetic_data_for_ctr_prediction(df, numeric_features, categorical_features):
    """准备合成数据用于CTR预测"""

    print("Preparing synthetic data for CTR prediction...")

    # 处理数值特征
    numeric_data = df[numeric_features].fillna(0).values

    # 标准化数值特征 (简单的z-score标准化)
    numeric_data = (numeric_data - numeric_data.mean(axis=0)) / (numeric_data.std(axis=0) + 1e-8)

    # 处理类别特征 - 智能转换字符串到整数
    print("Converting categorical features...")
    categorical_data = np.zeros((len(df), len(categorical_features)), dtype=int)

    for i, col in enumerate(categorical_features):
        col_data = df[col].fillna('unknown')

        # 检查数据类型
        sample_val = col_data.iloc[0] if len(col_data) > 0 else 0

        if isinstance(sample_val, str):
            # 字符串类型 - 需要映射到整数
            print(f"  {col}: Converting strings to integers")
            unique_vals = col_data.unique()

            # 创建字符串到整数的映射
            val_to_idx = {val: idx for idx, val in enumerate(unique_vals)}
            categorical_data[:, i] = col_data.map(val_to_idx).values

            print(f"    Unique values: {len(unique_vals)}, Range: [0, {len(unique_vals)-1}]")

        else:
            # 数值类型 - 直接转换
            try:
                categorical_data[:, i] = col_data.astype(int).values
            except ValueError:
                # 如果转换失败，使用字符串映射方法
                print(f"  {col}: Fallback to string mapping")
                col_data_str = col_data.astype(str)
                unique_vals = col_data_str.unique()
                val_to_idx = {val: idx for idx, val in enumerate(unique_vals)}
                categorical_data[:, i] = col_data_str.map(val_to_idx).values

        # 确保索引在合理范围内 (CTR预测器的vocab_size限制)
        max_val = categorical_data[:, i].max()
        if max_val >= 10000:  # 如果索引过大，进行重新映射
            print(f"  {col}: Remapping large indices (max: {max_val})")
            unique_vals = np.unique(categorical_data[:, i])
            val_map = {val: idx for idx, val in enumerate(unique_vals)}
            categorical_data[:, i] = np.array([val_map[val] for val in categorical_data[:, i]])
            print(f"    After remapping: [0, {len(unique_vals)-1}]")

    print(f"Numeric data shape: {numeric_data.shape}")
    print(f"Categorical data shape: {categorical_data.shape}")
    print(f"Categorical data range: [{categorical_data.min()}, {categorical_data.max()}]")

    return numeric_data, categorical_data


def generate_improved_labels(ctr_predictor, numeric_data, categorical_data, device, batch_size=1000):
    """使用CTR预测器生成改进的标签"""
    
    print("Generating improved CTR labels...")
    
    ctr_predictor.eval()
    all_probs = []
    
    num_samples = len(numeric_data)
    
    with torch.no_grad():
        for i in range(0, num_samples, batch_size):
            end_idx = min(i + batch_size, num_samples)
            
            # 准备批次数据
            batch_numeric = torch.FloatTensor(numeric_data[i:end_idx]).to(device)
            batch_categorical = torch.LongTensor(categorical_data[i:end_idx]).to(device)
            
            try:
                # 检查输入数据范围
                cat_max = batch_categorical.max().item()
                cat_min = batch_categorical.min().item()

                if cat_max >= 10000 or cat_min < 0:
                    print(f"Warning: Categorical data out of range in batch {i//batch_size}: [{cat_min}, {cat_max}]")
                    # 截断到安全范围
                    batch_categorical = torch.clamp(batch_categorical, 0, 9999)

                # 预测CTR概率
                batch_probs = ctr_predictor(batch_numeric, batch_categorical)
                all_probs.extend(batch_probs.cpu().numpy())

            except Exception as e:
                print(f"Warning: Batch {i//batch_size} failed: {e}")
                print(f"  Numeric shape: {batch_numeric.shape}, range: [{batch_numeric.min():.3f}, {batch_numeric.max():.3f}]")
                print(f"  Categorical shape: {batch_categorical.shape}, range: [{batch_categorical.min()}, {batch_categorical.max()}]")

                # 使用默认概率
                default_probs = np.full(end_idx - i, 0.25)
                all_probs.extend(default_probs)
            
            if (i // batch_size + 1) % 10 == 0:
                print(f"Processed {i + batch_size} / {num_samples} samples")
    
    ctr_probs = np.array(all_probs)
    
    # 调整概率分布
    target_ctr_rate = 0.25  # Criteo的大致CTR率
    current_mean = ctr_probs.mean()
    
    if current_mean > 0:
        adjusted_probs = ctr_probs * (target_ctr_rate / current_mean)
        adjusted_probs = np.clip(adjusted_probs, 0.001, 0.999)
    else:
        adjusted_probs = np.full_like(ctr_probs, target_ctr_rate)
    
    # 生成二进制标签
    np.random.seed(2024)
    new_labels = np.random.binomial(1, adjusted_probs)
    
    print(f"Generated {len(new_labels)} labels")
    print(f"New CTR rate: {new_labels.mean():.4f}")
    print(f"Target CTR rate: {target_ctr_rate:.4f}")
    
    return new_labels, adjusted_probs


def fix_synthetic_data_labels(args):
    """修复合成数据的标签"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 1. 加载CTR预测器
    ctr_predictor, checkpoint = load_ctr_predictor(args.ctr_predictor, device)
    
    # 2. 加载合成数据
    print(f"Loading synthetic data from {args.synthetic_data}")
    synthetic_df = pd.read_csv(args.synthetic_data)

    print(f"Synthetic data shape: {synthetic_df.shape}")
    if 'Label' in synthetic_df.columns:
        print(f"Original CTR rate: {synthetic_df['Label'].mean():.4f}")

    # 3. 获取特征信息
    numeric_features = checkpoint['numeric_features']
    categorical_features = checkpoint['categorical_features']

    # 检查数据格式
    print(f"\n📊 Data format inspection:")
    print(f"Columns: {list(synthetic_df.columns)}")
    print(f"Expected numeric features: {numeric_features}")
    print(f"Expected categorical features: {categorical_features}")

    # 检查数值特征
    if len(numeric_features) > 0:
        numeric_sample = synthetic_df[numeric_features].head(3)
        print(f"\nNumeric features sample:")
        for col in numeric_features[:3]:  # 只显示前3个
            if col in synthetic_df.columns:
                sample_vals = numeric_sample[col].values
                print(f"  {col}: {sample_vals} (type: {type(sample_vals[0])})")

    # 检查类别特征
    if len(categorical_features) > 0:
        categorical_sample = synthetic_df[categorical_features].head(3)
        print(f"\nCategorical features sample:")
        for col in categorical_features[:3]:  # 只显示前3个
            if col in synthetic_df.columns:
                sample_vals = categorical_sample[col].values
                print(f"  {col}: {sample_vals} (type: {type(sample_vals[0])})")
                unique_count = synthetic_df[col].nunique()
                print(f"    Unique values: {unique_count}")

    # 4. 检查特征兼容性
    
    # 检查特征是否存在
    missing_features = []
    for feat in numeric_features + categorical_features:
        if feat not in synthetic_df.columns:
            missing_features.append(feat)

    if missing_features:
        print(f"❌ Missing features in synthetic data: {missing_features}")
        print("Available columns:", list(synthetic_df.columns))
        return None
    
    # 5. 准备数据进行CTR预测
    numeric_data, categorical_data = prepare_synthetic_data_for_ctr_prediction(
        synthetic_df, numeric_features, categorical_features
    )

    # 6. 生成改进的标签
    new_labels, new_probs = generate_improved_labels(
        ctr_predictor, numeric_data, categorical_data, device, args.batch_size
    )
    
    # 7. 更新数据
    synthetic_df['Label'] = new_labels
    synthetic_df['ctr_prob'] = new_probs

    # 8. 保存结果
    os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
    synthetic_df.to_csv(args.output_path, index=False)
    
    print(f"✅ Fixed synthetic data saved to {args.output_path}")
    
    # 9. 基本验证
    print(f"\n📊 Quality Check:")
    print(f"Final CTR rate: {new_labels.mean():.4f}")
    print(f"CTR prob range: [{new_probs.min():.4f}, {new_probs.max():.4f}]")
    print(f"CTR prob std: {new_probs.std():.4f}")
    
    return synthetic_df


def main():
    parser = argparse.ArgumentParser(description='Simple Label Fix for Synthetic Data')
    
    parser.add_argument('--synthetic_data', type=str, required=True,
                       help='Path to synthetic data CSV file')
    parser.add_argument('--ctr_predictor', type=str, required=True,
                       help='Path to trained CTR predictor')
    parser.add_argument('--output_path', type=str, required=True,
                       help='Output path for fixed synthetic data')
    parser.add_argument('--batch_size', type=int, default=1000,
                       help='Batch size for CTR prediction')
    
    args = parser.parse_args()
    
    print("🎯 Simple Label Fix for Synthetic Data")
    print("=" * 50)
    print("Strategy:")
    print("1. Load trained CTR predictor")
    print("2. Apply to synthetic features")
    print("3. Generate realistic CTR labels")
    print("4. Save improved synthetic data")
    print()
    
    try:
        fixed_df = fix_synthetic_data_labels(args)
        
        if fixed_df is not None:
            print("\n🎉 Label fixing completed successfully!")
            print(f"Fixed data saved to: {args.output_path}")
            print("\n📈 Expected improvements:")
            print("- AUC on real test data should increase significantly")
            print("- Feature-label relationships should be more realistic")
            print("- CTR distribution should match real data better")
            
            print("\n🧪 Next steps:")
            print("1. Test with your FM model:")
            print(f"   - Train on: {args.output_path}")
            print("   - Test on: real test data")
            print("2. Compare AUC with previous results")
            print("3. Expected: AUC should improve from 0.544 to 0.65+")
        
    except Exception as e:
        print(f"❌ Label fixing failed: {e}")
        import traceback
        traceback.print_exc()
        
        print("\n🔧 Troubleshooting:")
        print("1. Check that CTR predictor was trained successfully")
        print("2. Verify synthetic data has correct column names")
        print("3. Ensure sufficient GPU memory for batch processing")


if __name__ == "__main__":
    main()
