#!/usr/bin/env python3
"""
完整的高质量合成数据生成工作流
包含所有必要步骤的自动化脚本
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


def run_command(cmd, description):
    """运行命令并处理错误"""
    print(f"\n🚀 {description}")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed with error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error in {description}: {e}")
        return False


def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description} found: {file_path}")
        return True
    else:
        print(f"❌ {description} not found: {file_path}")
        return False


def main():
    parser = argparse.ArgumentParser(description='Complete High-Quality Synthetic Data Workflow')
    
    # 数据路径
    parser.add_argument('--data_path', type=str, default='/data/Criteo_x4',
                       help='Path to Criteo dataset')
    parser.add_argument('--output_dir', type=str, default='/data/synthetic_workflow',
                       help='Output directory for all generated files')
    
    # GAN相关
    parser.add_argument('--gan_checkpoint', type=str, 
                       default='/data/balanced_gan_v2/improved_best_model.pt',
                       help='Path to trained GAN checkpoint')
    parser.add_argument('--retrain_gan', action='store_true',
                       help='Retrain GAN from scratch (not recommended)')
    
    # CTR预测器相关
    parser.add_argument('--ctr_samples', type=int, default=500000,
                       help='Samples for CTR predictor training')
    parser.add_argument('--ctr_epochs', type=int, default=30,
                       help='Epochs for CTR predictor training')
    
    # 合成数据生成
    parser.add_argument('--synthetic_samples', type=int, default=100000,
                       help='Number of synthetic samples to generate')
    
    # 控制选项
    parser.add_argument('--skip_ctr_training', action='store_true',
                       help='Skip CTR predictor training if model exists')
    parser.add_argument('--quick_fix_only', action='store_true',
                       help='Only run quick fix approach (faster)')
    
    args = parser.parse_args()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("🎯 Complete High-Quality Synthetic Data Generation Workflow")
    print("=" * 70)
    print(f"Data path: {args.data_path}")
    print(f"Output directory: {args.output_dir}")
    print(f"GAN checkpoint: {args.gan_checkpoint}")
    print()
    
    # 定义文件路径
    ctr_predictor_path = os.path.join(args.output_dir, 'ctr_predictor.pt')
    synthetic_data_path = os.path.join(args.output_dir, 'high_quality_synthetic.csv')
    quick_fix_path = os.path.join(args.output_dir, 'quick_fix_synthetic.csv')
    
    success_count = 0
    total_steps = 0
    
    # Step 1: 检查GAN模型
    print("\n📋 Step 1: Check GAN Model")
    total_steps += 1
    
    if check_file_exists(args.gan_checkpoint, "GAN checkpoint"):
        print("✅ Using existing trained GAN model")
        success_count += 1
    elif args.retrain_gan:
        print("🔄 Retraining GAN (this will take a long time)...")
        cmd = [
            sys.executable, 'ctr-models-clean-v2/scripts/train_balanced_gan.py'
        ]
        if run_command(cmd, "GAN retraining"):
            success_count += 1
    else:
        print("❌ GAN checkpoint not found and retrain_gan=False")
        print("Please either:")
        print("1. Provide correct --gan_checkpoint path")
        print("2. Use --retrain_gan flag")
        return
    
    # Step 2: 训练CTR预测器 (如果需要)
    print("\n📋 Step 2: Train CTR Predictor")
    total_steps += 1
    
    if args.quick_fix_only:
        print("⏭️  Skipping CTR predictor training (quick_fix_only=True)")
        success_count += 1
    elif args.skip_ctr_training and check_file_exists(ctr_predictor_path, "CTR predictor"):
        print("⏭️  Using existing CTR predictor")
        success_count += 1
    else:
        print("🎯 Training dedicated CTR predictor...")
        cmd = [
            sys.executable, 'ctr-models-clean-v2/scripts/train_ctr_predictor.py',
            '--data_path', args.data_path,
            '--output_path', ctr_predictor_path,
            '--max_samples', str(args.ctr_samples),
            '--epochs', str(args.ctr_epochs),
            '--batch_size', '1024',
            '--learning_rate', '1e-3'
        ]
        if run_command(cmd, "CTR predictor training"):
            success_count += 1
    
    # Step 3: 生成高质量合成数据
    print("\n📋 Step 3: Generate High-Quality Synthetic Data")
    total_steps += 1
    
    if args.quick_fix_only:
        print("🔧 Using quick fix approach...")
        
        # 首先需要生成基础合成数据
        print("Generating basic synthetic data first...")
        # 这里需要调用现有的GAN生成脚本
        # 暂时跳过，因为需要更复杂的集成
        
        print("⚠️  Quick fix approach needs existing synthetic data")
        print("Please first generate synthetic data using your current GAN, then run:")
        print(f"python ctr-models-clean-v2/scripts/fix_synthetic_labels.py \\")
        print(f"  --synthetic_data /path/to/your/synthetic_data.csv \\")
        print(f"  --real_data {args.data_path}/train.csv \\")
        print(f"  --output_path {quick_fix_path}")
        
    else:
        print("🎯 Generating high-quality synthetic data with dedicated CTR predictor...")
        cmd = [
            sys.executable, 'ctr-models-clean-v2/scripts/generate_high_quality_synthetic.py',
            '--gan_checkpoint', args.gan_checkpoint,
            '--ctr_predictor', ctr_predictor_path,
            '--output_path', synthetic_data_path,
            '--num_samples', str(args.synthetic_samples),
            '--batch_size', '1000'
        ]
        if run_command(cmd, "High-quality synthetic data generation"):
            success_count += 1
    
    # 总结
    print("\n" + "=" * 70)
    print("📊 Workflow Summary")
    print(f"Completed steps: {success_count}/{total_steps}")
    
    if success_count == total_steps:
        print("🎉 All steps completed successfully!")
        
        if not args.quick_fix_only:
            print(f"\n✅ High-quality synthetic data saved to: {synthetic_data_path}")
            print("\n🧪 Next steps for testing:")
            print("1. Test the synthetic data with your FM model")
            print("2. Compare AUC with real data")
            print("3. Expected improvement: AUC should be much closer to 0.72")
            
            print(f"\n📈 Expected results:")
            print(f"- Synthetic train → Real test: AUC ~0.65-0.70 (vs current 0.544)")
            print(f"- Synthetic train → Synthetic test: AUC ~0.68-0.72 (vs current 0.49)")
        
    else:
        print("❌ Some steps failed. Please check the errors above.")
        print("\n🔧 Troubleshooting:")
        print("1. Check all file paths are correct")
        print("2. Ensure sufficient disk space and memory")
        print("3. Verify Criteo dataset is properly formatted")
        
        if success_count > 0:
            print(f"\n✅ {success_count} steps completed successfully")
            print("You can resume from the failed step")


if __name__ == "__main__":
    main()
