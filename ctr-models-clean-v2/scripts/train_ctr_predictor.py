#!/usr/bin/env python3
"""
训练专用的CTR预测器，用于为合成数据生成高质量的CTR标签
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.metrics import roc_auc_score, log_loss
import argparse
import logging

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))

from src.data_process.utils import seed_everything


class CTRPredictor(nn.Module):
    """专用的CTR预测模型"""
    
    def __init__(self, numeric_features, categorical_features, vocab_sizes, embedding_dim=16):
        super().__init__()
        
        self.num_numeric = len(numeric_features)
        self.num_categorical = len(categorical_features)
        self.embedding_dim = embedding_dim
        
        # 类别特征嵌入
        self.embeddings = nn.ModuleList([
            nn.Embedding(vocab_size, embedding_dim)
            for vocab_size in vocab_sizes
        ])
        
        # 特征处理
        total_dim = self.num_numeric + self.num_categorical * embedding_dim
        
        self.feature_processor = nn.Sequential(
            nn.Linear(total_dim, 512),
            nn.BatchNorm1d(512),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(512, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(0.3),
            
            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(0.2),
            
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.1),
            
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
    def forward(self, numeric_data, categorical_data):
        """前向传播"""
        features = []
        
        # 数值特征
        if numeric_data is not None and self.num_numeric > 0:
            features.append(numeric_data)
        
        # 类别特征嵌入
        if categorical_data is not None and self.num_categorical > 0:
            cat_embeddings = []
            for i, embedding in enumerate(self.embeddings):
                if i < categorical_data.size(1):
                    emb = embedding(categorical_data[:, i])
                    cat_embeddings.append(emb)
            
            if cat_embeddings:
                cat_features = torch.cat(cat_embeddings, dim=1)
                features.append(cat_features)
        
        # 组合特征
        if features:
            combined_features = torch.cat(features, dim=1)
            ctr_prob = self.feature_processor(combined_features)
            return ctr_prob.squeeze()
        else:
            raise ValueError("No valid features provided")


def load_criteo_data(data_path, max_samples=100000):
    """加载Criteo数据"""
    train_file = os.path.join(data_path, 'train.csv')
    
    print(f"Loading Criteo data from {train_file}")
    
    # 读取数据
    df = pd.read_csv(train_file, nrows=max_samples)
    print(f"Loaded {len(df)} samples")
    
    # 分离特征和标签
    label_col = 'Label'
    numeric_cols = [col for col in df.columns if col.startswith('I')]
    categorical_cols = [col for col in df.columns if col.startswith('C')]
    
    print(f"Numeric features: {len(numeric_cols)}")
    print(f"Categorical features: {len(categorical_cols)}")
    print(f"CTR rate: {df[label_col].mean():.4f}")
    
    # 处理数值特征 - 简单标准化
    for col in numeric_cols:
        df[col] = df[col].fillna(0)
        df[col] = (df[col] - df[col].mean()) / (df[col].std() + 1e-8)
    
    # 处理类别特征 - 标签编码
    vocab_sizes = []
    for col in categorical_cols:
        df[col] = df[col].fillna('unknown')
        unique_vals = df[col].unique()
        val_to_idx = {val: idx for idx, val in enumerate(unique_vals)}
        df[col] = df[col].map(val_to_idx)
        vocab_sizes.append(len(unique_vals))
    
    return df, numeric_cols, categorical_cols, vocab_sizes


def train_ctr_predictor(args):
    """训练CTR预测器"""
    
    # 设置随机种子
    seed_everything(args.seed)
    
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载数据
    df, numeric_cols, categorical_cols, vocab_sizes = load_criteo_data(
        args.data_path, args.max_samples
    )
    
    # 准备训练数据
    X_numeric = torch.FloatTensor(df[numeric_cols].values)
    X_categorical = torch.LongTensor(df[categorical_cols].values)
    y = torch.FloatTensor(df['Label'].values)
    
    # 数据分割
    n_train = int(0.8 * len(df))
    n_val = int(0.1 * len(df))
    
    train_idx = torch.randperm(len(df))
    
    X_numeric_train = X_numeric[train_idx[:n_train]]
    X_categorical_train = X_categorical[train_idx[:n_train]]
    y_train = y[train_idx[:n_train]]
    
    X_numeric_val = X_numeric[train_idx[n_train:n_train+n_val]]
    X_categorical_val = X_categorical[train_idx[n_train:n_train+n_val]]
    y_val = y[train_idx[n_train:n_train+n_val]]
    
    X_numeric_test = X_numeric[train_idx[n_train+n_val:]]
    X_categorical_test = X_categorical[train_idx[n_train+n_val:]]
    y_test = y[train_idx[n_train+n_val:]]
    
    print(f"Train: {len(y_train)}, Val: {len(y_val)}, Test: {len(y_test)}")
    
    # 创建模型
    model = CTRPredictor(
        numeric_features=numeric_cols,
        categorical_features=categorical_cols,
        vocab_sizes=vocab_sizes,
        embedding_dim=args.embedding_dim
    ).to(device)
    
    # 优化器和损失函数
    optimizer = torch.optim.Adam(model.parameters(), lr=args.learning_rate, weight_decay=1e-5)
    criterion = nn.BCELoss()
    
    # 训练循环
    best_val_auc = 0
    patience = 0
    
    for epoch in range(args.epochs):
        model.train()
        
        # 训练
        train_loss = 0
        train_preds = []
        train_labels = []
        
        for i in range(0, len(X_numeric_train), args.batch_size):
            end_idx = min(i + args.batch_size, len(X_numeric_train))
            
            batch_numeric = X_numeric_train[i:end_idx].to(device)
            batch_categorical = X_categorical_train[i:end_idx].to(device)
            batch_labels = y_train[i:end_idx].to(device)
            
            optimizer.zero_grad()
            
            outputs = model(batch_numeric, batch_categorical)
            loss = criterion(outputs, batch_labels)
            
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_preds.extend(outputs.detach().cpu().numpy())
            train_labels.extend(batch_labels.cpu().numpy())
        
        # 验证
        model.eval()
        val_preds = []
        val_labels = []
        
        with torch.no_grad():
            for i in range(0, len(X_numeric_val), args.batch_size):
                end_idx = min(i + args.batch_size, len(X_numeric_val))
                
                batch_numeric = X_numeric_val[i:end_idx].to(device)
                batch_categorical = X_categorical_val[i:end_idx].to(device)
                batch_labels = y_val[i:end_idx].to(device)
                
                outputs = model(batch_numeric, batch_categorical)
                
                val_preds.extend(outputs.cpu().numpy())
                val_labels.extend(batch_labels.cpu().numpy())
        
        # 计算指标
        train_auc = roc_auc_score(train_labels, train_preds)
        val_auc = roc_auc_score(val_labels, val_preds)
        
        print(f"Epoch {epoch+1}/{args.epochs}: "
              f"Train Loss: {train_loss/len(train_labels):.4f}, "
              f"Train AUC: {train_auc:.4f}, "
              f"Val AUC: {val_auc:.4f}")
        
        # 早停
        if val_auc > best_val_auc:
            best_val_auc = val_auc
            patience = 0
            
            # 保存最佳模型
            torch.save({
                'model_state_dict': model.state_dict(),
                'numeric_features': numeric_cols,
                'categorical_features': categorical_cols,
                'vocab_sizes': vocab_sizes,
                'embedding_dim': args.embedding_dim,
                'val_auc': val_auc
            }, args.output_path)
            
        else:
            patience += 1
            if patience >= args.patience:
                print(f"Early stopping at epoch {epoch+1}")
                break
    
    # 测试
    model.eval()
    test_preds = []
    test_labels = []
    
    with torch.no_grad():
        for i in range(0, len(X_numeric_test), args.batch_size):
            end_idx = min(i + args.batch_size, len(X_numeric_test))
            
            batch_numeric = X_numeric_test[i:end_idx].to(device)
            batch_categorical = X_categorical_test[i:end_idx].to(device)
            batch_labels = y_test[i:end_idx].to(device)
            
            outputs = model(batch_numeric, batch_categorical)
            
            test_preds.extend(outputs.cpu().numpy())
            test_labels.extend(batch_labels.cpu().numpy())
    
    test_auc = roc_auc_score(test_labels, test_preds)
    print(f"\nFinal Test AUC: {test_auc:.4f}")
    
    return model


def main():
    parser = argparse.ArgumentParser(description='Train CTR Predictor for Synthetic Data')
    
    parser.add_argument('--data_path', type=str, default='/data/Criteo_x4',
                       help='Path to Criteo dataset')
    parser.add_argument('--output_path', type=str, default='/data/ctr_predictor.pt',
                       help='Path to save trained model')
    parser.add_argument('--max_samples', type=int, default=500000,
                       help='Maximum samples to use for training')
    parser.add_argument('--embedding_dim', type=int, default=16,
                       help='Embedding dimension')
    parser.add_argument('--batch_size', type=int, default=1024,
                       help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=1e-3,
                       help='Learning rate')
    parser.add_argument('--epochs', type=int, default=50,
                       help='Number of epochs')
    parser.add_argument('--patience', type=int, default=5,
                       help='Early stopping patience')
    parser.add_argument('--seed', type=int, default=2024,
                       help='Random seed')
    
    args = parser.parse_args()
    
    print("🎯 Training dedicated CTR predictor for high-quality synthetic labels")
    print(f"Data path: {args.data_path}")
    print(f"Output path: {args.output_path}")
    
    model = train_ctr_predictor(args)
    
    print("✅ CTR predictor training completed!")
    print("Use this model to generate realistic CTR labels for synthetic data")


if __name__ == "__main__":
    main()
