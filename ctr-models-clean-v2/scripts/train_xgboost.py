import os
import sys
import logging
import argparse
from datetime import datetime
import wandb
import xgboost as xgb
import h5py
from pathlib import Path
# Add project root directory to Python path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))
from src.metrics import evaluate_metrics
from src.data_process.utils import seed_everything, print_to_list

def parse_args():
    parser = argparse.ArgumentParser(description='XGBoost for CTR Prediction')
    parser.add_argument('--dataset_name', type=str, default='Criteo')
    parser.add_argument('--data_path', type=str, default='../data/processed/')
    parser.add_argument('--model_output_path', type=str, default='../output/')
    parser.add_argument('--log_dir', type=str, default='../output/logs/')
    parser.add_argument('--metrics', nargs='+', default=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'])
    # XGBoost specific parameters
    parser.add_argument('--learning_rate', type=float, default=0.1)
    parser.add_argument('--n_estimators', type=int, default=3000)
    parser.add_argument('--max_depth', type=int, default=3)
    parser.add_argument('--min_child_weight', type=float, default=1.0)
    parser.add_argument('--colsample_bytree', type=float, default=0.8)
    parser.add_argument('--reg_lambda', type=float, default=1.0)
    parser.add_argument('--reg_alpha', type=float, default=0.0)
    parser.add_argument('--early_stopping_rounds', type=int, default=12)
    parser.add_argument('--seed', type=int, default=2019)
    parser.add_argument('--gpu_id', type=int, default=1)
    return parser.parse_args()

def load_h5_data(file_path, sample_size=None):
    with h5py.File(file_path, 'r') as hf:
        if sample_size is not None:
            # Get the total size of the dataset
            total_size = hf['data'].shape[0]
            # Take min of sample_size and total_size
            sample_size = min(sample_size, total_size)
            # Load only the first sample_size rows
            data = hf['data'][:sample_size]
        else:
            data = hf['data'][:]
    X = data[:, :-1]
    y = data[:, -1]
    return X, y

def main():
    args = parse_args()
    
    # Set seed
    seed_everything(args.seed)
    
    # Initialize wandb
    wandb.init(
        project=f"xgboost-ctr-{args.dataset_name}",
        name=f"xgboost_{datetime.now().strftime('%Y%m%d_%H%M')}_seed{args.seed}",
        config=vars(args)
    )
    
    # Set up logging
    os.makedirs(args.log_dir, exist_ok=True)
    log_filename = f"{args.dataset_name}_XGBoost_{args.seed}_{datetime.now().strftime('%Y%m%d_%H%M')}.log"
    log_path = Path(args.log_dir) / log_filename
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s %(levelname)s %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[logging.StreamHandler(sys.stdout),
                 logging.FileHandler(log_path)]
    )
    logger = logging.getLogger(__name__)
    
    # Load data with sample size (e.g., 1000 samples)
    X_train, y_train = load_h5_data(os.path.join(args.data_path, 'train.h5'))
    X_valid, y_valid = load_h5_data(os.path.join(args.data_path, 'valid.h5'))
    X_test, y_test = load_h5_data(os.path.join(args.data_path, 'test.h5'))
    
    # Create XGBoost datasets
    dtrain = xgb.DMatrix(X_train, label=y_train)
    dvalid = xgb.DMatrix(X_valid, label=y_valid)
    dtest = xgb.DMatrix(X_test, label=y_test)
    
    # Set XGBoost parameters
    params = {
        'objective': 'binary:logistic',
        'learning_rate': args.learning_rate,
        'max_depth': args.max_depth,
        'min_child_weight': args.min_child_weight,
        'colsample_bytree': args.colsample_bytree,
        'reg_lambda': args.reg_lambda,
        'reg_alpha': args.reg_alpha,
        'scale_pos_weight': 1,
        'max_delta_step': 1,
        'tree_method': 'hist' if args.gpu_id >= 0 else 'hist',
        'random_state': args.seed,
        'device': 'cuda' if args.gpu_id >= 0 else 'cpu'
    }
    
    # Train model
    logger.info("Start training XGBoost model...")
    model = xgb.train(
        params,
        dtrain,
        num_boost_round=args.n_estimators,
        evals=[(dtrain, 'train'), (dvalid, 'valid')],
        early_stopping_rounds=args.early_stopping_rounds,
        verbose_eval=10,
        )
    
    # Evaluate model
    logger.info("Evaluating model...")
    valid_pred = model.predict(dvalid)
    test_pred = model.predict(dtest)
    
    valid_result = evaluate_metrics(y_valid, valid_pred, args.metrics, stage='valid')
    test_result = evaluate_metrics(y_test, test_pred, args.metrics, stage='test')
    
    # Save results
    result_dir = os.path.join(args.model_output_path, 'XGBoost', args.dataset_name)
    os.makedirs(result_dir, exist_ok=True)
    result_filename = os.path.join(result_dir, f'result_seed{args.seed}.csv')
    
    timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
    command = ' '.join(sys.argv)
    result_line = (f"{timestamp},"
                  f"[command] python {command},"
                  f"[dataset] {args.dataset_name},"
                  f"[model] XGBoost,"
                  f"[valid] {print_to_list(valid_result)},"
                  f"[test] {print_to_list(test_result)}\n"
                  )
    
    with open(result_filename, 'a+') as f:
        f.write(result_line)
    
    # Save model
    model_path = os.path.join(result_dir, f'model_seed{args.seed}.json')
    model.save_model(model_path)
    
    wandb.finish()

if __name__ == "__main__":
    main()