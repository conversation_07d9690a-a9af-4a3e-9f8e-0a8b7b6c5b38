# import pandas as pd
# import os

# # 随机抽取测试集
# test_data = pd.read_csv('/data/Criteo_x4/test.csv')
# test_sample = test_data.sample(n=10000, random_state=42)
# test_sample.to_csv('/data/Criteo_synthetic/test.csv', index=False)

# # 随机抽取验证集  
# val_data = pd.read_csv('/data/Criteo_x4/valid.csv')
# val_sample = val_data.sample(n=10000, random_state=42)
# val_sample.to_csv('/data/Criteo_synthetic/valid.csv', index=False)

# print('Test and validation samples created')


# 全面检查数据质量
import pandas as pd
import numpy as np

def diagnose_data_quality(synthetic_path, real_path):
    synthetic = pd.read_csv(synthetic_path)
    real = pd.read_csv(real_path)
    
    print("=== 数据质量诊断 ===")
    
    # 1. 基本统计
    print(f"Synthetic shape: {synthetic.shape}")
    print(f"Real shape: {real.shape}")
    
    # 2. CTR率对比
    syn_ctr = synthetic['Label'].mean()
    real_ctr = real['Label'].mean()
    print(f"Synthetic CTR: {syn_ctr:.4f}")
    print(f"Real CTR: {real_ctr:.4f}")
    print(f"CTR ratio: {syn_ctr/real_ctr:.2f}")
    
    # 3. 数值特征统计
    numeric_cols = [f'I{i}' for i in range(1, 14)]
    print("\n=== 数值特征对比 ===")
    for col in numeric_cols[:3]:  # 检查前3个
        if col in synthetic.columns and col in real.columns:
            syn_mean = synthetic[col].mean()
            real_mean = real[col].mean()
            syn_std = synthetic[col].std()
            real_std = real[col].std()
            print(f"{col}: Syn({syn_mean:.3f}±{syn_std:.3f}) vs Real({real_mean:.3f}±{real_std:.3f})")
    
    # 4. 类别特征多样性
    categorical_cols = [f'C{i}' for i in range(1, 27)]
    print("\n=== 类别特征多样性 ===")
    for col in categorical_cols[:3]:  # 检查前3个
        if col in synthetic.columns and col in real.columns:
            syn_unique = synthetic[col].nunique()
            real_unique = real[col].nunique()
            print(f"{col}: Syn({syn_unique}) vs Real({real_unique}) unique values")

# 运行诊断
diagnose_data_quality('/data/Criteo_synthetic/train.csv', '/data/Criteo_x4/train.csv')