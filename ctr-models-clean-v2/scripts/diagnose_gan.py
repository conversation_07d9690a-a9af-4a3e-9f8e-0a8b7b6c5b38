#!/usr/bin/env python3
"""
诊断GAN模型生成质量的脚本
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))

from src.gan.trainer import RefinedGANConfig

def diagnose_gan_checkpoint(checkpoint_path):
    """诊断GAN检查点"""
    print(f"🔍 诊断检查点: {checkpoint_path}")
    
    if not os.path.exists(checkpoint_path):
        print(f"❌ 检查点文件不存在: {checkpoint_path}")
        return
    
    # 加载检查点
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    print(f"✅ 成功加载检查点")
    
    # 检查检查点内容
    print(f"📊 检查点信息:")
    print(f"  - Epoch: {checkpoint.get('epoch', 'Unknown')}")
    print(f"  - 训练阶段: {checkpoint.get('training_stage', 'Unknown')}")
    
    if 'metrics' in checkpoint:
        metrics = checkpoint['metrics']
        print(f"  - D_loss: {metrics.get('d_loss', 'N/A'):.4f}")
        print(f"  - G_loss: {metrics.get('g_loss', 'N/A'):.4f}")
        print(f"  - Numeric_std: {metrics.get('numeric_std', 'N/A'):.6f}")
        print(f"  - Numeric_range: {metrics.get('numeric_range', 'N/A'):.4f}")
        print(f"  - Real_score: {metrics.get('real_d_score', 'N/A'):.4f}")
        print(f"  - Fake_score: {metrics.get('fake_d_score', 'N/A'):.4f}")
    
    # 检查模型状态
    if 'generator_state_dict' in checkpoint:
        print(f"✅ Generator状态存在")
        gen_state = checkpoint['generator_state_dict']
        print(f"  - Generator参数数量: {len(gen_state)}")
        
        # 检查一些关键参数
        for key in list(gen_state.keys())[:5]:
            param = gen_state[key]
            if isinstance(param, torch.Tensor):
                print(f"  - {key}: {param.shape}, mean={param.mean().item():.6f}, std={param.std().item():.6f}")
    
    if 'discriminator_state_dict' in checkpoint:
        print(f"✅ Discriminator状态存在")
        disc_state = checkpoint['discriminator_state_dict']
        print(f"  - Discriminator参数数量: {len(disc_state)}")
    
    print()

def test_generator_output(checkpoint_path):
    """测试生成器输出"""
    print(f"🧪 测试生成器输出...")
    
    try:
        # 加载检查点
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        # 创建配置
        config = RefinedGANConfig()
        
        # 这里需要实际的特征信息来创建模型
        # 由于我们没有完整的特征信息，我们只能做基本检查
        print(f"⚠️  需要完整的特征信息来创建和测试模型")
        print(f"   建议在训练脚本中添加更详细的诊断信息")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🔍 GAN模型诊断工具")
    print("=" * 50)
    
    # 检查可能的检查点路径
    possible_paths = [
        "/data/improved_gan_v2/improved_best_model.pt",
        "/data/improved_gan_v2/improved_checkpoint_epoch_6.pt",
        "/data/improved_gan_v2/improved_checkpoint_epoch_3.pt"
    ]
    
    found_checkpoints = []
    for path in possible_paths:
        if os.path.exists(path):
            found_checkpoints.append(path)
    
    if not found_checkpoints:
        print("❌ 未找到任何检查点文件")
        print("请确保训练已经运行并生成了检查点")
        return
    
    print(f"✅ 找到 {len(found_checkpoints)} 个检查点:")
    for path in found_checkpoints:
        print(f"  - {path}")
    
    print()
    
    # 诊断每个检查点
    for checkpoint_path in found_checkpoints:
        diagnose_gan_checkpoint(checkpoint_path)
    
    # 测试最新的检查点
    if found_checkpoints:
        latest_checkpoint = found_checkpoints[0]  # 假设第一个是最新的
        test_generator_output(latest_checkpoint)
    
    print("🎯 诊断建议:")
    print("1. 检查训练日志中的数值特征标准差是否与评估结果一致")
    print("2. 确认生成器的输出格式和范围")
    print("3. 验证评估函数中的数据处理逻辑")
    print("4. 如果训练指标良好但评估指标差，可能是评估函数的问题")

if __name__ == "__main__":
    main()
