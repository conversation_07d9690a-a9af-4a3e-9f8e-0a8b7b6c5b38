#!/usr/bin/env python3
"""
改进的GAN训练脚本 - 解决训练不平衡和数值特征质量问题
主要改进：
1. 重新平衡的学习率 (G:D = 2:1)
2. 更强的数值特征多样性损失
3. 改进的判别器正则化
4. 更保守的温度控制
5. 优化的训练频率
"""

import os
import sys
import logging
import argparse
import json
import wandb
import torch
import numpy as np
from datetime import datetime
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))

# Import GAN modules
from src.gan.data_prep import prepare_gan_data
from src.gan.models import FixedImprovedGenerator, WeakDiscriminator
from src.gan.trainer import RefinedProgressiveTrainer, RefinedGANConfig, create_refined_dataloader

# Import existing utilities
from src.data_process.utils import seed_everything


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Improved GAN Training with Better Balance')
    
    # 数据集相关
    parser.add_argument('--dataset_name', type=str, default='Criteo',
                       choices=['Criteo', 'Avazu'],
                       help='Dataset name')
    parser.add_argument('--dataset_path', type=str, default='/data/Criteo_x4',
                       help='Path to dataset directory')
    parser.add_argument('--output_dir', type=str, default='/data/improved_gan',
                       help='Output directory for models and logs')
    
    # 模型参数
    parser.add_argument('--noise_dim', type=int, default=128,
                       help='Dimension of input noise')
    parser.add_argument('--embedding_dim', type=int, default=16,
                       help='Dimension of categorical embeddings')
    
    # 改进的训练参数
    parser.add_argument('--epochs', type=int, default=50,
                       help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=512,
                       help='Batch size')
    parser.add_argument('--generator_lr', type=float, default=2e-4,
                       help='Generator learning rate - improved balance')
    parser.add_argument('--discriminator_lr', type=float, default=1e-4,
                       help='Discriminator learning rate - improved balance')
    
    # 改进的温度控制
    parser.add_argument('--initial_temperature', type=float, default=3.0,
                       help='Initial temperature - higher for exploration')
    parser.add_argument('--min_temperature', type=float, default=1.5,
                       help='Minimum temperature - higher minimum')
    parser.add_argument('--temperature_decay', type=float, default=0.9995,
                       help='Temperature decay rate - slower')
    
    # 数据控制参数
    parser.add_argument('--max_vocab_size', type=int, default=10000,
                       help='Maximum vocabulary size per categorical feature')
    parser.add_argument('--sample_strategy', type=str, default='full',
                       choices=['full', 'random', 'progressive', 'balanced'],
                       help='Data sampling strategy')
    parser.add_argument('--max_samples', type=int, default=500000,
                       help='Maximum number of samples to use')
    
    # 梯度控制
    parser.add_argument('--max_grad_norm', type=float, default=1.0,
                       help='Maximum gradient norm for clipping')
    
    # 日志和保存
    parser.add_argument('--log_interval', type=int, default=100,
                       help='Logging interval (batches)')
    parser.add_argument('--save_interval', type=int, default=5,
                       help='Model saving interval (epochs)')
    parser.add_argument('--eval_interval', type=int, default=5,
                       help='Evaluation interval (epochs)')
    
    # 其他
    parser.add_argument('--seed', type=int, default=2024,
                       help='Random seed')
    parser.add_argument('--num_workers', type=int, default=2,
                       help='Number of data loading workers')
    parser.add_argument('--resume', type=str, default=None,
                       help='Path to checkpoint to resume from')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode (smaller dataset)')
    
    return parser.parse_args()


def setup_logging(output_dir, debug=False):
    """设置日志系统"""
    os.makedirs(output_dir, exist_ok=True)
    
    log_level = logging.DEBUG if debug else logging.INFO
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(output_dir, f'improved_gan_training_{timestamp}.log')
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(log_file)
        ]
    )
    
    return logging.getLogger(__name__)


def create_config_from_args(args):
    """从命令行参数创建配置对象"""
    config = RefinedGANConfig()
    
    # 更新配置
    config.noise_dim = args.noise_dim
    config.embedding_dim = args.embedding_dim
    config.generator_lr = args.generator_lr
    config.discriminator_lr = args.discriminator_lr
    config.batch_size = args.batch_size
    config.epochs = args.epochs
    config.initial_temperature = args.initial_temperature
    config.min_temperature = args.min_temperature
    config.temperature_decay = args.temperature_decay
    config.max_grad_norm = args.max_grad_norm
    config.log_interval = args.log_interval
    config.save_interval = args.save_interval
    config.dataset_name = args.dataset_name
    config.dataset_path = args.dataset_path
    
    return config


def load_and_prepare_data(args, logger):
    """加载和预处理数据"""
    logger.info(f"加载 {args.dataset_name} 数据集从 {args.dataset_path}")
    
    # 创建输出目录
    gan_data_dir = os.path.join(args.output_dir, 'data')
    os.makedirs(gan_data_dir, exist_ok=True)
    
    # 使用固定的数据量
    max_samples = args.max_samples
    if args.debug:
        max_samples = 10000
        logger.info(f"调试模式：使用 {max_samples} 样本")
    
    # 准备GAN训练数据
    processed_data, processor = prepare_gan_data(
        dataset_name=args.dataset_name,
        data_dir=args.dataset_path,
        output_dir=gan_data_dir,
        train_file='train.csv',
        use_cache=True,
        sample_strategy=args.sample_strategy,
        max_samples=max_samples,
        max_vocab_size=args.max_vocab_size
    )
    
    logger.info(f"最终数据集大小: {len(processed_data)} 样本")
    logger.info(f"特征信息: {processor.get_feature_info()}")
    
    # 创建数据加载器
    config = create_config_from_args(args)
    dataloader = create_refined_dataloader(processed_data, config)
    
    logger.info(f"创建数据加载器，共 {len(dataloader)} 批次")
    
    return dataloader, processor


def create_improved_models(processor, config, logger):
    """创建改进的GAN模型"""
    feature_info = processor.get_feature_info()
    
    logger.info("创建改进的Generator和Discriminator模型")
    
    # 创建生成器
    generator = FixedImprovedGenerator(
        noise_dim=config.noise_dim,
        numeric_features=feature_info['numeric_features'],
        categorical_features=feature_info['categorical_features'],
        vocab_sizes=feature_info['vocab_sizes'],
        embedding_dim=config.embedding_dim
    )
    
    # 创建判别器
    discriminator = WeakDiscriminator(
        numeric_features=feature_info['numeric_features'],
        categorical_features=feature_info['categorical_features'],
        vocab_sizes=feature_info['vocab_sizes'],
        embedding_dim=config.embedding_dim
    )
    
    # 统计参数量
    gen_params = sum(p.numel() for p in generator.parameters() if p.requires_grad)
    disc_params = sum(p.numel() for p in discriminator.parameters() if p.requires_grad)
    
    logger.info(f"Generator参数量: {gen_params:,}")
    logger.info(f"Discriminator参数量: {disc_params:,}")
    logger.info(f"参数比例 (G/D): {gen_params/disc_params:.2f}")
    logger.info(f"学习率比例 (G/D): {config.generator_lr/config.discriminator_lr:.2f}")
    
    return generator, discriminator


def main():
    """主训练函数"""
    # 解析参数
    args = parse_args()

    # 设置种子
    seed_everything(args.seed)

    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)

    # 设置日志
    logger = setup_logging(args.output_dir, args.debug)
    logger.info("开始改进的GAN训练 - 更好的平衡策略")
    logger.info(f"参数: {vars(args)}")

    # 初始化wandb
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    wandb.init(
        project=f"improved-gan-{args.dataset_name.lower()}",
        name=f"improved_gan_{args.dataset_name}_{timestamp}_seed{args.seed}",
        config=vars(args),
        dir=args.output_dir
    )

    try:
        # 1. 加载和预处理数据
        dataloader, processor = load_and_prepare_data(args, logger)

        # 2. 创建改进的模型
        config = create_config_from_args(args)
        generator, discriminator = create_improved_models(processor, config, logger)

        # 3. 创建改进的训练器
        trainer = RefinedProgressiveTrainer(generator, discriminator, config)

        # 4. 恢复检查点（如果有）
        start_epoch = 0
        if args.resume:
            logger.info(f"从检查点恢复: {args.resume}")
            trainer.load_checkpoint(args.resume)
            start_epoch = trainer.current_epoch

        # 5. 保存配置
        config_path = os.path.join(args.output_dir, 'improved_config.json')
        with open(config_path, 'w') as f:
            json.dump(vars(args), f, indent=2)

        # 6. 训练循环
        logger.info("开始改进的训练循环")
        best_numeric_quality = 0.0

        for epoch in range(start_epoch, config.epochs):
            logger.info(f"开始第{epoch + 1}/{config.epochs}个epoch (阶段: {trainer.training_stage})")

            # 训练一个epoch
            epoch_metrics = trainer.train_epoch(dataloader)

            logger.info(f"第{epoch + 1}个epoch完成. 指标: {epoch_metrics}")

            # 评估模型
            if (epoch + 1) % args.eval_interval == 0:
                # 这里可以添加评估逻辑
                logger.info(f"第{epoch + 1}个epoch评估完成")

            # 保存检查点
            if (epoch + 1) % config.save_interval == 0:
                checkpoint_path = os.path.join(args.output_dir, f'improved_checkpoint_epoch_{epoch + 1}.pt')
                trainer.save_checkpoint(checkpoint_path, epoch + 1, epoch_metrics)

        # 7. 训练完成，保存最终模型
        final_path = os.path.join(args.output_dir, 'improved_final_model.pt')
        trainer.save_checkpoint(final_path, config.epochs, epoch_metrics)

        logger.info("改进的训练成功完成！")
        logger.info(f"模型保存到: {args.output_dir}")

    except Exception as e:
        logger.error(f"改进训练失败: {e}")
        logger.error(f"错误详情:", exc_info=True)
        raise

    finally:
        wandb.finish()


if __name__ == "__main__":
    main()
