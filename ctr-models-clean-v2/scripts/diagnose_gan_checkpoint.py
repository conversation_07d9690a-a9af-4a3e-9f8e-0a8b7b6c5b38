#!/usr/bin/env python3
"""
诊断GAN检查点的兼容性问题
"""

import torch
import sys
from pathlib import Path

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))


def diagnose_gan_checkpoint(checkpoint_path):
    """诊断GAN检查点"""
    
    print(f"🔍 Diagnosing GAN checkpoint: {checkpoint_path}")
    print("=" * 60)
    
    # 加载检查点
    checkpoint = torch.load(checkpoint_path, map_location='cpu', weights_only=False)
    
    print("📋 Checkpoint contents:")
    for key in checkpoint.keys():
        if isinstance(checkpoint[key], dict):
            print(f"  {key}: dict with {len(checkpoint[key])} items")
        else:
            print(f"  {key}: {type(checkpoint[key])}")
    
    print("\n🏗️  Generator state dict structure:")
    if 'generator_state_dict' in checkpoint:
        gen_state = checkpoint['generator_state_dict']
        
        # 分析结构
        categorical_keys = [k for k in gen_state.keys() if 'categorical_generator' in k]
        numeric_keys = [k for k in gen_state.keys() if 'numeric_generator' in k]
        
        print(f"  Total parameters: {len(gen_state)}")
        print(f"  Categorical generator parameters: {len(categorical_keys)}")
        print(f"  Numeric generator parameters: {len(numeric_keys)}")
        
        print("\n📊 Categorical generator structure:")
        cat_structure = {}
        for key in categorical_keys:
            parts = key.split('.')
            if len(parts) >= 3:
                component = '.'.join(parts[:3])
                if component not in cat_structure:
                    cat_structure[component] = []
                cat_structure[component].append(key)
        
        for component, keys in cat_structure.items():
            print(f"  {component}: {len(keys)} parameters")
            if len(keys) <= 10:  # 只显示少量参数
                for key in keys[:5]:
                    tensor = gen_state[key]
                    print(f"    {key}: {tensor.shape}")
                if len(keys) > 5:
                    print(f"    ... and {len(keys) - 5} more")
        
        # 检查group_generators结构
        group_keys = [k for k in categorical_keys if 'group_generators' in k]
        if group_keys:
            print(f"\n🔢 Group generators found: {len(group_keys)} parameters")
            
            # 分析group数量
            groups = set()
            for key in group_keys:
                parts = key.split('.')
                for i, part in enumerate(parts):
                    if part == 'group_generators' and i + 1 < len(parts):
                        groups.add(int(parts[i + 1]))
            
            print(f"  Number of groups: {len(groups)}")
            print(f"  Group indices: {sorted(groups)}")
            
            # 分析每个group的结构
            for group_idx in sorted(groups):
                group_keys_for_idx = [k for k in group_keys if f'group_generators.{group_idx}.' in k]
                print(f"  Group {group_idx}: {len(group_keys_for_idx)} parameters")
                
                # 检查category_heads
                category_head_keys = [k for k in group_keys_for_idx if 'category_heads' in k]
                if category_head_keys:
                    categories = set()
                    for key in category_head_keys:
                        parts = key.split('.')
                        for i, part in enumerate(parts):
                            if part == 'category_heads' and i + 1 < len(parts):
                                categories.add(int(parts[i + 1]))
                    print(f"    Categories in group {group_idx}: {sorted(categories)}")
    
    print("\n💾 Checkpoint metadata:")
    if 'epoch' in checkpoint:
        print(f"  Epoch: {checkpoint['epoch']}")
    if 'training_stage' in checkpoint:
        print(f"  Training stage: {checkpoint['training_stage']}")
    if 'metrics' in checkpoint:
        metrics = checkpoint['metrics']
        print(f"  Metrics: {list(metrics.keys())}")
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                print(f"    {key}: {value:.4f}")
    
    print("\n🎯 Recommendations:")
    print("1. The model architecture depends on the exact feature configuration")
    print("2. To fix loading issues, you need to recreate the exact same feature setup")
    print("3. Alternative: Use the simple_label_fix.py approach to avoid GAN loading")
    print("4. For future training, save the complete model configuration in checkpoint")


def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Diagnose GAN Checkpoint')
    parser.add_argument('--checkpoint', type=str, required=True,
                       help='Path to GAN checkpoint')
    
    args = parser.parse_args()
    
    try:
        diagnose_gan_checkpoint(args.checkpoint)
    except Exception as e:
        print(f"❌ Diagnosis failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
