#!/usr/bin/env python3
"""
继续改进的GAN训练 - 基于当前良好的进展
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """继续训练改进的GAN"""
    
    # 设置路径
    script_dir = Path(__file__).parent
    improved_script = script_dir / "train_gan_improved.py"
    
    # 训练参数
    args = [
        sys.executable, str(improved_script),
        "--dataset_name", "Criteo",
        "--dataset_path", "/data/Criteo_x4",
        "--output_dir", "/data/improved_gan_v2",
        "--epochs", "40",
        "--batch_size", "512",
        "--generator_lr", "2e-4",
        "--discriminator_lr", "1e-4",
        "--initial_temperature", "3.0",
        "--min_temperature", "1.5",
        "--temperature_decay", "0.9995",
        "--max_samples", "500000",
        "--eval_interval", "3",  # 更频繁的评估
        "--save_interval", "3",  # 更频繁的保存
        "--seed", "2024"
    ]
    
    print("🚀 开始继续改进的GAN训练...")
    print("📊 主要改进:")
    print("  - 更高的数值特征多样性要求 (min_std: 0.08)")
    print("  - 更强的多样性损失权重 (lambda_diversity: 1.0)")
    print("  - 更强的数值多样性权重 (lambda_numeric_diversity: 8.0)")
    print("  - 更强的判别器正则化 (lambda_d_reg: 0.08)")
    print("  - 更频繁的评估和保存")
    print()
    print("🎯 目标:")
    print("  - 数值特征质量评分 > 0.8")
    print("  - 特征平均标准差 > 0.08")
    print("  - 保持训练稳定性")
    print()
    
    try:
        # 运行训练
        result = subprocess.run(args, check=True)
        print("✅ 训练成功完成!")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练失败: {e}")
        return 1
    except KeyboardInterrupt:
        print("⏹️ 训练被用户中断")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
