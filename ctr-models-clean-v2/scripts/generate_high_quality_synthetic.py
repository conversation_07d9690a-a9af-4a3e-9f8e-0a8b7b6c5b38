#!/usr/bin/env python3
"""
使用训练好的CTR预测器生成高质量的合成CTR数据
"""

import os
import sys
import torch
import numpy as np
import pandas as pd
from pathlib import Path
import argparse
import logging

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))

from src.gan.data_prep import prepare_gan_data
from src.gan.models import FixedImprovedGenerator, WeakDiscriminator
from src.gan.trainer import RefinedGANConfig
from scripts.train_ctr_predictor import CTRPredictor


def load_trained_models(gan_checkpoint_path, ctr_predictor_path, device):
    """加载训练好的GAN和CTR预测器"""

    print(f"Loading GAN from {gan_checkpoint_path}")
    gan_checkpoint = torch.load(gan_checkpoint_path, map_location=device)

    print(f"Loading CTR predictor from {ctr_predictor_path}")
    ctr_checkpoint = torch.load(ctr_predictor_path, map_location=device)

    # 从GAN checkpoint获取特征信息
    # 需要从训练时的配置重建模型
    from src.gan.data_prep import prepare_gan_data

    # 重新加载数据处理器以获取特征信息
    print("Loading data processor to get feature info...")
    processed_data, processor = prepare_gan_data(
        dataset_name='Criteo',
        data_dir='/data/Criteo_x4',  # 硬编码，应该从参数传入
        output_dir='/tmp/temp_gan_data',
        train_file='train.csv',
        use_cache=True,
        sample_strategy='full',
        max_samples=10000,  # 只需要获取特征信息，样本数少一点
        max_vocab_size=10000
    )

    feature_info = processor.get_feature_info()

    # 重建GAN生成器
    generator = FixedImprovedGenerator(
        noise_dim=128,  # 从checkpoint读取或使用默认值
        numeric_features=feature_info['numeric_features'],
        categorical_features=feature_info['categorical_features'],
        vocab_sizes=feature_info['vocab_sizes'],
        embedding_dim=16  # 从checkpoint读取或使用默认值
    ).to(device)

    # 加载GAN权重
    generator.load_state_dict(gan_checkpoint['generator_state_dict'])
    generator.eval()

    # 重建CTR预测器
    ctr_predictor = CTRPredictor(
        numeric_features=ctr_checkpoint['numeric_features'],
        categorical_features=ctr_checkpoint['categorical_features'],
        vocab_sizes=ctr_checkpoint['vocab_sizes'],
        embedding_dim=ctr_checkpoint['embedding_dim']
    ).to(device)

    ctr_predictor.load_state_dict(ctr_checkpoint['model_state_dict'])
    ctr_predictor.eval()

    print(f"✅ Models loaded successfully")
    print(f"CTR predictor validation AUC: {ctr_checkpoint['val_auc']:.4f}")

    return generator, ctr_predictor, processor


def generate_synthetic_batch(generator, ctr_predictor, processor, batch_size, device):
    """生成一批高质量的合成数据"""

    with torch.no_grad():
        # 生成噪声
        noise = torch.randn(batch_size, 128, device=device)  # 假设noise_dim=128

        # 通过GAN生成特征
        gen_output = generator(noise)

        # 准备CTR预测器的输入
        numeric_data = gen_output['numeric']

        # 处理类别数据 - 从概率转换为索引
        categorical_data = None
        if 'categorical_probs' in gen_output and gen_output['categorical_probs'] is not None:
            cat_indices = []
            for probs in gen_output['categorical_probs']:
                if probs is not None:
                    indices = torch.argmax(probs, dim=-1)
                    cat_indices.append(indices)

            if cat_indices:
                categorical_data = torch.stack(cat_indices, dim=1)

        # 数据预处理 - 标准化数值特征以匹配CTR预测器的期望输入
        if numeric_data is not None:
            # 简单的标准化 - 将[-2,2]范围映射到合理的数值范围
            numeric_data = numeric_data * 2.0  # 扩展范围

        # 使用CTR预测器生成高质量标签
        if numeric_data is not None and categorical_data is not None:
            # 确保类别索引在有效范围内
            categorical_data = torch.clamp(categorical_data, 0, 9999)  # 防止索引越界

            try:
                ctr_probs = ctr_predictor(numeric_data, categorical_data)
                ctr_labels = torch.bernoulli(ctr_probs).long()
            except Exception as e:
                print(f"Warning: CTR prediction failed: {e}")
                # 使用默认标签
                ctr_probs = torch.full((batch_size,), 0.25, device=device)
                ctr_labels = torch.bernoulli(ctr_probs).long()
        else:
            # 如果数据不完整，使用默认标签
            ctr_probs = torch.full((batch_size,), 0.25, device=device)
            ctr_labels = torch.bernoulli(ctr_probs).long()

        return {
            'numeric': numeric_data.cpu().numpy() if numeric_data is not None else None,
            'categorical': categorical_data.cpu().numpy() if categorical_data is not None else None,
            'ctr_labels': ctr_labels.cpu().numpy(),
            'ctr_probs': ctr_probs.cpu().numpy()
        }


def generate_high_quality_dataset(args):
    """生成高质量的合成数据集"""
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # 加载模型
    generator, ctr_predictor, processor = load_trained_models(
        args.gan_checkpoint, args.ctr_predictor, device
    )

    if generator is None:
        print("❌ GAN model loading failed")
        print("Please check the checkpoint path and model compatibility")
        return
    
    # 生成数据
    print(f"Generating {args.num_samples} high-quality synthetic samples")
    
    all_batches = []
    num_batches = (args.num_samples + args.batch_size - 1) // args.batch_size
    
    for i in range(num_batches):
        current_batch_size = min(args.batch_size, args.num_samples - i * args.batch_size)
        
        batch_data = generate_synthetic_batch(
            generator, ctr_predictor, processor, current_batch_size, device
        )
        
        all_batches.append(batch_data)
        
        if (i + 1) % 10 == 0:
            print(f"Generated {(i + 1) * args.batch_size} samples")
    
    # 合并所有批次
    combined_data = {}
    for key in all_batches[0].keys():
        combined_data[key] = np.concatenate([batch[key] for batch in all_batches])
    
    # 转换为DataFrame
    df_data = {}
    
    # 数值特征
    if combined_data['numeric'] is not None:
        for i in range(combined_data['numeric'].shape[1]):
            df_data[f'I{i+1}'] = combined_data['numeric'][:, i]
    
    # 类别特征
    if combined_data['categorical'] is not None:
        for i in range(combined_data['categorical'].shape[1]):
            df_data[f'C{i+1}'] = combined_data['categorical'][:, i]
    
    # CTR标签
    df_data['Label'] = combined_data['ctr_labels']
    df_data['ctr_prob'] = combined_data['ctr_probs']
    
    # 创建DataFrame
    df = pd.DataFrame(df_data)
    
    # 保存数据
    os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
    df.to_csv(args.output_path, index=False)
    
    print(f"✅ High-quality synthetic dataset saved to {args.output_path}")
    print(f"Dataset shape: {df.shape}")
    print(f"CTR rate: {df['Label'].mean():.4f}")
    print(f"CTR prob range: [{df['ctr_prob'].min():.4f}, {df['ctr_prob'].max():.4f}]")
    
    return df


def main():
    parser = argparse.ArgumentParser(description='Generate High-Quality Synthetic CTR Data')
    
    parser.add_argument('--gan_checkpoint', type=str, 
                       default='/data/balanced_gan_v2/improved_best_model.pt',
                       help='Path to trained GAN checkpoint')
    parser.add_argument('--ctr_predictor', type=str,
                       default='/data/ctr_predictor.pt',
                       help='Path to trained CTR predictor')
    parser.add_argument('--output_path', type=str,
                       default='/data/high_quality_synthetic_criteo.csv',
                       help='Output path for synthetic dataset')
    parser.add_argument('--num_samples', type=int, default=100000,
                       help='Number of synthetic samples to generate')
    parser.add_argument('--batch_size', type=int, default=1000,
                       help='Batch size for generation')
    
    args = parser.parse_args()
    
    print("🎯 Generating high-quality synthetic CTR data")
    print("=" * 50)
    print("Strategy:")
    print("1. Use trained GAN to generate realistic features")
    print("2. Use dedicated CTR predictor to generate meaningful labels")
    print("3. Ensure feature-label relationships match real data")
    print()
    
    try:
        df = generate_high_quality_dataset(args)
        
        print("\n🎉 High-quality synthetic data generation completed!")
        print("\nNext steps:")
        print("1. Test the synthetic data with FM model")
        print("2. Compare AUC with real data")
        print("3. Expected improvement: AUC should be much closer to 0.72")
        
    except Exception as e:
        print(f"❌ Generation failed: {e}")
        print("\nTo fix this, you need to:")
        print("1. First train the CTR predictor:")
        print("   python ctr-models-clean-v2/scripts/train_ctr_predictor.py")
        print("2. Then run this script")


if __name__ == "__main__":
    main()
