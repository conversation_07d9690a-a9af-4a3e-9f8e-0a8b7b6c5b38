#!/usr/bin/env python3
"""
全面分析GAN训练指标，识别所有关键问题
"""

import json
import matplotlib.pyplot as plt
import numpy as np

def analyze_gan_metrics():
    """分析GAN训练指标"""
    
    # 从log3.md提取的指标数据
    epochs_data = [
        # Epoch 1
        {
            'epoch': 1,
            'd_loss': 0.5541, 'd_adv_loss': 0.0156, 'd_ctr_loss': 0.5261, 'd_reg_loss': 0.1551,
            'real_d_score': 0.8002, 'fake_d_score': 0.1920,
            'g_loss': -3.3935, 'g_adv_loss': 0.6591, 'g_feature_loss': 1.3111, 
            'g_diversity_loss': -0.9998, 'g_numeric_diversity_loss': -0.3898,
            'fake_d_score_g': 0.1916, 'temperature': 3.0000, 
            'numeric_std': 0.0880, 'numeric_range': 3.9846
        },
        # Epoch 2
        {
            'epoch': 2,
            'd_loss': 0.5007, 'd_adv_loss': 0.0077, 'd_ctr_loss': 0.4789, 'd_reg_loss': 0.1767,
            'real_d_score': 0.8255, 'fake_d_score': 0.1739,
            'g_loss': -3.4632, 'g_adv_loss': 0.6844, 'g_feature_loss': 1.3007,
            'g_diversity_loss': -0.9999, 'g_numeric_diversity_loss': -0.4016,
            'fake_d_score_g': 0.1739, 'temperature': 2.9985,
            'numeric_std': 0.0746, 'numeric_range': 3.9997
        },
        # Epoch 3
        {
            'epoch': 3,
            'd_loss': 0.4834, 'd_adv_loss': 0.0073, 'd_ctr_loss': 0.4620, 'd_reg_loss': 0.1758,
            'real_d_score': 0.8253, 'fake_d_score': 0.1744,
            'g_loss': -3.4560, 'g_adv_loss': 0.6832, 'g_feature_loss': 1.3994,
            'g_diversity_loss': -0.9978, 'g_numeric_diversity_loss': -0.4014,
            'fake_d_score_g': 0.1744, 'temperature': 2.9970,
            'numeric_std': 0.0717, 'numeric_range': 3.9999
        },
        # Epoch 4
        {
            'epoch': 4,
            'd_loss': 0.4714, 'd_adv_loss': 0.0076, 'd_ctr_loss': 0.4498, 'd_reg_loss': 0.1756,
            'real_d_score': 0.8246, 'fake_d_score': 0.1742,
            'g_loss': -3.4119, 'g_adv_loss': 0.6850, 'g_feature_loss': 1.8599,
            'g_diversity_loss': -0.9820, 'g_numeric_diversity_loss': -0.4010,
            'fake_d_score_g': 0.1737, 'temperature': 2.9997,
            'numeric_std': 0.0716, 'numeric_range': 4.0000
        },
        # Epoch 5
        {
            'epoch': 5,
            'd_loss': 0.4622, 'd_adv_loss': 0.0073, 'd_ctr_loss': 0.4409, 'd_reg_loss': 0.1749,
            'real_d_score': 0.8251, 'fake_d_score': 0.1750,
            'g_loss': -3.4313, 'g_adv_loss': 0.6825, 'g_feature_loss': 2.0393,
            'g_diversity_loss': -0.9970, 'g_numeric_diversity_loss': -0.4023,
            'fake_d_score_g': 0.1749, 'temperature': 2.9985,
            'numeric_std': 0.0712, 'numeric_range': 4.0000
        },
        # Epoch 6
        {
            'epoch': 6,
            'd_loss': 0.4540, 'd_adv_loss': 0.0071, 'd_ctr_loss': 0.4329, 'd_reg_loss': 0.1749,
            'real_d_score': 0.8250, 'fake_d_score': 0.1748,
            'g_loss': -3.4153, 'g_adv_loss': 0.6831, 'g_feature_loss': 2.1274,
            'g_diversity_loss': -0.9857, 'g_numeric_diversity_loss': -0.4024,
            'fake_d_score_g': 0.1744, 'temperature': 2.9970,
            'numeric_std': 0.0715, 'numeric_range': 4.0000
        }
    ]
    
    print("🔍 GAN训练指标全面分析")
    print("=" * 60)
    
    # 1. 分析训练平衡问题
    print("\n📊 1. 训练平衡分析")
    print("-" * 30)
    
    for data in epochs_data:
        score_gap = data['real_d_score'] - data['fake_d_score']
        print(f"Epoch {data['epoch']}: Real={data['real_d_score']:.3f}, Fake={data['fake_d_score']:.3f}, Gap={score_gap:.3f}")
    
    avg_real = np.mean([d['real_d_score'] for d in epochs_data])
    avg_fake = np.mean([d['fake_d_score'] for d in epochs_data])
    avg_gap = avg_real - avg_fake
    
    print(f"\n平均值: Real={avg_real:.3f}, Fake={avg_fake:.3f}, Gap={avg_gap:.3f}")
    print(f"❌ 关键问题: Score gap = {avg_gap:.3f} (目标 <0.25)")
    print(f"❌ Fake_score过低: {avg_fake:.3f} (目标 0.4-0.5)")
    
    # 2. 分析损失函数趋势
    print("\n📈 2. 损失函数趋势分析")
    print("-" * 30)
    
    d_losses = [d['d_loss'] for d in epochs_data]
    g_losses = [d['g_loss'] for d in epochs_data]
    
    print(f"D_loss趋势: {d_losses[0]:.3f} → {d_losses[-1]:.3f} (变化: {d_losses[-1]-d_losses[0]:.3f})")
    print(f"G_loss趋势: {g_losses[0]:.3f} → {g_losses[-1]:.3f} (变化: {g_losses[-1]-g_losses[0]:.3f})")
    
    if d_losses[-1] < d_losses[0]:
        print("✅ D_loss下降 - 判别器在改进")
    else:
        print("⚠️  D_loss上升 - 判别器可能过强")
        
    if abs(g_losses[-1]) < abs(g_losses[0]):
        print("❌ G_loss绝对值下降 - 生成器可能在退化")
    else:
        print("✅ G_loss绝对值上升 - 生成器在努力对抗")
    
    # 3. 分析损失组件
    print("\n🔧 3. 损失组件分析")
    print("-" * 30)
    
    latest = epochs_data[-1]
    print(f"最新epoch ({latest['epoch']}) 损失组件:")
    print(f"  D_loss = {latest['d_loss']:.4f}")
    print(f"    ├─ d_adv_loss = {latest['d_adv_loss']:.4f} ({latest['d_adv_loss']/latest['d_loss']*100:.1f}%)")
    print(f"    ├─ d_ctr_loss = {latest['d_ctr_loss']:.4f} ({latest['d_ctr_loss']/latest['d_loss']*100:.1f}%)")
    print(f"    └─ d_reg_loss = {latest['d_reg_loss']:.4f} ({latest['d_reg_loss']/latest['d_loss']*100:.1f}%)")
    
    print(f"\n  G_loss = {latest['g_loss']:.4f}")
    print(f"    ├─ g_adv_loss = {latest['g_adv_loss']:.4f}")
    print(f"    ├─ g_feature_loss = {latest['g_feature_loss']:.4f}")
    print(f"    ├─ g_diversity_loss = {latest['g_diversity_loss']:.4f}")
    print(f"    └─ g_numeric_diversity_loss = {latest['g_numeric_diversity_loss']:.4f}")
    
    # 关键问题识别
    if latest['d_reg_loss'] / latest['d_loss'] > 0.3:
        print("✅ 判别器正则化强度适中")
    else:
        print("⚠️  判别器正则化可能不够强")
        
    if latest['g_feature_loss'] > 1.5:
        print("❌ 特征匹配损失过高 - 生成器难以匹配真实特征分布")
        
    # 4. 分析数值特征质量
    print("\n📊 4. 数值特征质量分析")
    print("-" * 30)
    
    std_trend = [d['numeric_std'] for d in epochs_data]
    range_trend = [d['numeric_range'] for d in epochs_data]
    
    print(f"数值标准差趋势: {std_trend[0]:.4f} → {std_trend[-1]:.4f}")
    print(f"数值范围趋势: {range_trend[0]:.4f} → {range_trend[-1]:.4f}")
    
    if std_trend[-1] < 0.08:
        print(f"⚠️  数值标准差 {std_trend[-1]:.4f} < 0.08 (目标)")
    else:
        print(f"✅ 数值标准差 {std_trend[-1]:.4f} ≥ 0.08")
        
    if range_trend[-1] >= 3.9:
        print(f"✅ 数值范围 {range_trend[-1]:.4f} 接近最大值4.0")
    else:
        print(f"❌ 数值范围 {range_trend[-1]:.4f} < 3.9")
    
    # 5. 温度控制分析
    print("\n🌡️  5. 温度控制分析")
    print("-" * 30)
    
    temp_trend = [d['temperature'] for d in epochs_data]
    print(f"温度趋势: {temp_trend[0]:.4f} → {temp_trend[-1]:.4f}")
    
    temp_decay_rate = (temp_trend[0] - temp_trend[-1]) / len(temp_trend)
    print(f"平均温度衰减率: {temp_decay_rate:.6f} per epoch")
    
    if temp_decay_rate < 0.001:
        print("✅ 温度衰减适中")
    else:
        print("⚠️  温度衰减可能过快")
    
    # 6. 关键问题总结
    print("\n🚨 6. 关键问题总结")
    print("-" * 30)
    
    critical_issues = []
    
    if avg_gap > 0.5:
        critical_issues.append(f"训练严重不平衡 (score_gap={avg_gap:.3f})")
    
    if avg_fake < 0.25:
        critical_issues.append(f"生成器过弱 (fake_score={avg_fake:.3f})")
        
    if latest['g_feature_loss'] > 2.0:
        critical_issues.append(f"特征匹配困难 (g_feature_loss={latest['g_feature_loss']:.3f})")
        
    if latest['d_reg_loss'] / latest['d_loss'] < 0.25:
        critical_issues.append("判别器正则化不足")
        
    if std_trend[-1] < 0.08:
        critical_issues.append(f"数值特征多样性不足 (std={std_trend[-1]:.4f})")
    
    if critical_issues:
        for i, issue in enumerate(critical_issues, 1):
            print(f"{i}. ❌ {issue}")
    else:
        print("✅ 未发现严重问题")
    
    # 7. 改进建议
    print("\n💡 7. 改进建议")
    print("-" * 30)
    
    print("基于分析结果，建议:")
    print("1. 🎯 大幅降低判别器学习率 (当前比例G:D=2:1 → 建议6:1)")
    print("2. 🔧 增强判别器正则化权重 (lambda_d_reg: 0.08 → 0.15)")
    print("3. 📈 增加生成器训练频率 (g_steps: 2 → 3)")
    print("4. 🎲 提高温度控制参数 (initial_temp: 3.0 → 3.5)")
    print("5. 📊 监控fake_score目标: 0.17 → 0.4+")
    
    return epochs_data

if __name__ == "__main__":
    analyze_gan_metrics()
