#!/usr/bin/env python3
"""
基于模式的标签修复 - 学习真实数据的统计模式来生成合理的CTR标签
避免复杂的深度学习模型，使用简单但有效的统计方法
"""

import pandas as pd
import numpy as np
import argparse
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score
import warnings
warnings.filterwarnings('ignore')


def load_real_data_patterns(real_data_path, max_samples=100000):
    """加载真实数据并学习CTR模式"""
    
    print(f"📊 Loading real data patterns from {real_data_path}")
    
    # 读取真实数据
    real_df = pd.read_csv(real_data_path, nrows=max_samples)
    print(f"Real data shape: {real_df.shape}")
    
    # 分离特征和标签
    label_col = 'Label'
    numeric_cols = [col for col in real_df.columns if col.startswith('I')]
    categorical_cols = [col for col in real_df.columns if col.startswith('C')]
    
    print(f"Numeric features: {len(numeric_cols)}")
    print(f"Categorical features: {len(categorical_cols)}")
    print(f"Real CTR rate: {real_df[label_col].mean():.4f}")
    
    return real_df, numeric_cols, categorical_cols


def create_simple_features(df, numeric_cols, categorical_cols):
    """创建简单的特征用于模式学习"""
    
    features = []
    feature_names = []
    
    # 1. 数值特征统计
    if numeric_cols:
        numeric_data = df[numeric_cols].fillna(0)
        
        # 基本统计特征
        features.append(numeric_data.mean(axis=1).values.reshape(-1, 1))
        feature_names.append('numeric_mean')
        
        features.append(numeric_data.std(axis=1).values.reshape(-1, 1))
        feature_names.append('numeric_std')
        
        features.append(numeric_data.sum(axis=1).values.reshape(-1, 1))
        feature_names.append('numeric_sum')
        
        # 非零特征数量
        non_zero_count = (numeric_data != 0).sum(axis=1).values.reshape(-1, 1)
        features.append(non_zero_count)
        feature_names.append('numeric_non_zero_count')
        
        # 前几个重要的数值特征
        for i, col in enumerate(numeric_cols[:5]):
            features.append(numeric_data[col].values.reshape(-1, 1))
            feature_names.append(f'numeric_{i}')
    
    # 2. 类别特征统计
    if categorical_cols:
        categorical_data = df[categorical_cols].fillna('unknown')
        
        # 类别特征的唯一值数量（作为数值特征）
        unique_counts = []
        for col in categorical_cols:
            # 将类别特征转换为数值（简单hash）
            if categorical_data[col].dtype == 'object':
                # 字符串类型，计算hash值的统计
                hash_values = categorical_data[col].apply(lambda x: hash(str(x)) % 10000)
                unique_counts.append(hash_values.values)
            else:
                # 数值类型，直接使用
                unique_counts.append(categorical_data[col].values)
        
        # 类别特征的统计
        cat_array = np.array(unique_counts).T
        
        features.append(cat_array.mean(axis=1).reshape(-1, 1))
        feature_names.append('categorical_mean')
        
        features.append(cat_array.std(axis=1).reshape(-1, 1))
        feature_names.append('categorical_std')
        
        # 前几个重要的类别特征
        for i in range(min(5, len(categorical_cols))):
            features.append(cat_array[:, i].reshape(-1, 1))
            feature_names.append(f'categorical_{i}')
    
    # 合并所有特征
    if features:
        X = np.hstack(features)
        print(f"Created {X.shape[1]} simple features: {feature_names}")
        return X, feature_names
    else:
        raise ValueError("No features could be created")


def train_pattern_model(real_df, numeric_cols, categorical_cols):
    """训练简单的模式识别模型"""
    
    print("🎯 Training pattern recognition model...")
    
    # 创建简单特征
    X, feature_names = create_simple_features(real_df, numeric_cols, categorical_cols)
    y = real_df['Label'].values
    
    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=2024, stratify=y
    )
    
    # 特征标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 训练多个简单模型
    models = {}
    
    # 1. 逻辑回归
    lr = LogisticRegression(random_state=2024, max_iter=1000)
    lr.fit(X_train_scaled, y_train)
    lr_pred = lr.predict_proba(X_test_scaled)[:, 1]
    lr_auc = roc_auc_score(y_test, lr_pred)
    models['logistic'] = (lr, scaler, lr_auc)
    
    # 2. 随机森林
    rf = RandomForestClassifier(n_estimators=100, random_state=2024, max_depth=10)
    rf.fit(X_train, y_train)  # RF不需要标准化
    rf_pred = rf.predict_proba(X_test)[:, 1]
    rf_auc = roc_auc_score(y_test, rf_pred)
    models['random_forest'] = (rf, None, rf_auc)
    
    print(f"Model performance on real data:")
    print(f"  Logistic Regression AUC: {lr_auc:.4f}")
    print(f"  Random Forest AUC: {rf_auc:.4f}")
    
    # 选择最佳模型
    best_model_name = max(models.keys(), key=lambda k: models[k][2])
    best_model, best_scaler, best_auc = models[best_model_name]
    
    print(f"Selected best model: {best_model_name} (AUC: {best_auc:.4f})")
    
    return best_model, best_scaler, feature_names, best_model_name


def apply_pattern_to_synthetic(synthetic_df, model, scaler, feature_names, model_name, 
                              numeric_cols, categorical_cols, target_ctr_rate=0.25):
    """将学习到的模式应用到合成数据"""
    
    print(f"🔄 Applying {model_name} pattern to synthetic data...")
    
    # 创建相同的简单特征
    X_synthetic, _ = create_simple_features(synthetic_df, numeric_cols, categorical_cols)
    
    # 预测CTR概率
    if scaler is not None:
        X_synthetic_scaled = scaler.transform(X_synthetic)
        ctr_probs = model.predict_proba(X_synthetic_scaled)[:, 1]
    else:
        ctr_probs = model.predict_proba(X_synthetic)[:, 1]
    
    # 调整概率分布以匹配目标CTR率
    current_mean = ctr_probs.mean()
    if current_mean > 0:
        # 简单的线性调整
        adjustment_factor = target_ctr_rate / current_mean
        adjusted_probs = ctr_probs * adjustment_factor
        adjusted_probs = np.clip(adjusted_probs, 0.001, 0.999)
    else:
        adjusted_probs = np.full_like(ctr_probs, target_ctr_rate)
    
    # 生成二进制标签
    np.random.seed(2024)
    new_labels = np.random.binomial(1, adjusted_probs)
    
    print(f"CTR probability statistics:")
    print(f"  Original mean: {current_mean:.4f}")
    print(f"  Adjusted mean: {adjusted_probs.mean():.4f}")
    print(f"  Target CTR rate: {target_ctr_rate:.4f}")
    print(f"  Final CTR rate: {new_labels.mean():.4f}")
    print(f"  Probability range: [{adjusted_probs.min():.4f}, {adjusted_probs.max():.4f}]")
    print(f"  Probability std: {adjusted_probs.std():.4f}")
    
    return new_labels, adjusted_probs


def main():
    parser = argparse.ArgumentParser(description='Pattern-Based Label Fix for Synthetic Data')
    
    parser.add_argument('--synthetic_data', type=str, required=True,
                       help='Path to synthetic data CSV file')
    parser.add_argument('--real_data', type=str, required=True,
                       help='Path to real Criteo train.csv file')
    parser.add_argument('--output_path', type=str, required=True,
                       help='Output path for fixed synthetic data')
    parser.add_argument('--real_samples', type=int, default=200000,
                       help='Number of real samples to use for pattern learning')
    parser.add_argument('--target_ctr_rate', type=float, default=0.25,
                       help='Target CTR rate for synthetic data')
    
    args = parser.parse_args()
    
    print("🎯 Pattern-Based Label Fix for Synthetic Data")
    print("=" * 60)
    print("Strategy:")
    print("1. Learn simple statistical patterns from real data")
    print("2. Train lightweight models on these patterns")
    print("3. Apply learned patterns to synthetic features")
    print("4. Generate realistic CTR labels")
    print()
    
    try:
        # 1. 学习真实数据模式
        real_df, numeric_cols, categorical_cols = load_real_data_patterns(
            args.real_data, args.real_samples
        )
        
        # 2. 训练模式识别模型
        model, scaler, feature_names, model_name = train_pattern_model(
            real_df, numeric_cols, categorical_cols
        )
        
        # 3. 加载合成数据
        print(f"\n📁 Loading synthetic data from {args.synthetic_data}")
        synthetic_df = pd.read_csv(args.synthetic_data)
        print(f"Synthetic data shape: {synthetic_df.shape}")
        
        if 'Label' in synthetic_df.columns:
            print(f"Original synthetic CTR rate: {synthetic_df['Label'].mean():.4f}")
        
        # 4. 应用模式到合成数据
        new_labels, new_probs = apply_pattern_to_synthetic(
            synthetic_df, model, scaler, feature_names, model_name,
            numeric_cols, categorical_cols, args.target_ctr_rate
        )
        
        # 5. 更新合成数据
        synthetic_df['Label'] = new_labels
        synthetic_df['ctr_prob'] = new_probs
        
        # 6. 保存结果
        synthetic_df.to_csv(args.output_path, index=False)
        
        print(f"\n✅ Pattern-based fixed synthetic data saved to {args.output_path}")
        
        # 7. 质量检查
        print(f"\n📊 Quality Check:")
        print(f"Final CTR rate: {new_labels.mean():.4f}")
        print(f"CTR prob range: [{new_probs.min():.4f}, {new_probs.max():.4f}]")
        print(f"CTR prob std: {new_probs.std():.4f}")
        
        print(f"\n🎯 Expected improvements:")
        print(f"- Should have better feature-label relationships")
        print(f"- CTR patterns should match real data better")
        print(f"- Expected AUC improvement: 0.52 → 0.60-0.65")
        
        print(f"\n🧪 Next steps:")
        print(f"1. Test with your FM model")
        print(f"2. Compare AUC with previous results")
        print(f"3. If still poor, the issue may be with synthetic feature quality")
        
    except Exception as e:
        print(f"❌ Pattern-based fixing failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
