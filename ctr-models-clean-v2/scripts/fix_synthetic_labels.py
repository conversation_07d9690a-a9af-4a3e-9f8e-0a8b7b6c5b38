#!/usr/bin/env python3
"""
快速修复现有合成数据的CTR标签问题
使用简单的启发式规则生成更合理的CTR标签
"""

import pandas as pd
import numpy as np
import argparse
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import roc_auc_score


def load_real_criteo_sample(real_data_path, sample_size=50000):
    """加载真实Criteo数据样本用于学习标签模式"""
    
    print(f"Loading real Criteo sample from {real_data_path}")
    
    # 读取真实数据
    real_df = pd.read_csv(real_data_path, nrows=sample_size)
    
    # 分离特征和标签
    label_col = 'Label'
    feature_cols = [col for col in real_df.columns if col != label_col]
    
    X = real_df[feature_cols]
    y = real_df[label_col]
    
    print(f"Real data shape: {X.shape}")
    print(f"Real CTR rate: {y.mean():.4f}")
    
    return X, y, feature_cols


def train_simple_ctr_model(X_real, y_real):
    """训练简单的CTR模型来学习标签模式"""
    
    print("Training simple CTR model on real data...")
    
    # 处理缺失值
    X_real = X_real.fillna(0)
    
    # 简单的特征工程 - 只使用数值特征
    numeric_cols = [col for col in X_real.columns if col.startswith('I')]
    X_numeric = X_real[numeric_cols]
    
    # 训练随机森林
    rf = RandomForestClassifier(
        n_estimators=100,
        max_depth=10,
        random_state=2024,
        n_jobs=-1
    )
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X_numeric, y_real, test_size=0.2, random_state=2024
    )
    
    # 训练
    rf.fit(X_train, y_train)
    
    # 评估
    y_pred_proba = rf.predict_proba(X_test)[:, 1]
    auc = roc_auc_score(y_test, y_pred_proba)
    
    print(f"Simple CTR model AUC: {auc:.4f}")
    
    return rf, numeric_cols


def fix_synthetic_labels(synthetic_data_path, real_data_path, output_path):
    """修复合成数据的CTR标签"""
    
    print("🔧 Fixing synthetic data labels...")
    
    # 1. 加载真实数据并训练简单模型
    X_real, y_real, feature_cols = load_real_criteo_sample(real_data_path)
    ctr_model, numeric_cols = train_simple_ctr_model(X_real, y_real)
    
    # 2. 加载合成数据
    print(f"Loading synthetic data from {synthetic_data_path}")
    synthetic_df = pd.read_csv(synthetic_data_path)
    
    print(f"Synthetic data shape: {synthetic_df.shape}")
    if 'Label' in synthetic_df.columns:
        print(f"Original synthetic CTR rate: {synthetic_df['Label'].mean():.4f}")
    
    # 3. 准备合成数据的特征
    synthetic_numeric = synthetic_df[numeric_cols].fillna(0)
    
    # 4. 使用训练好的模型预测CTR概率
    print("Generating improved CTR labels...")
    ctr_probs = ctr_model.predict_proba(synthetic_numeric)[:, 1]
    
    # 5. 调整概率分布以匹配真实数据
    real_ctr_rate = y_real.mean()
    current_mean = ctr_probs.mean()
    
    # 简单的概率调整
    adjusted_probs = ctr_probs * (real_ctr_rate / current_mean)
    adjusted_probs = np.clip(adjusted_probs, 0.001, 0.999)  # 避免极端值
    
    # 6. 生成二进制标签
    np.random.seed(2024)
    new_labels = np.random.binomial(1, adjusted_probs)
    
    # 7. 更新合成数据
    synthetic_df['Label'] = new_labels
    synthetic_df['ctr_prob'] = adjusted_probs
    
    print(f"New synthetic CTR rate: {new_labels.mean():.4f}")
    print(f"Target real CTR rate: {real_ctr_rate:.4f}")
    
    # 8. 保存修复后的数据
    synthetic_df.to_csv(output_path, index=False)
    
    print(f"✅ Fixed synthetic data saved to {output_path}")
    
    return synthetic_df


def validate_fixed_data(fixed_data_path, real_data_path):
    """验证修复后的数据质量"""
    
    print("\n🔍 Validating fixed synthetic data...")
    
    # 加载数据
    fixed_df = pd.read_csv(fixed_data_path)
    real_df = pd.read_csv(real_data_path, nrows=len(fixed_df))
    
    # 比较基本统计
    print("\nBasic Statistics Comparison:")
    print(f"Real CTR rate: {real_df['Label'].mean():.4f}")
    print(f"Fixed synthetic CTR rate: {fixed_df['Label'].mean():.4f}")
    
    # 比较数值特征分布
    numeric_cols = [col for col in fixed_df.columns if col.startswith('I')]
    
    print(f"\nNumeric Feature Comparison (first 5 features):")
    for col in numeric_cols[:5]:
        if col in real_df.columns:
            real_mean = real_df[col].mean()
            synthetic_mean = fixed_df[col].mean()
            print(f"{col}: Real={real_mean:.4f}, Synthetic={synthetic_mean:.4f}")
    
    print("\n✅ Validation completed")
    print("\nNext steps:")
    print("1. Test this fixed synthetic data with your FM model")
    print("2. Expected AUC improvement: should be closer to real data performance")
    print("3. If still poor, consider training the dedicated CTR predictor")


def main():
    parser = argparse.ArgumentParser(description='Fix Synthetic Data CTR Labels')
    
    parser.add_argument('--synthetic_data', type=str, required=True,
                       help='Path to synthetic data CSV file')
    parser.add_argument('--real_data', type=str, required=True,
                       help='Path to real Criteo train.csv file')
    parser.add_argument('--output_path', type=str, required=True,
                       help='Output path for fixed synthetic data')
    parser.add_argument('--validate', action='store_true',
                       help='Run validation after fixing')
    
    args = parser.parse_args()
    
    print("🎯 Quick Fix for Synthetic Data CTR Labels")
    print("=" * 50)
    print("Strategy:")
    print("1. Train simple model on real data to learn CTR patterns")
    print("2. Apply this model to synthetic features")
    print("3. Generate realistic CTR labels")
    print("4. Adjust label distribution to match real data")
    print()
    
    try:
        # Fix the labels
        fixed_df = fix_synthetic_labels(
            args.synthetic_data, args.real_data, args.output_path
        )
        
        # Validate if requested
        if args.validate:
            validate_fixed_data(args.output_path, args.real_data)
        
        print("\n🎉 Synthetic data label fixing completed!")
        print(f"Fixed data saved to: {args.output_path}")
        
    except Exception as e:
        print(f"❌ Label fixing failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
