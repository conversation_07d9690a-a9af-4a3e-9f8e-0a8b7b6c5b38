#!/usr/bin/env python3
"""
使用训练好的改进GAN生成合成数据
"""

import os
import sys
import logging
import argparse
import json
import torch
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime

# Add project root to path
ROOT_DIR = Path(__file__).resolve().parent.parent
sys.path.append(str(ROOT_DIR))

# Import improved GAN modules
from src.gan.data_prep import GANDataProcessor
from src.gan.models import FixedImprovedGenerator, WeakDiscriminator
from src.gan.trainer import RefinedProgressiveTrainer, RefinedGANConfig, create_refined_dataloader

# Import existing utilities
from src.data_process.utils import seed_everything


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Generate synthetic CTR data using trained improved GAN')
    
    # 模型相关
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to trained GAN model checkpoint')
    parser.add_argument('--preprocessing_path', type=str, 
                       help='Path to preprocessing info pickle file (auto-detect if not provided)')
    
    # 生成参数
    parser.add_argument('--num_samples', type=int, default=100000,
                       help='Number of synthetic samples to generate')
    parser.add_argument('--batch_size', type=int, default=1000,
                       help='Batch size for generation')
    parser.add_argument('--output_path', type=str, required=True,
                       help='Output path for synthetic data CSV')
    
    # 生成配置
    parser.add_argument('--temperature', type=float, default=0.5,
                       help='Temperature for Gumbel-Softmax (0.5 for improved model)')
    parser.add_argument('--hard_sampling', action='store_true', default=True,
                       help='Use hard sampling for categorical features')
    parser.add_argument('--add_ctr_labels', action='store_true', default=True,
                       help='Use discriminator to predict CTR labels')
    
    # 其他
    parser.add_argument('--seed', type=int, default=2024,
                       help='Random seed')
    parser.add_argument('--device', type=str, default='auto',
                       choices=['auto', 'cpu', 'cuda'],
                       help='Device to use for generation')
    
    return parser.parse_args()


def setup_logging():
    """设置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)


def auto_detect_preprocessing_path(model_path):
    """自动检测预处理文件路径"""
    model_dir = os.path.dirname(model_path)
    
    # 可能的路径
    possible_paths = [
        os.path.join(model_dir, 'data', 'gan_preprocessing_info.pkl'),
        os.path.join(model_dir, 'gan_preprocessing_info.pkl'),
        os.path.join(os.path.dirname(model_dir), 'data', 'gan_preprocessing_info.pkl'),
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
            
    return None


def load_improved_models_and_processor(model_path, preprocessing_path, device, logger):
    """加载改进的模型和预处理器"""
    logger.info(f"Loading improved models from {model_path}")
    
    # 加载检查点
    checkpoint = torch.load(model_path, map_location=device)
    
    # 获取配置 - 处理不同的配置格式
    if 'config' in checkpoint:
        config = checkpoint['config']
        # 如果config是字典，转换为对象
        if isinstance(config, dict):
            config_obj = RefinedGANConfig()
            for key, value in config.items():
                if hasattr(config_obj, key):
                    setattr(config_obj, key, value)
            config = config_obj
    else:
        # 使用默认配置
        logger.warning("No config found in checkpoint, using default config")
        config = RefinedGANConfig()
    
    # 自动检测预处理路径
    if preprocessing_path is None:
        preprocessing_path = auto_detect_preprocessing_path(model_path)
        if preprocessing_path is None:
            raise FileNotFoundError("Could not find preprocessing info file")
        logger.info(f"Auto-detected preprocessing path: {preprocessing_path}")
    
    logger.info(f"Loading preprocessing info from {preprocessing_path}")
    
    # 重建预处理器
    processor = GANDataProcessor(
        dataset_name=getattr(config, 'dataset_name', 'Criteo'),
        data_dir="",  # 不需要原始数据路径
        output_dir=os.path.dirname(preprocessing_path)
    )
    
    # 加载预处理信息
    feature_info = processor.load_preprocessing_info()
    
    logger.info(f"Feature info: {feature_info}")
    
    # 重建改进的模型
    generator = FixedImprovedGenerator(
        noise_dim=getattr(config, 'noise_dim', 128),
        numeric_features=feature_info['numeric_features'],
        categorical_features=feature_info['categorical_features'],
        vocab_sizes=feature_info['vocab_sizes'],
        embedding_dim=getattr(config, 'embedding_dim', 16)
    )
    
    discriminator = WeakDiscriminator(
        numeric_features=feature_info['numeric_features'],
        categorical_features=feature_info['categorical_features'],
        vocab_sizes=feature_info['vocab_sizes'],
        embedding_dim=getattr(config, 'embedding_dim', 16)
    )
    
    # 加载模型权重
    generator.load_state_dict(checkpoint['generator_state_dict'])
    discriminator.load_state_dict(checkpoint['discriminator_state_dict'])
    
    # 移动到设备
    generator.to(device)
    discriminator.to(device)
    
    # 设置为评估模式
    generator.eval()
    discriminator.eval()
    
    logger.info("Improved models loaded successfully")
    logger.info(f"Generator parameters: {sum(p.numel() for p in generator.parameters()):,}")
    logger.info(f"Discriminator parameters: {sum(p.numel() for p in discriminator.parameters()):,}")
    
    return generator, discriminator, processor, config


def generate_improved_batch_data(generator, discriminator, batch_size, config, args, device):
    """使用改进的模型生成一批数据"""
    with torch.no_grad():
        # 生成噪声
        noise = torch.randn(batch_size, getattr(config, 'noise_dim', 128), device=device)
        
        # 通过改进的生成器
        gen_output = generator(
            noise, 
            temperature=args.temperature, 
            hard=args.hard_sampling
        )
        
        # 准备返回数据
        batch_data = {}
        
        # 数值特征
        if gen_output['numeric'] is not None:
            batch_data['numeric'] = gen_output['numeric'].cpu().numpy()
        
        # 类别特征 - 处理改进模型的输出格式
        if gen_output['categorical_probs'] is not None:
            cat_indices = []
            for probs in gen_output['categorical_probs']:
                if probs is not None:  # 检查是否为None
                    if args.hard_sampling:
                        # 硬采样：直接取argmax
                        indices = torch.argmax(probs, dim=-1).cpu().numpy()
                    else:
                        # 软采样：多项式采样
                        indices = torch.multinomial(probs, 1).squeeze(-1).cpu().numpy()
                    cat_indices.append(indices)
                    
            if cat_indices:
                batch_data['categorical'] = np.column_stack(cat_indices)
        
        # 生成CTR标签（如果需要）
        if args.add_ctr_labels:
            _, ctr_scores, _ = discriminator(
                numeric_data=gen_output['numeric'],
                categorical_embeddings=gen_output['categorical_embeddings']
            )
            
            # 将CTR分数转换为二进制标签
            ctr_probs = ctr_scores.cpu().numpy().flatten()
            ctr_labels = np.random.binomial(1, ctr_probs)
            batch_data['ctr_labels'] = ctr_labels
            batch_data['ctr_probs'] = ctr_probs
        
        return batch_data


def generate_improved_synthetic_data(generator, discriminator, processor, config, args, device, logger):
    """使用改进的模型生成完整的合成数据集"""
    logger.info(f"Generating {args.num_samples} synthetic samples with improved GAN")
    
    # 计算批次数
    num_batches = (args.num_samples + args.batch_size - 1) // args.batch_size
    
    all_batches = []
    
    for batch_idx in range(num_batches):
        # 计算当前批次大小
        current_batch_size = min(args.batch_size, args.num_samples - batch_idx * args.batch_size)
        
        # 生成批次数据
        batch_data = generate_improved_batch_data(
            generator, discriminator, current_batch_size, config, args, device
        )
        
        all_batches.append(batch_data)
        
        # 进度日志
        if (batch_idx + 1) % 100 == 0 or batch_idx + 1 == num_batches:
            generated_so_far = min((batch_idx + 1) * args.batch_size, args.num_samples)
            logger.info(f"Generated {generated_so_far} / {args.num_samples} samples")
    
    # 合并所有批次
    logger.info("Combining all batches")
    combined_data = {}
    
    # 合并数值特征
    if all_batches and 'numeric' in all_batches[0]:
        combined_data['numeric'] = np.vstack([batch['numeric'] for batch in all_batches])
    
    # 合并类别特征
    if all_batches and 'categorical' in all_batches[0]:
        combined_data['categorical'] = np.vstack([batch['categorical'] for batch in all_batches])
    
    # 合并CTR标签
    if all_batches and 'ctr_labels' in all_batches[0]:
        combined_data['ctr_labels'] = np.concatenate([batch['ctr_labels'] for batch in all_batches])
        combined_data['ctr_probs'] = np.concatenate([batch['ctr_probs'] for batch in all_batches])
    
    # 转换为DataFrame
    logger.info("Converting to DataFrame")
    df_data = {}
    
    # 数值特征
    if 'numeric' in combined_data:
        for i, col in enumerate(processor.numeric_features):
            df_data[col] = combined_data['numeric'][:, i]
    
    # 类别特征（索引形式）
    if 'categorical' in combined_data:
        for i, col in enumerate(processor.categorical_features):
            df_data[f"{col}_idx"] = combined_data['categorical'][:, i]
    
    # CTR标签
    if 'ctr_labels' in combined_data:
        # 根据数据集确定标签列名
        label_col = getattr(processor, 'label_col', 'Label')
        df_data[label_col] = combined_data['ctr_labels']
        df_data['ctr_prob'] = combined_data['ctr_probs']
    
    synthetic_df = pd.DataFrame(df_data)
    
    # 反处理到原始格式
    logger.info("Reverse preprocessing to original format")
    synthetic_df_original = processor.reverse_preprocessing(synthetic_df)
    
    # 数据统计
    logger.info("Improved GAN generation statistics:")
    logger.info(f"Generated samples: {len(synthetic_df_original)}")
    
    if 'numeric' in combined_data:
        logger.info(f"Numeric features: {combined_data['numeric'].shape[1]}")
        logger.info(f"Numeric range: [{combined_data['numeric'].min():.4f}, {combined_data['numeric'].max():.4f}]")
    
    if 'categorical' in combined_data:
        logger.info(f"Categorical features: {combined_data['categorical'].shape[1]}")
        for i, col in enumerate(processor.categorical_features):
            unique_vals = len(np.unique(combined_data['categorical'][:, i]))
            vocab_size = processor.vocab_info[col]['vocab_size']
            logger.info(f"  {col}: {unique_vals} unique values (vocab_size: {vocab_size})")
    
    if 'ctr_labels' in combined_data:
        ctr_rate = combined_data['ctr_labels'].mean()
        logger.info(f"CTR rate: {ctr_rate:.4f}")
    
    return synthetic_df_original


def validate_improved_synthetic_data(synthetic_df, processor, logger):
    """验证改进GAN生成的合成数据"""
    logger.info("Validating improved synthetic data")
    
    # 检查数据形状
    expected_cols = len(processor.numeric_features) + len(processor.categorical_features)
    if hasattr(processor, 'label_col'):
        expected_cols += 1  # 标签列
    
    logger.info(f"Data shape: {synthetic_df.shape}")
    logger.info(f"Expected columns: ~{expected_cols}")
    
    # 检查缺失值
    missing_counts = synthetic_df.isnull().sum()
    if missing_counts.sum() > 0:
        logger.warning(f"Found missing values: {missing_counts[missing_counts > 0].to_dict()}")
    else:
        logger.info("No missing values found")
    
    # 检查数值特征范围
    for col in processor.numeric_features:
        if col in synthetic_df.columns:
            min_val, max_val = synthetic_df[col].min(), synthetic_df[col].max()
            logger.info(f"Numeric feature {col}: [{min_val:.4f}, {max_val:.4f}]")
    
    # 检查类别特征多样性
    for col in processor.categorical_features:
        if col in synthetic_df.columns:
            unique_count = synthetic_df[col].nunique()
            total_count = len(synthetic_df)
            diversity_ratio = unique_count / total_count
            logger.info(f"Categorical feature {col}: {unique_count} unique values ({diversity_ratio:.3f} diversity)")
    
    logger.info("Improved validation completed")


def main():
    """主函数"""
    # 解析参数
    args = parse_args()
    
    # 设置种子
    seed_everything(args.seed)
    
    # 设置日志
    logger = setup_logging()
    logger.info("Starting improved synthetic data generation")
    logger.info(f"Arguments: {vars(args)}")
    
    # 设置设备
    if args.device == 'auto':
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    else:
        device = torch.device(args.device)
    
    logger.info(f"Using device: {device}")
    
    try:
        # 1. 加载改进的模型和预处理器
        generator, discriminator, processor, config = load_improved_models_and_processor(
            args.model_path, args.preprocessing_path, device, logger
        )
        
        # 2. 生成合成数据
        synthetic_df = generate_improved_synthetic_data(
            generator, discriminator, processor, config, args, device, logger
        )
        
        # 3. 验证数据
        validate_improved_synthetic_data(synthetic_df, processor, logger)
        
        # 4. 保存数据
        logger.info(f"Saving improved synthetic data to {args.output_path}")
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(args.output_path), exist_ok=True)
        
        # 保存CSV
        synthetic_df.to_csv(args.output_path, index=False)
        
        # 保存元数据
        metadata = {
            'generation_time': datetime.now().isoformat(),
            'num_samples': len(synthetic_df),
            'model_path': args.model_path,
            'preprocessing_path': args.preprocessing_path,
            'generation_args': vars(args),
            'model_config': vars(config) if hasattr(config, '__dict__') else str(config),
            'model_type': 'improved_gan',
            'generator_type': 'FixedImprovedGenerator',
            'discriminator_type': 'WeakDiscriminator'
        }
        
        metadata_path = args.output_path.replace('.csv', '_metadata.json')
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        logger.info("Improved generation completed successfully!")
        logger.info(f"Synthetic data: {args.output_path}")
        logger.info(f"Metadata: {metadata_path}")
        logger.info(f"Generated {len(synthetic_df)} high-quality samples")
        
    except Exception as e:
        logger.error(f"Improved generation failed with error: {e}")
        logger.error("Error details:", exc_info=True)
        raise


if __name__ == "__main__":
    main()