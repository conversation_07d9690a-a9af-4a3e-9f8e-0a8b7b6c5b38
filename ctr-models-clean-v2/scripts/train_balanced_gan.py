#!/usr/bin/env python3
"""
平衡的GAN训练脚本 - 专注于提高Fake_score到0.4-0.5范围
目标: Real_score ~0.6, Fake_score ~0.4-0.5
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """启动平衡的GAN训练"""
    
    # 设置路径
    script_dir = Path(__file__).parent
    improved_script = script_dir / "train_gan_improved.py"
    
    # 平衡训练参数 - 专注于提高Fake_score
    args = [
        sys.executable, str(improved_script),
        "--dataset_name", "Criteo",
        "--dataset_path", "/data/Criteo_x4",
        "--output_dir", "/data/balanced_gan",
        "--epochs", "30",
        "--batch_size", "512",
        "--generator_lr", "4e-4",      # 进一步提高生成器学习率
        "--discriminator_lr", "4e-5",  # 进一步降低判别器学习率 (10:1 ratio)
        "--initial_temperature", "3.5", # 更高的初始温度
        "--min_temperature", "2.0",     # 更高的最小温度
        "--temperature_decay", "0.999", # 更慢的温度衰减
        "--max_samples", "500000",
        "--eval_interval", "2",         # 更频繁的评估
        "--save_interval", "2",         # 更频繁的保存
        "--seed", "2024"
    ]
    
    print("🎯 启动平衡的GAN训练 - 专注于提高Fake_score")
    print("=" * 60)
    print("📊 关键改进:")
    print("  - 生成器学习率: 3e-4 (提高50%)")
    print("  - 判别器学习率: 5e-5 (降低50%)")
    print("  - 学习率比例 G:D = 6:1 (vs 之前的2:1)")
    print("  - 更强的判别器正则化 (lambda_d_reg: 0.15)")
    print("  - 生成器训练频率: 3步/判别器1步")
    print("  - 更高的温度控制 (3.5 -> 2.0)")
    print()
    print("🎯 目标指标:")
    print("  - Real_score: 0.55-0.65 (当前 ~0.82)")
    print("  - Fake_score: 0.40-0.50 (当前 ~0.17)")
    print("  - Score_gap: <0.25 (当前 ~0.65)")
    print("  - 保持数值特征质量 (std >0.08)")
    print()
    print("🔍 监控要点:")
    print("  - Fake_score应该从0.17逐渐上升到0.4+")
    print("  - Real_score应该从0.82逐渐下降到0.6左右")
    print("  - 如果Fake_score超过0.6，说明生成器过强")
    print("  - 如果Real_score低于0.5，说明判别器过弱")
    print()
    
    try:
        # 运行训练
        print("🚀 开始训练...")
        result = subprocess.run(args, check=True)
        print("✅ 平衡训练成功完成!")
        
        print()
        print("📈 预期结果:")
        print("  - 更平衡的Real/Fake分数")
        print("  - 更稳定的训练动态")
        print("  - 更高质量的合成数据")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 训练失败: {e}")
        return 1
    except KeyboardInterrupt:
        print("⏹️ 训练被用户中断")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
