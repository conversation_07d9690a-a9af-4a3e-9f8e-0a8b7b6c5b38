#!/usr/bin/env python3
"""
H5 Dataset Sampling Script for CTR Models

This script samples large .h5 datasets to create smaller versions suitable for:
- Code testing and validation
- GitHub repository sharing
- Faster development iterations

The .h5 format is much more efficient for sampling than CSV files.

Usage:
    python sample_h5_dataset.py --dataset_path /data/Criteo_x4 --output_path ./data/Criteo_x4_sample --sample_size 10000
"""

import os
import h5py
import numpy as np
import argparse
import logging
from pathlib import Path
import shutil

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def sample_h5_file(input_path, output_path, sample_size=10000, random_seed=42):
    """
    Sample an H5 file by taking a random subset of rows
    
    Args:
        input_path (str): Path to input H5 file
        output_path (str): Path to output sampled H5 file
        sample_size (int): Number of samples to extract
        random_seed (int): Random seed for reproducibility
    """
    logger.info(f"Sampling {input_path} with {sample_size} samples")
    
    # Set random seed for reproducibility
    np.random.seed(random_seed)
    
    with h5py.File(input_path, 'r') as input_file:
        # Get the dataset (assuming it's stored under 'data' key)
        data_key = 'data'
        if data_key not in input_file:
            # Try to find the data key
            keys = list(input_file.keys())
            if len(keys) == 1:
                data_key = keys[0]
            else:
                raise ValueError(f"Multiple keys found in H5 file: {keys}. Please specify the correct data key.")
        
        original_data = input_file[data_key]
        total_size = original_data.shape[0]
        
        logger.info(f"Original dataset size: {total_size}")
        logger.info(f"Original dataset shape: {original_data.shape}")
        
        # Determine actual sample size
        actual_sample_size = min(sample_size, total_size)
        
        # Generate random indices for sampling
        if actual_sample_size < total_size:
            indices = np.random.choice(total_size, size=actual_sample_size, replace=False)
            indices = np.sort(indices)  # Sort for better I/O performance
        else:
            indices = np.arange(total_size)
        
        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Create new H5 file with sampled data
        with h5py.File(output_path, 'w') as output_file:
            # Sample the data
            sampled_data = original_data[indices]
            
            # Create dataset in output file
            output_file.create_dataset(data_key, data=sampled_data, compression='gzip')
            
            # Copy attributes if they exist
            for attr_name, attr_value in original_data.attrs.items():
                output_file[data_key].attrs[attr_name] = attr_value
    
    logger.info(f"Saved {actual_sample_size} samples to {output_path}")
    return actual_sample_size

def sample_h5_dataset(dataset_path, output_path, sample_size=10000, random_seed=42):
    """
    Sample a complete H5 dataset (train/valid/test splits) maintaining the exact structure
    needed for the CTR training pipeline.

    Args:
        dataset_path (str): Path to dataset directory containing .h5 files
        output_path (str): Path to output directory
        sample_size (int): Number of samples per file
        random_seed (int): Random seed for reproducibility
    """
    dataset_path = Path(dataset_path)
    output_path = Path(output_path)

    # Create output directory
    output_path.mkdir(parents=True, exist_ok=True)

    # Look for specific H5 files in the expected order
    expected_files = ['train.h5', 'valid.h5', 'test.h5']
    h5_files = []
    for filename in expected_files:
        h5_path = dataset_path / filename
        if h5_path.exists():
            h5_files.append(h5_path)
        else:
            logger.warning(f"Expected file {filename} not found in {dataset_path}")

    if not h5_files:
        logger.warning(f"No expected .h5 files found in {dataset_path}")
        return

    total_samples = 0
    sample_info = {}

    for h5_file in h5_files:
        output_file = output_path / h5_file.name
        samples = sample_h5_file(str(h5_file), str(output_file), sample_size, random_seed)
        total_samples += samples
        sample_info[h5_file.name] = samples

    # Copy essential files for the training pipeline
    essential_files = ['feature_encoder.pkl', 'feature_map.json']
    for filename in essential_files:
        input_file = dataset_path / filename
        output_file = output_path / filename
        if input_file.exists():
            shutil.copy2(str(input_file), str(output_file))
            logger.info(f"Copied {filename}")
        else:
            logger.warning(f"Essential file {filename} not found!")

    # Create dummy CSV files that point to H5 files (for compatibility)
    csv_files = ['train.csv', 'valid.csv', 'test.csv']
    for csv_file in csv_files:
        csv_path = output_path / csv_file
        # Create a minimal CSV header that matches Criteo format
        header = "Label,I1,I2,I3,I4,I5,I6,I7,I8,I9,I10,I11,I12,I13,C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11,C12,C13,C14,C15,C16,C17,C18,C19,C20,C21,C22,C23,C24,C25,C26\n"
        with open(csv_path, 'w') as f:
            f.write(header)
            f.write("# This is a placeholder CSV file. The actual data is in the corresponding .h5 file.\n")
        logger.info(f"Created placeholder {csv_file}")

    # Create a comprehensive README
    readme_content = f"""# Sampled Criteo Dataset for CTR Prediction

This is a sampled version of the Criteo dataset for testing and development purposes.

## Dataset Structure
This follows the exact structure expected by the CTR training pipeline:

### Data Files
- `train.h5`: {sample_info.get('train.h5', 0):,} training samples
- `valid.h5`: {sample_info.get('valid.h5', 0):,} validation samples
- `test.h5`: {sample_info.get('test.h5', 0):,} test samples
- `train.csv`, `valid.csv`, `test.csv`: Placeholder CSV files (data is in .h5 files)

### Metadata Files
- `feature_encoder.pkl`: Feature encoders for preprocessing
- `feature_map.json`: Feature specifications and vocabulary sizes

## Sampling Details
- Original dataset: {dataset_path}
- Sample size per file: {sample_size:,}
- Random seed: {random_seed}
- Total samples: {total_samples:,}

## Features
- **Numerical features**: I1-I13 (13 features)
- **Categorical features**: C1-C26 (26 features)
- **Label**: Binary classification target (0/1)
- **Total dimensions**: 40 (39 features + 1 label)

## Usage with Training Script

```bash
# Train a model using the sampled dataset
python scripts/train.py \\
    --dataset_name Criteo \\
    --dataset_path ./data/Criteo_x4_sample \\
    --model_name FM \\
    --epochs 10 \\
    --batch_size 1000
```

## Data Loading
The training pipeline automatically:
1. Looks for CSV files (train.csv, valid.csv, test.csv)
2. Checks for corresponding .h5 files (train.h5, valid.h5, test.h5)
3. Loads .h5 files if available (much faster)
4. Uses feature_encoder.pkl and feature_map.json for preprocessing

## Important Notes
- Results on this sampled dataset may not be representative of full dataset performance
- The sample maintains the original data distribution and feature encoding
- All preprocessing and feature engineering is preserved from the original dataset
- Compatible with all existing model training scripts
"""

    with open(output_path / 'README.md', 'w') as f:
        f.write(readme_content)

    logger.info(f"Criteo dataset sampling completed!")
    logger.info(f"Total samples: {total_samples:,}")
    logger.info(f"Sampled dataset saved to: {output_path}")
    logger.info(f"Ready for training with: python scripts/train.py --dataset_path {output_path}")

def inspect_h5_dataset(dataset_path):
    """
    Inspect H5 files in a dataset directory to understand their structure
    """
    dataset_path = Path(dataset_path)
    h5_files = list(dataset_path.glob('*.h5'))
    
    if not h5_files:
        logger.warning(f"No .h5 files found in {dataset_path}")
        return
    
    logger.info(f"Found {len(h5_files)} H5 files:")
    
    for h5_file in h5_files:
        logger.info(f"\n--- {h5_file.name} ---")
        try:
            with h5py.File(h5_file, 'r') as f:
                for key in f.keys():
                    dataset = f[key]
                    logger.info(f"  Key: {key}")
                    logger.info(f"  Shape: {dataset.shape}")
                    logger.info(f"  Dtype: {dataset.dtype}")
                    logger.info(f"  Size: {dataset.size * dataset.dtype.itemsize / (1024**2):.2f} MB")
        except Exception as e:
            logger.error(f"  Error reading file: {e}")

def main():
    parser = argparse.ArgumentParser(description='Sample large H5 datasets for testing and development')
    parser.add_argument('--dataset_path', type=str, required=True,
                       help='Path to the original dataset directory containing .h5 files')
    parser.add_argument('--output_path', type=str, required=False,
                       help='Path to save the sampled dataset (required unless using --inspect)')
    parser.add_argument('--sample_size', type=int, default=10000,
                       help='Number of samples per file (default: 10000)')
    parser.add_argument('--random_seed', type=int, default=42,
                       help='Random seed for reproducibility (default: 42)')
    parser.add_argument('--inspect', action='store_true',
                       help='Only inspect the dataset structure without sampling')

    args = parser.parse_args()

    # Validate inputs
    if not os.path.exists(args.dataset_path):
        raise ValueError(f"Dataset path does not exist: {args.dataset_path}")

    if not args.inspect and not args.output_path:
        raise ValueError("--output_path is required unless using --inspect mode")

    if args.sample_size <= 0:
        raise ValueError(f"Sample size must be positive, got: {args.sample_size}")

    if args.inspect:
        # Just inspect the dataset
        inspect_h5_dataset(args.dataset_path)
    else:
        # Run sampling
        sample_h5_dataset(
            dataset_path=args.dataset_path,
            output_path=args.output_path,
            sample_size=args.sample_size,
            random_seed=args.random_seed
        )

if __name__ == "__main__":
    main()
