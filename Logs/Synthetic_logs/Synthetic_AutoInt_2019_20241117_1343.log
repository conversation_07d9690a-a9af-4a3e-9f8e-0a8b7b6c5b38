2024-11-17 13:43:01 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='AutoInt', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-17 13:43:01 INFO Start process Synthetic !
2024-11-17 13:43:01 INFO Loading Synthetic dataset
2024-11-17 13:43:01 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 13:43:02 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 13:43:03 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 13:43:03 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 13:43:03 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 13:43:03 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 13:43:03 INFO Loading data done
2024-11-17 13:43:04 INFO Model: AutoInt(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (self_attention): Sequential(
    (0): MultiHeadSelfAttention(
      (W_q): Linear(in_features=16, out_features=64, bias=False)
      (W_k): Linear(in_features=16, out_features=64, bias=False)
      (W_v): Linear(in_features=16, out_features=64, bias=False)
      (W_res): Linear(in_features=16, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (1): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (2): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (3): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (4): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
  )
  (fc): Linear(in_features=640, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-17 13:43:04 INFO Model parameters: 61889
2024-11-17 13:43:04 INFO Start training model
2024-11-17 13:43:04 INFO Start training: 2570 batches/epoch
2024-11-17 13:43:04 INFO ************ Epoch=1 start ************
2024-11-17 13:47:10 INFO [Metrics] AUC-ROC: 0.906215 - AUC-PR: 0.774845 - ACC: 0.841386 - Precision: 0.671125 - Recall: 0.716799 - F1: 0.693211 - MCC: 0.586989 - Logloss: 0.315599 - MSE: 0.102046 - RMSE: 0.319447 - COPC: 1.012878 - KLD: 0.708327
2024-11-17 13:47:10 INFO Save best model: monitor(max): 0.590616
2024-11-17 13:47:10 INFO --- 2570/2570 batches finished ---
2024-11-17 13:47:10 INFO Train loss: 0.321668
2024-11-17 13:47:10 INFO ************ Epoch=1 end ************
2024-11-17 13:51:17 INFO [Metrics] AUC-ROC: 0.906281 - AUC-PR: 0.774988 - ACC: 0.841350 - Precision: 0.666650 - Recall: 0.730852 - F1: 0.697276 - MCC: 0.591237 - Logloss: 0.315291 - MSE: 0.101959 - RMSE: 0.319310 - COPC: 0.996448 - KLD: 0.707586
2024-11-17 13:51:17 INFO Save best model: monitor(max): 0.590989
2024-11-17 13:51:17 INFO --- 2570/2570 batches finished ---
2024-11-17 13:51:17 INFO Train loss: 0.315482
2024-11-17 13:51:17 INFO ************ Epoch=2 end ************
2024-11-17 13:55:24 INFO [Metrics] AUC-ROC: 0.906336 - AUC-PR: 0.775110 - ACC: 0.841404 - Precision: 0.673602 - Recall: 0.709323 - F1: 0.691001 - MCC: 0.584776 - Logloss: 0.315226 - MSE: 0.101940 - RMSE: 0.319281 - COPC: 1.004786 - KLD: 0.707347
2024-11-17 13:55:24 INFO Save best model: monitor(max): 0.591110
2024-11-17 13:55:24 INFO --- 2570/2570 batches finished ---
2024-11-17 13:55:24 INFO Train loss: 0.315277
2024-11-17 13:55:24 INFO ************ Epoch=3 end ************
2024-11-17 13:59:30 INFO [Metrics] AUC-ROC: 0.906368 - AUC-PR: 0.775145 - ACC: 0.841465 - Precision: 0.671132 - Recall: 0.717396 - F1: 0.693493 - MCC: 0.587328 - Logloss: 0.315452 - MSE: 0.101968 - RMSE: 0.319325 - COPC: 0.990786 - KLD: 0.708060
2024-11-17 13:59:30 INFO Monitor(max) STOP: 0.590915 !
2024-11-17 13:59:30 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 13:59:30 INFO --- 2570/2570 batches finished ---
2024-11-17 13:59:31 INFO Train loss: 0.315184
2024-11-17 13:59:31 INFO ************ Epoch=4 end ************
2024-11-17 14:03:38 INFO [Metrics] AUC-ROC: 0.906420 - AUC-PR: 0.775198 - ACC: 0.841479 - Precision: 0.671527 - Recall: 0.716276 - F1: 0.693180 - MCC: 0.587017 - Logloss: 0.315063 - MSE: 0.101895 - RMSE: 0.319210 - COPC: 1.007925 - KLD: 0.706866
2024-11-17 14:03:38 INFO Save best model: monitor(max): 0.591358
2024-11-17 14:03:38 INFO --- 2570/2570 batches finished ---
2024-11-17 14:03:38 INFO Train loss: 0.314907
2024-11-17 14:03:38 INFO ************ Epoch=5 end ************
2024-11-17 14:07:46 INFO [Metrics] AUC-ROC: 0.906419 - AUC-PR: 0.775185 - ACC: 0.841468 - Precision: 0.670368 - Recall: 0.719823 - F1: 0.694216 - MCC: 0.588073 - Logloss: 0.315035 - MSE: 0.101894 - RMSE: 0.319208 - COPC: 0.995045 - KLD: 0.706785
2024-11-17 14:07:46 INFO Save best model: monitor(max): 0.591384
2024-11-17 14:07:46 INFO --- 2570/2570 batches finished ---
2024-11-17 14:07:46 INFO Train loss: 0.314889
2024-11-17 14:07:46 INFO ************ Epoch=6 end ************
2024-11-17 14:12:08 INFO [Metrics] AUC-ROC: 0.906412 - AUC-PR: 0.775204 - ACC: 0.841425 - Precision: 0.670546 - Recall: 0.718916 - F1: 0.693890 - MCC: 0.587709 - Logloss: 0.315044 - MSE: 0.101893 - RMSE: 0.319207 - COPC: 1.005408 - KLD: 0.706827
2024-11-17 14:12:08 INFO Monitor(max) STOP: 0.591368 !
2024-11-17 14:12:08 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 14:12:08 INFO --- 2570/2570 batches finished ---
2024-11-17 14:12:08 INFO Train loss: 0.314873
2024-11-17 14:12:08 INFO ************ Epoch=7 end ************
2024-11-17 14:17:27 INFO [Metrics] AUC-ROC: 0.906420 - AUC-PR: 0.775210 - ACC: 0.841440 - Precision: 0.670092 - Recall: 0.720470 - F1: 0.694368 - MCC: 0.588213 - Logloss: 0.315023 - MSE: 0.101890 - RMSE: 0.319202 - COPC: 0.998938 - KLD: 0.706762
2024-11-17 14:17:27 INFO Save best model: monitor(max): 0.591397
2024-11-17 14:17:27 INFO --- 2570/2570 batches finished ---
2024-11-17 14:17:27 INFO Train loss: 0.314831
2024-11-17 14:17:27 INFO ************ Epoch=8 end ************
2024-11-17 14:24:05 INFO [Metrics] AUC-ROC: 0.906421 - AUC-PR: 0.775207 - ACC: 0.841433 - Precision: 0.670534 - Recall: 0.719019 - F1: 0.693931 - MCC: 0.587757 - Logloss: 0.315023 - MSE: 0.101890 - RMSE: 0.319202 - COPC: 0.999523 - KLD: 0.706766
2024-11-17 14:24:05 INFO Monitor(max) STOP: 0.591397 !
2024-11-17 14:24:05 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 14:24:05 INFO --- 2570/2570 batches finished ---
2024-11-17 14:24:05 INFO Train loss: 0.314826
2024-11-17 14:24:05 INFO ************ Epoch=9 end ************
2024-11-17 14:28:25 INFO [Metrics] AUC-ROC: 0.906421 - AUC-PR: 0.775208 - ACC: 0.841451 - Precision: 0.670705 - Recall: 0.718626 - F1: 0.693839 - MCC: 0.587674 - Logloss: 0.315023 - MSE: 0.101890 - RMSE: 0.319202 - COPC: 0.999779 - KLD: 0.706763
2024-11-17 14:28:25 INFO Save best model: monitor(max): 0.591398
2024-11-17 14:28:25 INFO --- 2570/2570 batches finished ---
2024-11-17 14:28:25 INFO Train loss: 0.314821
2024-11-17 14:28:25 INFO ************ Epoch=10 end ************
2024-11-17 14:33:33 INFO [Metrics] AUC-ROC: 0.906421 - AUC-PR: 0.775208 - ACC: 0.841450 - Precision: 0.670766 - Recall: 0.718425 - F1: 0.693778 - MCC: 0.587610 - Logloss: 0.315023 - MSE: 0.101890 - RMSE: 0.319202 - COPC: 1.000017 - KLD: 0.706763
2024-11-17 14:33:33 INFO Monitor(max) STOP: 0.591398 !
2024-11-17 14:33:33 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 14:33:33 INFO --- 2570/2570 batches finished ---
2024-11-17 14:33:33 INFO Train loss: 0.314818
2024-11-17 14:33:33 INFO ************ Epoch=11 end ************
2024-11-17 14:37:33 INFO [Metrics] AUC-ROC: 0.906421 - AUC-PR: 0.775208 - ACC: 0.841449 - Precision: 0.670732 - Recall: 0.718523 - F1: 0.693805 - MCC: 0.587638 - Logloss: 0.315023 - MSE: 0.101890 - RMSE: 0.319202 - COPC: 0.999486 - KLD: 0.706764
2024-11-17 14:37:33 INFO Monitor(max) STOP: 0.591398 !
2024-11-17 14:37:33 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 14:37:33 INFO Early stopping at epoch=12
2024-11-17 14:37:33 INFO --- 2570/2570 batches finished ---
2024-11-17 14:37:33 INFO Train loss: 0.314818
2024-11-17 14:37:33 INFO Training finished.
2024-11-17 14:37:33 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AutoInt/Synthetic/AutoInt_model_seed2019.ckpt
2024-11-17 14:37:33 INFO Start evaluate model
2024-11-17 14:38:09 INFO [Metrics] AUC-ROC: 0.906421 - AUC-PR: 0.775208 - ACC: 0.841451 - Precision: 0.670705 - Recall: 0.718626 - F1: 0.693839 - MCC: 0.587674 - Logloss: 0.315023 - MSE: 0.101890 - RMSE: 0.319202 - COPC: 0.999779 - KLD: 0.706763
2024-11-17 14:38:09 INFO Start testing model
2024-11-17 14:38:45 INFO [Metrics] AUC-ROC: 0.906360 - AUC-PR: 0.775495 - ACC: 0.841460 - Precision: 0.670964 - Recall: 0.717888 - F1: 0.693633 - MCC: 0.587468 - Logloss: 0.314909 - MSE: 0.101830 - RMSE: 0.319108 - COPC: 1.000567 - KLD: 0.706814
