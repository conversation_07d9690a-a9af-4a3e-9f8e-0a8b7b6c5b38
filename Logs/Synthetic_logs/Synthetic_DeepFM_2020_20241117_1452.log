2024-11-17 14:52:35 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='DeepFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-17 14:52:35 INFO Start process Synthetic !
2024-11-17 14:52:35 INFO Loading Synthetic dataset
2024-11-17 14:52:35 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 14:52:36 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 14:52:36 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 14:52:37 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 14:52:37 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 14:52:37 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 14:52:37 INFO Loading data done
2024-11-17 14:52:38 INFO Model: DeepFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (user_age): Embedding(22, 16)
        (item_price): Embedding(43, 16)
        (user_gender): Embedding(4, 16)
        (user_region): Embedding(334, 16)
        (device_type): Embedding(5, 16)
        (ad_category): Embedding(45, 16)
        (ad_placement): Embedding(8, 16)
        (hour_of_day): Embedding(25, 16)
        (day_of_week): Embedding(8, 16)
        (scene_type): Embedding(6, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (fm_layer): FM_Layer_v2(
    (inner_product_layer): InnerProductLayer_v2()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (user_age): Embedding(22, 1, padding_idx=21)
          (item_price): Embedding(43, 1, padding_idx=42)
          (user_gender): Embedding(4, 1, padding_idx=3)
          (user_region): Embedding(334, 1, padding_idx=333)
          (device_type): Embedding(5, 1, padding_idx=4)
          (ad_category): Embedding(45, 1, padding_idx=44)
          (ad_placement): Embedding(8, 1, padding_idx=7)
          (hour_of_day): Embedding(25, 1, padding_idx=24)
          (day_of_week): Embedding(8, 1, padding_idx=7)
          (scene_type): Embedding(6, 1, padding_idx=5)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=160, out_features=1000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=1000, out_features=1000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=1000, out_features=1000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=1000, out_features=1000, bias=True)
      (7): ReLU()
      (8): Linear(in_features=1000, out_features=1000, bias=True)
      (9): ReLU()
      (10): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-17 14:52:38 INFO Model parameters: 4174501
2024-11-17 14:52:38 INFO Start training model
2024-11-17 14:52:38 INFO Start training: 2570 batches/epoch
2024-11-17 14:52:38 INFO ************ Epoch=1 start ************
2024-11-17 14:57:29 INFO [Metrics] AUC-ROC: 0.906296 - AUC-PR: 0.774995 - ACC: 0.841288 - Precision: 0.676516 - Recall: 0.699740 - F1: 0.687932 - MCC: 0.581705 - Logloss: 0.315379 - MSE: 0.101966 - RMSE: 0.319321 - COPC: 1.003440 - KLD: 0.707860
2024-11-17 14:57:29 INFO Save best model: monitor(max): 0.590917
2024-11-17 14:57:29 INFO --- 2570/2570 batches finished ---
2024-11-17 14:57:29 INFO Train loss: 0.317885
2024-11-17 14:57:29 INFO ************ Epoch=1 end ************
2024-11-17 15:01:34 INFO [Metrics] AUC-ROC: 0.906293 - AUC-PR: 0.774941 - ACC: 0.841382 - Precision: 0.670975 - Recall: 0.717236 - F1: 0.693335 - MCC: 0.587113 - Logloss: 0.315224 - MSE: 0.101951 - RMSE: 0.319298 - COPC: 0.990492 - KLD: 0.707273
2024-11-17 15:01:34 INFO Save best model: monitor(max): 0.591069
2024-11-17 15:01:34 INFO --- 2570/2570 batches finished ---
2024-11-17 15:01:35 INFO Train loss: 0.315269
2024-11-17 15:01:35 INFO ************ Epoch=2 end ************
2024-11-17 15:05:57 INFO [Metrics] AUC-ROC: 0.906329 - AUC-PR: 0.775009 - ACC: 0.841144 - Precision: 0.677633 - Recall: 0.695392 - F1: 0.686398 - MCC: 0.580140 - Logloss: 0.315501 - MSE: 0.101969 - RMSE: 0.319327 - COPC: 0.982557 - KLD: 0.708240
2024-11-17 15:05:57 INFO Monitor(max) STOP: 0.590828 !
2024-11-17 15:05:57 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 15:05:57 INFO --- 2570/2570 batches finished ---
2024-11-17 15:05:57 INFO Train loss: 0.315138
2024-11-17 15:05:57 INFO ************ Epoch=3 end ************
2024-11-17 15:10:17 INFO [Metrics] AUC-ROC: 0.906375 - AUC-PR: 0.775117 - ACC: 0.841462 - Precision: 0.671982 - Recall: 0.714733 - F1: 0.692699 - MCC: 0.586517 - Logloss: 0.315045 - MSE: 0.101900 - RMSE: 0.319217 - COPC: 1.000042 - KLD: 0.706810
2024-11-17 15:10:17 INFO Save best model: monitor(max): 0.591330
2024-11-17 15:10:17 INFO --- 2570/2570 batches finished ---
2024-11-17 15:10:17 INFO Train loss: 0.314851
2024-11-17 15:10:17 INFO ************ Epoch=4 end ************
2024-11-17 15:14:26 INFO [Metrics] AUC-ROC: 0.906371 - AUC-PR: 0.775130 - ACC: 0.841410 - Precision: 0.672872 - Recall: 0.711587 - F1: 0.691689 - MCC: 0.585465 - Logloss: 0.315057 - MSE: 0.101902 - RMSE: 0.319222 - COPC: 1.001322 - KLD: 0.706849
2024-11-17 15:14:26 INFO Monitor(max) STOP: 0.591314 !
2024-11-17 15:14:26 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 15:14:26 INFO --- 2570/2570 batches finished ---
2024-11-17 15:14:26 INFO Train loss: 0.314813
2024-11-17 15:14:26 INFO ************ Epoch=5 end ************
2024-11-17 15:18:12 INFO [Metrics] AUC-ROC: 0.906370 - AUC-PR: 0.775112 - ACC: 0.841441 - Precision: 0.671868 - Recall: 0.714925 - F1: 0.692728 - MCC: 0.586533 - Logloss: 0.315049 - MSE: 0.101900 - RMSE: 0.319218 - COPC: 0.998813 - KLD: 0.706820
2024-11-17 15:18:12 INFO Monitor(max) STOP: 0.591321 !
2024-11-17 15:18:12 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 15:18:12 INFO Early stopping at epoch=6
2024-11-17 15:18:12 INFO --- 2570/2570 batches finished ---
2024-11-17 15:18:12 INFO Train loss: 0.314756
2024-11-17 15:18:12 INFO Training finished.
2024-11-17 15:18:12 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/DeepFM/Synthetic/DeepFM_model_seed2020.ckpt
2024-11-17 15:18:12 INFO Start evaluate model
2024-11-17 15:18:45 INFO [Metrics] AUC-ROC: 0.906375 - AUC-PR: 0.775117 - ACC: 0.841462 - Precision: 0.671982 - Recall: 0.714733 - F1: 0.692699 - MCC: 0.586517 - Logloss: 0.315045 - MSE: 0.101900 - RMSE: 0.319217 - COPC: 1.000042 - KLD: 0.706810
2024-11-17 15:18:46 INFO Start testing model
2024-11-17 15:19:19 INFO [Metrics] AUC-ROC: 0.906360 - AUC-PR: 0.775512 - ACC: 0.841336 - Precision: 0.671988 - Recall: 0.713732 - F1: 0.692231 - MCC: 0.585962 - Logloss: 0.314917 - MSE: 0.101832 - RMSE: 0.319111 - COPC: 1.000813 - KLD: 0.706837
