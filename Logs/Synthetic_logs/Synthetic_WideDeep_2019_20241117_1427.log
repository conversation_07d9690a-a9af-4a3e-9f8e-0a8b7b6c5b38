2024-11-17 14:27:05 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='WideDeep', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-17 14:27:05 INFO Start process Synthetic !
2024-11-17 14:27:05 INFO Loading Synthetic dataset
2024-11-17 14:27:05 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 14:27:06 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 14:27:06 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 14:27:07 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 14:27:07 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 14:27:07 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 14:27:07 INFO Loading data done
2024-11-17 14:27:08 INFO Model: WideDeep(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (user_age): Embedding(22, 16)
        (item_price): Embedding(43, 16)
        (user_gender): Embedding(4, 16)
        (user_region): Embedding(334, 16)
        (device_type): Embedding(5, 16)
        (ad_category): Embedding(45, 16)
        (ad_placement): Embedding(8, 16)
        (hour_of_day): Embedding(25, 16)
        (day_of_week): Embedding(8, 16)
        (scene_type): Embedding(6, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (user_age): Embedding(22, 1, padding_idx=21)
        (item_price): Embedding(43, 1, padding_idx=42)
        (user_gender): Embedding(4, 1, padding_idx=3)
        (user_region): Embedding(334, 1, padding_idx=333)
        (device_type): Embedding(5, 1, padding_idx=4)
        (ad_category): Embedding(45, 1, padding_idx=44)
        (ad_placement): Embedding(8, 1, padding_idx=7)
        (hour_of_day): Embedding(25, 1, padding_idx=24)
        (day_of_week): Embedding(8, 1, padding_idx=7)
        (scene_type): Embedding(6, 1, padding_idx=5)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=160, out_features=1000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=1000, out_features=1000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=1000, out_features=1000, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.2, inplace=False)
      (9): Linear(in_features=1000, out_features=1000, bias=True)
      (10): ReLU()
      (11): Dropout(p=0.2, inplace=False)
      (12): Linear(in_features=1000, out_features=1000, bias=True)
      (13): ReLU()
      (14): Dropout(p=0.2, inplace=False)
      (15): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-17 14:27:08 INFO Model parameters: 4174501
2024-11-17 14:27:08 INFO Start training model
2024-11-17 14:27:08 INFO Start training: 2570 batches/epoch
2024-11-17 14:27:08 INFO ************ Epoch=1 start ************
2024-11-17 14:32:06 INFO [Metrics] AUC-ROC: 0.906232 - AUC-PR: 0.775000 - ACC: 0.841201 - Precision: 0.674777 - Recall: 0.704217 - F1: 0.689183 - MCC: 0.582846 - Logloss: 0.315398 - MSE: 0.101975 - RMSE: 0.319336 - COPC: 0.990327 - KLD: 0.707828
2024-11-17 14:32:06 INFO Save best model: monitor(max): 0.590834
2024-11-17 14:32:06 INFO --- 2570/2570 batches finished ---
2024-11-17 14:32:06 INFO Train loss: 0.319053
2024-11-17 14:32:06 INFO ************ Epoch=1 end ************
2024-11-17 14:36:12 INFO [Metrics] AUC-ROC: 0.906340 - AUC-PR: 0.774965 - ACC: 0.841474 - Precision: 0.672143 - Recall: 0.714331 - F1: 0.692595 - MCC: 0.586420 - Logloss: 0.315197 - MSE: 0.101927 - RMSE: 0.319260 - COPC: 0.997669 - KLD: 0.707208
2024-11-17 14:36:12 INFO Save best model: monitor(max): 0.591142
2024-11-17 14:36:12 INFO --- 2570/2570 batches finished ---
2024-11-17 14:36:12 INFO Train loss: 0.315584
2024-11-17 14:36:12 INFO ************ Epoch=2 end ************
2024-11-17 14:40:05 INFO [Metrics] AUC-ROC: 0.906337 - AUC-PR: 0.775030 - ACC: 0.841378 - Precision: 0.673170 - Recall: 0.710430 - F1: 0.691298 - MCC: 0.585054 - Logloss: 0.315134 - MSE: 0.101924 - RMSE: 0.319255 - COPC: 1.011270 - KLD: 0.706967
2024-11-17 14:40:05 INFO Save best model: monitor(max): 0.591203
2024-11-17 14:40:05 INFO --- 2570/2570 batches finished ---
2024-11-17 14:40:05 INFO Train loss: 0.315394
2024-11-17 14:40:05 INFO ************ Epoch=3 end ************
2024-11-17 14:44:11 INFO [Metrics] AUC-ROC: 0.906355 - AUC-PR: 0.775032 - ACC: 0.841399 - Precision: 0.674151 - Recall: 0.707624 - F1: 0.690482 - MCC: 0.584260 - Logloss: 0.315180 - MSE: 0.101921 - RMSE: 0.319250 - COPC: 0.994570 - KLD: 0.707261
2024-11-17 14:44:11 INFO Monitor(max) STOP: 0.591175 !
2024-11-17 14:44:11 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 14:44:11 INFO --- 2570/2570 batches finished ---
2024-11-17 14:44:11 INFO Train loss: 0.315341
2024-11-17 14:44:11 INFO ************ Epoch=4 end ************
2024-11-17 14:48:24 INFO [Metrics] AUC-ROC: 0.906402 - AUC-PR: 0.775159 - ACC: 0.841509 - Precision: 0.672068 - Recall: 0.714837 - F1: 0.692793 - MCC: 0.586643 - Logloss: 0.315026 - MSE: 0.101891 - RMSE: 0.319203 - COPC: 1.000080 - KLD: 0.706769
2024-11-17 14:48:24 INFO Save best model: monitor(max): 0.591376
2024-11-17 14:48:24 INFO --- 2570/2570 batches finished ---
2024-11-17 14:48:24 INFO Train loss: 0.315082
2024-11-17 14:48:24 INFO ************ Epoch=5 end ************
2024-11-17 14:52:45 INFO [Metrics] AUC-ROC: 0.906395 - AUC-PR: 0.775158 - ACC: 0.841511 - Precision: 0.671587 - Recall: 0.716345 - F1: 0.693244 - MCC: 0.587102 - Logloss: 0.315013 - MSE: 0.101888 - RMSE: 0.319199 - COPC: 1.000782 - KLD: 0.706733
2024-11-17 14:52:45 INFO Save best model: monitor(max): 0.591381
2024-11-17 14:52:45 INFO --- 2570/2570 batches finished ---
2024-11-17 14:52:45 INFO Train loss: 0.315036
2024-11-17 14:52:45 INFO ************ Epoch=6 end ************
2024-11-17 14:57:46 INFO [Metrics] AUC-ROC: 0.906404 - AUC-PR: 0.775175 - ACC: 0.841514 - Precision: 0.671169 - Recall: 0.717666 - F1: 0.693639 - MCC: 0.587508 - Logloss: 0.315030 - MSE: 0.101890 - RMSE: 0.319202 - COPC: 1.001223 - KLD: 0.706789
2024-11-17 14:57:46 INFO Monitor(max) STOP: 0.591373 !
2024-11-17 14:57:46 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 14:57:46 INFO --- 2570/2570 batches finished ---
2024-11-17 14:57:46 INFO Train loss: 0.315019
2024-11-17 14:57:46 INFO ************ Epoch=7 end ************
2024-11-17 15:02:01 INFO [Metrics] AUC-ROC: 0.906406 - AUC-PR: 0.775183 - ACC: 0.841505 - Precision: 0.671405 - Recall: 0.716867 - F1: 0.693391 - MCC: 0.587249 - Logloss: 0.315009 - MSE: 0.101887 - RMSE: 0.319197 - COPC: 1.001276 - KLD: 0.706714
2024-11-17 15:02:01 INFO Save best model: monitor(max): 0.591397
2024-11-17 15:02:01 INFO --- 2570/2570 batches finished ---
2024-11-17 15:02:01 INFO Train loss: 0.314979
2024-11-17 15:02:01 INFO ************ Epoch=8 end ************
2024-11-17 15:06:28 INFO [Metrics] AUC-ROC: 0.906405 - AUC-PR: 0.775190 - ACC: 0.841519 - Precision: 0.671289 - Recall: 0.717332 - F1: 0.693547 - MCC: 0.587417 - Logloss: 0.315012 - MSE: 0.101887 - RMSE: 0.319197 - COPC: 1.002090 - KLD: 0.706729
2024-11-17 15:06:28 INFO Monitor(max) STOP: 0.591393 !
2024-11-17 15:06:28 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 15:06:28 INFO --- 2570/2570 batches finished ---
2024-11-17 15:06:28 INFO Train loss: 0.314981
2024-11-17 15:06:28 INFO ************ Epoch=9 end ************
2024-11-17 15:10:53 INFO [Metrics] AUC-ROC: 0.906406 - AUC-PR: 0.775190 - ACC: 0.841519 - Precision: 0.671287 - Recall: 0.717341 - F1: 0.693550 - MCC: 0.587420 - Logloss: 0.315008 - MSE: 0.101886 - RMSE: 0.319196 - COPC: 1.000559 - KLD: 0.706716
2024-11-17 15:10:53 INFO Monitor(max) STOP: 0.591397 !
2024-11-17 15:10:53 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 15:10:53 INFO Early stopping at epoch=10
2024-11-17 15:10:53 INFO --- 2570/2570 batches finished ---
2024-11-17 15:10:53 INFO Train loss: 0.314975
2024-11-17 15:10:53 INFO Training finished.
2024-11-17 15:10:53 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/WideDeep/Synthetic/WideDeep_model_seed2019.ckpt
2024-11-17 15:10:53 INFO Start evaluate model
2024-11-17 15:11:31 INFO [Metrics] AUC-ROC: 0.906406 - AUC-PR: 0.775183 - ACC: 0.841505 - Precision: 0.671405 - Recall: 0.716867 - F1: 0.693391 - MCC: 0.587249 - Logloss: 0.315009 - MSE: 0.101887 - RMSE: 0.319197 - COPC: 1.001276 - KLD: 0.706714
2024-11-17 15:11:31 INFO Start testing model
2024-11-17 15:12:08 INFO [Metrics] AUC-ROC: 0.906384 - AUC-PR: 0.775532 - ACC: 0.841498 - Precision: 0.671606 - Recall: 0.716182 - F1: 0.693178 - MCC: 0.587027 - Logloss: 0.314886 - MSE: 0.101820 - RMSE: 0.319093 - COPC: 1.002059 - KLD: 0.706752
