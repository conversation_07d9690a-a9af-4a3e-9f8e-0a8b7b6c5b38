2024-11-17 11:04:29 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='NFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=30000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-17 11:04:29 INFO Start process Synthetic !
2024-11-17 11:04:29 INFO Loading Synthetic dataset
2024-11-17 11:04:29 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 11:04:30 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 11:04:30 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 11:04:31 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 11:04:31 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 11:04:31 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 11:04:31 INFO Loading data done
2024-11-17 11:04:31 INFO Model: NFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (user_age): Embedding(22, 1, padding_idx=21)
        (item_price): Embedding(43, 1, padding_idx=42)
        (user_gender): Embedding(4, 1, padding_idx=3)
        (user_region): Embedding(334, 1, padding_idx=333)
        (device_type): Embedding(5, 1, padding_idx=4)
        (ad_category): Embedding(45, 1, padding_idx=44)
        (ad_placement): Embedding(8, 1, padding_idx=7)
        (hour_of_day): Embedding(25, 1, padding_idx=24)
        (day_of_week): Embedding(8, 1, padding_idx=7)
        (scene_type): Embedding(6, 1, padding_idx=5)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=16, out_features=1000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=1000, out_features=1000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=1000, out_features=1000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-17 11:04:31 INFO Model parameters: 2028501
2024-11-17 11:04:31 INFO Start training model
2024-11-17 11:04:31 INFO Start training: 857 batches/epoch
2024-11-17 11:04:31 INFO ************ Epoch=1 start ************
2024-11-17 11:08:29 INFO [Metrics] AUC-ROC: 0.906129 - AUC-PR: 0.774561 - ACC: 0.840970 - Precision: 0.676762 - Recall: 0.696584 - F1: 0.686530 - MCC: 0.580128 - Logloss: 0.315868 - MSE: 0.102147 - RMSE: 0.319604 - COPC: 0.990094 - KLD: 0.708876
2024-11-17 11:08:29 INFO Save best model: monitor(max): 0.590260
2024-11-17 11:08:29 INFO --- 857/857 batches finished ---
2024-11-17 11:08:29 INFO Train loss: 0.331002
2024-11-17 11:08:29 INFO ************ Epoch=1 end ************
2024-11-17 11:12:28 INFO [Metrics] AUC-ROC: 0.906183 - AUC-PR: 0.774640 - ACC: 0.841348 - Precision: 0.667580 - Recall: 0.727799 - F1: 0.696390 - MCC: 0.590285 - Logloss: 0.315698 - MSE: 0.102124 - RMSE: 0.319569 - COPC: 0.972761 - KLD: 0.707921
2024-11-17 11:12:28 INFO Save best model: monitor(max): 0.590485
2024-11-17 11:12:28 INFO --- 857/857 batches finished ---
2024-11-17 11:12:28 INFO Train loss: 0.315648
2024-11-17 11:12:28 INFO ************ Epoch=2 end ************
2024-11-17 11:16:30 INFO [Metrics] AUC-ROC: 0.906234 - AUC-PR: 0.774784 - ACC: 0.840408 - Precision: 0.689955 - Recall: 0.656758 - F1: 0.672948 - MCC: 0.567786 - Logloss: 0.315572 - MSE: 0.102036 - RMSE: 0.319431 - COPC: 1.017867 - KLD: 0.708222
2024-11-17 11:16:30 INFO Save best model: monitor(max): 0.590662
2024-11-17 11:16:30 INFO --- 857/857 batches finished ---
2024-11-17 11:16:30 INFO Train loss: 0.315394
2024-11-17 11:16:30 INFO ************ Epoch=3 end ************
2024-11-17 11:20:30 INFO [Metrics] AUC-ROC: 0.906248 - AUC-PR: 0.774896 - ACC: 0.841248 - Precision: 0.674293 - Recall: 0.706029 - F1: 0.689796 - MCC: 0.583479 - Logloss: 0.315303 - MSE: 0.101974 - RMSE: 0.319334 - COPC: 1.003532 - KLD: 0.707532
2024-11-17 11:20:30 INFO Save best model: monitor(max): 0.590945
2024-11-17 11:20:30 INFO --- 857/857 batches finished ---
2024-11-17 11:20:31 INFO Train loss: 0.315288
2024-11-17 11:20:31 INFO ************ Epoch=4 end ************
2024-11-17 11:24:31 INFO [Metrics] AUC-ROC: 0.906246 - AUC-PR: 0.774929 - ACC: 0.841368 - Precision: 0.667957 - Recall: 0.726731 - F1: 0.696106 - MCC: 0.589994 - Logloss: 0.315478 - MSE: 0.102006 - RMSE: 0.319383 - COPC: 0.980133 - KLD: 0.707935
2024-11-17 11:24:31 INFO Monitor(max) STOP: 0.590767 !
2024-11-17 11:24:31 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 11:24:31 INFO --- 857/857 batches finished ---
2024-11-17 11:24:31 INFO Train loss: 0.315205
2024-11-17 11:24:31 INFO ************ Epoch=5 end ************
2024-11-17 11:28:33 INFO [Metrics] AUC-ROC: 0.906320 - AUC-PR: 0.775066 - ACC: 0.841464 - Precision: 0.671137 - Recall: 0.717376 - F1: 0.693487 - MCC: 0.587321 - Logloss: 0.315119 - MSE: 0.101915 - RMSE: 0.319242 - COPC: 0.994159 - KLD: 0.707016
2024-11-17 11:28:33 INFO Save best model: monitor(max): 0.591201
2024-11-17 11:28:33 INFO --- 857/857 batches finished ---
2024-11-17 11:28:33 INFO Train loss: 0.314914
2024-11-17 11:28:33 INFO ************ Epoch=6 end ************
2024-11-17 11:32:34 INFO [Metrics] AUC-ROC: 0.906335 - AUC-PR: 0.775068 - ACC: 0.841399 - Precision: 0.667853 - Recall: 0.727317 - F1: 0.696317 - MCC: 0.590236 - Logloss: 0.315149 - MSE: 0.101936 - RMSE: 0.319274 - COPC: 0.988557 - KLD: 0.707001
2024-11-17 11:32:34 INFO Monitor(max) STOP: 0.591186 !
2024-11-17 11:32:34 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 11:32:34 INFO --- 857/857 batches finished ---
2024-11-17 11:32:34 INFO Train loss: 0.314884
2024-11-17 11:32:34 INFO ************ Epoch=7 end ************
2024-11-17 11:36:35 INFO [Metrics] AUC-ROC: 0.906341 - AUC-PR: 0.775084 - ACC: 0.841403 - Precision: 0.670710 - Recall: 0.718230 - F1: 0.693657 - MCC: 0.587457 - Logloss: 0.315097 - MSE: 0.101912 - RMSE: 0.319237 - COPC: 0.995949 - KLD: 0.706947
2024-11-17 11:36:35 INFO Save best model: monitor(max): 0.591244
2024-11-17 11:36:35 INFO --- 857/857 batches finished ---
2024-11-17 11:36:35 INFO Train loss: 0.314827
2024-11-17 11:36:35 INFO ************ Epoch=8 end ************
2024-11-17 11:40:33 INFO [Metrics] AUC-ROC: 0.906337 - AUC-PR: 0.775078 - ACC: 0.841414 - Precision: 0.671857 - Recall: 0.714742 - F1: 0.692637 - MCC: 0.586422 - Logloss: 0.315092 - MSE: 0.101911 - RMSE: 0.319235 - COPC: 0.999322 - KLD: 0.706947
2024-11-17 11:40:33 INFO Monitor(max) STOP: 0.591244 !
2024-11-17 11:40:33 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 11:40:33 INFO --- 857/857 batches finished ---
2024-11-17 11:40:33 INFO Train loss: 0.314829
2024-11-17 11:40:33 INFO ************ Epoch=9 end ************
2024-11-17 11:44:30 INFO [Metrics] AUC-ROC: 0.906338 - AUC-PR: 0.775079 - ACC: 0.841400 - Precision: 0.671372 - Recall: 0.716138 - F1: 0.693033 - MCC: 0.586816 - Logloss: 0.315091 - MSE: 0.101911 - RMSE: 0.319235 - COPC: 0.998688 - KLD: 0.706938
2024-11-17 11:44:30 INFO Save best model: monitor(max): 0.591248
2024-11-17 11:44:30 INFO --- 857/857 batches finished ---
2024-11-17 11:44:30 INFO Train loss: 0.314821
2024-11-17 11:44:30 INFO ************ Epoch=10 end ************
2024-11-17 11:48:27 INFO [Metrics] AUC-ROC: 0.906339 - AUC-PR: 0.775079 - ACC: 0.841404 - Precision: 0.671646 - Recall: 0.715319 - F1: 0.692795 - MCC: 0.586577 - Logloss: 0.315090 - MSE: 0.101911 - RMSE: 0.319235 - COPC: 1.000122 - KLD: 0.706938
2024-11-17 11:48:27 INFO Monitor(max) STOP: 0.591249 !
2024-11-17 11:48:27 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 11:48:27 INFO --- 857/857 batches finished ---
2024-11-17 11:48:27 INFO Train loss: 0.314813
2024-11-17 11:48:27 INFO ************ Epoch=11 end ************
2024-11-17 11:52:25 INFO [Metrics] AUC-ROC: 0.906339 - AUC-PR: 0.775079 - ACC: 0.841400 - Precision: 0.671547 - Recall: 0.715595 - F1: 0.692872 - MCC: 0.586652 - Logloss: 0.315090 - MSE: 0.101911 - RMSE: 0.319235 - COPC: 0.999598 - KLD: 0.706937
2024-11-17 11:52:25 INFO Monitor(max) STOP: 0.591248 !
2024-11-17 11:52:25 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 11:52:25 INFO Early stopping at epoch=12
2024-11-17 11:52:25 INFO --- 857/857 batches finished ---
2024-11-17 11:52:25 INFO Train loss: 0.314823
2024-11-17 11:52:25 INFO Training finished.
2024-11-17 11:52:25 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/NFM/Synthetic/NFM_model_seed2019.ckpt
2024-11-17 11:52:25 INFO Start evaluate model
2024-11-17 11:52:59 INFO [Metrics] AUC-ROC: 0.906338 - AUC-PR: 0.775079 - ACC: 0.841400 - Precision: 0.671372 - Recall: 0.716138 - F1: 0.693033 - MCC: 0.586816 - Logloss: 0.315091 - MSE: 0.101911 - RMSE: 0.319235 - COPC: 0.998688 - KLD: 0.706938
2024-11-17 11:53:00 INFO Start testing model
2024-11-17 11:53:34 INFO [Metrics] AUC-ROC: 0.906364 - AUC-PR: 0.775484 - ACC: 0.841406 - Precision: 0.671638 - Recall: 0.715362 - F1: 0.692811 - MCC: 0.586595 - Logloss: 0.314957 - MSE: 0.101843 - RMSE: 0.319129 - COPC: 0.999447 - KLD: 0.706929
