2024-11-17 12:10:09 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='FM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-17 12:10:09 INFO Start process Synthetic !
2024-11-17 12:10:09 INFO Loading Synthetic dataset
2024-11-17 12:10:09 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 12:10:10 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 12:10:10 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 12:10:11 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 12:10:11 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 12:10:11 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 12:10:11 INFO Loading data done
2024-11-17 12:10:12 INFO Model: FM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fm_layer): FM_Layer(
    (inner_product_layer): InnerProductLayer()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (user_age): Embedding(22, 1, padding_idx=21)
          (item_price): Embedding(43, 1, padding_idx=42)
          (user_gender): Embedding(4, 1, padding_idx=3)
          (user_region): Embedding(334, 1, padding_idx=333)
          (device_type): Embedding(5, 1, padding_idx=4)
          (ad_category): Embedding(45, 1, padding_idx=44)
          (ad_placement): Embedding(8, 1, padding_idx=7)
          (hour_of_day): Embedding(25, 1, padding_idx=24)
          (day_of_week): Embedding(8, 1, padding_idx=7)
          (scene_type): Embedding(6, 1, padding_idx=5)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
    (final_activation): Sigmoid()
  )
)
2024-11-17 12:10:12 INFO Model parameters: 8501
2024-11-17 12:10:12 INFO Start training model
2024-11-17 12:10:12 INFO Start training: 2570 batches/epoch
2024-11-17 12:10:12 INFO ************ Epoch=1 start ************
2024-11-17 12:14:09 INFO [Metrics] AUC-ROC: 0.904937 - AUC-PR: 0.771813 - ACC: 0.839293 - Precision: 0.695599 - Recall: 0.635098 - F1: 0.663973 - MCC: 0.559652 - Logloss: 0.322934 - MSE: 0.103764 - RMSE: 0.322124 - COPC: 1.005060 - KLD: 0.725357
2024-11-17 12:14:09 INFO Save best model: monitor(max): 0.582003
2024-11-17 12:14:09 INFO --- 2570/2570 batches finished ---
2024-11-17 12:14:09 INFO Train loss: 0.339212
2024-11-17 12:14:09 INFO ************ Epoch=1 end ************
2024-11-17 12:18:07 INFO [Metrics] AUC-ROC: 0.905144 - AUC-PR: 0.772089 - ACC: 0.839303 - Precision: 0.694199 - Recall: 0.638461 - F1: 0.665164 - MCC: 0.560539 - Logloss: 0.322159 - MSE: 0.103585 - RMSE: 0.321846 - COPC: 1.012818 - KLD: 0.722156
2024-11-17 12:18:07 INFO Save best model: monitor(max): 0.582985
2024-11-17 12:18:07 INFO --- 2570/2570 batches finished ---
2024-11-17 12:18:07 INFO Train loss: 0.322323
2024-11-17 12:18:07 INFO ************ Epoch=2 end ************
2024-11-17 12:22:06 INFO [Metrics] AUC-ROC: 0.905242 - AUC-PR: 0.772194 - ACC: 0.839303 - Precision: 0.698329 - Recall: 0.628882 - F1: 0.661789 - MCC: 0.558096 - Logloss: 0.321941 - MSE: 0.103547 - RMSE: 0.321787 - COPC: 1.012851 - KLD: 0.721214
2024-11-17 12:22:06 INFO Save best model: monitor(max): 0.583301
2024-11-17 12:22:06 INFO --- 2570/2570 batches finished ---
2024-11-17 12:22:06 INFO Train loss: 0.321948
2024-11-17 12:22:06 INFO ************ Epoch=3 end ************
2024-11-17 12:26:02 INFO [Metrics] AUC-ROC: 0.905329 - AUC-PR: 0.772307 - ACC: 0.839552 - Precision: 0.691235 - Recall: 0.647383 - F1: 0.668591 - MCC: 0.563421 - Logloss: 0.321814 - MSE: 0.103442 - RMSE: 0.321625 - COPC: 0.997276 - KLD: 0.720701
2024-11-17 12:26:02 INFO Save best model: monitor(max): 0.583514
2024-11-17 12:26:02 INFO --- 2570/2570 batches finished ---
2024-11-17 12:26:02 INFO Train loss: 0.321830
2024-11-17 12:26:02 INFO ************ Epoch=4 end ************
2024-11-17 12:30:06 INFO [Metrics] AUC-ROC: 0.905300 - AUC-PR: 0.772304 - ACC: 0.839364 - Precision: 0.692021 - Recall: 0.644115 - F1: 0.667209 - MCC: 0.562147 - Logloss: 0.321844 - MSE: 0.103490 - RMSE: 0.321698 - COPC: 1.004121 - KLD: 0.720816
2024-11-17 12:30:06 INFO Monitor(max) STOP: 0.583455 !
2024-11-17 12:30:06 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 12:30:06 INFO --- 2570/2570 batches finished ---
2024-11-17 12:30:06 INFO Train loss: 0.321764
2024-11-17 12:30:06 INFO ************ Epoch=5 end ************
2024-11-17 12:34:04 INFO [Metrics] AUC-ROC: 0.905421 - AUC-PR: 0.772735 - ACC: 0.839191 - Precision: 0.696500 - Recall: 0.632280 - F1: 0.662838 - MCC: 0.558703 - Logloss: 0.321410 - MSE: 0.103334 - RMSE: 0.321457 - COPC: 0.998858 - KLD: 0.719726
2024-11-17 12:34:04 INFO Save best model: monitor(max): 0.584011
2024-11-17 12:34:04 INFO --- 2570/2570 batches finished ---
2024-11-17 12:34:04 INFO Train loss: 0.321355
2024-11-17 12:34:04 INFO ************ Epoch=6 end ************
2024-11-17 12:38:02 INFO [Metrics] AUC-ROC: 0.905429 - AUC-PR: 0.772697 - ACC: 0.839250 - Precision: 0.694810 - Recall: 0.636639 - F1: 0.664454 - MCC: 0.559951 - Logloss: 0.321393 - MSE: 0.103332 - RMSE: 0.321454 - COPC: 1.001249 - KLD: 0.719694
2024-11-17 12:38:02 INFO Save best model: monitor(max): 0.584036
2024-11-17 12:38:02 INFO --- 2570/2570 batches finished ---
2024-11-17 12:38:02 INFO Train loss: 0.321304
2024-11-17 12:38:02 INFO ************ Epoch=7 end ************
2024-11-17 12:42:03 INFO [Metrics] AUC-ROC: 0.905428 - AUC-PR: 0.772706 - ACC: 0.839485 - Precision: 0.688354 - Recall: 0.654061 - F1: 0.670770 - MCC: 0.565055 - Logloss: 0.321405 - MSE: 0.103335 - RMSE: 0.321458 - COPC: 0.994438 - KLD: 0.719630
2024-11-17 12:42:03 INFO Monitor(max) STOP: 0.584023 !
2024-11-17 12:42:03 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 12:42:03 INFO --- 2570/2570 batches finished ---
2024-11-17 12:42:03 INFO Train loss: 0.321295
2024-11-17 12:42:03 INFO ************ Epoch=8 end ************
2024-11-17 12:46:02 INFO [Metrics] AUC-ROC: 0.905437 - AUC-PR: 0.772740 - ACC: 0.839262 - Precision: 0.694812 - Recall: 0.636716 - F1: 0.664497 - MCC: 0.559996 - Logloss: 0.321372 - MSE: 0.103327 - RMSE: 0.321446 - COPC: 0.999596 - KLD: 0.719615
2024-11-17 12:46:02 INFO Save best model: monitor(max): 0.584065
2024-11-17 12:46:02 INFO --- 2570/2570 batches finished ---
2024-11-17 12:46:02 INFO Train loss: 0.321247
2024-11-17 12:46:02 INFO ************ Epoch=9 end ************
2024-11-17 12:49:52 INFO [Metrics] AUC-ROC: 0.905440 - AUC-PR: 0.772745 - ACC: 0.839211 - Precision: 0.695117 - Recall: 0.635642 - F1: 0.664051 - MCC: 0.559608 - Logloss: 0.321368 - MSE: 0.103326 - RMSE: 0.321444 - COPC: 0.999910 - KLD: 0.719609
2024-11-17 12:49:52 INFO Save best model: monitor(max): 0.584072
2024-11-17 12:49:52 INFO --- 2570/2570 batches finished ---
2024-11-17 12:49:52 INFO Train loss: 0.321239
2024-11-17 12:49:52 INFO ************ Epoch=10 end ************
2024-11-17 12:53:47 INFO [Metrics] AUC-ROC: 0.905440 - AUC-PR: 0.772768 - ACC: 0.839229 - Precision: 0.694465 - Recall: 0.637299 - F1: 0.664655 - MCC: 0.560073 - Logloss: 0.321367 - MSE: 0.103327 - RMSE: 0.321445 - COPC: 0.998476 - KLD: 0.719593
2024-11-17 12:53:47 INFO Monitor(max) STOP: 0.584073 !
2024-11-17 12:53:47 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 12:53:47 INFO --- 2570/2570 batches finished ---
2024-11-17 12:53:47 INFO Train loss: 0.321237
2024-11-17 12:53:47 INFO ************ Epoch=11 end ************
2024-11-17 12:57:46 INFO [Metrics] AUC-ROC: 0.905441 - AUC-PR: 0.772764 - ACC: 0.839220 - Precision: 0.695215 - Recall: 0.635472 - F1: 0.664002 - MCC: 0.559583 - Logloss: 0.321366 - MSE: 0.103326 - RMSE: 0.321444 - COPC: 0.999536 - KLD: 0.719601
2024-11-17 12:57:46 INFO Save best model: monitor(max): 0.584075
2024-11-17 12:57:46 INFO --- 2570/2570 batches finished ---
2024-11-17 12:57:46 INFO Train loss: 0.321230
2024-11-17 12:57:46 INFO ************ Epoch=12 end ************
2024-11-17 13:01:45 INFO [Metrics] AUC-ROC: 0.905441 - AUC-PR: 0.772763 - ACC: 0.839209 - Precision: 0.695215 - Recall: 0.635394 - F1: 0.663960 - MCC: 0.559539 - Logloss: 0.321366 - MSE: 0.103326 - RMSE: 0.321443 - COPC: 0.999565 - KLD: 0.719600
2024-11-17 13:01:45 INFO Monitor(max) STOP: 0.584075 !
2024-11-17 13:01:45 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 13:01:45 INFO --- 2570/2570 batches finished ---
2024-11-17 13:01:45 INFO Train loss: 0.321233
2024-11-17 13:01:45 INFO ************ Epoch=13 end ************
2024-11-17 13:05:44 INFO [Metrics] AUC-ROC: 0.905442 - AUC-PR: 0.772762 - ACC: 0.839213 - Precision: 0.695280 - Recall: 0.635275 - F1: 0.663924 - MCC: 0.559518 - Logloss: 0.321366 - MSE: 0.103326 - RMSE: 0.321443 - COPC: 0.999647 - KLD: 0.719600
2024-11-17 13:05:44 INFO Save best model: monitor(max): 0.584076
2024-11-17 13:05:44 INFO --- 2570/2570 batches finished ---
2024-11-17 13:05:44 INFO Train loss: 0.321232
2024-11-17 13:05:44 INFO ************ Epoch=14 end ************
2024-11-17 13:09:43 INFO [Metrics] AUC-ROC: 0.905442 - AUC-PR: 0.772762 - ACC: 0.839212 - Precision: 0.695275 - Recall: 0.635274 - F1: 0.663922 - MCC: 0.559514 - Logloss: 0.321365 - MSE: 0.103326 - RMSE: 0.321443 - COPC: 0.999635 - KLD: 0.719599
2024-11-17 13:09:43 INFO Monitor(max) STOP: 0.584076 !
2024-11-17 13:09:43 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 13:09:43 INFO --- 2570/2570 batches finished ---
2024-11-17 13:09:43 INFO Train loss: 0.321232
2024-11-17 13:09:43 INFO ************ Epoch=15 end ************
2024-11-17 13:36:33 INFO [Metrics] AUC-ROC: 0.905442 - AUC-PR: 0.772761 - ACC: 0.839212 - Precision: 0.695049 - Recall: 0.635805 - F1: 0.664109 - MCC: 0.559651 - Logloss: 0.321365 - MSE: 0.103326 - RMSE: 0.321443 - COPC: 0.999477 - KLD: 0.719598
2024-11-17 13:36:33 INFO Monitor(max) STOP: 0.584077 !
2024-11-17 13:36:33 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 13:36:33 INFO Early stopping at epoch=16
2024-11-17 13:36:33 INFO --- 2570/2570 batches finished ---
2024-11-17 13:36:33 INFO Train loss: 0.321236
2024-11-17 13:36:33 INFO Training finished.
2024-11-17 13:36:33 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FM/Synthetic/FM_model_seed2020.ckpt
2024-11-17 13:36:33 INFO Start evaluate model
2024-11-17 13:37:08 INFO [Metrics] AUC-ROC: 0.905442 - AUC-PR: 0.772762 - ACC: 0.839213 - Precision: 0.695280 - Recall: 0.635275 - F1: 0.663924 - MCC: 0.559518 - Logloss: 0.321366 - MSE: 0.103326 - RMSE: 0.321443 - COPC: 0.999647 - KLD: 0.719600
2024-11-17 13:37:08 INFO Start testing model
2024-11-17 13:37:43 INFO [Metrics] AUC-ROC: 0.905444 - AUC-PR: 0.773252 - ACC: 0.839374 - Precision: 0.695982 - Recall: 0.634780 - F1: 0.663974 - MCC: 0.559751 - Logloss: 0.321294 - MSE: 0.103283 - RMSE: 0.321376 - COPC: 1.000601 - KLD: 0.719849
