2024-11-17 14:40:37 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='AutoInt', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-17 14:40:37 INFO Start process Synthetic !
2024-11-17 14:40:37 INFO Loading Synthetic dataset
2024-11-17 14:40:37 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 14:40:38 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 14:40:39 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 14:40:39 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 14:40:39 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 14:40:39 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 14:40:39 INFO Loading data done
2024-11-17 14:40:40 INFO Model: AutoInt(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (self_attention): Sequential(
    (0): MultiHeadSelfAttention(
      (W_q): Linear(in_features=16, out_features=64, bias=False)
      (W_k): Linear(in_features=16, out_features=64, bias=False)
      (W_v): Linear(in_features=16, out_features=64, bias=False)
      (W_res): Linear(in_features=16, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (1): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (2): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (3): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (4): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
  )
  (fc): Linear(in_features=640, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-17 14:40:40 INFO Model parameters: 61889
2024-11-17 14:40:40 INFO Start training model
2024-11-17 14:40:40 INFO Start training: 2570 batches/epoch
2024-11-17 14:40:40 INFO ************ Epoch=1 start ************
2024-11-17 14:44:49 INFO [Metrics] AUC-ROC: 0.906178 - AUC-PR: 0.774837 - ACC: 0.841422 - Precision: 0.670115 - Recall: 0.720254 - F1: 0.694280 - MCC: 0.588111 - Logloss: 0.315683 - MSE: 0.102050 - RMSE: 0.319452 - COPC: 0.986503 - KLD: 0.708674
2024-11-17 14:44:49 INFO Save best model: monitor(max): 0.590494
2024-11-17 14:44:49 INFO --- 2570/2570 batches finished ---
2024-11-17 14:44:49 INFO Train loss: 0.321588
2024-11-17 14:44:49 INFO ************ Epoch=1 end ************
2024-11-17 14:49:01 INFO [Metrics] AUC-ROC: 0.906224 - AUC-PR: 0.774884 - ACC: 0.841227 - Precision: 0.676893 - Recall: 0.698169 - F1: 0.687367 - MCC: 0.581120 - Logloss: 0.315554 - MSE: 0.102061 - RMSE: 0.319470 - COPC: 1.014125 - KLD: 0.707976
2024-11-17 14:49:01 INFO Save best model: monitor(max): 0.590670
2024-11-17 14:49:01 INFO --- 2570/2570 batches finished ---
2024-11-17 14:49:01 INFO Train loss: 0.315470
2024-11-17 14:49:01 INFO ************ Epoch=2 end ************
2024-11-17 14:53:22 INFO [Metrics] AUC-ROC: 0.906306 - AUC-PR: 0.775053 - ACC: 0.841388 - Precision: 0.669129 - Recall: 0.723122 - F1: 0.695078 - MCC: 0.588922 - Logloss: 0.315211 - MSE: 0.101942 - RMSE: 0.319283 - COPC: 0.997802 - KLD: 0.707303
2024-11-17 14:53:22 INFO Save best model: monitor(max): 0.591095
2024-11-17 14:53:22 INFO --- 2570/2570 batches finished ---
2024-11-17 14:53:22 INFO Train loss: 0.315304
2024-11-17 14:53:22 INFO ************ Epoch=3 end ************
2024-11-17 14:58:19 INFO [Metrics] AUC-ROC: 0.906295 - AUC-PR: 0.774934 - ACC: 0.841218 - Precision: 0.676687 - Recall: 0.698705 - F1: 0.687520 - MCC: 0.581258 - Logloss: 0.315257 - MSE: 0.101949 - RMSE: 0.319295 - COPC: 1.009751 - KLD: 0.707399
2024-11-17 14:58:19 INFO Monitor(max) STOP: 0.591038 !
2024-11-17 14:58:19 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 14:58:19 INFO --- 2570/2570 batches finished ---
2024-11-17 14:58:19 INFO Train loss: 0.315187
2024-11-17 14:58:19 INFO ************ Epoch=4 end ************
2024-11-17 15:02:37 INFO [Metrics] AUC-ROC: 0.906398 - AUC-PR: 0.775199 - ACC: 0.841449 - Precision: 0.671908 - Recall: 0.714861 - F1: 0.692719 - MCC: 0.586529 - Logloss: 0.315066 - MSE: 0.101900 - RMSE: 0.319218 - COPC: 0.999749 - KLD: 0.706891
2024-11-17 15:02:37 INFO Save best model: monitor(max): 0.591333
2024-11-17 15:02:37 INFO --- 2570/2570 batches finished ---
2024-11-17 15:02:37 INFO Train loss: 0.314918
2024-11-17 15:02:37 INFO ************ Epoch=5 end ************
2024-11-17 15:07:03 INFO [Metrics] AUC-ROC: 0.906362 - AUC-PR: 0.775124 - ACC: 0.841493 - Precision: 0.670970 - Recall: 0.718124 - F1: 0.693747 - MCC: 0.587605 - Logloss: 0.315063 - MSE: 0.101899 - RMSE: 0.319217 - COPC: 0.999882 - KLD: 0.706892
2024-11-17 15:07:03 INFO Monitor(max) STOP: 0.591299 !
2024-11-17 15:07:03 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 15:07:03 INFO --- 2570/2570 batches finished ---
2024-11-17 15:07:03 INFO Train loss: 0.314894
2024-11-17 15:07:03 INFO ************ Epoch=6 end ************
2024-11-17 15:11:31 INFO [Metrics] AUC-ROC: 0.906381 - AUC-PR: 0.775164 - ACC: 0.841494 - Precision: 0.670356 - Recall: 0.720066 - F1: 0.694322 - MCC: 0.588199 - Logloss: 0.315051 - MSE: 0.101897 - RMSE: 0.319212 - COPC: 0.997300 - KLD: 0.706853
2024-11-17 15:11:31 INFO Monitor(max) STOP: 0.591330 !
2024-11-17 15:11:31 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 15:11:31 INFO Early stopping at epoch=7
2024-11-17 15:11:31 INFO --- 2570/2570 batches finished ---
2024-11-17 15:11:31 INFO Train loss: 0.314847
2024-11-17 15:11:31 INFO Training finished.
2024-11-17 15:11:31 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AutoInt/Synthetic/AutoInt_model_seed2020.ckpt
2024-11-17 15:11:31 INFO Start evaluate model
2024-11-17 15:12:08 INFO [Metrics] AUC-ROC: 0.906398 - AUC-PR: 0.775199 - ACC: 0.841449 - Precision: 0.671908 - Recall: 0.714861 - F1: 0.692719 - MCC: 0.586529 - Logloss: 0.315066 - MSE: 0.101900 - RMSE: 0.319218 - COPC: 0.999749 - KLD: 0.706891
2024-11-17 15:12:09 INFO Start testing model
2024-11-17 15:12:46 INFO [Metrics] AUC-ROC: 0.906438 - AUC-PR: 0.775578 - ACC: 0.841408 - Precision: 0.672046 - Recall: 0.714118 - F1: 0.692443 - MCC: 0.586223 - Logloss: 0.314930 - MSE: 0.101834 - RMSE: 0.319115 - COPC: 1.000509 - KLD: 0.706869
