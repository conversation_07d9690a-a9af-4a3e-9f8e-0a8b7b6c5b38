2024-11-18 01:31:22 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='LSTM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-18 01:31:22 INFO Start process Synthetic !
2024-11-18 01:31:22 INFO Loading Synthetic dataset
2024-11-18 01:31:22 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-18 01:31:23 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-18 01:31:23 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-18 01:31:24 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-18 01:31:24 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-18 01:31:24 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-18 01:31:24 INFO Loading data done
2024-11-18 01:31:24 INFO Model: LSTM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (lstm): LSTM(16, 128, num_layers=2, batch_first=True, dropout=0.2)
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=272, out_features=64, bias=True)
      (1): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Dropout(p=0.2, inplace=False)
      (4): Linear(in_features=64, out_features=32, bias=True)
      (5): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (6): ReLU()
      (7): Dropout(p=0.2, inplace=False)
      (8): Linear(in_features=32, out_features=1, bias=True)
      (9): Sigmoid()
    )
  )
)
2024-11-18 01:31:24 INFO Model parameters: 234625
2024-11-18 01:31:24 INFO Start training model
2024-11-18 01:31:24 INFO Start training: 2570 batches/epoch
2024-11-18 01:31:24 INFO ************ Epoch=1 start ************
2024-11-18 01:34:20 INFO [Metrics] AUC-ROC: 0.906112 - AUC-PR: 0.774271 - ACC: 0.841359 - Precision: 0.668951 - Recall: 0.723456 - F1: 0.695137 - MCC: 0.588966 - Logloss: 0.315933 - MSE: 0.102105 - RMSE: 0.319539 - COPC: 1.000388 - KLD: 0.708930
2024-11-18 01:34:20 INFO Save best model: monitor(max): 0.590179
2024-11-18 01:34:20 INFO --- 2570/2570 batches finished ---
2024-11-18 01:34:20 INFO Train loss: 0.327299
2024-11-18 01:34:20 INFO ************ Epoch=1 end ************
2024-11-18 01:37:14 INFO [Metrics] AUC-ROC: 0.906262 - AUC-PR: 0.774666 - ACC: 0.841369 - Precision: 0.673574 - Recall: 0.709135 - F1: 0.690897 - MCC: 0.584649 - Logloss: 0.315555 - MSE: 0.102025 - RMSE: 0.319413 - COPC: 1.002850 - KLD: 0.708186
2024-11-18 01:37:14 INFO Save best model: monitor(max): 0.590707
2024-11-18 01:37:14 INFO --- 2570/2570 batches finished ---
2024-11-18 01:37:14 INFO Train loss: 0.318173
2024-11-18 01:37:14 INFO ************ Epoch=2 end ************
2024-11-18 01:40:09 INFO [Metrics] AUC-ROC: 0.906282 - AUC-PR: 0.774610 - ACC: 0.841347 - Precision: 0.670037 - Recall: 0.719909 - F1: 0.694078 - MCC: 0.587856 - Logloss: 0.315460 - MSE: 0.102010 - RMSE: 0.319390 - COPC: 1.003188 - KLD: 0.707791
2024-11-18 01:40:09 INFO Save best model: monitor(max): 0.590822
2024-11-18 01:40:09 INFO --- 2570/2570 batches finished ---
2024-11-18 01:40:09 INFO Train loss: 0.317716
2024-11-18 01:40:09 INFO ************ Epoch=3 end ************
2024-11-18 01:43:05 INFO [Metrics] AUC-ROC: 0.906295 - AUC-PR: 0.774784 - ACC: 0.841490 - Precision: 0.671594 - Recall: 0.716155 - F1: 0.693159 - MCC: 0.587003 - Logloss: 0.315369 - MSE: 0.101982 - RMSE: 0.319346 - COPC: 1.008416 - KLD: 0.707592
2024-11-18 01:43:05 INFO Save best model: monitor(max): 0.590927
2024-11-18 01:43:05 INFO --- 2570/2570 batches finished ---
2024-11-18 01:43:05 INFO Train loss: 0.317542
2024-11-18 01:43:05 INFO ************ Epoch=4 end ************
2024-11-18 01:45:59 INFO [Metrics] AUC-ROC: 0.906342 - AUC-PR: 0.774807 - ACC: 0.841505 - Precision: 0.670856 - Recall: 0.718578 - F1: 0.693897 - MCC: 0.587768 - Logloss: 0.315375 - MSE: 0.101996 - RMSE: 0.319369 - COPC: 1.009555 - KLD: 0.707490
2024-11-18 01:45:59 INFO Save best model: monitor(max): 0.590968
2024-11-18 01:45:59 INFO --- 2570/2570 batches finished ---
2024-11-18 01:46:00 INFO Train loss: 0.317390
2024-11-18 01:46:00 INFO ************ Epoch=5 end ************
2024-11-18 01:48:55 INFO [Metrics] AUC-ROC: 0.906336 - AUC-PR: 0.774946 - ACC: 0.841496 - Precision: 0.671564 - Recall: 0.716295 - F1: 0.693209 - MCC: 0.587057 - Logloss: 0.315263 - MSE: 0.101959 - RMSE: 0.319311 - COPC: 1.005882 - KLD: 0.707323
2024-11-18 01:48:55 INFO Save best model: monitor(max): 0.591074
2024-11-18 01:48:55 INFO --- 2570/2570 batches finished ---
2024-11-18 01:48:55 INFO Train loss: 0.317297
2024-11-18 01:48:55 INFO ************ Epoch=6 end ************
2024-11-18 01:51:51 INFO [Metrics] AUC-ROC: 0.906361 - AUC-PR: 0.774941 - ACC: 0.841490 - Precision: 0.671091 - Recall: 0.717720 - F1: 0.693623 - MCC: 0.587476 - Logloss: 0.315343 - MSE: 0.101981 - RMSE: 0.319345 - COPC: 1.003289 - KLD: 0.707377
2024-11-18 01:51:51 INFO Monitor(max) STOP: 0.591018 !
2024-11-18 01:51:51 INFO Reduce learning rate on plateau: 0.000100
2024-11-18 01:51:51 INFO --- 2570/2570 batches finished ---
2024-11-18 01:51:51 INFO Train loss: 0.317262
2024-11-18 01:51:51 INFO ************ Epoch=7 end ************
2024-11-18 01:54:45 INFO [Metrics] AUC-ROC: 0.906351 - AUC-PR: 0.774782 - ACC: 0.841475 - Precision: 0.671559 - Recall: 0.716148 - F1: 0.693137 - MCC: 0.586970 - Logloss: 0.315242 - MSE: 0.101954 - RMSE: 0.319302 - COPC: 1.008536 - KLD: 0.707113
2024-11-18 01:54:45 INFO Save best model: monitor(max): 0.591110
2024-11-18 01:54:45 INFO --- 2570/2570 batches finished ---
2024-11-18 01:54:45 INFO Train loss: 0.317081
2024-11-18 01:54:45 INFO ************ Epoch=8 end ************
2024-11-18 01:57:40 INFO [Metrics] AUC-ROC: 0.906341 - AUC-PR: 0.774789 - ACC: 0.841478 - Precision: 0.671426 - Recall: 0.716588 - F1: 0.693272 - MCC: 0.587110 - Logloss: 0.315205 - MSE: 0.101943 - RMSE: 0.319285 - COPC: 1.004132 - KLD: 0.707089
2024-11-18 01:57:40 INFO Save best model: monitor(max): 0.591136
2024-11-18 01:57:40 INFO --- 2570/2570 batches finished ---
2024-11-18 01:57:40 INFO Train loss: 0.317000
2024-11-18 01:57:40 INFO ************ Epoch=9 end ************
2024-11-18 02:00:27 INFO [Metrics] AUC-ROC: 0.906346 - AUC-PR: 0.774778 - ACC: 0.841475 - Precision: 0.671444 - Recall: 0.716504 - F1: 0.693243 - MCC: 0.587078 - Logloss: 0.315207 - MSE: 0.101944 - RMSE: 0.319287 - COPC: 1.007014 - KLD: 0.707086
2024-11-18 02:00:28 INFO Save best model: monitor(max): 0.591139
2024-11-18 02:00:28 INFO --- 2570/2570 batches finished ---
2024-11-18 02:00:28 INFO Train loss: 0.317005
2024-11-18 02:00:28 INFO ************ Epoch=10 end ************
2024-11-18 02:03:13 INFO [Metrics] AUC-ROC: 0.906338 - AUC-PR: 0.774751 - ACC: 0.841477 - Precision: 0.671480 - Recall: 0.716408 - F1: 0.693217 - MCC: 0.587053 - Logloss: 0.315210 - MSE: 0.101946 - RMSE: 0.319290 - COPC: 1.006741 - KLD: 0.707086
2024-11-18 02:03:13 INFO Monitor(max) STOP: 0.591128 !
2024-11-18 02:03:13 INFO Reduce learning rate on plateau: 0.000010
2024-11-18 02:03:13 INFO --- 2570/2570 batches finished ---
2024-11-18 02:03:13 INFO Train loss: 0.316978
2024-11-18 02:03:13 INFO ************ Epoch=11 end ************
2024-11-18 02:06:02 INFO [Metrics] AUC-ROC: 0.906335 - AUC-PR: 0.774726 - ACC: 0.841473 - Precision: 0.671460 - Recall: 0.716442 - F1: 0.693222 - MCC: 0.587056 - Logloss: 0.315192 - MSE: 0.101940 - RMSE: 0.319280 - COPC: 1.005351 - KLD: 0.707059
2024-11-18 02:06:02 INFO Save best model: monitor(max): 0.591143
2024-11-18 02:06:02 INFO --- 2570/2570 batches finished ---
2024-11-18 02:06:02 INFO Train loss: 0.316947
2024-11-18 02:06:02 INFO ************ Epoch=12 end ************
2024-11-18 02:08:51 INFO [Metrics] AUC-ROC: 0.906335 - AUC-PR: 0.774711 - ACC: 0.841469 - Precision: 0.671505 - Recall: 0.716270 - F1: 0.693166 - MCC: 0.586996 - Logloss: 0.315208 - MSE: 0.101945 - RMSE: 0.319288 - COPC: 1.006562 - KLD: 0.707066
2024-11-18 02:08:51 INFO Monitor(max) STOP: 0.591127 !
2024-11-18 02:08:51 INFO Reduce learning rate on plateau: 0.000001
2024-11-18 02:08:51 INFO --- 2570/2570 batches finished ---
2024-11-18 02:08:52 INFO Train loss: 0.316971
2024-11-18 02:08:52 INFO ************ Epoch=13 end ************
2024-11-18 02:11:39 INFO [Metrics] AUC-ROC: 0.906336 - AUC-PR: 0.774718 - ACC: 0.841465 - Precision: 0.671490 - Recall: 0.716285 - F1: 0.693164 - MCC: 0.586992 - Logloss: 0.315205 - MSE: 0.101943 - RMSE: 0.319285 - COPC: 1.007292 - KLD: 0.707060
2024-11-18 02:11:39 INFO Monitor(max) STOP: 0.591131 !
2024-11-18 02:11:39 INFO Reduce learning rate on plateau: 0.000001
2024-11-18 02:11:39 INFO Early stopping at epoch=14
2024-11-18 02:11:39 INFO --- 2570/2570 batches finished ---
2024-11-18 02:11:39 INFO Train loss: 0.316953
2024-11-18 02:11:39 INFO Training finished.
2024-11-18 02:11:39 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/LSTM/Synthetic/LSTM_model_seed2020.ckpt
2024-11-18 02:11:39 INFO Start evaluate model
2024-11-18 02:12:05 INFO [Metrics] AUC-ROC: 0.906335 - AUC-PR: 0.774726 - ACC: 0.841473 - Precision: 0.671460 - Recall: 0.716442 - F1: 0.693222 - MCC: 0.587056 - Logloss: 0.315192 - MSE: 0.101940 - RMSE: 0.319280 - COPC: 1.005351 - KLD: 0.707059
2024-11-18 02:12:05 INFO Start testing model
2024-11-18 02:12:32 INFO [Metrics] AUC-ROC: 0.906325 - AUC-PR: 0.775076 - ACC: 0.841445 - Precision: 0.671629 - Recall: 0.715694 - F1: 0.692961 - MCC: 0.586772 - Logloss: 0.315073 - MSE: 0.101876 - RMSE: 0.319181 - COPC: 1.006141 - KLD: 0.707087
