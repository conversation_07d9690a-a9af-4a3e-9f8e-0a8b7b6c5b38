2024-11-17 11:59:00 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='NFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=30000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-17 11:59:00 INFO Start process Synthetic !
2024-11-17 11:59:00 INFO Loading Synthetic dataset
2024-11-17 11:59:00 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 11:59:01 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 11:59:01 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 11:59:02 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 11:59:02 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 11:59:02 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 11:59:02 INFO Loading data done
2024-11-17 11:59:02 INFO Model: NFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (user_age): Embedding(22, 1, padding_idx=21)
        (item_price): Embedding(43, 1, padding_idx=42)
        (user_gender): Embedding(4, 1, padding_idx=3)
        (user_region): Embedding(334, 1, padding_idx=333)
        (device_type): Embedding(5, 1, padding_idx=4)
        (ad_category): Embedding(45, 1, padding_idx=44)
        (ad_placement): Embedding(8, 1, padding_idx=7)
        (hour_of_day): Embedding(25, 1, padding_idx=24)
        (day_of_week): Embedding(8, 1, padding_idx=7)
        (scene_type): Embedding(6, 1, padding_idx=5)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=16, out_features=1000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=1000, out_features=1000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=1000, out_features=1000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-17 11:59:02 INFO Model parameters: 2028501
2024-11-17 11:59:02 INFO Start training model
2024-11-17 11:59:02 INFO Start training: 857 batches/epoch
2024-11-17 11:59:02 INFO ************ Epoch=1 start ************
2024-11-17 12:02:59 INFO [Metrics] AUC-ROC: 0.906134 - AUC-PR: 0.774534 - ACC: 0.839139 - Precision: 0.725639 - Recall: 0.573331 - F1: 0.640555 - MCC: 0.544971 - Logloss: 0.316302 - MSE: 0.102259 - RMSE: 0.319780 - COPC: 1.041160 - KLD: 0.709461
2024-11-17 12:02:59 INFO Save best model: monitor(max): 0.589832
2024-11-17 12:02:59 INFO --- 857/857 batches finished ---
2024-11-17 12:02:59 INFO Train loss: 0.331100
2024-11-17 12:02:59 INFO ************ Epoch=1 end ************
2024-11-17 12:06:55 INFO [Metrics] AUC-ROC: 0.906187 - AUC-PR: 0.774726 - ACC: 0.840767 - Precision: 0.685797 - Recall: 0.670063 - F1: 0.677839 - MCC: 0.572175 - Logloss: 0.315546 - MSE: 0.102039 - RMSE: 0.319435 - COPC: 1.016230 - KLD: 0.708103
2024-11-17 12:06:55 INFO Save best model: monitor(max): 0.590640
2024-11-17 12:06:55 INFO --- 857/857 batches finished ---
2024-11-17 12:06:55 INFO Train loss: 0.315631
2024-11-17 12:06:55 INFO ************ Epoch=2 end ************
2024-11-17 12:10:50 INFO [Metrics] AUC-ROC: 0.906203 - AUC-PR: 0.774762 - ACC: 0.839786 - Precision: 0.714323 - Recall: 0.598502 - F1: 0.651303 - MCC: 0.551968 - Logloss: 0.315470 - MSE: 0.102043 - RMSE: 0.319442 - COPC: 1.018963 - KLD: 0.707686
2024-11-17 12:10:50 INFO Save best model: monitor(max): 0.590733
2024-11-17 12:10:50 INFO --- 857/857 batches finished ---
2024-11-17 12:10:50 INFO Train loss: 0.315423
2024-11-17 12:10:50 INFO ************ Epoch=3 end ************
2024-11-17 12:14:48 INFO [Metrics] AUC-ROC: 0.906265 - AUC-PR: 0.774900 - ACC: 0.841429 - Precision: 0.670922 - Recall: 0.717774 - F1: 0.693558 - MCC: 0.587371 - Logloss: 0.315350 - MSE: 0.101979 - RMSE: 0.319342 - COPC: 0.986533 - KLD: 0.707598
2024-11-17 12:14:48 INFO Save best model: monitor(max): 0.590915
2024-11-17 12:14:48 INFO --- 857/857 batches finished ---
2024-11-17 12:14:48 INFO Train loss: 0.315272
2024-11-17 12:14:48 INFO ************ Epoch=4 end ************
2024-11-17 12:18:46 INFO [Metrics] AUC-ROC: 0.906294 - AUC-PR: 0.774972 - ACC: 0.841169 - Precision: 0.677303 - Recall: 0.696533 - F1: 0.686783 - MCC: 0.580522 - Logloss: 0.315260 - MSE: 0.101967 - RMSE: 0.319323 - COPC: 1.009828 - KLD: 0.707311
2024-11-17 12:18:46 INFO Save best model: monitor(max): 0.591034
2024-11-17 12:18:46 INFO --- 857/857 batches finished ---
2024-11-17 12:18:46 INFO Train loss: 0.315192
2024-11-17 12:18:46 INFO ************ Epoch=5 end ************
2024-11-17 12:22:45 INFO [Metrics] AUC-ROC: 0.906246 - AUC-PR: 0.774920 - ACC: 0.841335 - Precision: 0.674210 - Recall: 0.706954 - F1: 0.690194 - MCC: 0.583931 - Logloss: 0.315471 - MSE: 0.101977 - RMSE: 0.319338 - COPC: 0.987320 - KLD: 0.708173
2024-11-17 12:22:45 INFO Monitor(max) STOP: 0.590775 !
2024-11-17 12:22:45 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 12:22:45 INFO --- 857/857 batches finished ---
2024-11-17 12:22:45 INFO Train loss: 0.315165
2024-11-17 12:22:45 INFO ************ Epoch=6 end ************
2024-11-17 12:26:42 INFO [Metrics] AUC-ROC: 0.906326 - AUC-PR: 0.775079 - ACC: 0.841425 - Precision: 0.671698 - Recall: 0.715325 - F1: 0.692825 - MCC: 0.586622 - Logloss: 0.315102 - MSE: 0.101913 - RMSE: 0.319238 - COPC: 1.001444 - KLD: 0.706975
2024-11-17 12:26:42 INFO Save best model: monitor(max): 0.591225
2024-11-17 12:26:43 INFO --- 857/857 batches finished ---
2024-11-17 12:26:43 INFO Train loss: 0.314883
2024-11-17 12:26:43 INFO ************ Epoch=7 end ************
2024-11-17 12:30:44 INFO [Metrics] AUC-ROC: 0.906337 - AUC-PR: 0.775079 - ACC: 0.841360 - Precision: 0.674647 - Recall: 0.705835 - F1: 0.689889 - MCC: 0.583650 - Logloss: 0.315125 - MSE: 0.101919 - RMSE: 0.319247 - COPC: 1.009801 - KLD: 0.707005
2024-11-17 12:30:44 INFO Monitor(max) STOP: 0.591212 !
2024-11-17 12:30:44 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 12:30:44 INFO --- 857/857 batches finished ---
2024-11-17 12:30:44 INFO Train loss: 0.314857
2024-11-17 12:30:44 INFO ************ Epoch=8 end ************
2024-11-17 12:34:42 INFO [Metrics] AUC-ROC: 0.906332 - AUC-PR: 0.775094 - ACC: 0.841412 - Precision: 0.670992 - Recall: 0.717424 - F1: 0.693432 - MCC: 0.587232 - Logloss: 0.315088 - MSE: 0.101909 - RMSE: 0.319232 - COPC: 0.996970 - KLD: 0.706927
2024-11-17 12:34:42 INFO Save best model: monitor(max): 0.591244
2024-11-17 12:34:42 INFO --- 857/857 batches finished ---
2024-11-17 12:34:42 INFO Train loss: 0.314814
2024-11-17 12:34:42 INFO ************ Epoch=9 end ************
2024-11-17 12:38:40 INFO [Metrics] AUC-ROC: 0.906333 - AUC-PR: 0.775097 - ACC: 0.841395 - Precision: 0.672333 - Recall: 0.713129 - F1: 0.692130 - MCC: 0.585899 - Logloss: 0.315085 - MSE: 0.101909 - RMSE: 0.319231 - COPC: 1.002073 - KLD: 0.706924
2024-11-17 12:38:40 INFO Save best model: monitor(max): 0.591247
2024-11-17 12:38:40 INFO --- 857/857 batches finished ---
2024-11-17 12:38:40 INFO Train loss: 0.314805
2024-11-17 12:38:40 INFO ************ Epoch=10 end ************
2024-11-17 12:42:37 INFO [Metrics] AUC-ROC: 0.906333 - AUC-PR: 0.775096 - ACC: 0.841414 - Precision: 0.671638 - Recall: 0.715426 - F1: 0.692840 - MCC: 0.586630 - Logloss: 0.315087 - MSE: 0.101908 - RMSE: 0.319231 - COPC: 1.000313 - KLD: 0.706929
2024-11-17 12:42:37 INFO Monitor(max) STOP: 0.591247 !
2024-11-17 12:42:37 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 12:42:37 INFO --- 857/857 batches finished ---
2024-11-17 12:42:37 INFO Train loss: 0.314803
2024-11-17 12:42:37 INFO ************ Epoch=11 end ************
2024-11-17 12:46:35 INFO [Metrics] AUC-ROC: 0.906333 - AUC-PR: 0.775096 - ACC: 0.841411 - Precision: 0.671606 - Recall: 0.715502 - F1: 0.692859 - MCC: 0.586647 - Logloss: 0.315086 - MSE: 0.101908 - RMSE: 0.319231 - COPC: 0.999842 - KLD: 0.706926
2024-11-17 12:46:35 INFO Monitor(max) STOP: 0.591248 !
2024-11-17 12:46:35 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 12:46:35 INFO Early stopping at epoch=12
2024-11-17 12:46:35 INFO --- 857/857 batches finished ---
2024-11-17 12:46:35 INFO Train loss: 0.314796
2024-11-17 12:46:35 INFO Training finished.
2024-11-17 12:46:35 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/NFM/Synthetic/NFM_model_seed2020.ckpt
2024-11-17 12:46:35 INFO Start evaluate model
2024-11-17 12:47:10 INFO [Metrics] AUC-ROC: 0.906333 - AUC-PR: 0.775097 - ACC: 0.841395 - Precision: 0.672333 - Recall: 0.713129 - F1: 0.692130 - MCC: 0.585899 - Logloss: 0.315085 - MSE: 0.101909 - RMSE: 0.319231 - COPC: 1.002073 - KLD: 0.706924
2024-11-17 12:47:10 INFO Start testing model
2024-11-17 12:47:44 INFO [Metrics] AUC-ROC: 0.906359 - AUC-PR: 0.775464 - ACC: 0.841417 - Precision: 0.672605 - Recall: 0.712468 - F1: 0.691963 - MCC: 0.585746 - Logloss: 0.314954 - MSE: 0.101844 - RMSE: 0.319130 - COPC: 1.002849 - KLD: 0.706909
