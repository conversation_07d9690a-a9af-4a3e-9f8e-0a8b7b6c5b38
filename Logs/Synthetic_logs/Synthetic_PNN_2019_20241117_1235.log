2024-11-17 12:35:16 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='PNN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-17 12:35:16 INFO Start process Synthetic !
2024-11-17 12:35:16 INFO Loading Synthetic dataset
2024-11-17 12:35:16 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 12:35:17 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 12:35:17 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 12:35:18 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 12:35:18 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 12:35:18 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 12:35:18 INFO Loading data done
2024-11-17 12:35:19 INFO Model: PNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=205, out_features=1000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=1000, out_features=1000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=1000, out_features=1, bias=True)
      (7): Sigmoid()
    )
  )
)
2024-11-17 12:35:19 INFO Model parameters: 1216001
2024-11-17 12:35:19 INFO Start training model
2024-11-17 12:35:19 INFO Start training: 2570 batches/epoch
2024-11-17 12:35:19 INFO ************ Epoch=1 start ************
2024-11-17 12:39:34 INFO [Metrics] AUC-ROC: 0.906337 - AUC-PR: 0.775013 - ACC: 0.841449 - Precision: 0.672186 - Recall: 0.714000 - F1: 0.692462 - MCC: 0.586269 - Logloss: 0.315563 - MSE: 0.101995 - RMSE: 0.319366 - COPC: 1.003242 - KLD: 0.708430
2024-11-17 12:39:34 INFO Save best model: monitor(max): 0.590773
2024-11-17 12:39:34 INFO --- 2570/2570 batches finished ---
2024-11-17 12:39:34 INFO Train loss: 0.318966
2024-11-17 12:39:34 INFO ************ Epoch=1 end ************
2024-11-17 12:43:51 INFO [Metrics] AUC-ROC: 0.906279 - AUC-PR: 0.775032 - ACC: 0.841479 - Precision: 0.671360 - Recall: 0.716798 - F1: 0.693335 - MCC: 0.587175 - Logloss: 0.315271 - MSE: 0.101951 - RMSE: 0.319298 - COPC: 0.997591 - KLD: 0.707508
2024-11-17 12:43:51 INFO Save best model: monitor(max): 0.591008
2024-11-17 12:43:51 INFO --- 2570/2570 batches finished ---
2024-11-17 12:43:51 INFO Train loss: 0.315611
2024-11-17 12:43:51 INFO ************ Epoch=2 end ************
2024-11-17 12:48:07 INFO [Metrics] AUC-ROC: 0.906331 - AUC-PR: 0.775088 - ACC: 0.841491 - Precision: 0.670857 - Recall: 0.718463 - F1: 0.693844 - MCC: 0.587704 - Logloss: 0.315320 - MSE: 0.101940 - RMSE: 0.319280 - COPC: 1.001674 - KLD: 0.707677
2024-11-17 12:48:07 INFO Save best model: monitor(max): 0.591011
2024-11-17 12:48:07 INFO --- 2570/2570 batches finished ---
2024-11-17 12:48:07 INFO Train loss: 0.315470
2024-11-17 12:48:07 INFO ************ Epoch=3 end ************
2024-11-17 12:52:18 INFO [Metrics] AUC-ROC: 0.906338 - AUC-PR: 0.775108 - ACC: 0.841502 - Precision: 0.670916 - Recall: 0.718362 - F1: 0.693829 - MCC: 0.587695 - Logloss: 0.315204 - MSE: 0.101921 - RMSE: 0.319251 - COPC: 0.990911 - KLD: 0.707332
2024-11-17 12:52:18 INFO Save best model: monitor(max): 0.591134
2024-11-17 12:52:18 INFO --- 2570/2570 batches finished ---
2024-11-17 12:52:18 INFO Train loss: 0.315366
2024-11-17 12:52:18 INFO ************ Epoch=4 end ************
2024-11-17 12:56:33 INFO [Metrics] AUC-ROC: 0.906349 - AUC-PR: 0.775086 - ACC: 0.841506 - Precision: 0.671139 - Recall: 0.717703 - F1: 0.693641 - MCC: 0.587505 - Logloss: 0.315130 - MSE: 0.101911 - RMSE: 0.319235 - COPC: 0.999936 - KLD: 0.707053
2024-11-17 12:56:33 INFO Save best model: monitor(max): 0.591219
2024-11-17 12:56:33 INFO --- 2570/2570 batches finished ---
2024-11-17 12:56:33 INFO Train loss: 0.315290
2024-11-17 12:56:33 INFO ************ Epoch=5 end ************
2024-11-17 13:00:48 INFO [Metrics] AUC-ROC: 0.906297 - AUC-PR: 0.775014 - ACC: 0.841501 - Precision: 0.671015 - Recall: 0.718047 - F1: 0.693735 - MCC: 0.587598 - Logloss: 0.315244 - MSE: 0.101928 - RMSE: 0.319262 - COPC: 1.007078 - KLD: 0.707449
2024-11-17 13:00:48 INFO Monitor(max) STOP: 0.591053 !
2024-11-17 13:00:48 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 13:00:48 INFO --- 2570/2570 batches finished ---
2024-11-17 13:00:49 INFO Train loss: 0.315253
2024-11-17 13:00:49 INFO ************ Epoch=6 end ************
2024-11-17 13:05:04 INFO [Metrics] AUC-ROC: 0.906360 - AUC-PR: 0.775140 - ACC: 0.841500 - Precision: 0.671037 - Recall: 0.717970 - F1: 0.693711 - MCC: 0.587573 - Logloss: 0.315056 - MSE: 0.101895 - RMSE: 0.319210 - COPC: 0.997759 - KLD: 0.706862
2024-11-17 13:05:04 INFO Save best model: monitor(max): 0.591304
2024-11-17 13:05:04 INFO --- 2570/2570 batches finished ---
2024-11-17 13:05:04 INFO Train loss: 0.314995
2024-11-17 13:05:04 INFO ************ Epoch=7 end ************
2024-11-17 13:09:21 INFO [Metrics] AUC-ROC: 0.906336 - AUC-PR: 0.775104 - ACC: 0.841498 - Precision: 0.670859 - Recall: 0.718517 - F1: 0.693870 - MCC: 0.587735 - Logloss: 0.315068 - MSE: 0.101899 - RMSE: 0.319216 - COPC: 1.000480 - KLD: 0.706900
2024-11-17 13:09:21 INFO Monitor(max) STOP: 0.591268 !
2024-11-17 13:09:21 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 13:09:21 INFO --- 2570/2570 batches finished ---
2024-11-17 13:09:21 INFO Train loss: 0.314942
2024-11-17 13:09:21 INFO ************ Epoch=8 end ************
2024-11-17 13:13:36 INFO [Metrics] AUC-ROC: 0.906344 - AUC-PR: 0.775109 - ACC: 0.841496 - Precision: 0.671079 - Recall: 0.717806 - F1: 0.693656 - MCC: 0.587514 - Logloss: 0.315061 - MSE: 0.101897 - RMSE: 0.319213 - COPC: 0.999932 - KLD: 0.706882
2024-11-17 13:13:36 INFO Monitor(max) STOP: 0.591283 !
2024-11-17 13:13:36 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 13:13:36 INFO Early stopping at epoch=9
2024-11-17 13:13:36 INFO --- 2570/2570 batches finished ---
2024-11-17 13:13:36 INFO Train loss: 0.314880
2024-11-17 13:13:36 INFO Training finished.
2024-11-17 13:13:36 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/PNN/Synthetic/PNN_model_seed2019.ckpt
2024-11-17 13:13:36 INFO Start evaluate model
2024-11-17 13:14:12 INFO [Metrics] AUC-ROC: 0.906360 - AUC-PR: 0.775140 - ACC: 0.841500 - Precision: 0.671037 - Recall: 0.717970 - F1: 0.693711 - MCC: 0.587573 - Logloss: 0.315056 - MSE: 0.101895 - RMSE: 0.319210 - COPC: 0.997759 - KLD: 0.706862
2024-11-17 13:14:12 INFO Start testing model
2024-11-17 13:14:49 INFO [Metrics] AUC-ROC: 0.906398 - AUC-PR: 0.775566 - ACC: 0.841471 - Precision: 0.671243 - Recall: 0.717105 - F1: 0.693416 - MCC: 0.587253 - Logloss: 0.314913 - MSE: 0.101826 - RMSE: 0.319102 - COPC: 0.998525 - KLD: 0.706828
