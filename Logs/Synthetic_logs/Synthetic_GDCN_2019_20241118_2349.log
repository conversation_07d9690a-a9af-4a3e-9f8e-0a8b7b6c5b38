2024-11-18 23:49:59 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='GDCN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=1000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-18 23:49:59 INFO Start process Synthetic !
2024-11-18 23:49:59 INFO Loading Synthetic dataset
2024-11-18 23:49:59 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-18 23:50:00 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-18 23:50:00 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-18 23:50:01 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-18 23:50:01 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-18 23:50:01 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-18 23:50:01 INFO Loading data done
2024-11-18 23:50:01 INFO Model: GDCN(
  (embedding): FeaturesEmbedding(
    (embed_dict): ModuleDict(
      (user_age): Embedding(22, 16)
      (item_price): Embedding(43, 16)
      (user_gender): Embedding(4, 16)
      (user_region): Embedding(334, 16)
      (device_type): Embedding(5, 16)
      (ad_category): Embedding(45, 16)
      (ad_placement): Embedding(8, 16)
      (hour_of_day): Embedding(25, 16)
      (day_of_week): Embedding(8, 16)
      (scene_type): Embedding(6, 16)
    )
  )
  (embedding_dropout): Dropout(p=0.0, inplace=False)
  (cross_net): GateCorssLayer(
    (w): ModuleList(
      (0-2): 3 x Linear(in_features=160, out_features=160, bias=False)
    )
    (wg): ModuleList(
      (0-2): 3 x Linear(in_features=160, out_features=160, bias=False)
    )
    (b): ParameterList(
        (0): Parameter containing: [torch.float32 of size 160]
        (1): Parameter containing: [torch.float32 of size 160]
        (2): Parameter containing: [torch.float32 of size 160]
    )
    (activation): Sigmoid()
  )
  (mlp): Sequential(
    (0): Linear(in_features=160, out_features=400, bias=True)
    (1): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): ReLU()
    (3): Dropout(p=0.5, inplace=False)
    (4): Linear(in_features=400, out_features=400, bias=True)
    (5): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (6): ReLU()
    (7): Dropout(p=0.5, inplace=False)
    (8): Linear(in_features=400, out_features=400, bias=True)
    (9): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (10): ReLU()
    (11): Dropout(p=0.5, inplace=False)
  )
  (fc): Linear(in_features=560, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-18 23:50:01 INFO Model parameters: 550241
2024-11-18 23:50:01 INFO Start training model
2024-11-18 23:50:01 INFO Start training: 25695 batches/epoch
2024-11-18 23:50:01 INFO ************ Epoch=1 start ************
2024-11-18 23:57:05 INFO [Metrics] AUC-ROC: 0.905946 - AUC-PR: 0.774133 - ACC: 0.841201 - Precision: 0.668877 - Recall: 0.722443 - F1: 0.694629 - MCC: 0.588342 - Logloss: 0.316021 - MSE: 0.102235 - RMSE: 0.319742 - COPC: 0.980419 - KLD: 0.708985
2024-11-18 23:57:05 INFO Save best model: monitor(max): 0.589925
2024-11-18 23:57:05 INFO --- 25695/25695 batches finished ---
2024-11-18 23:57:05 INFO Train loss: 0.317163
2024-11-18 23:57:05 INFO ************ Epoch=1 end ************
2024-11-19 00:04:08 INFO [Metrics] AUC-ROC: 0.906117 - AUC-PR: 0.774519 - ACC: 0.840670 - Precision: 0.681473 - Recall: 0.680974 - F1: 0.681223 - MCC: 0.575016 - Logloss: 0.315411 - MSE: 0.102017 - RMSE: 0.319401 - COPC: 1.003913 - KLD: 0.707710
2024-11-19 00:04:08 INFO Save best model: monitor(max): 0.590706
2024-11-19 00:04:08 INFO --- 25695/25695 batches finished ---
2024-11-19 00:04:09 INFO Train loss: 0.315495
2024-11-19 00:04:09 INFO ************ Epoch=2 end ************
2024-11-19 00:11:12 INFO [Metrics] AUC-ROC: 0.906224 - AUC-PR: 0.774815 - ACC: 0.841241 - Precision: 0.674483 - Recall: 0.705408 - F1: 0.689599 - MCC: 0.583281 - Logloss: 0.315310 - MSE: 0.101977 - RMSE: 0.319338 - COPC: 1.006888 - KLD: 0.707550
2024-11-19 00:11:12 INFO Save best model: monitor(max): 0.590915
2024-11-19 00:11:12 INFO --- 25695/25695 batches finished ---
2024-11-19 00:11:12 INFO Train loss: 0.315290
2024-11-19 00:11:12 INFO ************ Epoch=3 end ************
2024-11-19 00:18:18 INFO [Metrics] AUC-ROC: 0.906308 - AUC-PR: 0.774939 - ACC: 0.840611 - Precision: 0.695622 - Recall: 0.644417 - F1: 0.669041 - MCC: 0.564987 - Logloss: 0.315350 - MSE: 0.102002 - RMSE: 0.319377 - COPC: 1.011466 - KLD: 0.707464
2024-11-19 00:18:18 INFO Save best model: monitor(max): 0.590959
2024-11-19 00:18:18 INFO --- 25695/25695 batches finished ---
2024-11-19 00:18:18 INFO Train loss: 0.315188
2024-11-19 00:18:18 INFO ************ Epoch=4 end ************
2024-11-19 00:25:20 INFO [Metrics] AUC-ROC: 0.906186 - AUC-PR: 0.774655 - ACC: 0.840395 - Precision: 0.689311 - Recall: 0.658286 - F1: 0.673441 - MCC: 0.568168 - Logloss: 0.315513 - MSE: 0.102021 - RMSE: 0.319407 - COPC: 1.018450 - KLD: 0.708019
2024-11-19 00:25:20 INFO Monitor(max) STOP: 0.590673 !
2024-11-19 00:25:20 INFO Reduce learning rate on plateau: 0.000100
2024-11-19 00:25:20 INFO --- 25695/25695 batches finished ---
2024-11-19 00:25:20 INFO Train loss: 0.315100
2024-11-19 00:25:20 INFO ************ Epoch=5 end ************
2024-11-19 00:32:22 INFO [Metrics] AUC-ROC: 0.906252 - AUC-PR: 0.774824 - ACC: 0.841374 - Precision: 0.672912 - Recall: 0.711190 - F1: 0.691522 - MCC: 0.585274 - Logloss: 0.315213 - MSE: 0.101948 - RMSE: 0.319292 - COPC: 0.994783 - KLD: 0.707239
2024-11-19 00:32:22 INFO Save best model: monitor(max): 0.591039
2024-11-19 00:32:22 INFO --- 25695/25695 batches finished ---
2024-11-19 00:32:22 INFO Train loss: 0.314665
2024-11-19 00:32:22 INFO ************ Epoch=6 end ************
2024-11-19 00:39:26 INFO [Metrics] AUC-ROC: 0.906211 - AUC-PR: 0.774731 - ACC: 0.841084 - Precision: 0.675951 - Recall: 0.699831 - F1: 0.687684 - MCC: 0.581312 - Logloss: 0.315298 - MSE: 0.101971 - RMSE: 0.319329 - COPC: 0.997916 - KLD: 0.707467
2024-11-19 00:39:26 INFO Monitor(max) STOP: 0.590913 !
2024-11-19 00:39:26 INFO Reduce learning rate on plateau: 0.000010
2024-11-19 00:39:26 INFO --- 25695/25695 batches finished ---
2024-11-19 00:39:26 INFO Train loss: 0.314544
2024-11-19 00:39:26 INFO ************ Epoch=7 end ************
2024-11-19 00:46:30 INFO [Metrics] AUC-ROC: 0.906205 - AUC-PR: 0.774707 - ACC: 0.841066 - Precision: 0.676545 - Recall: 0.697953 - F1: 0.687082 - MCC: 0.580726 - Logloss: 0.315293 - MSE: 0.101971 - RMSE: 0.319329 - COPC: 0.999780 - KLD: 0.707458
2024-11-19 00:46:30 INFO Monitor(max) STOP: 0.590912 !
2024-11-19 00:46:30 INFO Reduce learning rate on plateau: 0.000001
2024-11-19 00:46:30 INFO Early stopping at epoch=8
2024-11-19 00:46:30 INFO --- 25695/25695 batches finished ---
2024-11-19 00:46:30 INFO Train loss: 0.314428
2024-11-19 00:46:30 INFO Training finished.
2024-11-19 00:46:30 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/GDCN/Synthetic/GDCN_model_seed2019.ckpt
2024-11-19 00:46:30 INFO Start evaluate model
2024-11-19 00:47:01 INFO [Metrics] AUC-ROC: 0.906252 - AUC-PR: 0.774824 - ACC: 0.841374 - Precision: 0.672912 - Recall: 0.711190 - F1: 0.691522 - MCC: 0.585274 - Logloss: 0.315213 - MSE: 0.101948 - RMSE: 0.319292 - COPC: 0.994783 - KLD: 0.707239
2024-11-19 00:47:02 INFO Start testing model
2024-11-19 00:47:32 INFO [Metrics] AUC-ROC: 0.906272 - AUC-PR: 0.775236 - ACC: 0.841269 - Precision: 0.673041 - Recall: 0.709980 - F1: 0.691017 - MCC: 0.584699 - Logloss: 0.315092 - MSE: 0.101884 - RMSE: 0.319193 - COPC: 0.995608 - KLD: 0.707284
