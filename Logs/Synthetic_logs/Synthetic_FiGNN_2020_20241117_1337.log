2024-11-17 13:37:07 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='FiGNN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-17 13:37:07 INFO Start process Synthetic !
2024-11-17 13:37:07 INFO Loading Synthetic dataset
2024-11-17 13:37:07 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 13:37:09 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 13:37:09 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 13:37:09 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 13:37:09 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 13:37:10 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 13:37:10 INFO Loading data done
2024-11-17 13:37:10 INFO Model: FiGNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fignn): FiGNN_Layer(
    (gnn): ModuleList(
      (0-3): 4 x GraphLayer()
    )
    (gru): GRUCell(16, 16)
    (leaky_relu): LeakyReLU(negative_slope=0.01)
    (W_attn): Linear(in_features=32, out_features=1, bias=False)
  )
  (fc): PredictionLayer(
    (mlp1): Linear(in_features=16, out_features=1, bias=False)
    (mlp2): Sequential(
      (0): Linear(in_features=160, out_features=10, bias=False)
      (1): Sigmoid()
    )
  )
  (final_activation): Sigmoid()
)
2024-11-17 13:37:10 INFO Model parameters: 31824
2024-11-17 13:37:10 INFO Start training model
2024-11-17 13:37:10 INFO Start training: 2570 batches/epoch
2024-11-17 13:37:10 INFO ************ Epoch=1 start ************
2024-11-17 13:40:59 INFO [Metrics] AUC-ROC: 0.906065 - AUC-PR: 0.774493 - ACC: 0.841406 - Precision: 0.671215 - Recall: 0.716674 - F1: 0.693200 - MCC: 0.586990 - Logloss: 0.316241 - MSE: 0.102178 - RMSE: 0.319653 - COPC: 0.986720 - KLD: 0.709927
2024-11-17 13:40:59 INFO Save best model: monitor(max): 0.589824
2024-11-17 13:40:59 INFO --- 2570/2570 batches finished ---
2024-11-17 13:40:59 INFO Train loss: 0.325625
2024-11-17 13:40:59 INFO ************ Epoch=1 end ************
2024-11-17 13:44:53 INFO [Metrics] AUC-ROC: 0.906126 - AUC-PR: 0.774693 - ACC: 0.840091 - Precision: 0.695892 - Recall: 0.640083 - F1: 0.666822 - MCC: 0.562713 - Logloss: 0.315786 - MSE: 0.102107 - RMSE: 0.319542 - COPC: 1.021886 - KLD: 0.708406
2024-11-17 13:44:53 INFO Save best model: monitor(max): 0.590340
2024-11-17 13:44:53 INFO --- 2570/2570 batches finished ---
2024-11-17 13:44:53 INFO Train loss: 0.315826
2024-11-17 13:44:53 INFO ************ Epoch=2 end ************
2024-11-17 13:48:52 INFO [Metrics] AUC-ROC: 0.906279 - AUC-PR: 0.775032 - ACC: 0.840935 - Precision: 0.687770 - Recall: 0.666161 - F1: 0.676793 - MCC: 0.571466 - Logloss: 0.315592 - MSE: 0.102047 - RMSE: 0.319448 - COPC: 1.022473 - KLD: 0.708138
2024-11-17 13:48:52 INFO Save best model: monitor(max): 0.590687
2024-11-17 13:48:52 INFO --- 2570/2570 batches finished ---
2024-11-17 13:48:52 INFO Train loss: 0.315507
2024-11-17 13:48:52 INFO ************ Epoch=3 end ************
2024-11-17 13:52:51 INFO [Metrics] AUC-ROC: 0.906273 - AUC-PR: 0.774960 - ACC: 0.841110 - Precision: 0.677016 - Recall: 0.696918 - F1: 0.686822 - MCC: 0.580513 - Logloss: 0.315488 - MSE: 0.102026 - RMSE: 0.319416 - COPC: 1.020069 - KLD: 0.707779
2024-11-17 13:52:51 INFO Save best model: monitor(max): 0.590785
2024-11-17 13:52:51 INFO --- 2570/2570 batches finished ---
2024-11-17 13:52:51 INFO Train loss: 0.315354
2024-11-17 13:52:51 INFO ************ Epoch=4 end ************
2024-11-17 13:56:51 INFO [Metrics] AUC-ROC: 0.906248 - AUC-PR: 0.774859 - ACC: 0.841388 - Precision: 0.670717 - Recall: 0.718091 - F1: 0.693596 - MCC: 0.587385 - Logloss: 0.315276 - MSE: 0.101973 - RMSE: 0.319331 - COPC: 0.996365 - KLD: 0.707386
2024-11-17 13:56:51 INFO Save best model: monitor(max): 0.590972
2024-11-17 13:56:51 INFO --- 2570/2570 batches finished ---
2024-11-17 13:56:52 INFO Train loss: 0.315267
2024-11-17 13:56:52 INFO ************ Epoch=5 end ************
2024-11-17 14:00:52 INFO [Metrics] AUC-ROC: 0.906258 - AUC-PR: 0.774888 - ACC: 0.841272 - Precision: 0.668302 - Recall: 0.724855 - F1: 0.695430 - MCC: 0.589225 - Logloss: 0.315226 - MSE: 0.101952 - RMSE: 0.319299 - COPC: 0.997105 - KLD: 0.707312
2024-11-17 14:00:52 INFO Save best model: monitor(max): 0.591032
2024-11-17 14:00:52 INFO --- 2570/2570 batches finished ---
2024-11-17 14:00:52 INFO Train loss: 0.315213
2024-11-17 14:00:52 INFO ************ Epoch=6 end ************
2024-11-17 14:04:54 INFO [Metrics] AUC-ROC: 0.906300 - AUC-PR: 0.774967 - ACC: 0.840548 - Precision: 0.691012 - Recall: 0.655137 - F1: 0.672596 - MCC: 0.567658 - Logloss: 0.315282 - MSE: 0.101975 - RMSE: 0.319336 - COPC: 1.003879 - KLD: 0.707405
2024-11-17 14:04:54 INFO Monitor(max) STOP: 0.591018 !
2024-11-17 14:04:54 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 14:04:54 INFO --- 2570/2570 batches finished ---
2024-11-17 14:04:54 INFO Train loss: 0.315162
2024-11-17 14:04:54 INFO ************ Epoch=7 end ************
2024-11-17 14:08:54 INFO [Metrics] AUC-ROC: 0.906362 - AUC-PR: 0.775150 - ACC: 0.841407 - Precision: 0.670749 - Recall: 0.718143 - F1: 0.693638 - MCC: 0.587439 - Logloss: 0.315089 - MSE: 0.101908 - RMSE: 0.319230 - COPC: 0.997919 - KLD: 0.706929
2024-11-17 14:08:54 INFO Save best model: monitor(max): 0.591273
2024-11-17 14:08:54 INFO --- 2570/2570 batches finished ---
2024-11-17 14:08:54 INFO Train loss: 0.314925
2024-11-17 14:08:54 INFO ************ Epoch=8 end ************
2024-11-17 14:13:19 INFO [Metrics] AUC-ROC: 0.906358 - AUC-PR: 0.775156 - ACC: 0.841427 - Precision: 0.671420 - Recall: 0.716206 - F1: 0.693091 - MCC: 0.586893 - Logloss: 0.315097 - MSE: 0.101910 - RMSE: 0.319233 - COPC: 0.998161 - KLD: 0.706957
2024-11-17 14:13:19 INFO Monitor(max) STOP: 0.591262 !
2024-11-17 14:13:19 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 14:13:19 INFO --- 2570/2570 batches finished ---
2024-11-17 14:13:19 INFO Train loss: 0.314907
2024-11-17 14:13:19 INFO ************ Epoch=9 end ************
2024-11-17 14:18:42 INFO [Metrics] AUC-ROC: 0.906354 - AUC-PR: 0.775136 - ACC: 0.841463 - Precision: 0.671062 - Recall: 0.717603 - F1: 0.693552 - MCC: 0.587387 - Logloss: 0.315084 - MSE: 0.101906 - RMSE: 0.319227 - COPC: 0.998473 - KLD: 0.706922
2024-11-17 14:18:42 INFO Monitor(max) STOP: 0.591270 !
2024-11-17 14:18:42 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 14:18:42 INFO Early stopping at epoch=10
2024-11-17 14:18:42 INFO --- 2570/2570 batches finished ---
2024-11-17 14:18:42 INFO Train loss: 0.314880
2024-11-17 14:18:42 INFO Training finished.
2024-11-17 14:18:42 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FiGNN/Synthetic/FiGNN_model_seed2020.ckpt
2024-11-17 14:18:42 INFO Start evaluate model
2024-11-17 14:19:20 INFO [Metrics] AUC-ROC: 0.906362 - AUC-PR: 0.775150 - ACC: 0.841407 - Precision: 0.670749 - Recall: 0.718143 - F1: 0.693638 - MCC: 0.587439 - Logloss: 0.315089 - MSE: 0.101908 - RMSE: 0.319230 - COPC: 0.997919 - KLD: 0.706929
2024-11-17 14:19:20 INFO Start testing model
2024-11-17 14:19:57 INFO [Metrics] AUC-ROC: 0.906367 - AUC-PR: 0.775458 - ACC: 0.841385 - Precision: 0.670984 - Recall: 0.717236 - F1: 0.693339 - MCC: 0.587120 - Logloss: 0.314965 - MSE: 0.101847 - RMSE: 0.319134 - COPC: 0.998714 - KLD: 0.706952
