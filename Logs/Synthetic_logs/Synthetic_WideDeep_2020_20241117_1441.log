2024-11-17 14:41:09 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='WideDeep', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-17 14:41:09 INFO Start process Synthetic !
2024-11-17 14:41:09 INFO Loading Synthetic dataset
2024-11-17 14:41:09 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 14:41:10 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 14:41:10 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 14:41:11 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 14:41:11 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 14:41:11 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 14:41:11 INFO Loading data done
2024-11-17 14:41:12 INFO Model: WideDeep(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (user_age): Embedding(22, 16)
        (item_price): Embedding(43, 16)
        (user_gender): Embedding(4, 16)
        (user_region): Embedding(334, 16)
        (device_type): Embedding(5, 16)
        (ad_category): Embedding(45, 16)
        (ad_placement): Embedding(8, 16)
        (hour_of_day): Embedding(25, 16)
        (day_of_week): Embedding(8, 16)
        (scene_type): Embedding(6, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (user_age): Embedding(22, 1, padding_idx=21)
        (item_price): Embedding(43, 1, padding_idx=42)
        (user_gender): Embedding(4, 1, padding_idx=3)
        (user_region): Embedding(334, 1, padding_idx=333)
        (device_type): Embedding(5, 1, padding_idx=4)
        (ad_category): Embedding(45, 1, padding_idx=44)
        (ad_placement): Embedding(8, 1, padding_idx=7)
        (hour_of_day): Embedding(25, 1, padding_idx=24)
        (day_of_week): Embedding(8, 1, padding_idx=7)
        (scene_type): Embedding(6, 1, padding_idx=5)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=160, out_features=1000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=1000, out_features=1000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=1000, out_features=1000, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.2, inplace=False)
      (9): Linear(in_features=1000, out_features=1000, bias=True)
      (10): ReLU()
      (11): Dropout(p=0.2, inplace=False)
      (12): Linear(in_features=1000, out_features=1000, bias=True)
      (13): ReLU()
      (14): Dropout(p=0.2, inplace=False)
      (15): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-17 14:41:12 INFO Model parameters: 4174501
2024-11-17 14:41:12 INFO Start training model
2024-11-17 14:41:12 INFO Start training: 2570 batches/epoch
2024-11-17 14:41:12 INFO ************ Epoch=1 start ************
2024-11-17 14:45:22 INFO [Metrics] AUC-ROC: 0.906299 - AUC-PR: 0.775074 - ACC: 0.841030 - Precision: 0.685085 - Recall: 0.673886 - F1: 0.679439 - MCC: 0.573791 - Logloss: 0.315302 - MSE: 0.101950 - RMSE: 0.319296 - COPC: 1.002429 - KLD: 0.707639
2024-11-17 14:45:22 INFO Save best model: monitor(max): 0.590997
2024-11-17 14:45:22 INFO --- 2570/2570 batches finished ---
2024-11-17 14:45:23 INFO Train loss: 0.318895
2024-11-17 14:45:23 INFO ************ Epoch=1 end ************
2024-11-17 14:49:38 INFO [Metrics] AUC-ROC: 0.906302 - AUC-PR: 0.774965 - ACC: 0.841359 - Precision: 0.673017 - Recall: 0.710750 - F1: 0.691369 - MCC: 0.585112 - Logloss: 0.315226 - MSE: 0.101926 - RMSE: 0.319259 - COPC: 1.003223 - KLD: 0.707397
2024-11-17 14:49:38 INFO Save best model: monitor(max): 0.591076
2024-11-17 14:49:38 INFO --- 2570/2570 batches finished ---
2024-11-17 14:49:38 INFO Train loss: 0.315540
2024-11-17 14:49:38 INFO ************ Epoch=2 end ************
2024-11-17 14:54:08 INFO [Metrics] AUC-ROC: 0.906338 - AUC-PR: 0.774951 - ACC: 0.840845 - Precision: 0.685276 - Recall: 0.672012 - F1: 0.678579 - MCC: 0.572879 - Logloss: 0.315432 - MSE: 0.101954 - RMSE: 0.319302 - COPC: 0.991119 - KLD: 0.708058
2024-11-17 14:54:08 INFO Monitor(max) STOP: 0.590906 !
2024-11-17 14:54:08 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 14:54:08 INFO --- 2570/2570 batches finished ---
2024-11-17 14:54:08 INFO Train loss: 0.315400
2024-11-17 14:54:08 INFO ************ Epoch=3 end ************
2024-11-17 14:59:03 INFO [Metrics] AUC-ROC: 0.906397 - AUC-PR: 0.775176 - ACC: 0.841512 - Precision: 0.671315 - Recall: 0.717198 - F1: 0.693498 - MCC: 0.587363 - Logloss: 0.315020 - MSE: 0.101889 - RMSE: 0.319200 - COPC: 1.000496 - KLD: 0.706743
2024-11-17 14:59:03 INFO Save best model: monitor(max): 0.591378
2024-11-17 14:59:03 INFO --- 2570/2570 batches finished ---
2024-11-17 14:59:03 INFO Train loss: 0.315102
2024-11-17 14:59:03 INFO ************ Epoch=4 end ************
2024-11-17 15:03:28 INFO [Metrics] AUC-ROC: 0.906402 - AUC-PR: 0.775211 - ACC: 0.841525 - Precision: 0.671810 - Recall: 0.715757 - F1: 0.693088 - MCC: 0.586952 - Logloss: 0.315025 - MSE: 0.101890 - RMSE: 0.319202 - COPC: 1.001714 - KLD: 0.706771
2024-11-17 15:03:28 INFO Monitor(max) STOP: 0.591377 !
2024-11-17 15:03:28 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 15:03:28 INFO --- 2570/2570 batches finished ---
2024-11-17 15:03:28 INFO Train loss: 0.315050
2024-11-17 15:03:28 INFO ************ Epoch=5 end ************
2024-11-17 15:07:55 INFO [Metrics] AUC-ROC: 0.906407 - AUC-PR: 0.775212 - ACC: 0.841528 - Precision: 0.671399 - Recall: 0.717058 - F1: 0.693478 - MCC: 0.587352 - Logloss: 0.315015 - MSE: 0.101887 - RMSE: 0.319197 - COPC: 0.999653 - KLD: 0.706737
2024-11-17 15:07:55 INFO Save best model: monitor(max): 0.591392
2024-11-17 15:07:55 INFO --- 2570/2570 batches finished ---
2024-11-17 15:07:55 INFO Train loss: 0.315018
2024-11-17 15:07:55 INFO ************ Epoch=6 end ************
2024-11-17 15:12:23 INFO [Metrics] AUC-ROC: 0.906409 - AUC-PR: 0.775214 - ACC: 0.841534 - Precision: 0.671439 - Recall: 0.716979 - F1: 0.693462 - MCC: 0.587339 - Logloss: 0.315016 - MSE: 0.101887 - RMSE: 0.319198 - COPC: 1.000546 - KLD: 0.706742
2024-11-17 15:12:23 INFO Save best model: monitor(max): 0.591393
2024-11-17 15:12:23 INFO --- 2570/2570 batches finished ---
2024-11-17 15:12:23 INFO Train loss: 0.314999
2024-11-17 15:12:23 INFO ************ Epoch=7 end ************
2024-11-17 15:16:12 INFO [Metrics] AUC-ROC: 0.906408 - AUC-PR: 0.775213 - ACC: 0.841534 - Precision: 0.671377 - Recall: 0.717179 - F1: 0.693523 - MCC: 0.587402 - Logloss: 0.315016 - MSE: 0.101887 - RMSE: 0.319197 - COPC: 1.000953 - KLD: 0.706741
2024-11-17 15:16:12 INFO Monitor(max) STOP: 0.591392 !
2024-11-17 15:16:12 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 15:16:12 INFO --- 2570/2570 batches finished ---
2024-11-17 15:16:12 INFO Train loss: 0.315001
2024-11-17 15:16:12 INFO ************ Epoch=8 end ************
2024-11-17 15:19:47 INFO [Metrics] AUC-ROC: 0.906407 - AUC-PR: 0.775213 - ACC: 0.841537 - Precision: 0.671381 - Recall: 0.717188 - F1: 0.693529 - MCC: 0.587410 - Logloss: 0.315014 - MSE: 0.101887 - RMSE: 0.319197 - COPC: 1.000338 - KLD: 0.706737
2024-11-17 15:19:47 INFO Monitor(max) STOP: 0.591393 !
2024-11-17 15:19:47 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 15:19:47 INFO Early stopping at epoch=9
2024-11-17 15:19:47 INFO --- 2570/2570 batches finished ---
2024-11-17 15:19:47 INFO Train loss: 0.314995
2024-11-17 15:19:47 INFO Training finished.
2024-11-17 15:19:47 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/WideDeep/Synthetic/WideDeep_model_seed2020.ckpt
2024-11-17 15:19:47 INFO Start evaluate model
2024-11-17 15:20:21 INFO [Metrics] AUC-ROC: 0.906409 - AUC-PR: 0.775214 - ACC: 0.841534 - Precision: 0.671439 - Recall: 0.716979 - F1: 0.693462 - MCC: 0.587339 - Logloss: 0.315016 - MSE: 0.101887 - RMSE: 0.319198 - COPC: 1.000546 - KLD: 0.706742
2024-11-17 15:20:21 INFO Start testing model
2024-11-17 15:20:55 INFO [Metrics] AUC-ROC: 0.906360 - AUC-PR: 0.775513 - ACC: 0.841485 - Precision: 0.671571 - Recall: 0.716189 - F1: 0.693163 - MCC: 0.587003 - Logloss: 0.314891 - MSE: 0.101821 - RMSE: 0.319095 - COPC: 1.001334 - KLD: 0.706776
