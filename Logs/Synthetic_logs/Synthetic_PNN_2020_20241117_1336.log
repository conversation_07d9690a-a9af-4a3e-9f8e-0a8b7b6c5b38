2024-11-17 13:37:00 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='PNN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-17 13:37:00 INFO Start process Synthetic !
2024-11-17 13:37:00 INFO Loading Synthetic dataset
2024-11-17 13:37:00 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 13:37:01 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 13:37:02 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 13:37:02 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 13:37:02 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 13:37:02 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 13:37:02 INFO Loading data done
2024-11-17 13:37:03 INFO Model: PNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=205, out_features=1000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=1000, out_features=1000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=1000, out_features=1, bias=True)
      (7): Sigmoid()
    )
  )
)
2024-11-17 13:37:03 INFO Model parameters: 1216001
2024-11-17 13:37:03 INFO Start training model
2024-11-17 13:37:03 INFO Start training: 2570 batches/epoch
2024-11-17 13:37:03 INFO ************ Epoch=1 start ************
2024-11-17 13:41:08 INFO [Metrics] AUC-ROC: 0.906250 - AUC-PR: 0.774875 - ACC: 0.841490 - Precision: 0.670902 - Recall: 0.718314 - F1: 0.693799 - MCC: 0.587657 - Logloss: 0.315438 - MSE: 0.101989 - RMSE: 0.319358 - COPC: 0.996129 - KLD: 0.708028
2024-11-17 13:41:08 INFO Save best model: monitor(max): 0.590812
2024-11-17 13:41:08 INFO --- 2570/2570 batches finished ---
2024-11-17 13:41:08 INFO Train loss: 0.319099
2024-11-17 13:41:08 INFO ************ Epoch=1 end ************
2024-11-17 13:45:17 INFO [Metrics] AUC-ROC: 0.906333 - AUC-PR: 0.775097 - ACC: 0.841495 - Precision: 0.670937 - Recall: 0.718244 - F1: 0.693785 - MCC: 0.587646 - Logloss: 0.315303 - MSE: 0.101964 - RMSE: 0.319318 - COPC: 1.000338 - KLD: 0.707523
2024-11-17 13:45:17 INFO Save best model: monitor(max): 0.591030
2024-11-17 13:45:17 INFO --- 2570/2570 batches finished ---
2024-11-17 13:45:17 INFO Train loss: 0.315626
2024-11-17 13:45:17 INFO ************ Epoch=2 end ************
2024-11-17 13:49:27 INFO [Metrics] AUC-ROC: 0.906349 - AUC-PR: 0.774958 - ACC: 0.841431 - Precision: 0.670167 - Recall: 0.720164 - F1: 0.694267 - MCC: 0.588103 - Logloss: 0.315551 - MSE: 0.102017 - RMSE: 0.319402 - COPC: 0.976891 - KLD: 0.707979
2024-11-17 13:49:27 INFO Monitor(max) STOP: 0.590798 !
2024-11-17 13:49:27 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 13:49:27 INFO --- 2570/2570 batches finished ---
2024-11-17 13:49:27 INFO Train loss: 0.315495
2024-11-17 13:49:27 INFO ************ Epoch=3 end ************
2024-11-17 13:53:36 INFO [Metrics] AUC-ROC: 0.906371 - AUC-PR: 0.775173 - ACC: 0.841508 - Precision: 0.670918 - Recall: 0.718408 - F1: 0.693852 - MCC: 0.587722 - Logloss: 0.315064 - MSE: 0.101899 - RMSE: 0.319216 - COPC: 0.993333 - KLD: 0.706846
2024-11-17 13:53:36 INFO Save best model: monitor(max): 0.591307
2024-11-17 13:53:36 INFO --- 2570/2570 batches finished ---
2024-11-17 13:53:36 INFO Train loss: 0.315096
2024-11-17 13:53:36 INFO ************ Epoch=4 end ************
2024-11-17 13:57:45 INFO [Metrics] AUC-ROC: 0.906387 - AUC-PR: 0.775127 - ACC: 0.841501 - Precision: 0.670086 - Recall: 0.720966 - F1: 0.694596 - MCC: 0.588486 - Logloss: 0.315071 - MSE: 0.101901 - RMSE: 0.319219 - COPC: 0.995321 - KLD: 0.706892
2024-11-17 13:57:45 INFO Save best model: monitor(max): 0.591315
2024-11-17 13:57:45 INFO --- 2570/2570 batches finished ---
2024-11-17 13:57:46 INFO Train loss: 0.315043
2024-11-17 13:57:46 INFO ************ Epoch=5 end ************
2024-11-17 14:01:50 INFO [Metrics] AUC-ROC: 0.906370 - AUC-PR: 0.775103 - ACC: 0.841512 - Precision: 0.671228 - Recall: 0.717467 - F1: 0.693578 - MCC: 0.587444 - Logloss: 0.315074 - MSE: 0.101900 - RMSE: 0.319219 - COPC: 1.005383 - KLD: 0.706900
2024-11-17 14:01:50 INFO Monitor(max) STOP: 0.591297 !
2024-11-17 14:01:50 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 14:01:50 INFO --- 2570/2570 batches finished ---
2024-11-17 14:01:50 INFO Train loss: 0.315010
2024-11-17 14:01:50 INFO ************ Epoch=6 end ************
2024-11-17 14:05:58 INFO [Metrics] AUC-ROC: 0.906378 - AUC-PR: 0.775145 - ACC: 0.841525 - Precision: 0.671063 - Recall: 0.718086 - F1: 0.693779 - MCC: 0.587658 - Logloss: 0.315053 - MSE: 0.101894 - RMSE: 0.319209 - COPC: 0.998049 - KLD: 0.706862
2024-11-17 14:05:58 INFO Save best model: monitor(max): 0.591324
2024-11-17 14:05:58 INFO --- 2570/2570 batches finished ---
2024-11-17 14:05:59 INFO Train loss: 0.314946
2024-11-17 14:05:59 INFO ************ Epoch=7 end ************
2024-11-17 14:10:12 INFO [Metrics] AUC-ROC: 0.906373 - AUC-PR: 0.775141 - ACC: 0.841524 - Precision: 0.671028 - Recall: 0.718193 - F1: 0.693810 - MCC: 0.587689 - Logloss: 0.315054 - MSE: 0.101895 - RMSE: 0.319209 - COPC: 1.000126 - KLD: 0.706868
2024-11-17 14:10:12 INFO Monitor(max) STOP: 0.591319 !
2024-11-17 14:10:12 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 14:10:12 INFO --- 2570/2570 batches finished ---
2024-11-17 14:10:12 INFO Train loss: 0.314923
2024-11-17 14:10:12 INFO ************ Epoch=8 end ************
2024-11-17 14:14:37 INFO [Metrics] AUC-ROC: 0.906373 - AUC-PR: 0.775142 - ACC: 0.841528 - Precision: 0.671077 - Recall: 0.718063 - F1: 0.693775 - MCC: 0.587656 - Logloss: 0.315053 - MSE: 0.101894 - RMSE: 0.319209 - COPC: 1.000027 - KLD: 0.706861
2024-11-17 14:14:37 INFO Monitor(max) STOP: 0.591320 !
2024-11-17 14:14:37 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 14:14:37 INFO Early stopping at epoch=9
2024-11-17 14:14:37 INFO --- 2570/2570 batches finished ---
2024-11-17 14:14:38 INFO Train loss: 0.314920
2024-11-17 14:14:38 INFO Training finished.
2024-11-17 14:14:38 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/PNN/Synthetic/PNN_model_seed2020.ckpt
2024-11-17 14:14:38 INFO Start evaluate model
2024-11-17 14:15:19 INFO [Metrics] AUC-ROC: 0.906378 - AUC-PR: 0.775145 - ACC: 0.841525 - Precision: 0.671063 - Recall: 0.718086 - F1: 0.693779 - MCC: 0.587658 - Logloss: 0.315053 - MSE: 0.101894 - RMSE: 0.319209 - COPC: 0.998049 - KLD: 0.706862
2024-11-17 14:15:19 INFO Start testing model
2024-11-17 14:16:25 INFO [Metrics] AUC-ROC: 0.906411 - AUC-PR: 0.775502 - ACC: 0.841486 - Precision: 0.671250 - Recall: 0.717193 - F1: 0.693462 - MCC: 0.587308 - Logloss: 0.314919 - MSE: 0.101829 - RMSE: 0.319106 - COPC: 0.998818 - KLD: 0.706854
