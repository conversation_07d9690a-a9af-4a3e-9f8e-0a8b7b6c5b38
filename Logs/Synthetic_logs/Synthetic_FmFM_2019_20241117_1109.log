2024-11-17 11:09:25 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Syntheti', model_name='FmFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-17 11:09:36 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='FmFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-17 11:09:36 INFO Start process Synthetic !
2024-11-17 11:09:36 INFO Loading Synthetic dataset
2024-11-17 11:09:36 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 11:09:38 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 11:09:38 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 11:09:38 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 11:09:38 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 11:09:38 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 11:09:38 INFO Loading data done
2024-11-17 11:09:39 INFO Model: FmFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (user_age): Embedding(22, 16)
        (item_price): Embedding(43, 16)
        (user_gender): Embedding(4, 16)
        (user_region): Embedding(334, 16)
        (device_type): Embedding(5, 16)
        (ad_category): Embedding(45, 16)
        (ad_placement): Embedding(8, 16)
        (hour_of_day): Embedding(25, 16)
        (day_of_week): Embedding(8, 16)
        (scene_type): Embedding(6, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (user_age): Embedding(22, 1, padding_idx=21)
        (item_price): Embedding(43, 1, padding_idx=42)
        (user_gender): Embedding(4, 1, padding_idx=3)
        (user_region): Embedding(334, 1, padding_idx=333)
        (device_type): Embedding(5, 1, padding_idx=4)
        (ad_category): Embedding(45, 1, padding_idx=44)
        (ad_placement): Embedding(8, 1, padding_idx=7)
        (hour_of_day): Embedding(25, 1, padding_idx=24)
        (day_of_week): Embedding(8, 1, padding_idx=7)
        (scene_type): Embedding(6, 1, padding_idx=5)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (final_activation): Sigmoid()
)
2024-11-17 11:09:39 INFO Model parameters: 20020
2024-11-17 11:09:39 INFO Start training model
2024-11-17 11:09:39 INFO Start training: 2570 batches/epoch
2024-11-17 11:09:39 INFO ************ Epoch=1 start ************
2024-11-17 11:13:44 INFO [Metrics] AUC-ROC: 0.905377 - AUC-PR: 0.772500 - ACC: 0.839931 - Precision: 0.677028 - Recall: 0.687868 - F1: 0.682405 - MCC: 0.575449 - Logloss: 0.321546 - MSE: 0.103364 - RMSE: 0.321502 - COPC: 0.988126 - KLD: 0.719771
2024-11-17 11:13:44 INFO Save best model: monitor(max): 0.583830
2024-11-17 11:13:44 INFO --- 2570/2570 batches finished ---
2024-11-17 11:13:44 INFO Train loss: 0.329336
2024-11-17 11:13:44 INFO ************ Epoch=1 end ************
2024-11-17 11:17:45 INFO [Metrics] AUC-ROC: 0.905258 - AUC-PR: 0.772088 - ACC: 0.839714 - Precision: 0.684129 - Recall: 0.666663 - F1: 0.675283 - MCC: 0.568986 - Logloss: 0.321530 - MSE: 0.103356 - RMSE: 0.321491 - COPC: 0.999567 - KLD: 0.719936
2024-11-17 11:17:45 INFO Monitor(max) STOP: 0.583729 !
2024-11-17 11:17:45 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 11:17:45 INFO --- 2570/2570 batches finished ---
2024-11-17 11:17:45 INFO Train loss: 0.321515
2024-11-17 11:17:45 INFO ************ Epoch=2 end ************
2024-11-17 11:21:47 INFO [Metrics] AUC-ROC: 0.905499 - AUC-PR: 0.772922 - ACC: 0.839324 - Precision: 0.692122 - Recall: 0.643578 - F1: 0.666968 - MCC: 0.561917 - Logloss: 0.321251 - MSE: 0.103294 - RMSE: 0.321394 - COPC: 0.997177 - KLD: 0.719159
2024-11-17 11:21:47 INFO Save best model: monitor(max): 0.584248
2024-11-17 11:21:47 INFO --- 2570/2570 batches finished ---
2024-11-17 11:21:47 INFO Train loss: 0.321226
2024-11-17 11:21:47 INFO ************ Epoch=3 end ************
2024-11-17 11:25:49 INFO [Metrics] AUC-ROC: 0.905497 - AUC-PR: 0.772852 - ACC: 0.839124 - Precision: 0.695238 - Recall: 0.634737 - F1: 0.663611 - MCC: 0.559180 - Logloss: 0.321243 - MSE: 0.103292 - RMSE: 0.321390 - COPC: 0.998531 - KLD: 0.719144
2024-11-17 11:25:49 INFO Save best model: monitor(max): 0.584254
2024-11-17 11:25:49 INFO --- 2570/2570 batches finished ---
2024-11-17 11:25:49 INFO Train loss: 0.321197
2024-11-17 11:25:49 INFO ************ Epoch=4 end ************
2024-11-17 11:29:52 INFO [Metrics] AUC-ROC: 0.905470 - AUC-PR: 0.772845 - ACC: 0.838975 - Precision: 0.700172 - Recall: 0.622441 - F1: 0.659022 - MCC: 0.555742 - Logloss: 0.321249 - MSE: 0.103307 - RMSE: 0.321414 - COPC: 1.003167 - KLD: 0.719180
2024-11-17 11:29:52 INFO Monitor(max) STOP: 0.584221 !
2024-11-17 11:29:52 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 11:29:52 INFO --- 2570/2570 batches finished ---
2024-11-17 11:29:52 INFO Train loss: 0.321193
2024-11-17 11:29:52 INFO ************ Epoch=5 end ************
2024-11-17 11:33:53 INFO [Metrics] AUC-ROC: 0.905493 - AUC-PR: 0.772931 - ACC: 0.838990 - Precision: 0.699958 - Recall: 0.623023 - F1: 0.659253 - MCC: 0.555921 - Logloss: 0.321231 - MSE: 0.103290 - RMSE: 0.321388 - COPC: 0.999503 - KLD: 0.719118
2024-11-17 11:33:53 INFO Save best model: monitor(max): 0.584261
2024-11-17 11:33:53 INFO --- 2570/2570 batches finished ---
2024-11-17 11:33:53 INFO Train loss: 0.321161
2024-11-17 11:33:53 INFO ************ Epoch=6 end ************
2024-11-17 11:37:52 INFO [Metrics] AUC-ROC: 0.905487 - AUC-PR: 0.772908 - ACC: 0.838977 - Precision: 0.700019 - Recall: 0.622798 - F1: 0.659155 - MCC: 0.555837 - Logloss: 0.321229 - MSE: 0.103283 - RMSE: 0.321376 - COPC: 0.998873 - KLD: 0.719111
2024-11-17 11:37:52 INFO Monitor(max) STOP: 0.584257 !
2024-11-17 11:37:52 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 11:37:52 INFO --- 2570/2570 batches finished ---
2024-11-17 11:37:52 INFO Train loss: 0.321155
2024-11-17 11:37:52 INFO ************ Epoch=7 end ************
2024-11-17 11:41:43 INFO [Metrics] AUC-ROC: 0.905493 - AUC-PR: 0.772907 - ACC: 0.839000 - Precision: 0.699220 - Recall: 0.624741 - F1: 0.659886 - MCC: 0.556372 - Logloss: 0.321228 - MSE: 0.103287 - RMSE: 0.321382 - COPC: 1.000096 - KLD: 0.719119
2024-11-17 11:41:43 INFO Save best model: monitor(max): 0.584266
2024-11-17 11:41:43 INFO --- 2570/2570 batches finished ---
2024-11-17 11:41:43 INFO Train loss: 0.321149
2024-11-17 11:41:43 INFO ************ Epoch=8 end ************
2024-11-17 11:45:35 INFO [Metrics] AUC-ROC: 0.905494 - AUC-PR: 0.772912 - ACC: 0.839046 - Precision: 0.698291 - Recall: 0.627162 - F1: 0.660818 - MCC: 0.557083 - Logloss: 0.321227 - MSE: 0.103287 - RMSE: 0.321383 - COPC: 0.999486 - KLD: 0.719114
2024-11-17 11:45:35 INFO Save best model: monitor(max): 0.584267
2024-11-17 11:45:35 INFO --- 2570/2570 batches finished ---
2024-11-17 11:45:35 INFO Train loss: 0.321150
2024-11-17 11:45:35 INFO ************ Epoch=9 end ************
2024-11-17 11:49:25 INFO [Metrics] AUC-ROC: 0.905496 - AUC-PR: 0.772912 - ACC: 0.839076 - Precision: 0.697727 - Recall: 0.628653 - F1: 0.661392 - MCC: 0.557526 - Logloss: 0.321227 - MSE: 0.103287 - RMSE: 0.321383 - COPC: 0.999494 - KLD: 0.719114
2024-11-17 11:49:25 INFO Save best model: monitor(max): 0.584269
2024-11-17 11:49:25 INFO --- 2570/2570 batches finished ---
2024-11-17 11:49:25 INFO Train loss: 0.321150
2024-11-17 11:49:25 INFO ************ Epoch=10 end ************
2024-11-17 11:53:14 INFO [Metrics] AUC-ROC: 0.905497 - AUC-PR: 0.772915 - ACC: 0.839069 - Precision: 0.697731 - Recall: 0.628588 - F1: 0.661358 - MCC: 0.557492 - Logloss: 0.321227 - MSE: 0.103287 - RMSE: 0.321383 - COPC: 0.999331 - KLD: 0.719112
2024-11-17 11:53:14 INFO Monitor(max) STOP: 0.584269 !
2024-11-17 11:53:14 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 11:53:14 INFO --- 2570/2570 batches finished ---
2024-11-17 11:53:14 INFO Train loss: 0.321151
2024-11-17 11:53:14 INFO ************ Epoch=11 end ************
2024-11-17 11:57:01 INFO [Metrics] AUC-ROC: 0.905497 - AUC-PR: 0.772916 - ACC: 0.839061 - Precision: 0.698241 - Recall: 0.627378 - F1: 0.660915 - MCC: 0.557170 - Logloss: 0.321227 - MSE: 0.103288 - RMSE: 0.321384 - COPC: 0.999801 - KLD: 0.719115
2024-11-17 11:57:01 INFO Save best model: monitor(max): 0.584270
2024-11-17 11:57:01 INFO --- 2570/2570 batches finished ---
2024-11-17 11:57:01 INFO Train loss: 0.321147
2024-11-17 11:57:01 INFO ************ Epoch=12 end ************
2024-11-17 12:00:59 INFO [Metrics] AUC-ROC: 0.905497 - AUC-PR: 0.772918 - ACC: 0.839067 - Precision: 0.697941 - Recall: 0.628098 - F1: 0.661180 - MCC: 0.557364 - Logloss: 0.321227 - MSE: 0.103287 - RMSE: 0.321383 - COPC: 0.999464 - KLD: 0.719112
2024-11-17 12:00:59 INFO Monitor(max) STOP: 0.584270 !
2024-11-17 12:00:59 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 12:00:59 INFO --- 2570/2570 batches finished ---
2024-11-17 12:00:59 INFO Train loss: 0.321149
2024-11-17 12:00:59 INFO ************ Epoch=13 end ************
2024-11-17 12:05:01 INFO [Metrics] AUC-ROC: 0.905498 - AUC-PR: 0.772919 - ACC: 0.839060 - Precision: 0.698109 - Recall: 0.627672 - F1: 0.661019 - MCC: 0.557242 - Logloss: 0.321227 - MSE: 0.103288 - RMSE: 0.321384 - COPC: 0.999649 - KLD: 0.719113
2024-11-17 12:05:01 INFO Monitor(max) STOP: 0.584271 !
2024-11-17 12:05:01 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 12:05:01 INFO Early stopping at epoch=14
2024-11-17 12:05:01 INFO --- 2570/2570 batches finished ---
2024-11-17 12:05:01 INFO Train loss: 0.321153
2024-11-17 12:05:01 INFO Training finished.
2024-11-17 12:05:01 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FmFM/Synthetic/FmFM_model_seed2019.ckpt
2024-11-17 12:05:01 INFO Start evaluate model
2024-11-17 12:05:38 INFO [Metrics] AUC-ROC: 0.905497 - AUC-PR: 0.772916 - ACC: 0.839061 - Precision: 0.698241 - Recall: 0.627378 - F1: 0.660915 - MCC: 0.557170 - Logloss: 0.321227 - MSE: 0.103288 - RMSE: 0.321384 - COPC: 0.999801 - KLD: 0.719115
2024-11-17 12:05:38 INFO Start testing model
2024-11-17 12:06:14 INFO [Metrics] AUC-ROC: 0.905511 - AUC-PR: 0.773407 - ACC: 0.839328 - Precision: 0.699364 - Recall: 0.626723 - F1: 0.661054 - MCC: 0.557612 - Logloss: 0.321154 - MSE: 0.103243 - RMSE: 0.321315 - COPC: 1.000791 - KLD: 0.719342
