2024-11-19 01:39:47 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='GDCN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=1000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-19 01:39:47 INFO Start process Synthetic !
2024-11-19 01:39:47 INFO Loading Synthetic dataset
2024-11-19 01:39:47 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-19 01:39:48 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-19 01:39:48 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-19 01:39:49 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-19 01:39:49 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-19 01:39:49 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-19 01:39:49 INFO Loading data done
2024-11-19 01:39:49 INFO Model: GDCN(
  (embedding): FeaturesEmbedding(
    (embed_dict): ModuleDict(
      (user_age): Embedding(22, 16)
      (item_price): Embedding(43, 16)
      (user_gender): Embedding(4, 16)
      (user_region): Embedding(334, 16)
      (device_type): Embedding(5, 16)
      (ad_category): Embedding(45, 16)
      (ad_placement): Embedding(8, 16)
      (hour_of_day): Embedding(25, 16)
      (day_of_week): Embedding(8, 16)
      (scene_type): Embedding(6, 16)
    )
  )
  (embedding_dropout): Dropout(p=0.0, inplace=False)
  (cross_net): GateCorssLayer(
    (w): ModuleList(
      (0-2): 3 x Linear(in_features=160, out_features=160, bias=False)
    )
    (wg): ModuleList(
      (0-2): 3 x Linear(in_features=160, out_features=160, bias=False)
    )
    (b): ParameterList(
        (0): Parameter containing: [torch.float32 of size 160]
        (1): Parameter containing: [torch.float32 of size 160]
        (2): Parameter containing: [torch.float32 of size 160]
    )
    (activation): Sigmoid()
  )
  (mlp): Sequential(
    (0): Linear(in_features=160, out_features=400, bias=True)
    (1): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): ReLU()
    (3): Dropout(p=0.5, inplace=False)
    (4): Linear(in_features=400, out_features=400, bias=True)
    (5): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (6): ReLU()
    (7): Dropout(p=0.5, inplace=False)
    (8): Linear(in_features=400, out_features=400, bias=True)
    (9): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (10): ReLU()
    (11): Dropout(p=0.5, inplace=False)
  )
  (fc): Linear(in_features=560, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-19 01:39:49 INFO Model parameters: 550241
2024-11-19 01:39:49 INFO Start training model
2024-11-19 01:39:50 INFO Start training: 25695 batches/epoch
2024-11-19 01:39:50 INFO ************ Epoch=1 start ************
2024-11-19 01:46:51 INFO [Metrics] AUC-ROC: 0.906176 - AUC-PR: 0.774643 - ACC: 0.840913 - Precision: 0.678391 - Recall: 0.691452 - F1: 0.684859 - MCC: 0.578520 - Logloss: 0.315580 - MSE: 0.102074 - RMSE: 0.319489 - COPC: 0.986544 - KLD: 0.708121
2024-11-19 01:46:51 INFO Save best model: monitor(max): 0.590596
2024-11-19 01:46:51 INFO --- 25695/25695 batches finished ---
2024-11-19 01:46:51 INFO Train loss: 0.316963
2024-11-19 01:46:51 INFO ************ Epoch=1 end ************
2024-11-19 01:54:06 INFO [Metrics] AUC-ROC: 0.906229 - AUC-PR: 0.774802 - ACC: 0.840851 - Precision: 0.683806 - Recall: 0.675976 - F1: 0.679868 - MCC: 0.573993 - Logloss: 0.315381 - MSE: 0.102007 - RMSE: 0.319386 - COPC: 1.013470 - KLD: 0.707580
2024-11-19 01:54:06 INFO Save best model: monitor(max): 0.590848
2024-11-19 01:54:06 INFO --- 25695/25695 batches finished ---
2024-11-19 01:54:06 INFO Train loss: 0.315452
2024-11-19 01:54:06 INFO ************ Epoch=2 end ************
2024-11-19 02:01:14 INFO [Metrics] AUC-ROC: 0.906280 - AUC-PR: 0.774855 - ACC: 0.840436 - Precision: 0.693542 - Recall: 0.648139 - F1: 0.670072 - MCC: 0.565567 - Logloss: 0.315350 - MSE: 0.102002 - RMSE: 0.319378 - COPC: 1.015609 - KLD: 0.707431
2024-11-19 02:01:14 INFO Save best model: monitor(max): 0.590930
2024-11-19 02:01:14 INFO --- 25695/25695 batches finished ---
2024-11-19 02:01:14 INFO Train loss: 0.315270
2024-11-19 02:01:14 INFO ************ Epoch=3 end ************
2024-11-19 02:08:18 INFO [Metrics] AUC-ROC: 0.906118 - AUC-PR: 0.774578 - ACC: 0.841175 - Precision: 0.669620 - Recall: 0.719873 - F1: 0.693838 - MCC: 0.587503 - Logloss: 0.315437 - MSE: 0.102016 - RMSE: 0.319399 - COPC: 0.991506 - KLD: 0.707700
2024-11-19 02:08:18 INFO Monitor(max) STOP: 0.590680 !
2024-11-19 02:08:18 INFO Reduce learning rate on plateau: 0.000100
2024-11-19 02:08:18 INFO --- 25695/25695 batches finished ---
2024-11-19 02:08:18 INFO Train loss: 0.315174
2024-11-19 02:08:18 INFO ************ Epoch=4 end ************
2024-11-19 02:15:57 INFO [Metrics] AUC-ROC: 0.906321 - AUC-PR: 0.774951 - ACC: 0.841323 - Precision: 0.671765 - Recall: 0.714316 - F1: 0.692388 - MCC: 0.586111 - Logloss: 0.315154 - MSE: 0.101927 - RMSE: 0.319260 - COPC: 0.997661 - KLD: 0.707117
2024-11-19 02:15:57 INFO Save best model: monitor(max): 0.591167
2024-11-19 02:15:57 INFO --- 25695/25695 batches finished ---
2024-11-19 02:15:57 INFO Train loss: 0.314743
2024-11-19 02:15:57 INFO ************ Epoch=5 end ************
2024-11-19 02:23:07 INFO [Metrics] AUC-ROC: 0.906275 - AUC-PR: 0.774874 - ACC: 0.841315 - Precision: 0.674170 - Recall: 0.706916 - F1: 0.690155 - MCC: 0.583878 - Logloss: 0.315211 - MSE: 0.101947 - RMSE: 0.319292 - COPC: 0.999737 - KLD: 0.707251
2024-11-19 02:23:07 INFO Monitor(max) STOP: 0.591064 !
2024-11-19 02:23:07 INFO Reduce learning rate on plateau: 0.000010
2024-11-19 02:23:07 INFO --- 25695/25695 batches finished ---
2024-11-19 02:23:07 INFO Train loss: 0.314643
2024-11-19 02:23:07 INFO ************ Epoch=6 end ************
2024-11-19 02:30:11 INFO [Metrics] AUC-ROC: 0.906267 - AUC-PR: 0.774855 - ACC: 0.841268 - Precision: 0.673939 - Recall: 0.707245 - F1: 0.690190 - MCC: 0.583880 - Logloss: 0.315212 - MSE: 0.101946 - RMSE: 0.319290 - COPC: 1.000132 - KLD: 0.707254
2024-11-19 02:30:11 INFO Monitor(max) STOP: 0.591054 !
2024-11-19 02:30:11 INFO Reduce learning rate on plateau: 0.000001
2024-11-19 02:30:11 INFO Early stopping at epoch=7
2024-11-19 02:30:11 INFO --- 25695/25695 batches finished ---
2024-11-19 02:30:12 INFO Train loss: 0.314536
2024-11-19 02:30:12 INFO Training finished.
2024-11-19 02:30:12 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/GDCN/Synthetic/GDCN_model_seed2020.ckpt
2024-11-19 02:30:12 INFO Start evaluate model
2024-11-19 02:30:42 INFO [Metrics] AUC-ROC: 0.906321 - AUC-PR: 0.774951 - ACC: 0.841323 - Precision: 0.671765 - Recall: 0.714316 - F1: 0.692388 - MCC: 0.586111 - Logloss: 0.315154 - MSE: 0.101927 - RMSE: 0.319260 - COPC: 0.997661 - KLD: 0.707117
2024-11-19 02:30:43 INFO Start testing model
2024-11-19 02:31:13 INFO [Metrics] AUC-ROC: 0.906331 - AUC-PR: 0.775406 - ACC: 0.841312 - Precision: 0.672035 - Recall: 0.713402 - F1: 0.692101 - MCC: 0.585815 - Logloss: 0.315035 - MSE: 0.101862 - RMSE: 0.319158 - COPC: 0.998406 - KLD: 0.707171
