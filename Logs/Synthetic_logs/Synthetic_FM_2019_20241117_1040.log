2024-11-17 10:40:50 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='FM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-17 10:40:50 INFO Start process Synthetic !
2024-11-17 10:40:50 INFO Start get feature encoder and feature map
2024-11-17 10:41:13 INFO Reading training data from /data/ctr/Synthetic/train.csv
2024-11-17 10:41:25 INFO Fill NaN done!
2024-11-17 10:42:54 INFO Process categorical column: user_age 
2024-11-17 10:42:58 INFO Process categorical column: item_price 
2024-11-17 10:43:02 INFO Process categorical column: user_gender 
2024-11-17 10:43:04 INFO Process categorical column: user_region 
2024-11-17 10:43:06 INFO Process categorical column: device_type 
2024-11-17 10:43:09 INFO Process categorical column: ad_category 
2024-11-17 10:43:11 INFO Process categorical column: ad_placement 
2024-11-17 10:43:13 INFO Process categorical column: hour_of_day 
2024-11-17 10:43:15 INFO Process categorical column: day_of_week 
2024-11-17 10:43:17 INFO Process categorical column: scene_type 
2024-11-17 10:43:19 INFO Set feature index
2024-11-17 10:43:19 INFO Pickle feature_encode: /data/ctr/Synthetic/feature_encoder.pkl
2024-11-17 10:43:19 INFO Save feature_map to json: /data/ctr/Synthetic/feature_map.json
2024-11-17 10:43:19 INFO Loading Synthetic dataset
2024-11-17 10:43:43 INFO Start process data for training, validation or testing
2024-11-17 10:43:55 INFO Fill NaN done!
2024-11-17 10:45:24 INFO Transform feature
2024-11-17 10:46:02 INFO Saving h5 data at /data/ctr/Synthetic/train.h5
2024-11-17 10:46:06 INFO Start process data for training, validation or testing
2024-11-17 10:46:08 INFO Fill NaN done!
2024-11-17 10:46:19 INFO Transform feature
2024-11-17 10:46:24 INFO Saving h5 data at /data/ctr/Synthetic/valid.h5
2024-11-17 10:46:27 INFO Start process data for training, validation or testing
2024-11-17 10:46:28 INFO Fill NaN done!
2024-11-17 10:46:39 INFO Transform feature
2024-11-17 10:46:44 INFO Saving h5 data at /data/ctr/Synthetic/test.h5
2024-11-17 10:46:45 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 10:46:45 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 10:46:45 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 10:46:45 INFO Loading data done
2024-11-17 10:46:45 INFO Model: FM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fm_layer): FM_Layer(
    (inner_product_layer): InnerProductLayer()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (user_age): Embedding(22, 1, padding_idx=21)
          (item_price): Embedding(43, 1, padding_idx=42)
          (user_gender): Embedding(4, 1, padding_idx=3)
          (user_region): Embedding(334, 1, padding_idx=333)
          (device_type): Embedding(5, 1, padding_idx=4)
          (ad_category): Embedding(45, 1, padding_idx=44)
          (ad_placement): Embedding(8, 1, padding_idx=7)
          (hour_of_day): Embedding(25, 1, padding_idx=24)
          (day_of_week): Embedding(8, 1, padding_idx=7)
          (scene_type): Embedding(6, 1, padding_idx=5)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
    (final_activation): Sigmoid()
  )
)
2024-11-17 10:46:45 INFO Model parameters: 8501
2024-11-17 10:46:45 INFO Start training model
2024-11-17 10:46:45 INFO Start training: 2570 batches/epoch
2024-11-17 10:46:45 INFO ************ Epoch=1 start ************
2024-11-17 10:50:00 INFO [Metrics] AUC-ROC: 0.904989 - AUC-PR: 0.771852 - ACC: 0.839684 - Precision: 0.690563 - Recall: 0.649993 - F1: 0.669664 - MCC: 0.564401 - Logloss: 0.322853 - MSE: 0.103676 - RMSE: 0.321987 - COPC: 1.001858 - KLD: 0.725533
2024-11-17 10:50:00 INFO Save best model: monitor(max): 0.582136
2024-11-17 10:50:00 INFO --- 2570/2570 batches finished ---
2024-11-17 10:50:00 INFO Train loss: 0.340153
2024-11-17 10:50:00 INFO ************ Epoch=1 end ************
2024-11-17 10:53:26 INFO [Metrics] AUC-ROC: 0.905228 - AUC-PR: 0.772398 - ACC: 0.839455 - Precision: 0.692139 - Recall: 0.644481 - F1: 0.667460 - MCC: 0.562443 - Logloss: 0.322001 - MSE: 0.103520 - RMSE: 0.321745 - COPC: 1.005714 - KLD: 0.722173
2024-11-17 10:53:26 INFO Save best model: monitor(max): 0.583227
2024-11-17 10:53:26 INFO --- 2570/2570 batches finished ---
2024-11-17 10:53:26 INFO Train loss: 0.322336
2024-11-17 10:53:26 INFO ************ Epoch=2 end ************
2024-11-17 10:56:58 INFO [Metrics] AUC-ROC: 0.905209 - AUC-PR: 0.772254 - ACC: 0.839493 - Precision: 0.689027 - Recall: 0.652428 - F1: 0.670228 - MCC: 0.564634 - Logloss: 0.321925 - MSE: 0.103527 - RMSE: 0.321756 - COPC: 0.998128 - KLD: 0.721426
2024-11-17 10:56:58 INFO Save best model: monitor(max): 0.583285
2024-11-17 10:56:58 INFO --- 2570/2570 batches finished ---
2024-11-17 10:56:58 INFO Train loss: 0.321959
2024-11-17 10:56:58 INFO ************ Epoch=3 end ************
2024-11-17 11:00:40 INFO [Metrics] AUC-ROC: 0.905264 - AUC-PR: 0.772145 - ACC: 0.839745 - Precision: 0.681764 - Recall: 0.673232 - F1: 0.677471 - MCC: 0.570883 - Logloss: 0.321854 - MSE: 0.103508 - RMSE: 0.321726 - COPC: 0.991854 - KLD: 0.720818
2024-11-17 11:00:40 INFO Save best model: monitor(max): 0.583409
2024-11-17 11:00:40 INFO --- 2570/2570 batches finished ---
2024-11-17 11:00:40 INFO Train loss: 0.321828
2024-11-17 11:00:40 INFO ************ Epoch=4 end ************
2024-11-17 11:04:20 INFO [Metrics] AUC-ROC: 0.905279 - AUC-PR: 0.772423 - ACC: 0.839286 - Precision: 0.691562 - Recall: 0.644664 - F1: 0.667290 - MCC: 0.562119 - Logloss: 0.321713 - MSE: 0.103449 - RMSE: 0.321635 - COPC: 0.999604 - KLD: 0.720518
2024-11-17 11:04:20 INFO Save best model: monitor(max): 0.583566
2024-11-17 11:04:20 INFO --- 2570/2570 batches finished ---
2024-11-17 11:04:20 INFO Train loss: 0.321764
2024-11-17 11:04:20 INFO ************ Epoch=5 end ************
2024-11-17 11:08:02 INFO [Metrics] AUC-ROC: 0.905356 - AUC-PR: 0.772434 - ACC: 0.839692 - Precision: 0.684607 - Recall: 0.665236 - F1: 0.674782 - MCC: 0.568544 - Logloss: 0.321718 - MSE: 0.103447 - RMSE: 0.321632 - COPC: 0.991971 - KLD: 0.720398
2024-11-17 11:08:02 INFO Save best model: monitor(max): 0.583638
2024-11-17 11:08:02 INFO --- 2570/2570 batches finished ---
2024-11-17 11:08:02 INFO Train loss: 0.321709
2024-11-17 11:08:02 INFO ************ Epoch=6 end ************
2024-11-17 11:11:50 INFO [Metrics] AUC-ROC: 0.905278 - AUC-PR: 0.772266 - ACC: 0.839446 - Precision: 0.688457 - Recall: 0.653510 - F1: 0.670529 - MCC: 0.564820 - Logloss: 0.321752 - MSE: 0.103473 - RMSE: 0.321672 - COPC: 0.997023 - KLD: 0.720454
2024-11-17 11:11:50 INFO Monitor(max) STOP: 0.583526 !
2024-11-17 11:11:50 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 11:11:50 INFO --- 2570/2570 batches finished ---
2024-11-17 11:11:50 INFO Train loss: 0.321678
2024-11-17 11:11:50 INFO ************ Epoch=7 end ************
2024-11-17 11:15:42 INFO [Metrics] AUC-ROC: 0.905412 - AUC-PR: 0.772697 - ACC: 0.839058 - Precision: 0.701088 - Recall: 0.620994 - F1: 0.658615 - MCC: 0.555574 - Logloss: 0.321378 - MSE: 0.103332 - RMSE: 0.321453 - COPC: 1.004602 - KLD: 0.719688
2024-11-17 11:15:42 INFO Save best model: monitor(max): 0.584034
2024-11-17 11:15:42 INFO --- 2570/2570 batches finished ---
2024-11-17 11:15:42 INFO Train loss: 0.321284
2024-11-17 11:15:42 INFO ************ Epoch=8 end ************
2024-11-17 11:19:36 INFO [Metrics] AUC-ROC: 0.905424 - AUC-PR: 0.772676 - ACC: 0.839107 - Precision: 0.699249 - Recall: 0.625425 - F1: 0.660280 - MCC: 0.556784 - Logloss: 0.321372 - MSE: 0.103330 - RMSE: 0.321450 - COPC: 1.006316 - KLD: 0.719666
2024-11-17 11:19:36 INFO Save best model: monitor(max): 0.584052
2024-11-17 11:19:36 INFO --- 2570/2570 batches finished ---
2024-11-17 11:19:36 INFO Train loss: 0.321239
2024-11-17 11:19:36 INFO ************ Epoch=9 end ************
2024-11-17 11:23:28 INFO [Metrics] AUC-ROC: 0.905431 - AUC-PR: 0.772742 - ACC: 0.839249 - Precision: 0.694698 - Recall: 0.636895 - F1: 0.664542 - MCC: 0.560015 - Logloss: 0.321348 - MSE: 0.103320 - RMSE: 0.321434 - COPC: 1.000030 - KLD: 0.719557
2024-11-17 11:23:28 INFO Save best model: monitor(max): 0.584083
2024-11-17 11:23:28 INFO --- 2570/2570 batches finished ---
2024-11-17 11:23:28 INFO Train loss: 0.321230
2024-11-17 11:23:28 INFO ************ Epoch=10 end ************
2024-11-17 11:27:21 INFO [Metrics] AUC-ROC: 0.905420 - AUC-PR: 0.772697 - ACC: 0.839027 - Precision: 0.700145 - Recall: 0.622863 - F1: 0.659247 - MCC: 0.555965 - Logloss: 0.321359 - MSE: 0.103328 - RMSE: 0.321446 - COPC: 1.003471 - KLD: 0.719585
2024-11-17 11:27:21 INFO Monitor(max) STOP: 0.584061 !
2024-11-17 11:27:21 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 11:27:21 INFO --- 2570/2570 batches finished ---
2024-11-17 11:27:21 INFO Train loss: 0.321225
2024-11-17 11:27:21 INFO ************ Epoch=11 end ************
2024-11-17 11:31:13 INFO [Metrics] AUC-ROC: 0.905440 - AUC-PR: 0.772774 - ACC: 0.839157 - Precision: 0.696792 - Recall: 0.631366 - F1: 0.662467 - MCC: 0.558394 - Logloss: 0.321335 - MSE: 0.103319 - RMSE: 0.321433 - COPC: 0.999933 - KLD: 0.719518
2024-11-17 11:31:13 INFO Save best model: monitor(max): 0.584104
2024-11-17 11:31:13 INFO --- 2570/2570 batches finished ---
2024-11-17 11:31:13 INFO Train loss: 0.321185
2024-11-17 11:31:13 INFO ************ Epoch=12 end ************
2024-11-17 11:35:06 INFO [Metrics] AUC-ROC: 0.905445 - AUC-PR: 0.772792 - ACC: 0.839207 - Precision: 0.694924 - Recall: 0.636061 - F1: 0.664191 - MCC: 0.559705 - Logloss: 0.321333 - MSE: 0.103318 - RMSE: 0.321432 - COPC: 0.998763 - KLD: 0.719506
2024-11-17 11:35:06 INFO Save best model: monitor(max): 0.584112
2024-11-17 11:35:06 INFO --- 2570/2570 batches finished ---
2024-11-17 11:35:06 INFO Train loss: 0.321175
2024-11-17 11:35:06 INFO ************ Epoch=13 end ************
2024-11-17 11:59:22 INFO [Metrics] AUC-ROC: 0.905445 - AUC-PR: 0.772787 - ACC: 0.839202 - Precision: 0.696571 - Recall: 0.632196 - F1: 0.662824 - MCC: 0.558707 - Logloss: 0.321330 - MSE: 0.103318 - RMSE: 0.321431 - COPC: 1.000590 - KLD: 0.719518
2024-11-17 11:59:22 INFO Save best model: monitor(max): 0.584115
2024-11-17 11:59:22 INFO --- 2570/2570 batches finished ---
2024-11-17 11:59:22 INFO Train loss: 0.321172
2024-11-17 11:59:22 INFO ************ Epoch=14 end ************
2024-11-17 12:03:14 INFO [Metrics] AUC-ROC: 0.905443 - AUC-PR: 0.772778 - ACC: 0.839203 - Precision: 0.695620 - Recall: 0.634407 - F1: 0.663605 - MCC: 0.559273 - Logloss: 0.321330 - MSE: 0.103318 - RMSE: 0.321432 - COPC: 0.999663 - KLD: 0.719504
2024-11-17 12:03:14 INFO Monitor(max) STOP: 0.584113 !
2024-11-17 12:03:14 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 12:03:14 INFO --- 2570/2570 batches finished ---
2024-11-17 12:03:14 INFO Train loss: 0.321169
2024-11-17 12:03:14 INFO ************ Epoch=15 end ************
2024-11-17 12:07:07 INFO [Metrics] AUC-ROC: 0.905443 - AUC-PR: 0.772781 - ACC: 0.839207 - Precision: 0.695582 - Recall: 0.634525 - F1: 0.663652 - MCC: 0.559312 - Logloss: 0.321330 - MSE: 0.103318 - RMSE: 0.321431 - COPC: 0.999494 - KLD: 0.719502
2024-11-17 12:07:07 INFO Monitor(max) STOP: 0.584114 !
2024-11-17 12:07:07 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 12:07:07 INFO Early stopping at epoch=16
2024-11-17 12:07:07 INFO --- 2570/2570 batches finished ---
2024-11-17 12:07:07 INFO Train loss: 0.321169
2024-11-17 12:07:07 INFO Training finished.
2024-11-17 12:07:07 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FM/Synthetic/FM_model_seed2019.ckpt
2024-11-17 12:07:07 INFO Start evaluate model
2024-11-17 12:07:43 INFO [Metrics] AUC-ROC: 0.905445 - AUC-PR: 0.772787 - ACC: 0.839202 - Precision: 0.696571 - Recall: 0.632196 - F1: 0.662824 - MCC: 0.558707 - Logloss: 0.321330 - MSE: 0.103318 - RMSE: 0.321431 - COPC: 1.000590 - KLD: 0.719518
2024-11-17 12:07:43 INFO Start testing model
2024-11-17 12:08:19 INFO [Metrics] AUC-ROC: 0.905458 - AUC-PR: 0.773282 - ACC: 0.839438 - Precision: 0.697545 - Recall: 0.631623 - F1: 0.662949 - MCC: 0.559092 - Logloss: 0.321256 - MSE: 0.103273 - RMSE: 0.321361 - COPC: 1.001574 - KLD: 0.719751
