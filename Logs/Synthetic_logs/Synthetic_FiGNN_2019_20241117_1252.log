2024-11-17 12:52:23 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='FiGNN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-17 12:52:23 INFO Start process Synthetic !
2024-11-17 12:52:23 INFO Loading Synthetic dataset
2024-11-17 12:52:23 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-17 12:52:24 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-17 12:52:24 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-17 12:52:25 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-17 12:52:25 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-17 12:52:25 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-17 12:52:25 INFO Loading data done
2024-11-17 12:52:25 INFO Model: FiGNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fignn): FiGNN_Layer(
    (gnn): ModuleList(
      (0-3): 4 x GraphLayer()
    )
    (gru): GRUCell(16, 16)
    (leaky_relu): LeakyReLU(negative_slope=0.01)
    (W_attn): Linear(in_features=32, out_features=1, bias=False)
  )
  (fc): PredictionLayer(
    (mlp1): Linear(in_features=16, out_features=1, bias=False)
    (mlp2): Sequential(
      (0): Linear(in_features=160, out_features=10, bias=False)
      (1): Sigmoid()
    )
  )
  (final_activation): Sigmoid()
)
2024-11-17 12:52:25 INFO Model parameters: 31824
2024-11-17 12:52:25 INFO Start training model
2024-11-17 12:52:25 INFO Start training: 2570 batches/epoch
2024-11-17 12:52:25 INFO ************ Epoch=1 start ************
2024-11-17 12:56:25 INFO [Metrics] AUC-ROC: 0.906077 - AUC-PR: 0.774545 - ACC: 0.840660 - Precision: 0.684651 - Recall: 0.672303 - F1: 0.678421 - MCC: 0.572567 - Logloss: 0.316289 - MSE: 0.102203 - RMSE: 0.319692 - COPC: 1.018586 - KLD: 0.709483
2024-11-17 12:56:25 INFO Save best model: monitor(max): 0.589789
2024-11-17 12:56:25 INFO --- 2570/2570 batches finished ---
2024-11-17 12:56:25 INFO Train loss: 0.324782
2024-11-17 12:56:25 INFO ************ Epoch=1 end ************
2024-11-17 13:00:24 INFO [Metrics] AUC-ROC: 0.906225 - AUC-PR: 0.774651 - ACC: 0.841238 - Precision: 0.670796 - Recall: 0.716665 - F1: 0.692972 - MCC: 0.586651 - Logloss: 0.315580 - MSE: 0.102046 - RMSE: 0.319447 - COPC: 0.992768 - KLD: 0.708125
2024-11-17 13:00:24 INFO Save best model: monitor(max): 0.590645
2024-11-17 13:00:24 INFO --- 2570/2570 batches finished ---
2024-11-17 13:00:24 INFO Train loss: 0.315847
2024-11-17 13:00:24 INFO ************ Epoch=2 end ************
2024-11-17 13:04:25 INFO [Metrics] AUC-ROC: 0.906216 - AUC-PR: 0.774747 - ACC: 0.840690 - Precision: 0.688835 - Recall: 0.661639 - F1: 0.674963 - MCC: 0.569710 - Logloss: 0.315600 - MSE: 0.102083 - RMSE: 0.319504 - COPC: 1.021715 - KLD: 0.708019
2024-11-17 13:04:25 INFO Monitor(max) STOP: 0.590616 !
2024-11-17 13:04:25 INFO Reduce learning rate on plateau: 0.000100
2024-11-17 13:04:25 INFO --- 2570/2570 batches finished ---
2024-11-17 13:04:25 INFO Train loss: 0.315463
2024-11-17 13:04:25 INFO ************ Epoch=3 end ************
2024-11-17 13:08:27 INFO [Metrics] AUC-ROC: 0.906305 - AUC-PR: 0.774963 - ACC: 0.841387 - Precision: 0.672208 - Recall: 0.713449 - F1: 0.692215 - MCC: 0.585978 - Logloss: 0.315193 - MSE: 0.101937 - RMSE: 0.319276 - COPC: 1.006199 - KLD: 0.707179
2024-11-17 13:08:27 INFO Save best model: monitor(max): 0.591111
2024-11-17 13:08:27 INFO --- 2570/2570 batches finished ---
2024-11-17 13:08:27 INFO Train loss: 0.315046
2024-11-17 13:08:27 INFO ************ Epoch=4 end ************
2024-11-17 13:12:29 INFO [Metrics] AUC-ROC: 0.906349 - AUC-PR: 0.775062 - ACC: 0.841229 - Precision: 0.674319 - Recall: 0.705804 - F1: 0.689702 - MCC: 0.583373 - Logloss: 0.315178 - MSE: 0.101934 - RMSE: 0.319271 - COPC: 1.007120 - KLD: 0.707143
2024-11-17 13:12:29 INFO Save best model: monitor(max): 0.591171
2024-11-17 13:12:29 INFO --- 2570/2570 batches finished ---
2024-11-17 13:12:29 INFO Train loss: 0.315006
2024-11-17 13:12:29 INFO ************ Epoch=5 end ************
2024-11-17 13:16:17 INFO [Metrics] AUC-ROC: 0.906314 - AUC-PR: 0.774967 - ACC: 0.841293 - Precision: 0.670879 - Recall: 0.716843 - F1: 0.693100 - MCC: 0.586817 - Logloss: 0.315167 - MSE: 0.101932 - RMSE: 0.319269 - COPC: 0.997543 - KLD: 0.707112
2024-11-17 13:16:17 INFO Monitor(max) STOP: 0.591148 !
2024-11-17 13:16:17 INFO Reduce learning rate on plateau: 0.000010
2024-11-17 13:16:17 INFO --- 2570/2570 batches finished ---
2024-11-17 13:16:18 INFO Train loss: 0.314990
2024-11-17 13:16:18 INFO ************ Epoch=6 end ************
2024-11-17 13:19:59 INFO [Metrics] AUC-ROC: 0.906342 - AUC-PR: 0.775050 - ACC: 0.841395 - Precision: 0.672284 - Recall: 0.713279 - F1: 0.692175 - MCC: 0.585944 - Logloss: 0.315135 - MSE: 0.101920 - RMSE: 0.319250 - COPC: 1.003061 - KLD: 0.707041
2024-11-17 13:19:59 INFO Save best model: monitor(max): 0.591207
2024-11-17 13:19:59 INFO --- 2570/2570 batches finished ---
2024-11-17 13:19:59 INFO Train loss: 0.314942
2024-11-17 13:19:59 INFO ************ Epoch=7 end ************
2024-11-17 13:23:39 INFO [Metrics] AUC-ROC: 0.906343 - AUC-PR: 0.775051 - ACC: 0.841435 - Precision: 0.670996 - Recall: 0.717589 - F1: 0.693511 - MCC: 0.587327 - Logloss: 0.315130 - MSE: 0.101919 - RMSE: 0.319247 - COPC: 1.000741 - KLD: 0.707034
2024-11-17 13:23:39 INFO Save best model: monitor(max): 0.591212
2024-11-17 13:23:39 INFO --- 2570/2570 batches finished ---
2024-11-17 13:23:39 INFO Train loss: 0.314939
2024-11-17 13:23:39 INFO ************ Epoch=8 end ************
2024-11-17 13:27:22 INFO [Metrics] AUC-ROC: 0.906333 - AUC-PR: 0.775025 - ACC: 0.841406 - Precision: 0.671516 - Recall: 0.715743 - F1: 0.692924 - MCC: 0.586710 - Logloss: 0.315131 - MSE: 0.101919 - RMSE: 0.319248 - COPC: 1.002347 - KLD: 0.707035
2024-11-17 13:27:22 INFO Monitor(max) STOP: 0.591202 !
2024-11-17 13:27:22 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 13:27:22 INFO --- 2570/2570 batches finished ---
2024-11-17 13:27:22 INFO Train loss: 0.314934
2024-11-17 13:27:22 INFO ************ Epoch=9 end ************
2024-11-17 13:31:05 INFO [Metrics] AUC-ROC: 0.906338 - AUC-PR: 0.775036 - ACC: 0.841426 - Precision: 0.670958 - Recall: 0.717639 - F1: 0.693514 - MCC: 0.587324 - Logloss: 0.315129 - MSE: 0.101918 - RMSE: 0.319247 - COPC: 0.999561 - KLD: 0.707030
2024-11-17 13:31:05 INFO Monitor(max) STOP: 0.591209 !
2024-11-17 13:31:05 INFO Reduce learning rate on plateau: 0.000001
2024-11-17 13:31:05 INFO Early stopping at epoch=10
2024-11-17 13:31:05 INFO --- 2570/2570 batches finished ---
2024-11-17 13:31:05 INFO Train loss: 0.314929
2024-11-17 13:31:05 INFO Training finished.
2024-11-17 13:31:05 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FiGNN/Synthetic/FiGNN_model_seed2019.ckpt
2024-11-17 13:31:05 INFO Start evaluate model
2024-11-17 13:31:41 INFO [Metrics] AUC-ROC: 0.906343 - AUC-PR: 0.775051 - ACC: 0.841435 - Precision: 0.670996 - Recall: 0.717589 - F1: 0.693511 - MCC: 0.587327 - Logloss: 0.315130 - MSE: 0.101919 - RMSE: 0.319247 - COPC: 1.000741 - KLD: 0.707034
2024-11-17 13:31:41 INFO Start testing model
2024-11-17 13:32:17 INFO [Metrics] AUC-ROC: 0.906350 - AUC-PR: 0.775427 - ACC: 0.841412 - Precision: 0.671237 - Recall: 0.716661 - F1: 0.693206 - MCC: 0.587001 - Logloss: 0.315008 - MSE: 0.101854 - RMSE: 0.319146 - COPC: 1.001505 - KLD: 0.707076
