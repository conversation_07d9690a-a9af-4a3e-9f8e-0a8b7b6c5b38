2024-11-23 13:13:13 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='CCPM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=15000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-23 13:13:13 INFO Start process Synthetic !
2024-11-23 13:13:13 INFO Loading Synthetic dataset
2024-11-23 13:13:13 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-23 13:13:15 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-23 13:13:15 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-23 13:13:15 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-23 13:13:15 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-23 13:13:16 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-23 13:13:16 INFO Loading data done
2024-11-23 13:13:16 INFO Model: CCPM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (conv_layer): CCPM_ConvLayer(
    (conv_layer): Sequential(
      (0): ZeroPad2d((0, 0, 6, 6))
      (1): Conv2d(1, 64, kernel_size=(7, 1), stride=(1, 1))
      (2): KMaxPooling()
      (3): Tanh()
      (4): ZeroPad2d((0, 0, 4, 4))
      (5): Conv2d(64, 128, kernel_size=(5, 1), stride=(1, 1))
      (6): KMaxPooling()
      (7): Tanh()
      (8): ZeroPad2d((0, 0, 2, 2))
      (9): Conv2d(128, 256, kernel_size=(3, 1), stride=(1, 1))
      (10): KMaxPooling()
      (11): Tanh()
    )
  )
  (fc): Linear(in_features=12288, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-23 13:13:16 INFO Model parameters: 160449
2024-11-23 13:13:16 INFO Start training model
2024-11-23 13:13:17 INFO Start training: 1713 batches/epoch
2024-11-23 13:13:17 INFO ************ Epoch=1 start ************
2024-11-23 14:13:39 INFO [Metrics] AUC-ROC: 0.905969 - AUC-PR: 0.774097 - ACC: 0.840635 - Precision: 0.679920 - Recall: 0.685024 - F1: 0.682462 - MCC: 0.576095 - Logloss: 0.316912 - MSE: 0.102338 - RMSE: 0.319903 - COPC: 0.978530 - KLD: 0.710529
2024-11-23 14:13:39 INFO Save best model: monitor(max): 0.589057
2024-11-23 14:13:39 INFO --- 1713/1713 batches finished ---
2024-11-23 14:13:39 INFO Train loss: 0.328679
2024-11-23 14:13:39 INFO ************ Epoch=1 end ************
2024-11-23 15:13:47 INFO [Metrics] AUC-ROC: 0.906044 - AUC-PR: 0.774347 - ACC: 0.840449 - Precision: 0.681581 - Recall: 0.679016 - F1: 0.680296 - MCC: 0.573998 - Logloss: 0.316151 - MSE: 0.102208 - RMSE: 0.319700 - COPC: 0.985632 - KLD: 0.709049
2024-11-23 15:13:47 INFO Save best model: monitor(max): 0.589893
2024-11-23 15:13:47 INFO --- 1713/1713 batches finished ---
2024-11-23 15:13:47 INFO Train loss: 0.316564
2024-11-23 15:13:47 INFO ************ Epoch=2 end ************
2024-11-23 16:13:52 INFO [Metrics] AUC-ROC: 0.906170 - AUC-PR: 0.774629 - ACC: 0.840033 - Precision: 0.697853 - Recall: 0.635113 - F1: 0.665007 - MCC: 0.561314 - Logloss: 0.315828 - MSE: 0.102127 - RMSE: 0.319573 - COPC: 1.002399 - KLD: 0.708585
2024-11-23 16:13:52 INFO Save best model: monitor(max): 0.590342
2024-11-23 16:13:52 INFO --- 1713/1713 batches finished ---
2024-11-23 16:13:52 INFO Train loss: 0.316110
2024-11-23 16:13:52 INFO ************ Epoch=3 end ************
2024-11-23 17:14:14 INFO [Metrics] AUC-ROC: 0.906152 - AUC-PR: 0.774579 - ACC: 0.840470 - Precision: 0.687225 - Recall: 0.664156 - F1: 0.675494 - MCC: 0.569919 - Logloss: 0.315691 - MSE: 0.102105 - RMSE: 0.319539 - COPC: 0.996806 - KLD: 0.708440
2024-11-23 17:14:14 INFO Save best model: monitor(max): 0.590461
2024-11-23 17:14:14 INFO --- 1713/1713 batches finished ---
2024-11-23 17:14:14 INFO Train loss: 0.315850
2024-11-23 17:14:14 INFO ************ Epoch=4 end ************
2024-11-23 18:14:15 INFO [Metrics] AUC-ROC: 0.906145 - AUC-PR: 0.774541 - ACC: 0.840189 - Precision: 0.691374 - Recall: 0.651646 - F1: 0.670922 - MCC: 0.565946 - Logloss: 0.315621 - MSE: 0.102085 - RMSE: 0.319507 - COPC: 0.996000 - KLD: 0.708272
2024-11-23 18:14:15 INFO Save best model: monitor(max): 0.590525
2024-11-23 18:14:15 INFO --- 1713/1713 batches finished ---
2024-11-23 18:14:15 INFO Train loss: 0.315737
2024-11-23 18:14:15 INFO ************ Epoch=5 end ************
2024-11-23 19:14:02 INFO [Metrics] AUC-ROC: 0.906132 - AUC-PR: 0.774520 - ACC: 0.840058 - Precision: 0.692702 - Recall: 0.647460 - F1: 0.669318 - MCC: 0.564556 - Logloss: 0.315611 - MSE: 0.102086 - RMSE: 0.319509 - COPC: 0.996097 - KLD: 0.708233
2024-11-23 19:14:02 INFO Monitor(max) STOP: 0.590521 !
2024-11-23 19:14:02 INFO Reduce learning rate on plateau: 0.000100
2024-11-23 19:14:02 INFO --- 1713/1713 batches finished ---
2024-11-23 19:14:02 INFO Train loss: 0.315673
2024-11-23 19:14:02 INFO ************ Epoch=6 end ************
2024-11-23 20:13:35 INFO [Metrics] AUC-ROC: 0.906233 - AUC-PR: 0.774751 - ACC: 0.841313 - Precision: 0.673001 - Recall: 0.710440 - F1: 0.691214 - MCC: 0.584926 - Logloss: 0.315352 - MSE: 0.101995 - RMSE: 0.319367 - COPC: 1.001722 - KLD: 0.707603
2024-11-23 20:13:35 INFO Save best model: monitor(max): 0.590880
2024-11-23 20:13:35 INFO --- 1713/1713 batches finished ---
2024-11-23 20:13:35 INFO Train loss: 0.315227
2024-11-23 20:13:35 INFO ************ Epoch=7 end ************
2024-11-23 21:12:54 INFO [Metrics] AUC-ROC: 0.906240 - AUC-PR: 0.774716 - ACC: 0.841337 - Precision: 0.673120 - Recall: 0.710271 - F1: 0.691196 - MCC: 0.584925 - Logloss: 0.315326 - MSE: 0.101989 - RMSE: 0.319358 - COPC: 1.001922 - KLD: 0.707524
2024-11-23 21:12:54 INFO Save best model: monitor(max): 0.590914
2024-11-23 21:12:54 INFO --- 1713/1713 batches finished ---
2024-11-23 21:12:54 INFO Train loss: 0.315130
2024-11-23 21:12:54 INFO ************ Epoch=8 end ************
2024-11-23 22:12:49 INFO [Metrics] AUC-ROC: 0.906250 - AUC-PR: 0.774737 - ACC: 0.841332 - Precision: 0.673214 - Recall: 0.709943 - F1: 0.691091 - MCC: 0.584816 - Logloss: 0.315316 - MSE: 0.101988 - RMSE: 0.319356 - COPC: 1.001999 - KLD: 0.707494
2024-11-23 22:12:49 INFO Save best model: monitor(max): 0.590934
2024-11-23 22:12:49 INFO --- 1713/1713 batches finished ---
2024-11-23 22:12:49 INFO Train loss: 0.315078
2024-11-23 22:12:49 INFO ************ Epoch=9 end ************
2024-11-23 23:12:10 INFO [Metrics] AUC-ROC: 0.906243 - AUC-PR: 0.774762 - ACC: 0.841344 - Precision: 0.673364 - Recall: 0.709580 - F1: 0.690998 - MCC: 0.584732 - Logloss: 0.315314 - MSE: 0.101987 - RMSE: 0.319354 - COPC: 1.002029 - KLD: 0.707496
2024-11-23 23:12:10 INFO Monitor(max) STOP: 0.590929 !
2024-11-23 23:12:10 INFO Reduce learning rate on plateau: 0.000010
2024-11-23 23:12:10 INFO --- 1713/1713 batches finished ---
2024-11-23 23:12:11 INFO Train loss: 0.315047
2024-11-23 23:12:11 INFO ************ Epoch=10 end ************
2024-11-24 00:11:31 INFO [Metrics] AUC-ROC: 0.906268 - AUC-PR: 0.774821 - ACC: 0.841214 - Precision: 0.676236 - Recall: 0.699996 - F1: 0.687911 - MCC: 0.581629 - Logloss: 0.315274 - MSE: 0.101973 - RMSE: 0.319333 - COPC: 1.000276 - KLD: 0.707393
2024-11-24 00:11:31 INFO Save best model: monitor(max): 0.590994
2024-11-24 00:11:31 INFO --- 1713/1713 batches finished ---
2024-11-24 00:11:31 INFO Train loss: 0.314960
2024-11-24 00:11:31 INFO ************ Epoch=11 end ************
2024-11-24 01:10:52 INFO [Metrics] AUC-ROC: 0.906269 - AUC-PR: 0.774841 - ACC: 0.841225 - Precision: 0.676269 - Recall: 0.699979 - F1: 0.687920 - MCC: 0.581645 - Logloss: 0.315270 - MSE: 0.101971 - RMSE: 0.319329 - COPC: 1.000259 - KLD: 0.707390
2024-11-24 01:10:52 INFO Save best model: monitor(max): 0.590999
2024-11-24 01:10:52 INFO --- 1713/1713 batches finished ---
2024-11-24 01:10:52 INFO Train loss: 0.314947
2024-11-24 01:10:52 INFO ************ Epoch=12 end ************
2024-11-24 02:10:06 INFO [Metrics] AUC-ROC: 0.906271 - AUC-PR: 0.774853 - ACC: 0.841206 - Precision: 0.676306 - Recall: 0.699731 - F1: 0.687819 - MCC: 0.581535 - Logloss: 0.315267 - MSE: 0.101969 - RMSE: 0.319327 - COPC: 1.000283 - KLD: 0.707388
2024-11-24 02:10:06 INFO Save best model: monitor(max): 0.591004
2024-11-24 02:10:06 INFO --- 1713/1713 batches finished ---
2024-11-24 02:10:06 INFO Train loss: 0.314939
2024-11-24 02:10:06 INFO ************ Epoch=13 end ************
2024-11-24 03:09:25 INFO [Metrics] AUC-ROC: 0.906256 - AUC-PR: 0.774822 - ACC: 0.841172 - Precision: 0.676308 - Recall: 0.699461 - F1: 0.687690 - MCC: 0.581385 - Logloss: 0.315272 - MSE: 0.101971 - RMSE: 0.319329 - COPC: 1.000298 - KLD: 0.707400
2024-11-24 03:09:25 INFO Monitor(max) STOP: 0.590983 !
2024-11-24 03:09:25 INFO Reduce learning rate on plateau: 0.000001
2024-11-24 03:09:25 INFO --- 1713/1713 batches finished ---
2024-11-24 03:09:25 INFO Train loss: 0.314934
2024-11-24 03:09:25 INFO ************ Epoch=14 end ************
2024-11-24 04:08:42 INFO [Metrics] AUC-ROC: 0.906263 - AUC-PR: 0.774839 - ACC: 0.841203 - Precision: 0.675995 - Recall: 0.700623 - F1: 0.688089 - MCC: 0.581791 - Logloss: 0.315266 - MSE: 0.101969 - RMSE: 0.319326 - COPC: 0.999998 - KLD: 0.707386
2024-11-24 04:08:42 INFO Monitor(max) STOP: 0.590998 !
2024-11-24 04:08:42 INFO Reduce learning rate on plateau: 0.000001
2024-11-24 04:08:42 INFO Early stopping at epoch=15
2024-11-24 04:08:42 INFO --- 1713/1713 batches finished ---
2024-11-24 04:08:42 INFO Train loss: 0.314923
2024-11-24 04:08:42 INFO Training finished.
2024-11-24 04:08:42 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/CCPM/Synthetic/CCPM_model_seed2020.ckpt
2024-11-24 04:08:42 INFO Start evaluate model
2024-11-24 04:12:13 INFO [Metrics] AUC-ROC: 0.906271 - AUC-PR: 0.774853 - ACC: 0.841206 - Precision: 0.676306 - Recall: 0.699731 - F1: 0.687819 - MCC: 0.581535 - Logloss: 0.315267 - MSE: 0.101969 - RMSE: 0.319327 - COPC: 1.000283 - KLD: 0.707388
2024-11-24 04:12:13 INFO Start testing model
2024-11-24 04:15:40 INFO [Metrics] AUC-ROC: 0.906319 - AUC-PR: 0.775346 - ACC: 0.841064 - Precision: 0.676328 - Recall: 0.698576 - F1: 0.687272 - MCC: 0.580904 - Logloss: 0.315125 - MSE: 0.101896 - RMSE: 0.319212 - COPC: 1.001062 - KLD: 0.707388
