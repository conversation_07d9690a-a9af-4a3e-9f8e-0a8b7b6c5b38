2024-11-21 08:48:38 INFO all args: Namespace(dataset_name='Synthetic', dataset_path='/data/ctr/Synthetic', model_name='CCPM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=6000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-21 08:48:38 INFO Start process Synthetic !
2024-11-21 08:48:38 INFO Loading Synthetic dataset
2024-11-21 08:48:38 INFO Load h5 data from /data/ctr/Synthetic/train.h5
2024-11-21 08:48:40 INFO Load h5 data from /data/ctr/Synthetic/valid.h5
2024-11-21 08:48:40 INFO Load h5 data from /data/ctr/Synthetic/test.h5
2024-11-21 08:48:40 INFO Train samples: total/25694417, pos/6423604, neg/19270813, ratio/25.00%
2024-11-21 08:48:40 INFO Validation samples: total/3211802, pos/802950, neg/2408852, ratio/25.00%
2024-11-21 08:48:40 INFO Test samples: total/3211803, pos/802951, neg/2408852, ratio/25.00%
2024-11-21 08:48:40 INFO Loading data done
2024-11-21 08:48:41 INFO Model: CCPM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_age): Embedding(22, 16, padding_idx=21)
      (item_price): Embedding(43, 16, padding_idx=42)
      (user_gender): Embedding(4, 16, padding_idx=3)
      (user_region): Embedding(334, 16, padding_idx=333)
      (device_type): Embedding(5, 16, padding_idx=4)
      (ad_category): Embedding(45, 16, padding_idx=44)
      (ad_placement): Embedding(8, 16, padding_idx=7)
      (hour_of_day): Embedding(25, 16, padding_idx=24)
      (day_of_week): Embedding(8, 16, padding_idx=7)
      (scene_type): Embedding(6, 16, padding_idx=5)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (conv_layer): CCPM_ConvLayer(
    (conv_layer): Sequential(
      (0): ZeroPad2d((0, 0, 6, 6))
      (1): Conv2d(1, 64, kernel_size=(7, 1), stride=(1, 1))
      (2): KMaxPooling()
      (3): Tanh()
      (4): ZeroPad2d((0, 0, 4, 4))
      (5): Conv2d(64, 128, kernel_size=(5, 1), stride=(1, 1))
      (6): KMaxPooling()
      (7): Tanh()
      (8): ZeroPad2d((0, 0, 2, 2))
      (9): Conv2d(128, 256, kernel_size=(3, 1), stride=(1, 1))
      (10): KMaxPooling()
      (11): Tanh()
    )
  )
  (fc): Linear(in_features=12288, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-21 08:48:41 INFO Model parameters: 160449
2024-11-21 08:48:41 INFO Start training model
2024-11-21 08:48:41 INFO Start training: 4283 batches/epoch
2024-11-21 08:48:41 INFO ************ Epoch=1 start ************
2024-11-21 09:44:22 INFO [Metrics] AUC-ROC: 0.905982 - AUC-PR: 0.774120 - ACC: 0.839360 - Precision: 0.717513 - Recall: 0.589545 - F1: 0.647265 - MCC: 0.548954 - Logloss: 0.316748 - MSE: 0.102418 - RMSE: 0.320028 - COPC: 1.038318 - KLD: 0.709956
2024-11-21 09:44:22 INFO Save best model: monitor(max): 0.589235
2024-11-21 09:44:22 INFO --- 4283/4283 batches finished ---
2024-11-21 09:44:22 INFO Train loss: 0.322744
2024-11-21 09:44:22 INFO ************ Epoch=1 end ************
2024-11-21 10:39:46 INFO [Metrics] AUC-ROC: 0.906010 - AUC-PR: 0.773956 - ACC: 0.840039 - Precision: 0.699662 - Recall: 0.631033 - F1: 0.663578 - MCC: 0.560300 - Logloss: 0.316364 - MSE: 0.102329 - RMSE: 0.319890 - COPC: 1.026260 - KLD: 0.709548
2024-11-21 10:39:46 INFO Save best model: monitor(max): 0.589646
2024-11-21 10:39:46 INFO --- 4283/4283 batches finished ---
2024-11-21 10:39:46 INFO Train loss: 0.316414
2024-11-21 10:39:46 INFO ************ Epoch=2 end ************
2024-11-21 11:35:36 INFO [Metrics] AUC-ROC: 0.906024 - AUC-PR: 0.774296 - ACC: 0.840325 - Precision: 0.692620 - Recall: 0.649573 - F1: 0.670407 - MCC: 0.565697 - Logloss: 0.316037 - MSE: 0.102213 - RMSE: 0.319707 - COPC: 1.012625 - KLD: 0.709152
2024-11-21 11:35:36 INFO Save best model: monitor(max): 0.589986
2024-11-21 11:35:36 INFO --- 4283/4283 batches finished ---
2024-11-21 11:35:36 INFO Train loss: 0.316165
2024-11-21 11:35:36 INFO ************ Epoch=3 end ************
2024-11-21 13:28:40 INFO [Metrics] AUC-ROC: 0.906084 - AUC-PR: 0.774263 - ACC: 0.840855 - Precision: 0.683243 - Recall: 0.677524 - F1: 0.680372 - MCC: 0.574434 - Logloss: 0.315852 - MSE: 0.102155 - RMSE: 0.319617 - COPC: 1.008547 - KLD: 0.708704
2024-11-21 13:28:40 INFO Save best model: monitor(max): 0.590232
2024-11-21 13:28:40 INFO --- 4283/4283 batches finished ---
2024-11-21 13:28:40 INFO Train loss: 0.315929
2024-11-21 13:28:40 INFO ************ Epoch=5 end ************
2024-11-21 14:24:40 INFO [Metrics] AUC-ROC: 0.906139 - AUC-PR: 0.774613 - ACC: 0.840761 - Precision: 0.686251 - Recall: 0.668825 - F1: 0.677426 - MCC: 0.571820 - Logloss: 0.315800 - MSE: 0.102119 - RMSE: 0.319560 - COPC: 1.008819 - KLD: 0.708685
2024-11-21 14:24:40 INFO Save best model: monitor(max): 0.590340
2024-11-21 14:24:40 INFO --- 4283/4283 batches finished ---
2024-11-21 14:24:40 INFO Train loss: 0.315855
2024-11-21 14:24:40 INFO ************ Epoch=6 end ************
2024-11-21 15:20:37 INFO [Metrics] AUC-ROC: 0.906116 - AUC-PR: 0.774505 - ACC: 0.840303 - Precision: 0.698038 - Recall: 0.636591 - F1: 0.665900 - MCC: 0.562295 - Logloss: 0.315901 - MSE: 0.102175 - RMSE: 0.319648 - COPC: 1.017015 - KLD: 0.708690
2024-11-21 15:20:37 INFO Monitor(max) STOP: 0.590215 !
2024-11-21 15:20:37 INFO Reduce learning rate on plateau: 0.000100
2024-11-21 15:20:37 INFO --- 4283/4283 batches finished ---
2024-11-21 15:20:38 INFO Train loss: 0.315806
2024-11-21 15:20:38 INFO ************ Epoch=7 end ************
2024-11-21 16:16:57 INFO [Metrics] AUC-ROC: 0.906248 - AUC-PR: 0.774768 - ACC: 0.841286 - Precision: 0.673453 - Recall: 0.708855 - F1: 0.690701 - MCC: 0.584397 - Logloss: 0.315307 - MSE: 0.101980 - RMSE: 0.319344 - COPC: 1.000041 - KLD: 0.707498
2024-11-21 16:16:57 INFO Save best model: monitor(max): 0.590941
2024-11-21 16:16:57 INFO --- 4283/4283 batches finished ---
2024-11-21 16:16:57 INFO Train loss: 0.315294
2024-11-21 16:16:57 INFO ************ Epoch=8 end ************
2024-11-21 17:13:06 INFO [Metrics] AUC-ROC: 0.906275 - AUC-PR: 0.774863 - ACC: 0.841236 - Precision: 0.673200 - Recall: 0.709235 - F1: 0.690748 - MCC: 0.584409 - Logloss: 0.315272 - MSE: 0.101968 - RMSE: 0.319325 - COPC: 0.999851 - KLD: 0.707416
2024-11-21 17:13:06 INFO Save best model: monitor(max): 0.591003
2024-11-21 17:13:06 INFO --- 4283/4283 batches finished ---
2024-11-21 17:13:06 INFO Train loss: 0.315186
2024-11-21 17:13:06 INFO ************ Epoch=9 end ************
2024-11-21 18:08:53 INFO [Metrics] AUC-ROC: 0.906263 - AUC-PR: 0.774810 - ACC: 0.841281 - Precision: 0.672676 - Recall: 0.711189 - F1: 0.691397 - MCC: 0.585087 - Logloss: 0.315272 - MSE: 0.101971 - RMSE: 0.319329 - COPC: 0.999302 - KLD: 0.707406
2024-11-21 18:08:53 INFO Monitor(max) STOP: 0.590991 !
2024-11-21 18:08:53 INFO Reduce learning rate on plateau: 0.000010
2024-11-21 18:08:53 INFO --- 4283/4283 batches finished ---
2024-11-21 18:08:53 INFO Train loss: 0.315136
2024-11-21 18:08:53 INFO ************ Epoch=10 end ************
2024-11-21 19:04:24 INFO [Metrics] AUC-ROC: 0.906301 - AUC-PR: 0.774898 - ACC: 0.841367 - Precision: 0.673548 - Recall: 0.709201 - F1: 0.690915 - MCC: 0.584665 - Logloss: 0.315231 - MSE: 0.101957 - RMSE: 0.319307 - COPC: 1.000711 - KLD: 0.707306
2024-11-21 19:04:24 INFO Save best model: monitor(max): 0.591070
2024-11-21 19:04:24 INFO --- 4283/4283 batches finished ---
2024-11-21 19:04:24 INFO Train loss: 0.315043
2024-11-21 19:04:24 INFO ************ Epoch=11 end ************
2024-11-21 19:59:55 INFO [Metrics] AUC-ROC: 0.906311 - AUC-PR: 0.774934 - ACC: 0.841334 - Precision: 0.673382 - Recall: 0.709446 - F1: 0.690944 - MCC: 0.584671 - Logloss: 0.315220 - MSE: 0.101952 - RMSE: 0.319299 - COPC: 1.000675 - KLD: 0.707279
2024-11-21 19:59:55 INFO Save best model: monitor(max): 0.591091
2024-11-21 19:59:55 INFO --- 4283/4283 batches finished ---
2024-11-21 19:59:55 INFO Train loss: 0.315026
2024-11-21 19:59:55 INFO ************ Epoch=12 end ************
2024-11-21 20:55:03 INFO [Metrics] AUC-ROC: 0.906307 - AUC-PR: 0.774959 - ACC: 0.841374 - Precision: 0.673415 - Recall: 0.709657 - F1: 0.691061 - MCC: 0.584815 - Logloss: 0.315220 - MSE: 0.101950 - RMSE: 0.319296 - COPC: 1.000686 - KLD: 0.707288
2024-11-21 20:55:03 INFO Monitor(max) STOP: 0.591088 !
2024-11-21 20:55:03 INFO Reduce learning rate on plateau: 0.000001
2024-11-21 20:55:03 INFO --- 4283/4283 batches finished ---
2024-11-21 20:55:04 INFO Train loss: 0.315017
2024-11-21 20:55:04 INFO ************ Epoch=13 end ************
2024-11-21 21:49:55 INFO [Metrics] AUC-ROC: 0.906311 - AUC-PR: 0.774948 - ACC: 0.841397 - Precision: 0.673525 - Recall: 0.709501 - F1: 0.691045 - MCC: 0.584815 - Logloss: 0.315214 - MSE: 0.101949 - RMSE: 0.319295 - COPC: 1.000179 - KLD: 0.707276
2024-11-21 21:49:55 INFO Save best model: monitor(max): 0.591096
2024-11-21 21:49:55 INFO --- 4283/4283 batches finished ---
2024-11-21 21:49:55 INFO Train loss: 0.315005
2024-11-21 21:49:55 INFO ************ Epoch=14 end ************
2024-11-21 22:44:49 INFO [Metrics] AUC-ROC: 0.906308 - AUC-PR: 0.774939 - ACC: 0.841406 - Precision: 0.673580 - Recall: 0.709399 - F1: 0.691026 - MCC: 0.584802 - Logloss: 0.315215 - MSE: 0.101950 - RMSE: 0.319296 - COPC: 1.000183 - KLD: 0.707275
2024-11-21 22:44:49 INFO Monitor(max) STOP: 0.591093 !
2024-11-21 22:44:49 INFO Reduce learning rate on plateau: 0.000001
2024-11-21 22:44:49 INFO --- 4283/4283 batches finished ---
2024-11-21 22:44:49 INFO Train loss: 0.315004
2024-11-21 22:44:49 INFO ************ Epoch=15 end ************
2024-11-21 23:39:39 INFO [Metrics] AUC-ROC: 0.906308 - AUC-PR: 0.774941 - ACC: 0.841410 - Precision: 0.673562 - Recall: 0.709490 - F1: 0.691060 - MCC: 0.584839 - Logloss: 0.315215 - MSE: 0.101950 - RMSE: 0.319296 - COPC: 1.000166 - KLD: 0.707276
2024-11-21 23:39:39 INFO Monitor(max) STOP: 0.591093 !
2024-11-21 23:39:39 INFO Reduce learning rate on plateau: 0.000001
2024-11-21 23:39:39 INFO Early stopping at epoch=16
2024-11-21 23:39:39 INFO --- 4283/4283 batches finished ---
2024-11-21 23:39:39 INFO Train loss: 0.315002
2024-11-21 23:39:39 INFO Training finished.
2024-11-21 23:39:39 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/CCPM/Synthetic/CCPM_model_seed2019.ckpt
2024-11-21 23:39:40 INFO Start evaluate model
2024-11-21 23:42:44 INFO [Metrics] AUC-ROC: 0.906311 - AUC-PR: 0.774948 - ACC: 0.841397 - Precision: 0.673525 - Recall: 0.709501 - F1: 0.691045 - MCC: 0.584815 - Logloss: 0.315214 - MSE: 0.101949 - RMSE: 0.319295 - COPC: 1.000179 - KLD: 0.707276
2024-11-21 23:42:44 INFO Start testing model
2024-11-21 23:45:49 INFO [Metrics] AUC-ROC: 0.906278 - AUC-PR: 0.775331 - ACC: 0.841321 - Precision: 0.673648 - Recall: 0.708539 - F1: 0.690653 - MCC: 0.584374 - Logloss: 0.315111 - MSE: 0.101886 - RMSE: 0.319197 - COPC: 1.000970 - KLD: 0.707369
