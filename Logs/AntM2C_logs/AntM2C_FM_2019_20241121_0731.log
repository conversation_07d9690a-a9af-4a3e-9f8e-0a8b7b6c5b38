2024-11-21 07:31:52 INFO all args: Namespace(dataset_name='AntM2C', dataset_path='/data/ctr/AntM2C/', model_name='FM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-21 07:31:52 INFO Start process AntM2C !
2024-11-21 07:31:52 INFO Start get feature encoder and feature map
2024-11-21 07:31:54 INFO Reading training data from /data/ctr/AntM2C/train.csv
2024-11-21 07:31:55 INFO Fill NaN done!
2024-11-21 07:31:55 INFO Process categorical column: user_id 
2024-11-21 07:31:55 INFO Process categorical column: item_id 
2024-11-21 07:31:55 INFO Process categorical column: deep_features_14 
2024-11-21 07:31:56 INFO Process categorical column: deep_features_19 
2024-11-21 07:31:56 INFO Process categorical column: deep_features_20 
2024-11-21 07:31:56 INFO Process categorical column: deep_features_21 
2024-11-21 07:31:56 INFO Process categorical column: deep_features_22 
2024-11-21 07:31:56 INFO Process categorical column: deep_features_23 
2024-11-21 07:31:56 INFO Process categorical column: deep_features_24 
2024-11-21 07:31:56 INFO Process categorical column: deep_features_25 
2024-11-21 07:31:56 INFO Process categorical column: deep_features_26 
2024-11-21 07:31:56 INFO Process categorical column: hour 
2024-11-21 07:31:57 INFO Process categorical column: weekday 
2024-11-21 07:31:57 INFO Process categorical column: weekend 
2024-11-21 07:31:57 INFO Set feature index
2024-11-21 07:31:57 INFO Pickle feature_encode: /data/ctr/AntM2C/feature_encoder.pkl
2024-11-21 07:31:57 INFO Save feature_map to json: /data/ctr/AntM2C/feature_map.json
2024-11-21 07:31:57 INFO Loading AntM2C dataset
2024-11-21 07:31:59 INFO Start process data for training, validation or testing
2024-11-21 07:32:00 INFO Fill NaN done!
2024-11-21 07:32:00 INFO Transform feature
2024-11-21 07:32:03 INFO Saving h5 data at /data/ctr/AntM2C/train.h5
2024-11-21 07:32:03 INFO Start process data for training, validation or testing
2024-11-21 07:32:04 INFO Fill NaN done!
2024-11-21 07:32:04 INFO Transform feature
2024-11-21 07:32:04 INFO Saving h5 data at /data/ctr/AntM2C/valid.h5
2024-11-21 07:32:04 INFO Start process data for training, validation or testing
2024-11-21 07:32:04 INFO Fill NaN done!
2024-11-21 07:32:04 INFO Transform feature
2024-11-21 07:32:05 INFO Saving h5 data at /data/ctr/AntM2C/test.h5
2024-11-21 07:32:05 INFO Train samples: total/1537877, pos/711221, neg/826656, ratio/46.25%
2024-11-21 07:32:05 INFO Validation samples: total/170876, pos/85112, neg/85764, ratio/49.81%
2024-11-21 07:32:05 INFO Test samples: total/189862, pos/91814, neg/98048, ratio/48.36%
2024-11-21 07:32:05 INFO Loading data done
2024-11-21 07:32:05 INFO Model: FM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_id): Embedding(66290, 16, padding_idx=66289)
      (item_id): Embedding(8537, 16, padding_idx=8536)
      (deep_features_14): Embedding(2156, 16, padding_idx=2155)
      (deep_features_19): Embedding(14, 16, padding_idx=13)
      (deep_features_20): Embedding(6, 16, padding_idx=5)
      (deep_features_21): Embedding(80, 16, padding_idx=79)
      (deep_features_22): Embedding(4, 16, padding_idx=3)
      (deep_features_23): Embedding(7, 16, padding_idx=6)
      (deep_features_24): Embedding(32, 16, padding_idx=31)
      (deep_features_25): Embedding(357, 16, padding_idx=356)
      (deep_features_26): Embedding(2480, 16, padding_idx=2479)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fm_layer): FM_Layer(
    (inner_product_layer): InnerProductLayer()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (user_id): Embedding(66290, 1, padding_idx=66289)
          (item_id): Embedding(8537, 1, padding_idx=8536)
          (deep_features_14): Embedding(2156, 1, padding_idx=2155)
          (deep_features_19): Embedding(14, 1, padding_idx=13)
          (deep_features_20): Embedding(6, 1, padding_idx=5)
          (deep_features_21): Embedding(80, 1, padding_idx=79)
          (deep_features_22): Embedding(4, 1, padding_idx=3)
          (deep_features_23): Embedding(7, 1, padding_idx=6)
          (deep_features_24): Embedding(32, 1, padding_idx=31)
          (deep_features_25): Embedding(357, 1, padding_idx=356)
          (deep_features_26): Embedding(2480, 1, padding_idx=2479)
          (hour): Embedding(25, 1, padding_idx=24)
          (weekday): Embedding(8, 1, padding_idx=7)
          (weekend): Embedding(3, 1, padding_idx=2)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
    (final_activation): Sigmoid()
  )
)
2024-11-21 07:32:05 INFO Model parameters: 1359984
2024-11-21 07:32:05 INFO Start training model
2024-11-21 07:32:06 INFO Start training: 154 batches/epoch
2024-11-21 07:32:06 INFO ************ Epoch=1 start ************
2024-11-21 07:32:15 INFO [Metrics] AUC-ROC: 0.811690 - AUC-PR: 0.818572 - ACC: 0.727498 - Precision: 0.807528 - Recall: 0.594640 - F1: 0.684923 - MCC: 0.471009 - Logloss: 0.559279 - MSE: 0.187468 - RMSE: 0.432976 - COPC: 1.029090 - KLD: 0.566584
2024-11-21 07:32:15 INFO Save best model: monitor(max): 0.252411
2024-11-21 07:32:15 INFO --- 154/154 batches finished ---
2024-11-21 07:32:15 INFO Train loss: 0.599244
2024-11-21 07:32:15 INFO ************ Epoch=1 end ************
2024-11-21 07:32:24 INFO [Metrics] AUC-ROC: 0.840014 - AUC-PR: 0.839000 - ACC: 0.755413 - Precision: 0.761778 - Recall: 0.740530 - F1: 0.751004 - MCC: 0.510965 - Logloss: 0.498167 - MSE: 0.164687 - RMSE: 0.405817 - COPC: 0.941740 - KLD: 0.510162
2024-11-21 07:32:24 INFO Save best model: monitor(max): 0.341848
2024-11-21 07:32:24 INFO --- 154/154 batches finished ---
2024-11-21 07:32:24 INFO Train loss: 0.440083
2024-11-21 07:32:24 INFO ************ Epoch=2 end ************
2024-11-21 07:32:33 INFO [Metrics] AUC-ROC: 0.842849 - AUC-PR: 0.840840 - ACC: 0.756853 - Precision: 0.786259 - Recall: 0.702933 - F1: 0.742264 - MCC: 0.516391 - Logloss: 0.488002 - MSE: 0.161034 - RMSE: 0.401291 - COPC: 0.998333 - KLD: 0.495782
2024-11-21 07:32:33 INFO Save best model: monitor(max): 0.354847
2024-11-21 07:32:33 INFO --- 154/154 batches finished ---
2024-11-21 07:32:33 INFO Train loss: 0.399810
2024-11-21 07:32:33 INFO ************ Epoch=3 end ************
2024-11-21 07:32:42 INFO [Metrics] AUC-ROC: 0.860518 - AUC-PR: 0.852491 - ACC: 0.766573 - Precision: 0.832581 - Recall: 0.665100 - F1: 0.739476 - MCC: 0.543831 - Logloss: 0.472853 - MSE: 0.155333 - RMSE: 0.394124 - COPC: 1.088198 - KLD: 0.470687
2024-11-21 07:32:42 INFO Save best model: monitor(max): 0.387665
2024-11-21 07:32:42 INFO --- 154/154 batches finished ---
2024-11-21 07:32:42 INFO Train loss: 0.382012
2024-11-21 07:32:42 INFO ************ Epoch=4 end ************
2024-11-21 07:32:51 INFO [Metrics] AUC-ROC: 0.857227 - AUC-PR: 0.849909 - ACC: 0.771899 - Precision: 0.771239 - Recall: 0.770632 - F1: 0.770935 - MCC: 0.543790 - Logloss: 0.473403 - MSE: 0.154070 - RMSE: 0.392517 - COPC: 0.958729 - KLD: 0.472076
2024-11-21 07:32:51 INFO Monitor(max) STOP: 0.383824 !
2024-11-21 07:32:51 INFO Reduce learning rate on plateau: 0.000100
2024-11-21 07:32:51 INFO --- 154/154 batches finished ---
2024-11-21 07:32:51 INFO Train loss: 0.363452
2024-11-21 07:32:51 INFO ************ Epoch=5 end ************
2024-11-21 07:33:00 INFO [Metrics] AUC-ROC: 0.859138 - AUC-PR: 0.851344 - ACC: 0.767703 - Precision: 0.826091 - Recall: 0.675921 - F1: 0.743499 - MCC: 0.544086 - Logloss: 0.473479 - MSE: 0.155417 - RMSE: 0.394230 - COPC: 1.085602 - KLD: 0.472271
2024-11-21 07:33:00 INFO Monitor(max) STOP: 0.385659 !
2024-11-21 07:33:00 INFO Reduce learning rate on plateau: 0.000010
2024-11-21 07:33:00 INFO Early stopping at epoch=6
2024-11-21 07:33:00 INFO --- 154/154 batches finished ---
2024-11-21 07:33:00 INFO Train loss: 0.353561
2024-11-21 07:33:00 INFO Training finished.
2024-11-21 07:33:00 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FM/AntM2C/FM_model_seed2019.ckpt
2024-11-21 07:33:00 INFO Start evaluate model
2024-11-21 07:33:02 INFO [Metrics] AUC-ROC: 0.860518 - AUC-PR: 0.852491 - ACC: 0.766573 - Precision: 0.832581 - Recall: 0.665100 - F1: 0.739476 - MCC: 0.543831 - Logloss: 0.472853 - MSE: 0.155333 - RMSE: 0.394124 - COPC: 1.088198 - KLD: 0.470687
2024-11-21 07:33:02 INFO Start testing model
2024-11-21 07:33:03 INFO [Metrics] AUC-ROC: 0.838288 - AUC-PR: 0.832888 - ACC: 0.734202 - Precision: 0.881512 - Recall: 0.520291 - F1: 0.654361 - MCC: 0.503257 - Logloss: 0.521725 - MSE: 0.174591 - RMSE: 0.417840 - COPC: 1.255254 - KLD: 0.533077
