2024-11-21 08:07:52 INFO all args: Namespace(dataset_name='AntM2C', dataset_path='/data/ctr/AntM2C/', model_name='DeepFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-21 08:07:52 INFO Start process AntM2C !
2024-11-21 08:07:52 INFO Loading AntM2C dataset
2024-11-21 08:07:52 INFO Load h5 data from /data/ctr/AntM2C/train.h5
2024-11-21 08:07:52 INFO Load h5 data from /data/ctr/AntM2C/valid.h5
2024-11-21 08:07:52 INFO Load h5 data from /data/ctr/AntM2C/test.h5
2024-11-21 08:07:52 INFO Train samples: total/1537877, pos/711221, neg/826656, ratio/46.25%
2024-11-21 08:07:52 INFO Validation samples: total/170876, pos/85112, neg/85764, ratio/49.81%
2024-11-21 08:07:52 INFO Test samples: total/189862, pos/91814, neg/98048, ratio/48.36%
2024-11-21 08:07:52 INFO Loading data done
2024-11-21 08:07:53 INFO Model: DeepFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (user_id): Embedding(66290, 16)
        (item_id): Embedding(8537, 16)
        (deep_features_14): Embedding(2156, 16)
        (deep_features_19): Embedding(14, 16)
        (deep_features_20): Embedding(6, 16)
        (deep_features_21): Embedding(80, 16)
        (deep_features_22): Embedding(4, 16)
        (deep_features_23): Embedding(7, 16)
        (deep_features_24): Embedding(32, 16)
        (deep_features_25): Embedding(357, 16)
        (deep_features_26): Embedding(2480, 16)
        (hour): Embedding(25, 16)
        (weekday): Embedding(8, 16)
        (weekend): Embedding(3, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (fm_layer): FM_Layer_v2(
    (inner_product_layer): InnerProductLayer_v2()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (user_id): Embedding(66290, 1, padding_idx=66289)
          (item_id): Embedding(8537, 1, padding_idx=8536)
          (deep_features_14): Embedding(2156, 1, padding_idx=2155)
          (deep_features_19): Embedding(14, 1, padding_idx=13)
          (deep_features_20): Embedding(6, 1, padding_idx=5)
          (deep_features_21): Embedding(80, 1, padding_idx=79)
          (deep_features_22): Embedding(4, 1, padding_idx=3)
          (deep_features_23): Embedding(7, 1, padding_idx=6)
          (deep_features_24): Embedding(32, 1, padding_idx=31)
          (deep_features_25): Embedding(357, 1, padding_idx=356)
          (deep_features_26): Embedding(2480, 1, padding_idx=2479)
          (hour): Embedding(25, 1, padding_idx=24)
          (weekday): Embedding(8, 1, padding_idx=7)
          (weekend): Embedding(3, 1, padding_idx=2)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=224, out_features=1000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=1000, out_features=1000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=1000, out_features=1000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=1000, out_features=1000, bias=True)
      (7): ReLU()
      (8): Linear(in_features=1000, out_features=1000, bias=True)
      (9): ReLU()
      (10): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-21 08:07:53 INFO Model parameters: 5589984
2024-11-21 08:07:53 INFO Start training model
2024-11-21 08:07:53 INFO Start training: 154 batches/epoch
2024-11-21 08:07:53 INFO ************ Epoch=1 start ************
2024-11-21 08:08:03 INFO [Metrics] AUC-ROC: 0.872207 - AUC-PR: 0.863405 - ACC: 0.784873 - Precision: 0.778866 - Recall: 0.793343 - F1: 0.786038 - MCC: 0.569868 - Logloss: 0.446961 - MSE: 0.145321 - RMSE: 0.381210 - COPC: 0.990723 - KLD: 0.441684
2024-11-21 08:08:03 INFO Save best model: monitor(max): 0.425246
2024-11-21 08:08:03 INFO --- 154/154 batches finished ---
2024-11-21 08:08:03 INFO Train loss: 0.435791
2024-11-21 08:08:03 INFO ************ Epoch=1 end ************
2024-11-21 08:08:13 INFO [Metrics] AUC-ROC: 0.872134 - AUC-PR: 0.864919 - ACC: 0.789596 - Precision: 0.793841 - Recall: 0.780196 - F1: 0.786959 - MCC: 0.579243 - Logloss: 0.465140 - MSE: 0.147284 - RMSE: 0.383776 - COPC: 1.094289 - KLD: 0.473097
2024-11-21 08:08:13 INFO Monitor(max) STOP: 0.406994 !
2024-11-21 08:08:13 INFO Reduce learning rate on plateau: 0.000100
2024-11-21 08:08:13 INFO --- 154/154 batches finished ---
2024-11-21 08:08:13 INFO Train loss: 0.338418
2024-11-21 08:08:13 INFO ************ Epoch=2 end ************
2024-11-21 08:08:22 INFO [Metrics] AUC-ROC: 0.872744 - AUC-PR: 0.865473 - ACC: 0.788730 - Precision: 0.801617 - Recall: 0.765215 - F1: 0.782993 - MCC: 0.577972 - Logloss: 0.463598 - MSE: 0.146897 - RMSE: 0.383272 - COPC: 1.047397 - KLD: 0.460558
2024-11-21 08:08:22 INFO Monitor(max) STOP: 0.409146 !
2024-11-21 08:08:22 INFO Reduce learning rate on plateau: 0.000010
2024-11-21 08:08:22 INFO Early stopping at epoch=3
2024-11-21 08:08:22 INFO --- 154/154 batches finished ---
2024-11-21 08:08:22 INFO Train loss: 0.312375
2024-11-21 08:08:22 INFO Training finished.
2024-11-21 08:08:22 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/DeepFM/AntM2C/DeepFM_model_seed2019.ckpt
2024-11-21 08:08:22 INFO Start evaluate model
2024-11-21 08:08:24 INFO [Metrics] AUC-ROC: 0.872207 - AUC-PR: 0.863405 - ACC: 0.784873 - Precision: 0.778866 - Recall: 0.793343 - F1: 0.786038 - MCC: 0.569868 - Logloss: 0.446961 - MSE: 0.145321 - RMSE: 0.381210 - COPC: 0.990723 - KLD: 0.441684
2024-11-21 08:08:24 INFO Start testing model
2024-11-21 08:08:26 INFO [Metrics] AUC-ROC: 0.855441 - AUC-PR: 0.848374 - ACC: 0.766636 - Precision: 0.749630 - Recall: 0.776908 - F1: 0.763025 - MCC: 0.533638 - Logloss: 0.472438 - MSE: 0.155167 - RMSE: 0.393912 - COPC: 0.946760 - KLD: 0.495236
