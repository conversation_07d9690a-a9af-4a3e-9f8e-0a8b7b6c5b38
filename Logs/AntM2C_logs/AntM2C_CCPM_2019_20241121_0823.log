2024-11-21 08:23:40 INFO all args: Namespace(dataset_name='AntM2C', dataset_path='/data/ctr/AntM2C/', model_name='CCPM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=6000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-21 08:23:40 INFO Start process AntM2C !
2024-11-21 08:23:40 INFO Loading AntM2C dataset
2024-11-21 08:23:40 INFO Load h5 data from /data/ctr/AntM2C/train.h5
2024-11-21 08:23:40 INFO Load h5 data from /data/ctr/AntM2C/valid.h5
2024-11-21 08:23:40 INFO Load h5 data from /data/ctr/AntM2C/test.h5
2024-11-21 08:23:40 INFO Train samples: total/1537877, pos/711221, neg/826656, ratio/46.25%
2024-11-21 08:23:40 INFO Validation samples: total/170876, pos/85112, neg/85764, ratio/49.81%
2024-11-21 08:23:40 INFO Test samples: total/189862, pos/91814, neg/98048, ratio/48.36%
2024-11-21 08:23:40 INFO Loading data done
2024-11-21 08:23:41 INFO Model: CCPM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_id): Embedding(66290, 16, padding_idx=66289)
      (item_id): Embedding(8537, 16, padding_idx=8536)
      (deep_features_14): Embedding(2156, 16, padding_idx=2155)
      (deep_features_19): Embedding(14, 16, padding_idx=13)
      (deep_features_20): Embedding(6, 16, padding_idx=5)
      (deep_features_21): Embedding(80, 16, padding_idx=79)
      (deep_features_22): Embedding(4, 16, padding_idx=3)
      (deep_features_23): Embedding(7, 16, padding_idx=6)
      (deep_features_24): Embedding(32, 16, padding_idx=31)
      (deep_features_25): Embedding(357, 16, padding_idx=356)
      (deep_features_26): Embedding(2480, 16, padding_idx=2479)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (conv_layer): CCPM_ConvLayer(
    (conv_layer): Sequential(
      (0): ZeroPad2d((0, 0, 6, 6))
      (1): Conv2d(1, 128, kernel_size=(7, 1), stride=(1, 1))
      (2): KMaxPooling()
      (3): Tanh()
      (4): ZeroPad2d((0, 0, 4, 4))
      (5): Conv2d(128, 256, kernel_size=(5, 1), stride=(1, 1))
      (6): KMaxPooling()
      (7): Tanh()
      (8): ZeroPad2d((0, 0, 2, 2))
      (9): Conv2d(256, 512, kernel_size=(3, 1), stride=(1, 1))
      (10): KMaxPooling()
      (11): Tanh()
    )
  )
  (fc): Linear(in_features=24576, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-21 08:23:41 INFO Model parameters: 1863409
2024-11-21 08:23:41 INFO Start training model
2024-11-21 08:23:41 INFO Start training: 257 batches/epoch
2024-11-21 08:23:41 INFO ************ Epoch=1 start ************
2024-11-21 08:28:06 INFO [Metrics] AUC-ROC: 0.862595 - AUC-PR: 0.852526 - ACC: 0.768669 - Precision: 0.807532 - Recall: 0.703156 - F1: 0.751738 - MCC: 0.541616 - Logloss: 0.464840 - MSE: 0.152778 - RMSE: 0.390868 - COPC: 1.068897 - KLD: 0.462037
2024-11-21 08:28:06 INFO Save best model: monitor(max): 0.397755
2024-11-21 08:28:06 INFO --- 257/257 batches finished ---
2024-11-21 08:28:06 INFO Train loss: 0.518783
2024-11-21 08:28:06 INFO ************ Epoch=1 end ************
2024-11-21 08:32:23 INFO [Metrics] AUC-ROC: 0.868941 - AUC-PR: 0.858373 - ACC: 0.782965 - Precision: 0.760208 - Recall: 0.824267 - F1: 0.790942 - MCC: 0.568067 - Logloss: 0.457587 - MSE: 0.148585 - RMSE: 0.385468 - COPC: 0.945860 - KLD: 0.450113
2024-11-21 08:32:23 INFO Save best model: monitor(max): 0.411354
2024-11-21 08:32:23 INFO --- 257/257 batches finished ---
2024-11-21 08:32:23 INFO Train loss: 0.361046
2024-11-21 08:32:23 INFO ************ Epoch=2 end ************
2024-11-21 08:36:42 INFO [Metrics] AUC-ROC: 0.868632 - AUC-PR: 0.861384 - ACC: 0.783100 - Precision: 0.804976 - Recall: 0.745042 - F1: 0.773850 - MCC: 0.567635 - Logloss: 0.469374 - MSE: 0.150561 - RMSE: 0.388022 - COPC: 1.075710 - KLD: 0.476918
2024-11-21 08:36:42 INFO Monitor(max) STOP: 0.399258 !
2024-11-21 08:36:42 INFO Reduce learning rate on plateau: 0.000100
2024-11-21 08:36:42 INFO --- 257/257 batches finished ---
2024-11-21 08:36:42 INFO Train loss: 0.333344
2024-11-21 08:36:42 INFO ************ Epoch=3 end ************
2024-11-21 08:41:01 INFO [Metrics] AUC-ROC: 0.866014 - AUC-PR: 0.857746 - ACC: 0.782421 - Precision: 0.796545 - Recall: 0.756368 - F1: 0.775937 - MCC: 0.565467 - Logloss: 0.505153 - MSE: 0.155749 - RMSE: 0.394651 - COPC: 1.046109 - KLD: 0.520865
2024-11-21 08:41:01 INFO Monitor(max) STOP: 0.360862 !
2024-11-21 08:41:01 INFO Reduce learning rate on plateau: 0.000010
2024-11-21 08:41:01 INFO Early stopping at epoch=4
2024-11-21 08:41:01 INFO --- 257/257 batches finished ---
2024-11-21 08:41:01 INFO Train loss: 0.313684
2024-11-21 08:41:01 INFO Training finished.
2024-11-21 08:41:01 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/CCPM/AntM2C/CCPM_model_seed2019.ckpt
2024-11-21 08:41:01 INFO Start evaluate model
2024-11-21 08:41:15 INFO [Metrics] AUC-ROC: 0.868941 - AUC-PR: 0.858373 - ACC: 0.782965 - Precision: 0.760208 - Recall: 0.824267 - F1: 0.790942 - MCC: 0.568067 - Logloss: 0.457587 - MSE: 0.148585 - RMSE: 0.385468 - COPC: 0.945860 - KLD: 0.450113
2024-11-21 08:41:15 INFO Start testing model
2024-11-21 08:41:31 INFO [Metrics] AUC-ROC: 0.851274 - AUC-PR: 0.841864 - ACC: 0.761917 - Precision: 0.728752 - Recall: 0.808657 - F1: 0.766628 - MCC: 0.527938 - Logloss: 0.489528 - MSE: 0.160969 - RMSE: 0.401209 - COPC: 0.907142 - KLD: 0.505714
