2024-11-21 07:58:49 INFO all args: Namespace(dataset_name='AntM2C', dataset_path='/data/ctr/AntM2C/', model_name='PNN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-21 07:58:49 INFO Start process AntM2C !
2024-11-21 07:58:49 INFO Loading AntM2C dataset
2024-11-21 07:58:49 INFO Load h5 data from /data/ctr/AntM2C/train.h5
2024-11-21 07:58:49 INFO Load h5 data from /data/ctr/AntM2C/valid.h5
2024-11-21 07:58:49 INFO Load h5 data from /data/ctr/AntM2C/test.h5
2024-11-21 07:58:49 INFO Train samples: total/1537877, pos/711221, neg/826656, ratio/46.25%
2024-11-21 07:58:49 INFO Validation samples: total/170876, pos/85112, neg/85764, ratio/49.81%
2024-11-21 07:58:49 INFO Test samples: total/189862, pos/91814, neg/98048, ratio/48.36%
2024-11-21 07:58:49 INFO Loading data done
2024-11-21 07:58:49 INFO Model: PNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_id): Embedding(66290, 16, padding_idx=66289)
      (item_id): Embedding(8537, 16, padding_idx=8536)
      (deep_features_14): Embedding(2156, 16, padding_idx=2155)
      (deep_features_19): Embedding(14, 16, padding_idx=13)
      (deep_features_20): Embedding(6, 16, padding_idx=5)
      (deep_features_21): Embedding(80, 16, padding_idx=79)
      (deep_features_22): Embedding(4, 16, padding_idx=3)
      (deep_features_23): Embedding(7, 16, padding_idx=6)
      (deep_features_24): Embedding(32, 16, padding_idx=31)
      (deep_features_25): Embedding(357, 16, padding_idx=356)
      (deep_features_26): Embedding(2480, 16, padding_idx=2479)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=315, out_features=1000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=1000, out_features=1000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=1000, out_features=1, bias=True)
      (7): Sigmoid()
    )
  )
)
2024-11-21 07:58:49 INFO Model parameters: 2597985
2024-11-21 07:58:49 INFO Start training model
2024-11-21 07:58:49 INFO Start training: 154 batches/epoch
2024-11-21 07:58:49 INFO ************ Epoch=1 start ************
2024-11-21 07:59:00 INFO [Metrics] AUC-ROC: 0.866105 - AUC-PR: 0.853766 - ACC: 0.777453 - Precision: 0.750820 - Recall: 0.827991 - F1: 0.787520 - MCC: 0.558003 - Logloss: 0.457771 - MSE: 0.149294 - RMSE: 0.386386 - COPC: 0.940435 - KLD: 0.453801
2024-11-21 07:59:00 INFO Save best model: monitor(max): 0.408334
2024-11-21 07:59:00 INFO --- 154/154 batches finished ---
2024-11-21 07:59:00 INFO Train loss: 0.426374
2024-11-21 07:59:00 INFO ************ Epoch=1 end ************
2024-11-21 07:59:10 INFO [Metrics] AUC-ROC: 0.872316 - AUC-PR: 0.864624 - ACC: 0.788619 - Precision: 0.771978 - Recall: 0.816912 - F1: 0.793810 - MCC: 0.578297 - Logloss: 0.452058 - MSE: 0.146143 - RMSE: 0.382287 - COPC: 1.003256 - KLD: 0.454048
2024-11-21 07:59:10 INFO Save best model: monitor(max): 0.420258
2024-11-21 07:59:10 INFO --- 154/154 batches finished ---
2024-11-21 07:59:10 INFO Train loss: 0.345809
2024-11-21 07:59:10 INFO ************ Epoch=2 end ************
2024-11-21 07:59:19 INFO [Metrics] AUC-ROC: 0.867223 - AUC-PR: 0.861002 - ACC: 0.782825 - Precision: 0.753909 - Recall: 0.837297 - F1: 0.793418 - MCC: 0.569290 - Logloss: 0.480937 - MSE: 0.152684 - RMSE: 0.390749 - COPC: 0.957426 - KLD: 0.480736
2024-11-21 07:59:19 INFO Monitor(max) STOP: 0.386286 !
2024-11-21 07:59:19 INFO Reduce learning rate on plateau: 0.000100
2024-11-21 07:59:19 INFO --- 154/154 batches finished ---
2024-11-21 07:59:20 INFO Train loss: 0.315836
2024-11-21 07:59:20 INFO ************ Epoch=3 end ************
2024-11-21 07:59:29 INFO [Metrics] AUC-ROC: 0.871600 - AUC-PR: 0.863584 - ACC: 0.785687 - Precision: 0.801437 - Recall: 0.757379 - F1: 0.778785 - MCC: 0.572137 - Logloss: 0.479455 - MSE: 0.149949 - RMSE: 0.387232 - COPC: 1.081429 - KLD: 0.486035
2024-11-21 07:59:29 INFO Monitor(max) STOP: 0.392145 !
2024-11-21 07:59:29 INFO Reduce learning rate on plateau: 0.000010
2024-11-21 07:59:29 INFO Early stopping at epoch=4
2024-11-21 07:59:29 INFO --- 154/154 batches finished ---
2024-11-21 07:59:29 INFO Train loss: 0.311562
2024-11-21 07:59:29 INFO Training finished.
2024-11-21 07:59:29 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/PNN/AntM2C/PNN_model_seed2019.ckpt
2024-11-21 07:59:29 INFO Start evaluate model
2024-11-21 07:59:31 INFO [Metrics] AUC-ROC: 0.872316 - AUC-PR: 0.864624 - ACC: 0.788619 - Precision: 0.771978 - Recall: 0.816912 - F1: 0.793810 - MCC: 0.578297 - Logloss: 0.452058 - MSE: 0.146143 - RMSE: 0.382287 - COPC: 1.003256 - KLD: 0.454048
2024-11-21 07:59:31 INFO Start testing model
2024-11-21 07:59:33 INFO [Metrics] AUC-ROC: 0.853190 - AUC-PR: 0.846413 - ACC: 0.766462 - Precision: 0.744032 - Recall: 0.788246 - F1: 0.765501 - MCC: 0.534183 - Logloss: 0.484445 - MSE: 0.158418 - RMSE: 0.398018 - COPC: 0.986844 - KLD: 0.516707
