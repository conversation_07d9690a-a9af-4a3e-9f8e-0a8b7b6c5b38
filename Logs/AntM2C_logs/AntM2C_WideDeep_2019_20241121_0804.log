2024-11-21 08:04:09 INFO all args: Namespace(dataset_name='AntM2C', dataset_path='/data/ctr/AntM2C/', model_name='WideDeep', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-21 08:04:09 INFO Start process AntM2C !
2024-11-21 08:04:09 INFO Loading AntM2C dataset
2024-11-21 08:04:09 INFO Load h5 data from /data/ctr/AntM2C/train.h5
2024-11-21 08:04:09 INFO Load h5 data from /data/ctr/AntM2C/valid.h5
2024-11-21 08:04:09 INFO Load h5 data from /data/ctr/AntM2C/test.h5
2024-11-21 08:04:09 INFO Train samples: total/1537877, pos/711221, neg/826656, ratio/46.25%
2024-11-21 08:04:09 INFO Validation samples: total/170876, pos/85112, neg/85764, ratio/49.81%
2024-11-21 08:04:09 INFO Test samples: total/189862, pos/91814, neg/98048, ratio/48.36%
2024-11-21 08:04:09 INFO Loading data done
2024-11-21 08:04:10 INFO Model: WideDeep(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (user_id): Embedding(66290, 16)
        (item_id): Embedding(8537, 16)
        (deep_features_14): Embedding(2156, 16)
        (deep_features_19): Embedding(14, 16)
        (deep_features_20): Embedding(6, 16)
        (deep_features_21): Embedding(80, 16)
        (deep_features_22): Embedding(4, 16)
        (deep_features_23): Embedding(7, 16)
        (deep_features_24): Embedding(32, 16)
        (deep_features_25): Embedding(357, 16)
        (deep_features_26): Embedding(2480, 16)
        (hour): Embedding(25, 16)
        (weekday): Embedding(8, 16)
        (weekend): Embedding(3, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (user_id): Embedding(66290, 1, padding_idx=66289)
        (item_id): Embedding(8537, 1, padding_idx=8536)
        (deep_features_14): Embedding(2156, 1, padding_idx=2155)
        (deep_features_19): Embedding(14, 1, padding_idx=13)
        (deep_features_20): Embedding(6, 1, padding_idx=5)
        (deep_features_21): Embedding(80, 1, padding_idx=79)
        (deep_features_22): Embedding(4, 1, padding_idx=3)
        (deep_features_23): Embedding(7, 1, padding_idx=6)
        (deep_features_24): Embedding(32, 1, padding_idx=31)
        (deep_features_25): Embedding(357, 1, padding_idx=356)
        (deep_features_26): Embedding(2480, 1, padding_idx=2479)
        (hour): Embedding(25, 1, padding_idx=24)
        (weekday): Embedding(8, 1, padding_idx=7)
        (weekend): Embedding(3, 1, padding_idx=2)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=224, out_features=1000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=1000, out_features=1000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=1000, out_features=1000, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.2, inplace=False)
      (9): Linear(in_features=1000, out_features=1000, bias=True)
      (10): ReLU()
      (11): Dropout(p=0.2, inplace=False)
      (12): Linear(in_features=1000, out_features=1000, bias=True)
      (13): ReLU()
      (14): Dropout(p=0.2, inplace=False)
      (15): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-21 08:04:10 INFO Model parameters: 5589984
2024-11-21 08:04:10 INFO Start training model
2024-11-21 08:04:10 INFO Start training: 154 batches/epoch
2024-11-21 08:04:10 INFO ************ Epoch=1 start ************
2024-11-21 08:04:20 INFO [Metrics] AUC-ROC: 0.867701 - AUC-PR: 0.856906 - ACC: 0.778243 - Precision: 0.791163 - Recall: 0.753748 - F1: 0.772002 - MCC: 0.557018 - Logloss: 0.453052 - MSE: 0.148015 - RMSE: 0.384727 - COPC: 1.006687 - KLD: 0.449704
2024-11-21 08:04:20 INFO Save best model: monitor(max): 0.414649
2024-11-21 08:04:20 INFO --- 154/154 batches finished ---
2024-11-21 08:04:21 INFO Train loss: 0.442678
2024-11-21 08:04:21 INFO ************ Epoch=1 end ************
2024-11-21 08:04:31 INFO [Metrics] AUC-ROC: 0.876531 - AUC-PR: 0.868583 - ACC: 0.790761 - Precision: 0.787099 - Recall: 0.794941 - F1: 0.791000 - MCC: 0.581560 - Logloss: 0.449812 - MSE: 0.144492 - RMSE: 0.380121 - COPC: 1.065310 - KLD: 0.451965
2024-11-21 08:04:31 INFO Save best model: monitor(max): 0.426719
2024-11-21 08:04:31 INFO --- 154/154 batches finished ---
2024-11-21 08:04:31 INFO Train loss: 0.347758
2024-11-21 08:04:31 INFO ************ Epoch=2 end ************
2024-11-21 08:04:41 INFO [Metrics] AUC-ROC: 0.870234 - AUC-PR: 0.863535 - ACC: 0.786845 - Precision: 0.796000 - Recall: 0.769186 - F1: 0.782363 - MCC: 0.573953 - Logloss: 0.498735 - MSE: 0.151102 - RMSE: 0.388718 - COPC: 1.079432 - KLD: 0.518737
2024-11-21 08:04:41 INFO Monitor(max) STOP: 0.371500 !
2024-11-21 08:04:41 INFO Reduce learning rate on plateau: 0.000100
2024-11-21 08:04:41 INFO --- 154/154 batches finished ---
2024-11-21 08:04:41 INFO Train loss: 0.314886
2024-11-21 08:04:41 INFO ************ Epoch=3 end ************
2024-11-21 08:04:52 INFO [Metrics] AUC-ROC: 0.870558 - AUC-PR: 0.864973 - ACC: 0.792188 - Precision: 0.773772 - Recall: 0.823574 - F1: 0.797896 - MCC: 0.585677 - Logloss: 0.490859 - MSE: 0.147427 - RMSE: 0.383962 - COPC: 1.023076 - KLD: 0.500264
2024-11-21 08:04:52 INFO Monitor(max) STOP: 0.379699 !
2024-11-21 08:04:52 INFO Reduce learning rate on plateau: 0.000010
2024-11-21 08:04:52 INFO Early stopping at epoch=4
2024-11-21 08:04:52 INFO --- 154/154 batches finished ---
2024-11-21 08:04:52 INFO Train loss: 0.297292
2024-11-21 08:04:52 INFO Training finished.
2024-11-21 08:04:52 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/WideDeep/AntM2C/WideDeep_model_seed2019.ckpt
2024-11-21 08:04:52 INFO Start evaluate model
2024-11-21 08:04:53 INFO [Metrics] AUC-ROC: 0.876531 - AUC-PR: 0.868583 - ACC: 0.790761 - Precision: 0.787099 - Recall: 0.794941 - F1: 0.791000 - MCC: 0.581560 - Logloss: 0.449812 - MSE: 0.144492 - RMSE: 0.380121 - COPC: 1.065310 - KLD: 0.451965
2024-11-21 08:04:53 INFO Start testing model
2024-11-21 08:04:55 INFO [Metrics] AUC-ROC: 0.858770 - AUC-PR: 0.851486 - ACC: 0.771866 - Precision: 0.764547 - Recall: 0.763315 - F1: 0.763931 - MCC: 0.543217 - Logloss: 0.479421 - MSE: 0.154896 - RMSE: 0.393569 - COPC: 1.050546 - KLD: 0.514721
