2024-11-21 07:53:46 INFO all args: Namespace(dataset_name='AntM2C', dataset_path='/data/ctr/AntM2C/', model_name='FmFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-21 07:53:46 INFO Start process AntM2C !
2024-11-21 07:53:46 INFO Loading AntM2C dataset
2024-11-21 07:53:46 INFO Load h5 data from /data/ctr/AntM2C/train.h5
2024-11-21 07:53:46 INFO Load h5 data from /data/ctr/AntM2C/valid.h5
2024-11-21 07:53:46 INFO Load h5 data from /data/ctr/AntM2C/test.h5
2024-11-21 07:53:46 INFO Train samples: total/1537877, pos/711221, neg/826656, ratio/46.25%
2024-11-21 07:53:46 INFO Validation samples: total/170876, pos/85112, neg/85764, ratio/49.81%
2024-11-21 07:53:46 INFO Test samples: total/189862, pos/91814, neg/98048, ratio/48.36%
2024-11-21 07:53:46 INFO Loading data done
2024-11-21 07:53:46 INFO Model: FmFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (user_id): Embedding(66290, 16)
        (item_id): Embedding(8537, 16)
        (deep_features_14): Embedding(2156, 16)
        (deep_features_19): Embedding(14, 16)
        (deep_features_20): Embedding(6, 16)
        (deep_features_21): Embedding(80, 16)
        (deep_features_22): Embedding(4, 16)
        (deep_features_23): Embedding(7, 16)
        (deep_features_24): Embedding(32, 16)
        (deep_features_25): Embedding(357, 16)
        (deep_features_26): Embedding(2480, 16)
        (hour): Embedding(25, 16)
        (weekday): Embedding(8, 16)
        (weekend): Embedding(3, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (user_id): Embedding(66290, 1, padding_idx=66289)
        (item_id): Embedding(8537, 1, padding_idx=8536)
        (deep_features_14): Embedding(2156, 1, padding_idx=2155)
        (deep_features_19): Embedding(14, 1, padding_idx=13)
        (deep_features_20): Embedding(6, 1, padding_idx=5)
        (deep_features_21): Embedding(80, 1, padding_idx=79)
        (deep_features_22): Embedding(4, 1, padding_idx=3)
        (deep_features_23): Embedding(7, 1, padding_idx=6)
        (deep_features_24): Embedding(32, 1, padding_idx=31)
        (deep_features_25): Embedding(357, 1, padding_idx=356)
        (deep_features_26): Embedding(2480, 1, padding_idx=2479)
        (hour): Embedding(25, 1, padding_idx=24)
        (weekday): Embedding(8, 1, padding_idx=7)
        (weekend): Embedding(3, 1, padding_idx=2)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (final_activation): Sigmoid()
)
2024-11-21 07:53:46 INFO Model parameters: 1383279
2024-11-21 07:53:46 INFO Start training model
2024-11-21 07:53:46 INFO Start training: 154 batches/epoch
2024-11-21 07:53:46 INFO ************ Epoch=1 start ************
2024-11-21 07:53:57 INFO [Metrics] AUC-ROC: 0.851255 - AUC-PR: 0.845728 - ACC: 0.765069 - Precision: 0.769231 - Recall: 0.754770 - F1: 0.761932 - MCC: 0.530191 - Logloss: 0.476096 - MSE: 0.156822 - RMSE: 0.396008 - COPC: 0.978891 - KLD: 0.479963
2024-11-21 07:53:57 INFO Save best model: monitor(max): 0.375160
2024-11-21 07:53:57 INFO --- 154/154 batches finished ---
2024-11-21 07:53:57 INFO Train loss: 0.507718
2024-11-21 07:53:57 INFO ************ Epoch=1 end ************
2024-11-21 07:54:08 INFO [Metrics] AUC-ROC: 0.864256 - AUC-PR: 0.853960 - ACC: 0.779536 - Precision: 0.782307 - Recall: 0.772288 - F1: 0.777265 - MCC: 0.559090 - Logloss: 0.461237 - MSE: 0.149988 - RMSE: 0.387283 - COPC: 1.000634 - KLD: 0.460033
2024-11-21 07:54:08 INFO Save best model: monitor(max): 0.403019
2024-11-21 07:54:08 INFO --- 154/154 batches finished ---
2024-11-21 07:54:08 INFO Train loss: 0.360071
2024-11-21 07:54:08 INFO ************ Epoch=2 end ************
2024-11-21 07:54:18 INFO [Metrics] AUC-ROC: 0.865949 - AUC-PR: 0.854988 - ACC: 0.783398 - Precision: 0.778241 - Recall: 0.790347 - F1: 0.784247 - MCC: 0.566884 - Logloss: 0.463303 - MSE: 0.149443 - RMSE: 0.386579 - COPC: 0.989566 - KLD: 0.461882
2024-11-21 07:54:18 INFO Monitor(max) STOP: 0.402646 !
2024-11-21 07:54:18 INFO Reduce learning rate on plateau: 0.000100
2024-11-21 07:54:18 INFO --- 154/154 batches finished ---
2024-11-21 07:54:18 INFO Train loss: 0.344102
2024-11-21 07:54:18 INFO ************ Epoch=3 end ************
2024-11-21 07:54:29 INFO [Metrics] AUC-ROC: 0.864739 - AUC-PR: 0.853909 - ACC: 0.779249 - Precision: 0.806314 - Recall: 0.732846 - F1: 0.767826 - MCC: 0.560655 - Logloss: 0.474268 - MSE: 0.152247 - RMSE: 0.390189 - COPC: 1.077368 - KLD: 0.478865
2024-11-21 07:54:29 INFO Monitor(max) STOP: 0.390472 !
2024-11-21 07:54:29 INFO Reduce learning rate on plateau: 0.000010
2024-11-21 07:54:29 INFO Early stopping at epoch=4
2024-11-21 07:54:29 INFO --- 154/154 batches finished ---
2024-11-21 07:54:29 INFO Train loss: 0.329048
2024-11-21 07:54:29 INFO Training finished.
2024-11-21 07:54:29 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FmFM/AntM2C/FmFM_model_seed2019.ckpt
2024-11-21 07:54:29 INFO Start evaluate model
2024-11-21 07:54:30 INFO [Metrics] AUC-ROC: 0.864256 - AUC-PR: 0.853960 - ACC: 0.779536 - Precision: 0.782307 - Recall: 0.772288 - F1: 0.777265 - MCC: 0.559090 - Logloss: 0.461237 - MSE: 0.149988 - RMSE: 0.387283 - COPC: 1.000634 - KLD: 0.460033
2024-11-21 07:54:30 INFO Start testing model
2024-11-21 07:54:32 INFO [Metrics] AUC-ROC: 0.847421 - AUC-PR: 0.838477 - ACC: 0.764977 - Precision: 0.768551 - Recall: 0.735487 - F1: 0.751656 - MCC: 0.529262 - Logloss: 0.484227 - MSE: 0.158696 - RMSE: 0.398367 - COPC: 0.992054 - KLD: 0.512831
