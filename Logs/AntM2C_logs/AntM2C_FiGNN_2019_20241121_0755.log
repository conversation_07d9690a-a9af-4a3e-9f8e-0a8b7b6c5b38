2024-11-21 07:55:57 INFO all args: Namespace(dataset_name='AntM2C', dataset_path='/data/ctr/AntM2C/', model_name='FiGNN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-21 07:55:57 INFO Start process AntM2C !
2024-11-21 07:55:57 INFO Loading AntM2C dataset
2024-11-21 07:55:57 INFO Load h5 data from /data/ctr/AntM2C/train.h5
2024-11-21 07:55:57 INFO Load h5 data from /data/ctr/AntM2C/valid.h5
2024-11-21 07:55:57 INFO Load h5 data from /data/ctr/AntM2C/test.h5
2024-11-21 07:55:57 INFO Train samples: total/1537877, pos/711221, neg/826656, ratio/46.25%
2024-11-21 07:55:57 INFO Validation samples: total/170876, pos/85112, neg/85764, ratio/49.81%
2024-11-21 07:55:57 INFO Test samples: total/189862, pos/91814, neg/98048, ratio/48.36%
2024-11-21 07:55:57 INFO Loading data done
2024-11-21 07:55:57 INFO Model: FiGNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_id): Embedding(66290, 16, padding_idx=66289)
      (item_id): Embedding(8537, 16, padding_idx=8536)
      (deep_features_14): Embedding(2156, 16, padding_idx=2155)
      (deep_features_19): Embedding(14, 16, padding_idx=13)
      (deep_features_20): Embedding(6, 16, padding_idx=5)
      (deep_features_21): Embedding(80, 16, padding_idx=79)
      (deep_features_22): Embedding(4, 16, padding_idx=3)
      (deep_features_23): Embedding(7, 16, padding_idx=6)
      (deep_features_24): Embedding(32, 16, padding_idx=31)
      (deep_features_25): Embedding(357, 16, padding_idx=356)
      (deep_features_26): Embedding(2480, 16, padding_idx=2479)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fignn): FiGNN_Layer(
    (gnn): ModuleList(
      (0-3): 4 x GraphLayer()
    )
    (gru): GRUCell(16, 16)
    (leaky_relu): LeakyReLU(negative_slope=0.01)
    (W_attn): Linear(in_features=32, out_features=1, bias=False)
  )
  (fc): PredictionLayer(
    (mlp1): Linear(in_features=16, out_features=1, bias=False)
    (mlp2): Sequential(
      (0): Linear(in_features=224, out_features=14, bias=False)
      (1): Sigmoid()
    )
  )
  (final_activation): Sigmoid()
)
2024-11-21 07:55:57 INFO Model parameters: 1313536
2024-11-21 07:55:57 INFO Start training model
2024-11-21 07:55:57 INFO Start training: 154 batches/epoch
2024-11-21 07:55:57 INFO ************ Epoch=1 start ************
2024-11-21 07:56:08 INFO [Metrics] AUC-ROC: 0.853211 - AUC-PR: 0.846591 - ACC: 0.767270 - Precision: 0.766905 - Recall: 0.765391 - F1: 0.766148 - MCC: 0.534530 - Logloss: 0.474641 - MSE: 0.156150 - RMSE: 0.395158 - COPC: 0.968893 - KLD: 0.479264
2024-11-21 07:56:08 INFO Save best model: monitor(max): 0.378570
2024-11-21 07:56:08 INFO --- 154/154 batches finished ---
2024-11-21 07:56:08 INFO Train loss: 0.535324
2024-11-21 07:56:08 INFO ************ Epoch=1 end ************
2024-11-21 07:56:18 INFO [Metrics] AUC-ROC: 0.865924 - AUC-PR: 0.854469 - ACC: 0.781069 - Precision: 0.766963 - Recall: 0.805080 - F1: 0.785560 - MCC: 0.562905 - Logloss: 0.457787 - MSE: 0.149070 - RMSE: 0.386096 - COPC: 0.963444 - KLD: 0.456118
2024-11-21 07:56:18 INFO Save best model: monitor(max): 0.408137
2024-11-21 07:56:18 INFO --- 154/154 batches finished ---
2024-11-21 07:56:18 INFO Train loss: 0.362391
2024-11-21 07:56:18 INFO ************ Epoch=2 end ************
2024-11-21 07:56:28 INFO [Metrics] AUC-ROC: 0.868337 - AUC-PR: 0.857093 - ACC: 0.786336 - Precision: 0.775029 - Recall: 0.804587 - F1: 0.789531 - MCC: 0.573142 - Logloss: 0.456053 - MSE: 0.147671 - RMSE: 0.384280 - COPC: 0.989823 - KLD: 0.457546
2024-11-21 07:56:28 INFO Save best model: monitor(max): 0.412285
2024-11-21 07:56:28 INFO --- 154/154 batches finished ---
2024-11-21 07:56:28 INFO Train loss: 0.343550
2024-11-21 07:56:28 INFO ************ Epoch=3 end ************
2024-11-21 07:56:38 INFO [Metrics] AUC-ROC: 0.869637 - AUC-PR: 0.859315 - ACC: 0.788859 - Precision: 0.778815 - Recall: 0.804610 - F1: 0.791503 - MCC: 0.578079 - Logloss: 0.457974 - MSE: 0.147121 - RMSE: 0.383564 - COPC: 1.002575 - KLD: 0.463356
2024-11-21 07:56:38 INFO Monitor(max) STOP: 0.411663 !
2024-11-21 07:56:38 INFO Reduce learning rate on plateau: 0.000100
2024-11-21 07:56:38 INFO --- 154/154 batches finished ---
2024-11-21 07:56:38 INFO Train loss: 0.336457
2024-11-21 07:56:38 INFO ************ Epoch=4 end ************
2024-11-21 07:56:49 INFO [Metrics] AUC-ROC: 0.867850 - AUC-PR: 0.857862 - ACC: 0.787226 - Precision: 0.797251 - Recall: 0.768176 - F1: 0.782444 - MCC: 0.574766 - Logloss: 0.476562 - MSE: 0.150310 - RMSE: 0.387698 - COPC: 1.060314 - KLD: 0.493597
2024-11-21 07:56:49 INFO Monitor(max) STOP: 0.391289 !
2024-11-21 07:56:49 INFO Reduce learning rate on plateau: 0.000010
2024-11-21 07:56:49 INFO Early stopping at epoch=5
2024-11-21 07:56:49 INFO --- 154/154 batches finished ---
2024-11-21 07:56:49 INFO Train loss: 0.322657
2024-11-21 07:56:49 INFO Training finished.
2024-11-21 07:56:49 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FiGNN/AntM2C/FiGNN_model_seed2019.ckpt
2024-11-21 07:56:49 INFO Start evaluate model
2024-11-21 07:56:50 INFO [Metrics] AUC-ROC: 0.868337 - AUC-PR: 0.857093 - ACC: 0.786336 - Precision: 0.775029 - Recall: 0.804587 - F1: 0.789531 - MCC: 0.573142 - Logloss: 0.456053 - MSE: 0.147671 - RMSE: 0.384280 - COPC: 0.989823 - KLD: 0.457546
2024-11-21 07:56:50 INFO Start testing model
2024-11-21 07:56:52 INFO [Metrics] AUC-ROC: 0.850047 - AUC-PR: 0.840013 - ACC: 0.766667 - Precision: 0.751144 - Recall: 0.773880 - F1: 0.762343 - MCC: 0.533509 - Logloss: 0.483911 - MSE: 0.158440 - RMSE: 0.398046 - COPC: 0.970655 - KLD: 0.514037
