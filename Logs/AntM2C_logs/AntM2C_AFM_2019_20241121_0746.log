2024-11-21 07:46:47 INFO all args: Namespace(dataset_name='AntM2C', dataset_path='/data/ctr/AntM2C/', model_name='AFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=30000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-21 07:46:47 INFO Start process AntM2C !
2024-11-21 07:46:47 INFO Loading AntM2C dataset
2024-11-21 07:46:47 INFO Load h5 data from /data/ctr/AntM2C/train.h5
2024-11-21 07:46:47 INFO Load h5 data from /data/ctr/AntM2C/valid.h5
2024-11-21 07:46:47 INFO Load h5 data from /data/ctr/AntM2C/test.h5
2024-11-21 07:46:47 INFO Train samples: total/1537877, pos/711221, neg/826656, ratio/46.25%
2024-11-21 07:46:47 INFO Validation samples: total/170876, pos/85112, neg/85764, ratio/49.81%
2024-11-21 07:46:47 INFO Test samples: total/189862, pos/91814, neg/98048, ratio/48.36%
2024-11-21 07:46:47 INFO Loading data done
2024-11-21 07:46:48 INFO Model: AFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_id): Embedding(66290, 16, padding_idx=66289)
      (item_id): Embedding(8537, 16, padding_idx=8536)
      (deep_features_14): Embedding(2156, 16, padding_idx=2155)
      (deep_features_19): Embedding(14, 16, padding_idx=13)
      (deep_features_20): Embedding(6, 16, padding_idx=5)
      (deep_features_21): Embedding(80, 16, padding_idx=79)
      (deep_features_22): Embedding(4, 16, padding_idx=3)
      (deep_features_23): Embedding(7, 16, padding_idx=6)
      (deep_features_24): Embedding(32, 16, padding_idx=31)
      (deep_features_25): Embedding(357, 16, padding_idx=356)
      (deep_features_26): Embedding(2480, 16, padding_idx=2479)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (elementwise_product_layer): InnerProductLayer()
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (user_id): Embedding(66290, 1, padding_idx=66289)
        (item_id): Embedding(8537, 1, padding_idx=8536)
        (deep_features_14): Embedding(2156, 1, padding_idx=2155)
        (deep_features_19): Embedding(14, 1, padding_idx=13)
        (deep_features_20): Embedding(6, 1, padding_idx=5)
        (deep_features_21): Embedding(80, 1, padding_idx=79)
        (deep_features_22): Embedding(4, 1, padding_idx=3)
        (deep_features_23): Embedding(7, 1, padding_idx=6)
        (deep_features_24): Embedding(32, 1, padding_idx=31)
        (deep_features_25): Embedding(357, 1, padding_idx=356)
        (deep_features_26): Embedding(2480, 1, padding_idx=2479)
        (hour): Embedding(25, 1, padding_idx=24)
        (weekday): Embedding(8, 1, padding_idx=7)
        (weekend): Embedding(3, 1, padding_idx=2)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (attention): Sequential(
    (0): Linear(in_features=16, out_features=32, bias=True)
    (1): ReLU()
    (2): Linear(in_features=32, out_features=1, bias=False)
    (3): Softmax(dim=1)
  )
  (weight_p): Linear(in_features=16, out_features=1, bias=False)
  (dropout1): Dropout(p=0, inplace=False)
  (dropout2): Dropout(p=0, inplace=False)
  (final_activation): Sigmoid()
)
2024-11-21 07:46:48 INFO Model parameters: 1360576
2024-11-21 07:46:48 INFO Start training model
2024-11-21 07:46:48 INFO Start training: 52 batches/epoch
2024-11-21 07:46:48 INFO ************ Epoch=1 start ************
2024-11-21 07:46:59 INFO [Metrics] AUC-ROC: 0.693264 - AUC-PR: 0.677412 - ACC: 0.513097 - Precision: 0.802723 - Recall: 0.029784 - F1: 0.057437 - MCC: 0.083603 - Logloss: 0.685527 - MSE: 0.246211 - RMSE: 0.496197 - COPC: 1.071942 - KLD: 0.686443
2024-11-21 07:46:59 INFO Save best model: monitor(max): 0.007737
2024-11-21 07:46:59 INFO --- 52/52 batches finished ---
2024-11-21 07:46:59 INFO Train loss: 0.683916
2024-11-21 07:46:59 INFO ************ Epoch=1 end ************
2024-11-21 07:47:10 INFO [Metrics] AUC-ROC: 0.719745 - AUC-PR: 0.710607 - ACC: 0.587666 - Precision: 0.814437 - Recall: 0.222977 - F1: 0.350102 - MCC: 0.251411 - Logloss: 0.674494 - MSE: 0.240731 - RMSE: 0.490644 - COPC: 1.067167 - KLD: 0.675116
2024-11-21 07:47:10 INFO Save best model: monitor(max): 0.045251
2024-11-21 07:47:10 INFO --- 52/52 batches finished ---
2024-11-21 07:47:10 INFO Train loss: 0.666765
2024-11-21 07:47:10 INFO ************ Epoch=2 end ************
2024-11-21 07:47:21 INFO [Metrics] AUC-ROC: 0.751617 - AUC-PR: 0.752564 - ACC: 0.657623 - Precision: 0.818354 - Recall: 0.401812 - F1: 0.538983 - MCC: 0.364447 - Logloss: 0.658851 - MSE: 0.232998 - RMSE: 0.482699 - COPC: 1.065780 - KLD: 0.658968
2024-11-21 07:47:21 INFO Save best model: monitor(max): 0.092766
2024-11-21 07:47:21 INFO --- 52/52 batches finished ---
2024-11-21 07:47:21 INFO Train loss: 0.648089
2024-11-21 07:47:21 INFO ************ Epoch=3 end ************
2024-11-21 07:47:31 INFO [Metrics] AUC-ROC: 0.803939 - AUC-PR: 0.811284 - ACC: 0.710983 - Precision: 0.870080 - Recall: 0.493432 - F1: 0.629735 - MCC: 0.466801 - Logloss: 0.587214 - MSE: 0.199086 - RMSE: 0.446191 - COPC: 1.044872 - KLD: 0.593881
2024-11-21 07:47:31 INFO Save best model: monitor(max): 0.216725
2024-11-21 07:47:31 INFO --- 52/52 batches finished ---
2024-11-21 07:47:31 INFO Train loss: 0.594734
2024-11-21 07:47:31 INFO ************ Epoch=4 end ************
2024-11-21 07:47:42 INFO [Metrics] AUC-ROC: 0.833521 - AUC-PR: 0.830859 - ACC: 0.721254 - Precision: 0.868364 - Recall: 0.519057 - F1: 0.649739 - MCC: 0.482184 - Logloss: 0.512196 - MSE: 0.171139 - RMSE: 0.413689 - COPC: 1.071552 - KLD: 0.520165
2024-11-21 07:47:42 INFO Save best model: monitor(max): 0.321324
2024-11-21 07:47:42 INFO --- 52/52 batches finished ---
2024-11-21 07:47:42 INFO Train loss: 0.461985
2024-11-21 07:47:42 INFO ************ Epoch=5 end ************
2024-11-21 07:47:53 INFO [Metrics] AUC-ROC: 0.849686 - AUC-PR: 0.842123 - ACC: 0.734720 - Precision: 0.853191 - Recall: 0.564550 - F1: 0.679488 - MCC: 0.497958 - Logloss: 0.490797 - MSE: 0.163366 - RMSE: 0.404186 - COPC: 1.082319 - KLD: 0.493516
2024-11-21 07:47:53 INFO Save best model: monitor(max): 0.358889
2024-11-21 07:47:53 INFO --- 52/52 batches finished ---
2024-11-21 07:47:53 INFO Train loss: 0.400207
2024-11-21 07:47:53 INFO ************ Epoch=6 end ************
2024-11-21 07:48:04 INFO [Metrics] AUC-ROC: 0.857971 - AUC-PR: 0.848361 - ACC: 0.747741 - Precision: 0.843627 - Recall: 0.605849 - F1: 0.705235 - MCC: 0.515726 - Logloss: 0.478954 - MSE: 0.158530 - RMSE: 0.398158 - COPC: 1.086485 - KLD: 0.478018
2024-11-21 07:48:04 INFO Save best model: monitor(max): 0.379017
2024-11-21 07:48:04 INFO --- 52/52 batches finished ---
2024-11-21 07:48:04 INFO Train loss: 0.378674
2024-11-21 07:48:04 INFO ************ Epoch=7 end ************
2024-11-21 07:48:14 INFO [Metrics] AUC-ROC: 0.862569 - AUC-PR: 0.852174 - ACC: 0.758193 - Precision: 0.835759 - Recall: 0.640380 - F1: 0.725139 - MCC: 0.530564 - Logloss: 0.470939 - MSE: 0.155204 - RMSE: 0.393960 - COPC: 1.087562 - KLD: 0.468128
2024-11-21 07:48:14 INFO Save best model: monitor(max): 0.391630
2024-11-21 07:48:14 INFO --- 52/52 batches finished ---
2024-11-21 07:48:14 INFO Train loss: 0.365347
2024-11-21 07:48:14 INFO ************ Epoch=8 end ************
2024-11-21 07:48:25 INFO [Metrics] AUC-ROC: 0.865562 - AUC-PR: 0.854954 - ACC: 0.767709 - Precision: 0.829692 - Recall: 0.671468 - F1: 0.742242 - MCC: 0.545014 - Logloss: 0.465237 - MSE: 0.152805 - RMSE: 0.390902 - COPC: 1.088075 - KLD: 0.461759
2024-11-21 07:48:25 INFO Save best model: monitor(max): 0.400324
2024-11-21 07:48:25 INFO --- 52/52 batches finished ---
2024-11-21 07:48:25 INFO Train loss: 0.355111
2024-11-21 07:48:25 INFO ************ Epoch=9 end ************
2024-11-21 07:48:36 INFO [Metrics] AUC-ROC: 0.867551 - AUC-PR: 0.856798 - ACC: 0.773081 - Precision: 0.821941 - Recall: 0.694978 - F1: 0.753147 - MCC: 0.552477 - Logloss: 0.461073 - MSE: 0.150950 - RMSE: 0.388523 - COPC: 1.083108 - KLD: 0.457811
2024-11-21 07:48:36 INFO Save best model: monitor(max): 0.406477
2024-11-21 07:48:36 INFO --- 52/52 batches finished ---
2024-11-21 07:48:36 INFO Train loss: 0.346518
2024-11-21 07:48:36 INFO ************ Epoch=10 end ************
2024-11-21 07:48:47 INFO [Metrics] AUC-ROC: 0.868974 - AUC-PR: 0.858076 - ACC: 0.776516 - Precision: 0.817165 - Recall: 0.710229 - F1: 0.759954 - MCC: 0.557567 - Logloss: 0.458843 - MSE: 0.149851 - RMSE: 0.387106 - COPC: 1.080101 - KLD: 0.456016
2024-11-21 07:48:47 INFO Save best model: monitor(max): 0.410132
2024-11-21 07:48:47 INFO --- 52/52 batches finished ---
2024-11-21 07:48:47 INFO Train loss: 0.339230
2024-11-21 07:48:47 INFO ************ Epoch=11 end ************
2024-11-21 07:48:58 INFO [Metrics] AUC-ROC: 0.870026 - AUC-PR: 0.858987 - ACC: 0.779255 - Precision: 0.815375 - Recall: 0.719804 - F1: 0.764615 - MCC: 0.562156 - Logloss: 0.457953 - MSE: 0.149265 - RMSE: 0.386348 - COPC: 1.079021 - KLD: 0.455611
2024-11-21 07:48:58 INFO Save best model: monitor(max): 0.412073
2024-11-21 07:48:58 INFO --- 52/52 batches finished ---
2024-11-21 07:48:58 INFO Train loss: 0.333057
2024-11-21 07:48:58 INFO ************ Epoch=12 end ************
2024-11-21 07:49:08 INFO [Metrics] AUC-ROC: 0.870764 - AUC-PR: 0.859601 - ACC: 0.780531 - Precision: 0.813702 - Recall: 0.725479 - F1: 0.767063 - MCC: 0.564178 - Logloss: 0.457903 - MSE: 0.149003 - RMSE: 0.386009 - COPC: 1.078635 - KLD: 0.456162
2024-11-21 07:49:08 INFO Save best model: monitor(max): 0.412861
2024-11-21 07:49:08 INFO --- 52/52 batches finished ---
2024-11-21 07:49:08 INFO Train loss: 0.327837
2024-11-21 07:49:08 INFO ************ Epoch=13 end ************
2024-11-21 07:49:19 INFO [Metrics] AUC-ROC: 0.871293 - AUC-PR: 0.859990 - ACC: 0.781467 - Precision: 0.812271 - Recall: 0.729968 - F1: 0.768923 - MCC: 0.565651 - Logloss: 0.458382 - MSE: 0.148919 - RMSE: 0.385901 - COPC: 1.078672 - KLD: 0.457368
2024-11-21 07:49:19 INFO Save best model: monitor(max): 0.412911
2024-11-21 07:49:19 INFO --- 52/52 batches finished ---
2024-11-21 07:49:19 INFO Train loss: 0.323437
2024-11-21 07:49:19 INFO ************ Epoch=14 end ************
2024-11-21 07:49:29 INFO [Metrics] AUC-ROC: 0.871618 - AUC-PR: 0.860176 - ACC: 0.782234 - Precision: 0.811292 - Recall: 0.733387 - F1: 0.770375 - MCC: 0.566904 - Logloss: 0.459250 - MSE: 0.148962 - RMSE: 0.385957 - COPC: 1.078670 - KLD: 0.459093
2024-11-21 07:49:29 INFO Monitor(max) STOP: 0.412368 !
2024-11-21 07:49:29 INFO Reduce learning rate on plateau: 0.000100
2024-11-21 07:49:29 INFO --- 52/52 batches finished ---
2024-11-21 07:49:29 INFO Train loss: 0.319704
2024-11-21 07:49:29 INFO ************ Epoch=15 end ************
2024-11-21 07:49:41 INFO [Metrics] AUC-ROC: 0.871548 - AUC-PR: 0.859916 - ACC: 0.781953 - Precision: 0.812467 - Recall: 0.730955 - F1: 0.769558 - MCC: 0.566572 - Logloss: 0.460016 - MSE: 0.149221 - RMSE: 0.386292 - COPC: 1.083939 - KLD: 0.459741
2024-11-21 07:49:41 INFO Monitor(max) STOP: 0.411531 !
2024-11-21 07:49:41 INFO Reduce learning rate on plateau: 0.000010
2024-11-21 07:49:41 INFO Early stopping at epoch=16
2024-11-21 07:49:41 INFO --- 52/52 batches finished ---
2024-11-21 07:49:41 INFO Train loss: 0.315503
2024-11-21 07:49:41 INFO Training finished.
2024-11-21 07:49:41 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AFM/AntM2C/AFM_model_seed2019.ckpt
2024-11-21 07:49:41 INFO Start evaluate model
2024-11-21 07:49:42 INFO [Metrics] AUC-ROC: 0.871293 - AUC-PR: 0.859990 - ACC: 0.781467 - Precision: 0.812271 - Recall: 0.729968 - F1: 0.768923 - MCC: 0.565651 - Logloss: 0.458382 - MSE: 0.148919 - RMSE: 0.385901 - COPC: 1.078672 - KLD: 0.457368
2024-11-21 07:49:43 INFO Start testing model
2024-11-21 07:49:44 INFO [Metrics] AUC-ROC: 0.852809 - AUC-PR: 0.843128 - ACC: 0.768642 - Precision: 0.788725 - Recall: 0.712408 - F1: 0.748627 - MCC: 0.537736 - Logloss: 0.485294 - MSE: 0.158692 - RMSE: 0.398362 - COPC: 1.065218 - KLD: 0.516945
