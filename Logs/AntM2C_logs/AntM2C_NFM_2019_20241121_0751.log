2024-11-21 07:51:11 INFO all args: Namespace(dataset_name='AntM2C', dataset_path='/data/ctr/AntM2C/', model_name='NFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=30000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-21 07:51:11 INFO Start process AntM2C !
2024-11-21 07:51:11 INFO Loading AntM2C dataset
2024-11-21 07:51:11 INFO Load h5 data from /data/ctr/AntM2C/train.h5
2024-11-21 07:51:11 INFO Load h5 data from /data/ctr/AntM2C/valid.h5
2024-11-21 07:51:11 INFO Load h5 data from /data/ctr/AntM2C/test.h5
2024-11-21 07:51:11 INFO Train samples: total/1537877, pos/711221, neg/826656, ratio/46.25%
2024-11-21 07:51:11 INFO Validation samples: total/170876, pos/85112, neg/85764, ratio/49.81%
2024-11-21 07:51:11 INFO Test samples: total/189862, pos/91814, neg/98048, ratio/48.36%
2024-11-21 07:51:11 INFO Loading data done
2024-11-21 07:51:12 INFO Model: NFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_id): Embedding(66290, 16, padding_idx=66289)
      (item_id): Embedding(8537, 16, padding_idx=8536)
      (deep_features_14): Embedding(2156, 16, padding_idx=2155)
      (deep_features_19): Embedding(14, 16, padding_idx=13)
      (deep_features_20): Embedding(6, 16, padding_idx=5)
      (deep_features_21): Embedding(80, 16, padding_idx=79)
      (deep_features_22): Embedding(4, 16, padding_idx=3)
      (deep_features_23): Embedding(7, 16, padding_idx=6)
      (deep_features_24): Embedding(32, 16, padding_idx=31)
      (deep_features_25): Embedding(357, 16, padding_idx=356)
      (deep_features_26): Embedding(2480, 16, padding_idx=2479)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (user_id): Embedding(66290, 1, padding_idx=66289)
        (item_id): Embedding(8537, 1, padding_idx=8536)
        (deep_features_14): Embedding(2156, 1, padding_idx=2155)
        (deep_features_19): Embedding(14, 1, padding_idx=13)
        (deep_features_20): Embedding(6, 1, padding_idx=5)
        (deep_features_21): Embedding(80, 1, padding_idx=79)
        (deep_features_22): Embedding(4, 1, padding_idx=3)
        (deep_features_23): Embedding(7, 1, padding_idx=6)
        (deep_features_24): Embedding(32, 1, padding_idx=31)
        (deep_features_25): Embedding(357, 1, padding_idx=356)
        (deep_features_26): Embedding(2480, 1, padding_idx=2479)
        (hour): Embedding(25, 1, padding_idx=24)
        (weekday): Embedding(8, 1, padding_idx=7)
        (weekend): Embedding(3, 1, padding_idx=2)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=16, out_features=1000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=1000, out_features=1000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=1000, out_features=1000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-21 07:51:12 INFO Model parameters: 3379984
2024-11-21 07:51:12 INFO Start training model
2024-11-21 07:51:12 INFO Start training: 52 batches/epoch
2024-11-21 07:51:12 INFO ************ Epoch=1 start ************
2024-11-21 07:51:22 INFO [Metrics] AUC-ROC: 0.816056 - AUC-PR: 0.797353 - ACC: 0.732321 - Precision: 0.694591 - Recall: 0.825606 - F1: 0.754453 - MCC: 0.473438 - Logloss: 0.555524 - MSE: 0.187573 - RMSE: 0.433096 - COPC: 1.060465 - KLD: 0.539041
2024-11-21 07:51:22 INFO Save best model: monitor(max): 0.260532
2024-11-21 07:51:22 INFO --- 52/52 batches finished ---
2024-11-21 07:51:22 INFO Train loss: 0.650105
2024-11-21 07:51:22 INFO ************ Epoch=1 end ************
2024-11-21 07:51:33 INFO [Metrics] AUC-ROC: 0.856281 - AUC-PR: 0.846104 - ACC: 0.766532 - Precision: 0.734412 - Recall: 0.832245 - F1: 0.780273 - MCC: 0.538048 - Logloss: 0.485237 - MSE: 0.159273 - RMSE: 0.399090 - COPC: 0.892161 - KLD: 0.474012
2024-11-21 07:51:33 INFO Save best model: monitor(max): 0.371044
2024-11-21 07:51:33 INFO --- 52/52 batches finished ---
2024-11-21 07:51:33 INFO Train loss: 0.402556
2024-11-21 07:51:33 INFO ************ Epoch=2 end ************
2024-11-21 07:51:43 INFO [Metrics] AUC-ROC: 0.863922 - AUC-PR: 0.852045 - ACC: 0.757801 - Precision: 0.848381 - Recall: 0.625540 - F1: 0.720115 - MCC: 0.533745 - Logloss: 0.489016 - MSE: 0.160963 - RMSE: 0.401202 - COPC: 1.218529 - KLD: 0.467622
2024-11-21 07:51:43 INFO Save best model: monitor(max): 0.374905
2024-11-21 07:51:43 INFO --- 52/52 batches finished ---
2024-11-21 07:51:43 INFO Train loss: 0.400269
2024-11-21 07:51:43 INFO ************ Epoch=3 end ************
2024-11-21 07:51:54 INFO [Metrics] AUC-ROC: 0.861820 - AUC-PR: 0.850161 - ACC: 0.769002 - Precision: 0.727327 - Recall: 0.857834 - F1: 0.787208 - MCC: 0.547112 - Logloss: 0.482604 - MSE: 0.158281 - RMSE: 0.397845 - COPC: 0.869570 - KLD: 0.470763
2024-11-21 07:51:54 INFO Save best model: monitor(max): 0.379216
2024-11-21 07:51:54 INFO --- 52/52 batches finished ---
2024-11-21 07:51:54 INFO Train loss: 0.381582
2024-11-21 07:51:54 INFO ************ Epoch=4 end ************
2024-11-21 07:52:04 INFO [Metrics] AUC-ROC: 0.869389 - AUC-PR: 0.856927 - ACC: 0.784557 - Precision: 0.811319 - Recall: 0.739426 - F1: 0.773706 - MCC: 0.571194 - Logloss: 0.458815 - MSE: 0.148955 - RMSE: 0.385947 - COPC: 1.088569 - KLD: 0.456490
2024-11-21 07:52:04 INFO Save best model: monitor(max): 0.410574
2024-11-21 07:52:04 INFO --- 52/52 batches finished ---
2024-11-21 07:52:04 INFO Train loss: 0.351064
2024-11-21 07:52:04 INFO ************ Epoch=5 end ************
2024-11-21 07:52:15 INFO [Metrics] AUC-ROC: 0.870444 - AUC-PR: 0.858364 - ACC: 0.782837 - Precision: 0.821898 - Recall: 0.720039 - F1: 0.767604 - MCC: 0.569826 - Logloss: 0.467047 - MSE: 0.150856 - RMSE: 0.388402 - COPC: 1.131022 - KLD: 0.466865
2024-11-21 07:52:15 INFO Monitor(max) STOP: 0.403397 !
2024-11-21 07:52:15 INFO Reduce learning rate on plateau: 0.000100
2024-11-21 07:52:15 INFO --- 52/52 batches finished ---
2024-11-21 07:52:15 INFO Train loss: 0.336706
2024-11-21 07:52:15 INFO ************ Epoch=6 end ************
2024-11-21 07:52:25 INFO [Metrics] AUC-ROC: 0.870356 - AUC-PR: 0.858372 - ACC: 0.786307 - Precision: 0.807970 - Recall: 0.748990 - F1: 0.777362 - MCC: 0.574010 - Logloss: 0.461461 - MSE: 0.148757 - RMSE: 0.385691 - COPC: 1.082581 - KLD: 0.463484
2024-11-21 07:52:25 INFO Monitor(max) STOP: 0.408894 !
2024-11-21 07:52:25 INFO Reduce learning rate on plateau: 0.000010
2024-11-21 07:52:25 INFO Early stopping at epoch=7
2024-11-21 07:52:25 INFO --- 52/52 batches finished ---
2024-11-21 07:52:25 INFO Train loss: 0.322700
2024-11-21 07:52:25 INFO Training finished.
2024-11-21 07:52:25 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/NFM/AntM2C/NFM_model_seed2019.ckpt
2024-11-21 07:52:25 INFO Start evaluate model
2024-11-21 07:52:27 INFO [Metrics] AUC-ROC: 0.869389 - AUC-PR: 0.856927 - ACC: 0.784557 - Precision: 0.811319 - Recall: 0.739426 - F1: 0.773706 - MCC: 0.571194 - Logloss: 0.458815 - MSE: 0.148955 - RMSE: 0.385947 - COPC: 1.088569 - KLD: 0.456490
2024-11-21 07:52:27 INFO Start testing model
2024-11-21 07:52:29 INFO [Metrics] AUC-ROC: 0.851974 - AUC-PR: 0.840806 - ACC: 0.766741 - Precision: 0.788444 - Recall: 0.707474 - F1: 0.745768 - MCC: 0.534113 - Logloss: 0.485670 - MSE: 0.158719 - RMSE: 0.398395 - COPC: 1.071107 - KLD: 0.516859
