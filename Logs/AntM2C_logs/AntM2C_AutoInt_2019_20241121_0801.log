2024-11-21 08:01:32 INFO all args: Namespace(dataset_name='AntM2C', dataset_path='/data/ctr/AntM2C/', model_name='AutoInt', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-21 08:01:32 INFO Start process AntM2C !
2024-11-21 08:01:32 INFO Loading AntM2C dataset
2024-11-21 08:01:32 INFO Load h5 data from /data/ctr/AntM2C/train.h5
2024-11-21 08:01:32 INFO Load h5 data from /data/ctr/AntM2C/valid.h5
2024-11-21 08:01:32 INFO Load h5 data from /data/ctr/AntM2C/test.h5
2024-11-21 08:01:32 INFO Train samples: total/1537877, pos/711221, neg/826656, ratio/46.25%
2024-11-21 08:01:32 INFO Validation samples: total/170876, pos/85112, neg/85764, ratio/49.81%
2024-11-21 08:01:32 INFO Test samples: total/189862, pos/91814, neg/98048, ratio/48.36%
2024-11-21 08:01:32 INFO Loading data done
2024-11-21 08:01:33 INFO Model: AutoInt(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (user_id): Embedding(66290, 16, padding_idx=66289)
      (item_id): Embedding(8537, 16, padding_idx=8536)
      (deep_features_14): Embedding(2156, 16, padding_idx=2155)
      (deep_features_19): Embedding(14, 16, padding_idx=13)
      (deep_features_20): Embedding(6, 16, padding_idx=5)
      (deep_features_21): Embedding(80, 16, padding_idx=79)
      (deep_features_22): Embedding(4, 16, padding_idx=3)
      (deep_features_23): Embedding(7, 16, padding_idx=6)
      (deep_features_24): Embedding(32, 16, padding_idx=31)
      (deep_features_25): Embedding(357, 16, padding_idx=356)
      (deep_features_26): Embedding(2480, 16, padding_idx=2479)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (self_attention): Sequential(
    (0): MultiHeadSelfAttention(
      (W_q): Linear(in_features=16, out_features=64, bias=False)
      (W_k): Linear(in_features=16, out_features=64, bias=False)
      (W_v): Linear(in_features=16, out_features=64, bias=False)
      (W_res): Linear(in_features=16, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (1): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (2): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (3): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (4): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
  )
  (fc): Linear(in_features=896, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-21 08:01:33 INFO Model parameters: 1334129
2024-11-21 08:01:33 INFO Start training model
2024-11-21 08:01:33 INFO Start training: 154 batches/epoch
2024-11-21 08:01:33 INFO ************ Epoch=1 start ************
2024-11-21 08:01:43 INFO [Metrics] AUC-ROC: 0.862011 - AUC-PR: 0.851287 - ACC: 0.773579 - Precision: 0.751142 - Recall: 0.815655 - F1: 0.782070 - MCC: 0.549311 - Logloss: 0.466385 - MSE: 0.152358 - RMSE: 0.390330 - COPC: 0.929838 - KLD: 0.465969
2024-11-21 08:01:43 INFO Save best model: monitor(max): 0.395626
2024-11-21 08:01:43 INFO --- 154/154 batches finished ---
2024-11-21 08:01:43 INFO Train loss: 0.456733
2024-11-21 08:01:43 INFO ************ Epoch=1 end ************
2024-11-21 08:01:53 INFO [Metrics] AUC-ROC: 0.870518 - AUC-PR: 0.858798 - ACC: 0.784867 - Precision: 0.778055 - Recall: 0.794812 - F1: 0.786344 - MCC: 0.569895 - Logloss: 0.451035 - MSE: 0.146433 - RMSE: 0.382665 - COPC: 1.009509 - KLD: 0.449348
2024-11-21 08:01:53 INFO Save best model: monitor(max): 0.419483
2024-11-21 08:01:53 INFO --- 154/154 batches finished ---
2024-11-21 08:01:53 INFO Train loss: 0.353540
2024-11-21 08:01:53 INFO ************ Epoch=2 end ************
2024-11-21 08:02:04 INFO [Metrics] AUC-ROC: 0.872573 - AUC-PR: 0.863294 - ACC: 0.788461 - Precision: 0.787701 - Recall: 0.787562 - F1: 0.787632 - MCC: 0.576915 - Logloss: 0.455968 - MSE: 0.146547 - RMSE: 0.382815 - COPC: 1.056285 - KLD: 0.459737
2024-11-21 08:02:04 INFO Monitor(max) STOP: 0.416604 !
2024-11-21 08:02:04 INFO Reduce learning rate on plateau: 0.000100
2024-11-21 08:02:04 INFO --- 154/154 batches finished ---
2024-11-21 08:02:04 INFO Train loss: 0.336343
2024-11-21 08:02:04 INFO ************ Epoch=3 end ************
2024-11-21 08:02:14 INFO [Metrics] AUC-ROC: 0.870950 - AUC-PR: 0.862515 - ACC: 0.786594 - Precision: 0.788160 - Recall: 0.781641 - F1: 0.784887 - MCC: 0.573187 - Logloss: 0.461713 - MSE: 0.147998 - RMSE: 0.384705 - COPC: 1.045829 - KLD: 0.467545
2024-11-21 08:02:14 INFO Monitor(max) STOP: 0.409237 !
2024-11-21 08:02:14 INFO Reduce learning rate on plateau: 0.000010
2024-11-21 08:02:14 INFO Early stopping at epoch=4
2024-11-21 08:02:14 INFO --- 154/154 batches finished ---
2024-11-21 08:02:14 INFO Train loss: 0.316896
2024-11-21 08:02:14 INFO Training finished.
2024-11-21 08:02:14 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AutoInt/AntM2C/AutoInt_model_seed2019.ckpt
2024-11-21 08:02:14 INFO Start evaluate model
2024-11-21 08:02:16 INFO [Metrics] AUC-ROC: 0.870518 - AUC-PR: 0.858798 - ACC: 0.784867 - Precision: 0.778055 - Recall: 0.794812 - F1: 0.786344 - MCC: 0.569895 - Logloss: 0.451035 - MSE: 0.146433 - RMSE: 0.382665 - COPC: 1.009509 - KLD: 0.449348
2024-11-21 08:02:16 INFO Start testing model
2024-11-21 08:02:17 INFO [Metrics] AUC-ROC: 0.853368 - AUC-PR: 0.843009 - ACC: 0.768874 - Precision: 0.771121 - Recall: 0.742414 - F1: 0.756495 - MCC: 0.537051 - Logloss: 0.479240 - MSE: 0.156833 - RMSE: 0.396022 - COPC: 1.022966 - KLD: 0.509090
