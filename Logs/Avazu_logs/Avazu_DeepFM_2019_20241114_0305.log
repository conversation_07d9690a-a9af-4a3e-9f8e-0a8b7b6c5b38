2024-11-14 03:05:56 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='DeepFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-14 03:05:56 INFO Start process Ava<PERSON> !
2024-11-14 03:05:56 INFO Loading Avazu dataset
2024-11-14 03:05:56 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-14 03:05:59 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-14 03:05:59 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-14 03:06:00 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-14 03:06:00 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-14 03:06:00 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-14 03:06:00 INFO Loading data done
2024-11-14 03:06:02 INFO Model: DeepFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (C1): Embedding(8, 16)
        (banner_pos): Embedding(8, 16)
        (site_id): Embedding(4623, 16)
        (site_domain): Embedding(7307, 16)
        (site_category): Embedding(26, 16)
        (app_id): Embedding(8218, 16)
        (app_domain): Embedding(527, 16)
        (app_category): Embedding(36, 16)
        (device_id): Embedding(2329635, 16)
        (device_ip): Embedding(6011540, 16)
        (device_model): Embedding(8067, 16)
        (device_type): Embedding(6, 16)
        (device_conn_type): Embedding(5, 16)
        (C14): Embedding(2611, 16)
        (C15): Embedding(9, 16)
        (C16): Embedding(10, 16)
        (C17): Embedding(435, 16)
        (C18): Embedding(5, 16)
        (C19): Embedding(69, 16)
        (C20): Embedding(173, 16)
        (C21): Embedding(61, 16)
        (hour): Embedding(25, 16)
        (weekday): Embedding(8, 16)
        (weekend): Embedding(3, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (fm_layer): FM_Layer_v2(
    (inner_product_layer): InnerProductLayer_v2()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (C1): Embedding(8, 1, padding_idx=7)
          (banner_pos): Embedding(8, 1, padding_idx=7)
          (site_id): Embedding(4623, 1, padding_idx=4622)
          (site_domain): Embedding(7307, 1, padding_idx=7306)
          (site_category): Embedding(26, 1, padding_idx=25)
          (app_id): Embedding(8218, 1, padding_idx=8217)
          (app_domain): Embedding(527, 1, padding_idx=526)
          (app_category): Embedding(36, 1, padding_idx=35)
          (device_id): Embedding(2329635, 1, padding_idx=2329634)
          (device_ip): Embedding(6011540, 1, padding_idx=6011539)
          (device_model): Embedding(8067, 1, padding_idx=8066)
          (device_type): Embedding(6, 1, padding_idx=5)
          (device_conn_type): Embedding(5, 1, padding_idx=4)
          (C14): Embedding(2611, 1, padding_idx=2610)
          (C15): Embedding(9, 1, padding_idx=8)
          (C16): Embedding(10, 1, padding_idx=9)
          (C17): Embedding(435, 1, padding_idx=434)
          (C18): Embedding(5, 1, padding_idx=4)
          (C19): Embedding(69, 1, padding_idx=68)
          (C20): Embedding(173, 1, padding_idx=172)
          (C21): Embedding(61, 1, padding_idx=60)
          (hour): Embedding(25, 1, padding_idx=24)
          (weekday): Embedding(8, 1, padding_idx=7)
          (weekend): Embedding(3, 1, padding_idx=2)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=384, out_features=2000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=2000, out_features=2000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=2000, out_features=2000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=2000, out_features=2000, bias=True)
      (7): ReLU()
      (8): Linear(in_features=2000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-14 03:06:02 INFO Model parameters: 155126056
2024-11-14 03:06:02 INFO Start training model
2024-11-14 03:06:03 INFO Start training: 3235 batches/epoch
2024-11-14 03:06:03 INFO ************ Epoch=1 start ************
2024-11-14 03:12:56 INFO [Metrics] AUC-ROC: 0.795032 - AUC-PR: 0.442771 - ACC: 0.839507 - Precision: 0.616641 - Recall: 0.144967 - F1: 0.234747 - MCC: 0.242674 - Logloss: 0.371713 - MSE: 0.116997 - RMSE: 0.342048 - COPC: 1.061481 - KLD: 1.372763
2024-11-14 03:12:56 INFO Save best model: monitor(max): 0.423319
2024-11-14 03:12:56 INFO --- 3235/3235 batches finished ---
2024-11-14 03:12:56 INFO Train loss: 0.380169
2024-11-14 03:12:56 INFO ************ Epoch=1 end ************
2024-11-14 03:19:29 INFO [Metrics] AUC-ROC: 0.769450 - AUC-PR: 0.365579 - ACC: 0.810526 - Precision: 0.410584 - Recall: 0.265936 - F1: 0.322796 - MCC: 0.225432 - Logloss: 0.428603 - MSE: 0.135203 - RMSE: 0.367699 - COPC: 0.886550 - KLD: 1.528734
2024-11-14 03:19:29 INFO Monitor(max) STOP: 0.340847 !
2024-11-14 03:19:29 INFO Reduce learning rate on plateau: 0.000100
2024-11-14 03:19:29 INFO --- 3235/3235 batches finished ---
2024-11-14 03:19:29 INFO Train loss: 0.290883
2024-11-14 03:19:29 INFO ************ Epoch=2 end ************
2024-11-14 03:24:32 INFO [Metrics] AUC-ROC: 0.749358 - AUC-PR: 0.348049 - ACC: 0.785140 - Precision: 0.364508 - Recall: 0.356898 - F1: 0.360663 - MCC: 0.231571 - Logloss: 0.557497 - MSE: 0.161091 - RMSE: 0.401361 - COPC: 0.740768 - KLD: 1.799062
2024-11-14 03:24:32 INFO Monitor(max) STOP: 0.191861 !
2024-11-14 03:24:32 INFO Reduce learning rate on plateau: 0.000010
2024-11-14 03:24:32 INFO Early stopping at epoch=3
2024-11-14 03:24:32 INFO --- 3235/3235 batches finished ---
2024-11-14 03:24:32 INFO Train loss: 0.254035
2024-11-14 03:24:32 INFO Training finished.
2024-11-14 03:24:32 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/DeepFM/Avazu/DeepFM_model_seed2019.ckpt
2024-11-14 03:24:32 INFO Start evaluate model
2024-11-14 03:25:13 INFO [Metrics] AUC-ROC: 0.795032 - AUC-PR: 0.442771 - ACC: 0.839507 - Precision: 0.616641 - Recall: 0.144967 - F1: 0.234747 - MCC: 0.242674 - Logloss: 0.371713 - MSE: 0.116997 - RMSE: 0.342048 - COPC: 1.061481 - KLD: 1.372763
2024-11-14 03:25:13 INFO Start testing model
2024-11-14 03:25:53 INFO [Metrics] AUC-ROC: 0.795165 - AUC-PR: 0.443127 - ACC: 0.839724 - Precision: 0.619300 - Recall: 0.145671 - F1: 0.235862 - MCC: 0.244186 - Logloss: 0.371592 - MSE: 0.116963 - RMSE: 0.341999 - COPC: 1.062292 - KLD: 1.372187
