2024-11-13 13:07:58 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='FmFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-13 13:07:58 INFO Start process Ava<PERSON> !
2024-11-13 13:07:58 INFO Loading Avazu dataset
2024-11-13 13:07:58 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-13 13:08:01 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-13 13:08:01 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-13 13:08:02 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-13 13:08:02 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-13 13:08:02 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-13 13:08:02 INFO Loading data done
2024-11-13 13:08:04 INFO Model: FmFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (C1): Embedding(8, 16)
        (banner_pos): Embedding(8, 16)
        (site_id): Embedding(4623, 16)
        (site_domain): Embedding(7307, 16)
        (site_category): Embedding(26, 16)
        (app_id): Embedding(8218, 16)
        (app_domain): Embedding(527, 16)
        (app_category): Embedding(36, 16)
        (device_id): Embedding(2329635, 16)
        (device_ip): Embedding(6011540, 16)
        (device_model): Embedding(8067, 16)
        (device_type): Embedding(6, 16)
        (device_conn_type): Embedding(5, 16)
        (C14): Embedding(2611, 16)
        (C15): Embedding(9, 16)
        (C16): Embedding(10, 16)
        (C17): Embedding(435, 16)
        (C18): Embedding(5, 16)
        (C19): Embedding(69, 16)
        (C20): Embedding(173, 16)
        (C21): Embedding(61, 16)
        (hour): Embedding(25, 16)
        (weekday): Embedding(8, 16)
        (weekend): Embedding(3, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (C1): Embedding(8, 1, padding_idx=7)
        (banner_pos): Embedding(8, 1, padding_idx=7)
        (site_id): Embedding(4623, 1, padding_idx=4622)
        (site_domain): Embedding(7307, 1, padding_idx=7306)
        (site_category): Embedding(26, 1, padding_idx=25)
        (app_id): Embedding(8218, 1, padding_idx=8217)
        (app_domain): Embedding(527, 1, padding_idx=526)
        (app_category): Embedding(36, 1, padding_idx=35)
        (device_id): Embedding(2329635, 1, padding_idx=2329634)
        (device_ip): Embedding(6011540, 1, padding_idx=6011539)
        (device_model): Embedding(8067, 1, padding_idx=8066)
        (device_type): Embedding(6, 1, padding_idx=5)
        (device_conn_type): Embedding(5, 1, padding_idx=4)
        (C14): Embedding(2611, 1, padding_idx=2610)
        (C15): Embedding(9, 1, padding_idx=8)
        (C16): Embedding(10, 1, padding_idx=9)
        (C17): Embedding(435, 1, padding_idx=434)
        (C18): Embedding(5, 1, padding_idx=4)
        (C19): Embedding(69, 1, padding_idx=68)
        (C20): Embedding(173, 1, padding_idx=172)
        (C21): Embedding(61, 1, padding_idx=60)
        (hour): Embedding(25, 1, padding_idx=24)
        (weekday): Embedding(8, 1, padding_idx=7)
        (weekend): Embedding(3, 1, padding_idx=2)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (final_activation): Sigmoid()
)
2024-11-13 13:08:04 INFO Model parameters: 142418711
2024-11-13 13:08:04 INFO Start training model
2024-11-13 13:08:05 INFO Start training: 3235 batches/epoch
2024-11-13 13:08:05 INFO ************ Epoch=1 start ************
2024-11-13 13:15:44 INFO [Metrics] AUC-ROC: 0.780839 - AUC-PR: 0.420355 - ACC: 0.837567 - Precision: 0.599902 - Recall: 0.130361 - F1: 0.214180 - MCC: 0.224220 - Logloss: 0.379597 - MSE: 0.119466 - RMSE: 0.345638 - COPC: 0.975587 - KLD: 1.409643
2024-11-13 13:15:44 INFO Save best model: monitor(max): 0.401242
2024-11-13 13:15:45 INFO --- 3235/3235 batches finished ---
2024-11-13 13:15:45 INFO Train loss: 0.398860
2024-11-13 13:15:45 INFO ************ Epoch=1 end ************
2024-11-13 13:23:26 INFO [Metrics] AUC-ROC: 0.789118 - AUC-PR: 0.433579 - ACC: 0.838673 - Precision: 0.607441 - Recall: 0.141143 - F1: 0.229063 - MCC: 0.236234 - Logloss: 0.374733 - MSE: 0.117977 - RMSE: 0.343477 - COPC: 1.018858 - KLD: 1.387488
2024-11-13 13:23:26 INFO Save best model: monitor(max): 0.414385
2024-11-13 13:23:27 INFO --- 3235/3235 batches finished ---
2024-11-13 13:23:27 INFO Train loss: 0.386287
2024-11-13 13:23:27 INFO ************ Epoch=2 end ************
2024-11-13 13:30:15 INFO [Metrics] AUC-ROC: 0.790695 - AUC-PR: 0.434438 - ACC: 0.838451 - Precision: 0.585033 - Recall: 0.167264 - F1: 0.260150 - MCC: 0.249812 - Logloss: 0.374751 - MSE: 0.117975 - RMSE: 0.343474 - COPC: 1.023246 - KLD: 1.386001
2024-11-13 13:30:15 INFO Save best model: monitor(max): 0.415944
2024-11-13 13:30:16 INFO --- 3235/3235 batches finished ---
2024-11-13 13:30:16 INFO Train loss: 0.368012
2024-11-13 13:30:16 INFO ************ Epoch=3 end ************
2024-11-13 13:35:16 INFO [Metrics] AUC-ROC: 0.787453 - AUC-PR: 0.427810 - ACC: 0.837154 - Precision: 0.561034 - Recall: 0.188387 - F1: 0.282062 - MCC: 0.256224 - Logloss: 0.379502 - MSE: 0.119260 - RMSE: 0.345341 - COPC: 1.005918 - KLD: 1.402867
2024-11-13 13:35:16 INFO Monitor(max) STOP: 0.407952 !
2024-11-13 13:35:16 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 13:35:16 INFO --- 3235/3235 batches finished ---
2024-11-13 13:35:16 INFO Train loss: 0.348505
2024-11-13 13:35:16 INFO ************ Epoch=4 end ************
2024-11-13 13:40:17 INFO [Metrics] AUC-ROC: 0.777776 - AUC-PR: 0.411613 - ACC: 0.831055 - Precision: 0.505266 - Recall: 0.243198 - F1: 0.328351 - MCC: 0.266555 - Logloss: 0.408363 - MSE: 0.125510 - RMSE: 0.354274 - COPC: 1.006771 - KLD: 1.502053
2024-11-13 13:40:17 INFO Monitor(max) STOP: 0.369413 !
2024-11-13 13:40:17 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 13:40:17 INFO Early stopping at epoch=5
2024-11-13 13:40:17 INFO --- 3235/3235 batches finished ---
2024-11-13 13:40:17 INFO Train loss: 0.296762
2024-11-13 13:40:17 INFO Training finished.
2024-11-13 13:40:17 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FmFM/Avazu/FmFM_model_seed2020.ckpt
2024-11-13 13:40:18 INFO Start evaluate model
2024-11-13 13:40:48 INFO [Metrics] AUC-ROC: 0.790695 - AUC-PR: 0.434438 - ACC: 0.838451 - Precision: 0.585033 - Recall: 0.167264 - F1: 0.260150 - MCC: 0.249812 - Logloss: 0.374751 - MSE: 0.117975 - RMSE: 0.343474 - COPC: 1.023246 - KLD: 1.386001
2024-11-13 13:40:48 INFO Start testing model
2024-11-13 13:41:18 INFO [Metrics] AUC-ROC: 0.790854 - AUC-PR: 0.434629 - ACC: 0.838499 - Precision: 0.585458 - Recall: 0.167532 - F1: 0.260516 - MCC: 0.250183 - Logloss: 0.374704 - MSE: 0.117952 - RMSE: 0.343442 - COPC: 1.024120 - KLD: 1.385776
