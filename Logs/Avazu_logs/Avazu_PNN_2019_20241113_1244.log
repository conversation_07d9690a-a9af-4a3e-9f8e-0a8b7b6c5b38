2024-11-13 12:44:06 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='PNN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-13 12:44:06 INFO Start process <PERSON><PERSON> !
2024-11-13 12:44:06 INFO Loading Avazu dataset
2024-11-13 12:44:06 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-13 12:44:09 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-13 12:44:09 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-13 12:44:10 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-13 12:44:10 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-13 12:44:10 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-13 12:44:10 INFO Loading data done
2024-11-13 12:44:12 INFO Model: PNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=660, out_features=1000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=1000, out_features=1000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=1000, out_features=1000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=1000, out_features=1, bias=True)
      (7): Sigmoid()
    )
  )
)
2024-11-13 12:44:12 INFO Model parameters: 136638641
2024-11-13 12:44:12 INFO Start training model
2024-11-13 12:44:12 INFO Start training: 3235 batches/epoch
2024-11-13 12:44:12 INFO ************ Epoch=1 start ************
2024-11-13 12:48:13 INFO [Metrics] AUC-ROC: 0.795725 - AUC-PR: 0.443948 - ACC: 0.839666 - Precision: 0.617271 - Recall: 0.146801 - F1: 0.237192 - MCC: 0.244482 - Logloss: 0.370264 - MSE: 0.116758 - RMSE: 0.341698 - COPC: 1.009865 - KLD: 1.366905
2024-11-13 12:48:13 INFO Save best model: monitor(max): 0.425461
2024-11-13 12:48:14 INFO --- 3235/3235 batches finished ---
2024-11-13 12:48:14 INFO Train loss: 0.380314
2024-11-13 12:48:14 INFO ************ Epoch=1 end ************
2024-11-13 12:53:18 INFO [Metrics] AUC-ROC: 0.768952 - AUC-PR: 0.381375 - ACC: 0.822326 - Precision: 0.452621 - Recall: 0.221329 - F1: 0.297287 - MCC: 0.226667 - Logloss: 0.404605 - MSE: 0.127409 - RMSE: 0.356944 - COPC: 0.935771 - KLD: 1.490423
2024-11-13 12:53:18 INFO Monitor(max) STOP: 0.364348 !
2024-11-13 12:53:18 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 12:53:18 INFO --- 3235/3235 batches finished ---
2024-11-13 12:53:18 INFO Train loss: 0.290006
2024-11-13 12:53:18 INFO ************ Epoch=2 end ************
2024-11-13 12:59:02 INFO [Metrics] AUC-ROC: 0.753288 - AUC-PR: 0.363122 - ACC: 0.794343 - Precision: 0.383963 - Recall: 0.349318 - F1: 0.365822 - MCC: 0.243808 - Logloss: 0.508352 - MSE: 0.148926 - RMSE: 0.385910 - COPC: 0.788499 - KLD: 1.772304
2024-11-13 12:59:02 INFO Monitor(max) STOP: 0.244936 !
2024-11-13 12:59:02 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 12:59:02 INFO Early stopping at epoch=3
2024-11-13 12:59:02 INFO --- 3235/3235 batches finished ---
2024-11-13 12:59:02 INFO Train loss: 0.248867
2024-11-13 12:59:02 INFO Training finished.
2024-11-13 12:59:02 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/PNN/Avazu/PNN_model_seed2019.ckpt
2024-11-13 12:59:03 INFO Start evaluate model
2024-11-13 12:59:35 INFO [Metrics] AUC-ROC: 0.795725 - AUC-PR: 0.443948 - ACC: 0.839666 - Precision: 0.617271 - Recall: 0.146801 - F1: 0.237192 - MCC: 0.244482 - Logloss: 0.370264 - MSE: 0.116758 - RMSE: 0.341698 - COPC: 1.009865 - KLD: 1.366905
2024-11-13 12:59:35 INFO Start testing model
2024-11-13 13:00:07 INFO [Metrics] AUC-ROC: 0.795922 - AUC-PR: 0.443910 - ACC: 0.839686 - Precision: 0.617625 - Recall: 0.146746 - F1: 0.237146 - MCC: 0.244554 - Logloss: 0.370190 - MSE: 0.116737 - RMSE: 0.341668 - COPC: 1.010588 - KLD: 1.366488
