2024-11-14 02:17:31 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='AutoInt', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-14 02:17:31 INFO Start process <PERSON><PERSON> !
2024-11-14 02:17:31 INFO Loading Avazu dataset
2024-11-14 02:17:31 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-14 02:17:34 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-14 02:17:34 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-14 02:17:35 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-14 02:17:35 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-14 02:17:35 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-14 02:17:35 INFO Loading data done
2024-11-14 02:17:38 INFO Model: AutoInt(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (self_attention): Sequential(
    (0): MultiHeadSelfAttention(
      (W_q): Linear(in_features=16, out_features=128, bias=False)
      (W_k): Linear(in_features=16, out_features=128, bias=False)
      (W_v): Linear(in_features=16, out_features=128, bias=False)
      (W_res): Linear(in_features=16, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (1): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (2): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (3): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (4): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (5): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (6): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
  )
  (fc): Linear(in_features=3072, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-14 02:17:38 INFO Model parameters: 134280817
2024-11-14 02:17:38 INFO Start training model
2024-11-14 02:17:38 INFO Start training: 3235 batches/epoch
2024-11-14 02:17:38 INFO ************ Epoch=1 start ************
2024-11-14 02:23:26 INFO [Metrics] AUC-ROC: 0.789886 - AUC-PR: 0.434900 - ACC: 0.838786 - Precision: 0.608880 - Recall: 0.141481 - F1: 0.229610 - MCC: 0.237014 - Logloss: 0.374406 - MSE: 0.117914 - RMSE: 0.343387 - COPC: 0.949429 - KLD: 1.385011
2024-11-14 02:23:26 INFO Save best model: monitor(max): 0.415480
2024-11-14 02:23:26 INFO --- 3235/3235 batches finished ---
2024-11-14 02:23:27 INFO Train loss: 0.386759
2024-11-14 02:23:27 INFO ************ Epoch=1 end ************
2024-11-14 02:32:09 INFO [Metrics] AUC-ROC: 0.756062 - AUC-PR: 0.396865 - ACC: 0.833410 - Precision: 0.524877 - Recall: 0.199753 - F1: 0.289378 - MCC: 0.248571 - Logloss: 0.426646 - MSE: 0.124926 - RMSE: 0.353449 - COPC: 1.061634 - KLD: 1.630382
2024-11-14 02:32:09 INFO Monitor(max) STOP: 0.329416 !
2024-11-14 02:32:09 INFO Reduce learning rate on plateau: 0.000100
2024-11-14 02:32:09 INFO --- 3235/3235 batches finished ---
2024-11-14 02:32:09 INFO Train loss: 0.300216
2024-11-14 02:32:09 INFO ************ Epoch=2 end ************
2024-11-14 02:40:21 INFO [Metrics] AUC-ROC: 0.753894 - AUC-PR: 0.389776 - ACC: 0.830038 - Precision: 0.499013 - Recall: 0.232456 - F1: 0.317167 - MCC: 0.256974 - Logloss: 0.498993 - MSE: 0.130097 - RMSE: 0.360690 - COPC: 1.063583 - KLD: 1.899205
2024-11-14 02:40:21 INFO Monitor(max) STOP: 0.254901 !
2024-11-14 02:40:21 INFO Reduce learning rate on plateau: 0.000010
2024-11-14 02:40:21 INFO Early stopping at epoch=3
2024-11-14 02:40:21 INFO --- 3235/3235 batches finished ---
2024-11-14 02:40:21 INFO Train loss: 0.273680
2024-11-14 02:40:21 INFO Training finished.
2024-11-14 02:40:21 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AutoInt/Avazu/AutoInt_model_seed2019.ckpt
2024-11-14 02:40:22 INFO Start evaluate model
2024-11-14 02:40:54 INFO [Metrics] AUC-ROC: 0.789886 - AUC-PR: 0.434900 - ACC: 0.838786 - Precision: 0.608880 - Recall: 0.141481 - F1: 0.229610 - MCC: 0.237014 - Logloss: 0.374406 - MSE: 0.117914 - RMSE: 0.343387 - COPC: 0.949429 - KLD: 1.385011
2024-11-14 02:40:54 INFO Start testing model
2024-11-14 02:41:24 INFO [Metrics] AUC-ROC: 0.790040 - AUC-PR: 0.434976 - ACC: 0.838960 - Precision: 0.611274 - Recall: 0.141783 - F1: 0.230177 - MCC: 0.238083 - Logloss: 0.374332 - MSE: 0.117884 - RMSE: 0.343342 - COPC: 0.950001 - KLD: 1.384720
