2024-11-14 03:14:32 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='FM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-14 03:14:32 INFO Start process Ava<PERSON> !
2024-11-14 03:14:32 INFO Loading Avazu dataset
2024-11-14 03:14:32 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-14 03:14:34 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-14 03:14:35 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-14 03:14:35 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-14 03:14:35 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-14 03:14:35 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-14 03:14:35 INFO Loading data done
2024-11-14 03:14:38 INFO Model: FM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fm_layer): FM_Layer(
    (inner_product_layer): InnerProductLayer()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (C1): Embedding(8, 1, padding_idx=7)
          (banner_pos): Embedding(8, 1, padding_idx=7)
          (site_id): Embedding(4623, 1, padding_idx=4622)
          (site_domain): Embedding(7307, 1, padding_idx=7306)
          (site_category): Embedding(26, 1, padding_idx=25)
          (app_id): Embedding(8218, 1, padding_idx=8217)
          (app_domain): Embedding(527, 1, padding_idx=526)
          (app_category): Embedding(36, 1, padding_idx=35)
          (device_id): Embedding(2329635, 1, padding_idx=2329634)
          (device_ip): Embedding(6011540, 1, padding_idx=6011539)
          (device_model): Embedding(8067, 1, padding_idx=8066)
          (device_type): Embedding(6, 1, padding_idx=5)
          (device_conn_type): Embedding(5, 1, padding_idx=4)
          (C14): Embedding(2611, 1, padding_idx=2610)
          (C15): Embedding(9, 1, padding_idx=8)
          (C16): Embedding(10, 1, padding_idx=9)
          (C17): Embedding(435, 1, padding_idx=434)
          (C18): Embedding(5, 1, padding_idx=4)
          (C19): Embedding(69, 1, padding_idx=68)
          (C20): Embedding(173, 1, padding_idx=172)
          (C21): Embedding(61, 1, padding_idx=60)
          (hour): Embedding(25, 1, padding_idx=24)
          (weekday): Embedding(8, 1, padding_idx=7)
          (weekend): Embedding(3, 1, padding_idx=2)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
    (final_activation): Sigmoid()
  )
)
2024-11-14 03:14:38 INFO Model parameters: 142348056
2024-11-14 03:14:38 INFO Start training model
2024-11-14 03:14:38 INFO Start training: 3235 batches/epoch
2024-11-14 03:14:38 INFO ************ Epoch=1 start ************
2024-11-14 03:20:34 INFO [Metrics] AUC-ROC: 0.774781 - AUC-PR: 0.413917 - ACC: 0.837081 - Precision: 0.610034 - Recall: 0.112430 - F1: 0.189867 - MCC: 0.210745 - Logloss: 0.382973 - MSE: 0.120332 - RMSE: 0.346889 - COPC: 1.000279 - KLD: 1.426557
2024-11-14 03:20:34 INFO Save best model: monitor(max): 0.391808
2024-11-14 03:20:35 INFO --- 3235/3235 batches finished ---
2024-11-14 03:20:35 INFO Train loss: 0.398694
2024-11-14 03:20:35 INFO ************ Epoch=1 end ************
2024-11-14 03:25:35 INFO [Metrics] AUC-ROC: 0.779999 - AUC-PR: 0.420998 - ACC: 0.837424 - Precision: 0.596668 - Recall: 0.131399 - F1: 0.215368 - MCC: 0.224080 - Logloss: 0.380053 - MSE: 0.119510 - RMSE: 0.345702 - COPC: 0.992311 - KLD: 1.412650
2024-11-14 03:25:35 INFO Save best model: monitor(max): 0.399946
2024-11-14 03:25:36 INFO --- 3235/3235 batches finished ---
2024-11-14 03:25:36 INFO Train loss: 0.390354
2024-11-14 03:25:36 INFO ************ Epoch=2 end ************
2024-11-14 03:30:13 INFO [Metrics] AUC-ROC: 0.782052 - AUC-PR: 0.422906 - ACC: 0.837551 - Precision: 0.590198 - Recall: 0.141747 - F1: 0.228593 - MCC: 0.230868 - Logloss: 0.379046 - MSE: 0.119275 - RMSE: 0.345362 - COPC: 0.968235 - KLD: 1.407041
2024-11-14 03:30:13 INFO Save best model: monitor(max): 0.403006
2024-11-14 03:30:14 INFO --- 3235/3235 batches finished ---
2024-11-14 03:30:14 INFO Train loss: 0.387135
2024-11-14 03:30:14 INFO ************ Epoch=3 end ************
2024-11-14 03:35:10 INFO [Metrics] AUC-ROC: 0.783981 - AUC-PR: 0.426985 - ACC: 0.837843 - Precision: 0.587699 - Recall: 0.150922 - F1: 0.240168 - MCC: 0.237660 - Logloss: 0.377861 - MSE: 0.118883 - RMSE: 0.344794 - COPC: 0.991306 - KLD: 1.402123
2024-11-14 03:35:10 INFO Save best model: monitor(max): 0.406120
2024-11-14 03:35:11 INFO --- 3235/3235 batches finished ---
2024-11-14 03:35:11 INFO Train loss: 0.384208
2024-11-14 03:35:11 INFO ************ Epoch=4 end ************
2024-11-14 03:40:07 INFO [Metrics] AUC-ROC: 0.785012 - AUC-PR: 0.427904 - ACC: 0.837917 - Precision: 0.588580 - Recall: 0.151092 - F1: 0.240458 - MCC: 0.238115 - Logloss: 0.377513 - MSE: 0.118780 - RMSE: 0.344645 - COPC: 1.019941 - KLD: 1.400267
2024-11-14 03:40:07 INFO Save best model: monitor(max): 0.407499
2024-11-14 03:40:08 INFO --- 3235/3235 batches finished ---
2024-11-14 03:40:08 INFO Train loss: 0.381618
2024-11-14 03:40:08 INFO ************ Epoch=5 end ************
2024-11-14 03:45:05 INFO [Metrics] AUC-ROC: 0.785266 - AUC-PR: 0.427620 - ACC: 0.837931 - Precision: 0.594981 - Recall: 0.142706 - F1: 0.230200 - MCC: 0.233334 - Logloss: 0.377932 - MSE: 0.118894 - RMSE: 0.344811 - COPC: 1.064216 - KLD: 1.400553
2024-11-14 03:45:05 INFO Monitor(max) STOP: 0.407334 !
2024-11-14 03:45:05 INFO Reduce learning rate on plateau: 0.000100
2024-11-14 03:45:05 INFO --- 3235/3235 batches finished ---
2024-11-14 03:45:06 INFO Train loss: 0.379214
2024-11-14 03:45:06 INFO ************ Epoch=6 end ************
2024-11-14 03:49:43 INFO [Metrics] AUC-ROC: 0.788822 - AUC-PR: 0.433452 - ACC: 0.838510 - Precision: 0.590027 - Recall: 0.160476 - F1: 0.252325 - MCC: 0.246278 - Logloss: 0.375126 - MSE: 0.118077 - RMSE: 0.343624 - COPC: 1.004709 - KLD: 1.389293
2024-11-14 03:49:43 INFO Save best model: monitor(max): 0.413696
2024-11-14 03:49:44 INFO --- 3235/3235 batches finished ---
2024-11-14 03:49:44 INFO Train loss: 0.353539
2024-11-14 03:49:44 INFO ************ Epoch=7 end ************
2024-11-14 03:54:21 INFO [Metrics] AUC-ROC: 0.789247 - AUC-PR: 0.433801 - ACC: 0.838415 - Precision: 0.587150 - Recall: 0.163076 - F1: 0.255257 - MCC: 0.247295 - Logloss: 0.374908 - MSE: 0.118024 - RMSE: 0.343547 - COPC: 0.998799 - KLD: 1.388163
2024-11-14 03:54:21 INFO Save best model: monitor(max): 0.414339
2024-11-14 03:54:22 INFO --- 3235/3235 batches finished ---
2024-11-14 03:54:22 INFO Train loss: 0.350584
2024-11-14 03:54:22 INFO ************ Epoch=8 end ************
2024-11-14 03:58:56 INFO [Metrics] AUC-ROC: 0.789509 - AUC-PR: 0.433930 - ACC: 0.838447 - Precision: 0.589301 - Recall: 0.160361 - F1: 0.252116 - MCC: 0.245918 - Logloss: 0.374816 - MSE: 0.117997 - RMSE: 0.343507 - COPC: 1.008099 - KLD: 1.387695
2024-11-14 03:58:56 INFO Save best model: monitor(max): 0.414692
2024-11-14 03:58:57 INFO --- 3235/3235 batches finished ---
2024-11-14 03:58:57 INFO Train loss: 0.349029
2024-11-14 03:58:57 INFO ************ Epoch=9 end ************
2024-11-14 04:03:23 INFO [Metrics] AUC-ROC: 0.789383 - AUC-PR: 0.433622 - ACC: 0.838262 - Precision: 0.583617 - Recall: 0.165797 - F1: 0.258234 - MCC: 0.248126 - Logloss: 0.374879 - MSE: 0.118031 - RMSE: 0.343557 - COPC: 0.985719 - KLD: 1.387691
2024-11-14 04:03:23 INFO Monitor(max) STOP: 0.414504 !
2024-11-14 04:03:23 INFO Reduce learning rate on plateau: 0.000010
2024-11-14 04:03:23 INFO --- 3235/3235 batches finished ---
2024-11-14 04:03:24 INFO Train loss: 0.347868
2024-11-14 04:03:24 INFO ************ Epoch=10 end ************
2024-11-14 04:07:50 INFO [Metrics] AUC-ROC: 0.789687 - AUC-PR: 0.434305 - ACC: 0.838412 - Precision: 0.585550 - Recall: 0.165628 - F1: 0.258217 - MCC: 0.248720 - Logloss: 0.374709 - MSE: 0.117970 - RMSE: 0.343467 - COPC: 0.998731 - KLD: 1.387119
2024-11-14 04:07:50 INFO Save best model: monitor(max): 0.414978
2024-11-14 04:07:51 INFO --- 3235/3235 batches finished ---
2024-11-14 04:07:52 INFO Train loss: 0.342938
2024-11-14 04:07:52 INFO ************ Epoch=11 end ************
2024-11-14 04:12:21 INFO [Metrics] AUC-ROC: 0.789714 - AUC-PR: 0.434438 - ACC: 0.838403 - Precision: 0.585170 - Recall: 0.166074 - F1: 0.258722 - MCC: 0.248928 - Logloss: 0.374703 - MSE: 0.117962 - RMSE: 0.343456 - COPC: 0.999774 - KLD: 1.387112
2024-11-14 04:12:21 INFO Save best model: monitor(max): 0.415010
2024-11-14 04:12:22 INFO --- 3235/3235 batches finished ---
2024-11-14 04:12:22 INFO Train loss: 0.342798
2024-11-14 04:12:22 INFO ************ Epoch=12 end ************
2024-11-14 04:16:51 INFO [Metrics] AUC-ROC: 0.789724 - AUC-PR: 0.434294 - ACC: 0.838364 - Precision: 0.583705 - Recall: 0.167745 - F1: 0.260599 - MCC: 0.249687 - Logloss: 0.374716 - MSE: 0.117978 - RMSE: 0.343479 - COPC: 0.995012 - KLD: 1.387051
2024-11-14 04:16:51 INFO Monitor(max) STOP: 0.415007 !
2024-11-14 04:16:51 INFO Reduce learning rate on plateau: 0.000001
2024-11-14 04:16:51 INFO --- 3235/3235 batches finished ---
2024-11-14 04:16:51 INFO Train loss: 0.342682
2024-11-14 04:16:51 INFO ************ Epoch=13 end ************
2024-11-14 04:21:24 INFO [Metrics] AUC-ROC: 0.789717 - AUC-PR: 0.434333 - ACC: 0.838371 - Precision: 0.584183 - Recall: 0.167076 - F1: 0.259839 - MCC: 0.249344 - Logloss: 0.374707 - MSE: 0.117971 - RMSE: 0.343470 - COPC: 0.995803 - KLD: 1.387045
2024-11-14 04:21:24 INFO Monitor(max) STOP: 0.415010 !
2024-11-14 04:21:24 INFO Reduce learning rate on plateau: 0.000001
2024-11-14 04:21:24 INFO Early stopping at epoch=14
2024-11-14 04:21:24 INFO --- 3235/3235 batches finished ---
2024-11-14 04:21:24 INFO Train loss: 0.342128
2024-11-14 04:21:24 INFO Training finished.
2024-11-14 04:21:24 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FM/Avazu/FM_model_seed2019.ckpt
2024-11-14 04:21:24 INFO Start evaluate model
2024-11-14 04:22:05 INFO [Metrics] AUC-ROC: 0.789714 - AUC-PR: 0.434438 - ACC: 0.838403 - Precision: 0.585170 - Recall: 0.166074 - F1: 0.258722 - MCC: 0.248928 - Logloss: 0.374703 - MSE: 0.117962 - RMSE: 0.343456 - COPC: 0.999774 - KLD: 1.387112
2024-11-14 04:22:06 INFO Start testing model
2024-11-14 04:22:47 INFO [Metrics] AUC-ROC: 0.789551 - AUC-PR: 0.434271 - ACC: 0.838388 - Precision: 0.585112 - Recall: 0.165870 - F1: 0.258468 - MCC: 0.248746 - Logloss: 0.374826 - MSE: 0.117991 - RMSE: 0.343498 - COPC: 1.000453 - KLD: 1.387716
