2024-11-19 06:03:25 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='CCPM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=6000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-19 06:03:25 INFO Start process Ava<PERSON> !
2024-11-19 06:03:25 INFO Loading Avazu dataset
2024-11-19 06:03:25 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-19 06:03:28 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-19 06:03:29 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-19 06:03:29 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-19 06:03:29 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-19 06:03:30 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-19 06:03:30 INFO Loading data done
2024-11-19 06:03:32 INFO Model: CCPM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (conv_layer): CCPM_ConvLayer(
    (conv_layer): Sequential(
      (0): ZeroPad2d((0, 0, 6, 6))
      (1): Conv2d(1, 128, kernel_size=(7, 1), stride=(1, 1))
      (2): KMaxPooling()
      (3): Tanh()
      (4): ZeroPad2d((0, 0, 4, 4))
      (5): Conv2d(128, 256, kernel_size=(5, 1), stride=(1, 1))
      (6): KMaxPooling()
      (7): Tanh()
      (8): ZeroPad2d((0, 0, 2, 2))
      (9): Conv2d(256, 512, kernel_size=(3, 1), stride=(1, 1))
      (10): KMaxPooling()
      (11): Tanh()
    )
  )
  (fc): Linear(in_features=24576, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-19 06:03:32 INFO Model parameters: 134558065
2024-11-19 06:03:32 INFO Start training model
2024-11-19 06:03:32 INFO Start training: 5391 batches/epoch
2024-11-19 06:03:32 INFO ************ Epoch=1 start ************
2024-11-19 07:48:26 INFO [Metrics] AUC-ROC: 0.790082 - AUC-PR: 0.434721 - ACC: 0.838130 - Precision: 0.650138 - Recall: 0.101185 - F1: 0.175115 - MCC: 0.210777 - Logloss: 0.375985 - MSE: 0.118495 - RMSE: 0.344231 - COPC: 1.147428 - KLD: 1.383872
2024-11-19 07:48:26 INFO Save best model: monitor(max): 0.414097
2024-11-19 07:48:27 INFO --- 5391/5391 batches finished ---
2024-11-19 07:48:27 INFO Train loss: 0.384426
2024-11-19 07:48:27 INFO ************ Epoch=1 end ************
2024-11-19 09:33:16 INFO [Metrics] AUC-ROC: 0.781709 - AUC-PR: 0.415256 - ACC: 0.833767 - Precision: 0.527934 - Recall: 0.198844 - F1: 0.288882 - MCC: 0.249326 - Logloss: 0.386398 - MSE: 0.121603 - RMSE: 0.348716 - COPC: 1.033979 - KLD: 1.431306
2024-11-19 09:33:16 INFO Monitor(max) STOP: 0.395311 !
2024-11-19 09:33:16 INFO Reduce learning rate on plateau: 0.000100
2024-11-19 09:33:16 INFO --- 5391/5391 batches finished ---
2024-11-19 09:33:16 INFO Train loss: 0.317708
2024-11-19 09:33:16 INFO ************ Epoch=2 end ************
2024-11-19 11:17:53 INFO [Metrics] AUC-ROC: 0.769470 - AUC-PR: 0.397692 - ACC: 0.824630 - Precision: 0.470725 - Recall: 0.263467 - F1: 0.337842 - MCC: 0.259732 - Logloss: 0.420427 - MSE: 0.129406 - RMSE: 0.359730 - COPC: 0.943721 - KLD: 1.547438
2024-11-19 11:17:53 INFO Monitor(max) STOP: 0.349043 !
2024-11-19 11:17:53 INFO Reduce learning rate on plateau: 0.000010
2024-11-19 11:17:53 INFO Early stopping at epoch=3
2024-11-19 11:17:53 INFO --- 5391/5391 batches finished ---
2024-11-19 11:17:53 INFO Train loss: 0.266280
2024-11-19 11:17:53 INFO Training finished.
2024-11-19 11:17:53 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/CCPM/Avazu/CCPM_model_seed2019.ckpt
2024-11-19 11:17:53 INFO Start evaluate model
2024-11-19 11:24:07 INFO [Metrics] AUC-ROC: 0.790082 - AUC-PR: 0.434721 - ACC: 0.838130 - Precision: 0.650138 - Recall: 0.101185 - F1: 0.175115 - MCC: 0.210777 - Logloss: 0.375985 - MSE: 0.118495 - RMSE: 0.344231 - COPC: 1.147428 - KLD: 1.383872
2024-11-19 11:24:07 INFO Start testing model
2024-11-19 11:30:20 INFO [Metrics] AUC-ROC: 0.790161 - AUC-PR: 0.434678 - ACC: 0.838157 - Precision: 0.650526 - Recall: 0.101327 - F1: 0.175343 - MCC: 0.211035 - Logloss: 0.375991 - MSE: 0.118489 - RMSE: 0.344222 - COPC: 1.148178 - KLD: 1.383818
