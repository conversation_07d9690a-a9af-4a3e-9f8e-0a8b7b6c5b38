2024-11-14 02:50:41 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='AutoInt', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-14 02:50:41 INFO Start process <PERSON><PERSON> !
2024-11-14 02:50:41 INFO Loading Avazu dataset
2024-11-14 02:50:41 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-14 02:50:44 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-14 02:50:44 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-14 02:50:45 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-14 02:50:45 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-14 02:50:45 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-14 02:50:45 INFO Loading data done
2024-11-14 02:50:47 INFO Model: AutoInt(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (self_attention): Sequential(
    (0): MultiHeadSelfAttention(
      (W_q): Linear(in_features=16, out_features=128, bias=False)
      (W_k): Linear(in_features=16, out_features=128, bias=False)
      (W_v): Linear(in_features=16, out_features=128, bias=False)
      (W_res): Linear(in_features=16, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (1): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (2): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (3): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (4): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (5): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (6): MultiHeadSelfAttention(
      (W_q): Linear(in_features=128, out_features=128, bias=False)
      (W_k): Linear(in_features=128, out_features=128, bias=False)
      (W_v): Linear(in_features=128, out_features=128, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
  )
  (fc): Linear(in_features=3072, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-14 02:50:47 INFO Model parameters: 134280817
2024-11-14 02:50:47 INFO Start training model
2024-11-14 02:50:47 INFO Start training: 3235 batches/epoch
2024-11-14 02:50:47 INFO ************ Epoch=1 start ************
2024-11-14 02:59:14 INFO [Metrics] AUC-ROC: 0.789123 - AUC-PR: 0.434150 - ACC: 0.838836 - Precision: 0.610201 - Recall: 0.140891 - F1: 0.228926 - MCC: 0.236943 - Logloss: 0.375213 - MSE: 0.118115 - RMSE: 0.343678 - COPC: 0.934453 - KLD: 1.387907
2024-11-14 02:59:14 INFO Save best model: monitor(max): 0.413909
2024-11-14 02:59:15 INFO --- 3235/3235 batches finished ---
2024-11-14 02:59:15 INFO Train loss: 0.387001
2024-11-14 02:59:15 INFO ************ Epoch=1 end ************
2024-11-14 03:08:29 INFO [Metrics] AUC-ROC: 0.718080 - AUC-PR: 0.373377 - ACC: 0.833850 - Precision: 0.537376 - Recall: 0.154770 - F1: 0.240325 - MCC: 0.221995 - Logloss: 0.481972 - MSE: 0.128488 - RMSE: 0.358453 - COPC: 1.157074 - KLD: 1.933735
2024-11-14 03:08:29 INFO Monitor(max) STOP: 0.236108 !
2024-11-14 03:08:29 INFO Reduce learning rate on plateau: 0.000100
2024-11-14 03:08:29 INFO --- 3235/3235 batches finished ---
2024-11-14 03:08:29 INFO Train loss: 0.300831
2024-11-14 03:08:29 INFO ************ Epoch=2 end ************
2024-11-14 03:18:50 INFO [Metrics] AUC-ROC: 0.740840 - AUC-PR: 0.381257 - ACC: 0.830589 - Precision: 0.502704 - Recall: 0.216068 - F1: 0.302233 - MCC: 0.248781 - Logloss: 0.537225 - MSE: 0.132277 - RMSE: 0.363700 - COPC: 1.141027 - KLD: 2.078452
2024-11-14 03:18:50 INFO Monitor(max) STOP: 0.203615 !
2024-11-14 03:18:50 INFO Reduce learning rate on plateau: 0.000010
2024-11-14 03:18:50 INFO Early stopping at epoch=3
2024-11-14 03:18:50 INFO --- 3235/3235 batches finished ---
2024-11-14 03:18:50 INFO Train loss: 0.273784
2024-11-14 03:18:50 INFO Training finished.
2024-11-14 03:18:50 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AutoInt/Avazu/AutoInt_model_seed2020.ckpt
2024-11-14 03:18:50 INFO Start evaluate model
2024-11-14 03:19:28 INFO [Metrics] AUC-ROC: 0.789123 - AUC-PR: 0.434150 - ACC: 0.838836 - Precision: 0.610201 - Recall: 0.140891 - F1: 0.228926 - MCC: 0.236943 - Logloss: 0.375213 - MSE: 0.118115 - RMSE: 0.343678 - COPC: 0.934453 - KLD: 1.387907
2024-11-14 03:19:28 INFO Start testing model
2024-11-14 03:20:08 INFO [Metrics] AUC-ROC: 0.789307 - AUC-PR: 0.433937 - ACC: 0.838908 - Precision: 0.611317 - Recall: 0.140906 - F1: 0.229023 - MCC: 0.237330 - Logloss: 0.375158 - MSE: 0.118092 - RMSE: 0.343645 - COPC: 0.935032 - KLD: 1.387656
