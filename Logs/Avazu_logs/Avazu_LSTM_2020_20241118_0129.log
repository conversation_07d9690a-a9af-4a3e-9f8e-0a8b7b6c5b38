2024-11-18 01:29:42 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='LSTM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-18 01:29:42 INFO Start process Ava<PERSON> !
2024-11-18 01:29:42 INFO Loading Avazu dataset
2024-11-18 01:29:42 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-18 01:29:45 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-18 01:29:45 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-18 01:29:46 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-18 01:29:46 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-18 01:29:46 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-18 01:29:46 INFO Loading data done
2024-11-18 01:29:47 INFO Model: LSTM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (lstm): LSTM(16, 128, num_layers=2, batch_first=True, dropout=0.2)
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=496, out_features=64, bias=True)
      (1): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Dropout(p=0.2, inplace=False)
      (4): Linear(in_features=64, out_features=32, bias=True)
      (5): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (6): ReLU()
      (7): Dropout(p=0.2, inplace=False)
      (8): Linear(in_features=32, out_features=1, bias=True)
      (9): Sigmoid()
    )
  )
)
2024-11-18 01:29:47 INFO Model parameters: 134215601
2024-11-18 01:29:47 INFO Start training model
2024-11-18 01:29:47 INFO Start training: 3235 batches/epoch
2024-11-18 01:29:47 INFO ************ Epoch=1 start ************
2024-11-18 01:33:45 INFO [Metrics] AUC-ROC: 0.757428 - AUC-PR: 0.386414 - ACC: 0.835314 - Precision: 0.609259 - Recall: 0.084057 - F1: 0.147733 - MCC: 0.181283 - Logloss: 0.392242 - MSE: 0.123206 - RMSE: 0.351008 - COPC: 0.997851 - KLD: 1.468708
2024-11-18 01:33:45 INFO Save best model: monitor(max): 0.365186
2024-11-18 01:33:46 INFO --- 3235/3235 batches finished ---
2024-11-18 01:33:46 INFO Train loss: 0.402351
2024-11-18 01:33:46 INFO ************ Epoch=1 end ************
2024-11-18 01:37:46 INFO [Metrics] AUC-ROC: 0.763448 - AUC-PR: 0.392922 - ACC: 0.835581 - Precision: 0.611817 - Recall: 0.086792 - F1: 0.152018 - MCC: 0.184955 - Logloss: 0.389108 - MSE: 0.122450 - RMSE: 0.349928 - COPC: 0.966136 - KLD: 1.452810
2024-11-18 01:37:46 INFO Save best model: monitor(max): 0.374340
2024-11-18 01:37:47 INFO --- 3235/3235 batches finished ---
2024-11-18 01:37:47 INFO Train loss: 0.392013
2024-11-18 01:37:47 INFO ************ Epoch=2 end ************
2024-11-18 01:41:48 INFO [Metrics] AUC-ROC: 0.765840 - AUC-PR: 0.393342 - ACC: 0.834967 - Precision: 0.576812 - Recall: 0.105524 - F1: 0.178409 - MCC: 0.194099 - Logloss: 0.388265 - MSE: 0.122400 - RMSE: 0.349857 - COPC: 0.943327 - KLD: 1.446478
2024-11-18 01:41:48 INFO Save best model: monitor(max): 0.377575
2024-11-18 01:41:49 INFO --- 3235/3235 batches finished ---
2024-11-18 01:41:49 INFO Train loss: 0.385207
2024-11-18 01:41:49 INFO ************ Epoch=3 end ************
2024-11-18 01:45:51 INFO [Metrics] AUC-ROC: 0.763777 - AUC-PR: 0.385493 - ACC: 0.831312 - Precision: 0.512167 - Recall: 0.138545 - F1: 0.218094 - MCC: 0.200076 - Logloss: 0.391504 - MSE: 0.123808 - RMSE: 0.351864 - COPC: 0.929787 - KLD: 1.456104
2024-11-18 01:45:51 INFO Monitor(max) STOP: 0.372273 !
2024-11-18 01:45:51 INFO Reduce learning rate on plateau: 0.000100
2024-11-18 01:45:51 INFO --- 3235/3235 batches finished ---
2024-11-18 01:45:51 INFO Train loss: 0.376374
2024-11-18 01:45:51 INFO ************ Epoch=4 end ************
2024-11-18 01:49:53 INFO [Metrics] AUC-ROC: 0.758848 - AUC-PR: 0.373489 - ACC: 0.824266 - Precision: 0.453873 - Recall: 0.171774 - F1: 0.249225 - MCC: 0.198274 - Logloss: 0.398632 - MSE: 0.126401 - RMSE: 0.355529 - COPC: 0.903513 - KLD: 1.479797
2024-11-18 01:49:53 INFO Monitor(max) STOP: 0.360216 !
2024-11-18 01:49:53 INFO Reduce learning rate on plateau: 0.000010
2024-11-18 01:49:53 INFO Early stopping at epoch=5
2024-11-18 01:49:53 INFO --- 3235/3235 batches finished ---
2024-11-18 01:49:53 INFO Train loss: 0.366970
2024-11-18 01:49:53 INFO Training finished.
2024-11-18 01:49:53 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/LSTM/Avazu/LSTM_model_seed2020.ckpt
2024-11-18 01:49:54 INFO Start evaluate model
2024-11-18 01:50:27 INFO [Metrics] AUC-ROC: 0.765840 - AUC-PR: 0.393342 - ACC: 0.834967 - Precision: 0.576812 - Recall: 0.105524 - F1: 0.178409 - MCC: 0.194099 - Logloss: 0.388265 - MSE: 0.122400 - RMSE: 0.349857 - COPC: 0.943327 - KLD: 1.446478
2024-11-18 01:50:27 INFO Start testing model
2024-11-18 01:51:01 INFO [Metrics] AUC-ROC: 0.765791 - AUC-PR: 0.393653 - ACC: 0.835076 - Precision: 0.578633 - Recall: 0.105775 - F1: 0.178854 - MCC: 0.194889 - Logloss: 0.388289 - MSE: 0.122378 - RMSE: 0.349826 - COPC: 0.943948 - KLD: 1.446783
