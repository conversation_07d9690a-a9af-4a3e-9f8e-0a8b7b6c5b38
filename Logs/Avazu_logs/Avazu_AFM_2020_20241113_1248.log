2024-11-13 12:48:34 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='AFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=30000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-13 12:48:34 INFO Start process Ava<PERSON> !
2024-11-13 12:48:34 INFO Loading Avazu dataset
2024-11-13 12:48:34 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-13 12:48:36 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-13 12:48:37 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-13 12:48:38 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-13 12:48:38 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-13 12:48:38 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-13 12:48:38 INFO Loading data done
2024-11-13 12:48:40 INFO Model: AFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (elementwise_product_layer): InnerProductLayer()
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (C1): Embedding(8, 1, padding_idx=7)
        (banner_pos): Embedding(8, 1, padding_idx=7)
        (site_id): Embedding(4623, 1, padding_idx=4622)
        (site_domain): Embedding(7307, 1, padding_idx=7306)
        (site_category): Embedding(26, 1, padding_idx=25)
        (app_id): Embedding(8218, 1, padding_idx=8217)
        (app_domain): Embedding(527, 1, padding_idx=526)
        (app_category): Embedding(36, 1, padding_idx=35)
        (device_id): Embedding(2329635, 1, padding_idx=2329634)
        (device_ip): Embedding(6011540, 1, padding_idx=6011539)
        (device_model): Embedding(8067, 1, padding_idx=8066)
        (device_type): Embedding(6, 1, padding_idx=5)
        (device_conn_type): Embedding(5, 1, padding_idx=4)
        (C14): Embedding(2611, 1, padding_idx=2610)
        (C15): Embedding(9, 1, padding_idx=8)
        (C16): Embedding(10, 1, padding_idx=9)
        (C17): Embedding(435, 1, padding_idx=434)
        (C18): Embedding(5, 1, padding_idx=4)
        (C19): Embedding(69, 1, padding_idx=68)
        (C20): Embedding(173, 1, padding_idx=172)
        (C21): Embedding(61, 1, padding_idx=60)
        (hour): Embedding(25, 1, padding_idx=24)
        (weekday): Embedding(8, 1, padding_idx=7)
        (weekend): Embedding(3, 1, padding_idx=2)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (attention): Sequential(
    (0): Linear(in_features=16, out_features=32, bias=True)
    (1): ReLU()
    (2): Linear(in_features=32, out_features=1, bias=False)
    (3): Softmax(dim=1)
  )
  (weight_p): Linear(in_features=16, out_features=1, bias=False)
  (dropout1): Dropout(p=0, inplace=False)
  (dropout2): Dropout(p=0, inplace=False)
  (final_activation): Sigmoid()
)
2024-11-13 12:48:40 INFO Model parameters: 142348648
2024-11-13 12:48:40 INFO Start training model
2024-11-13 12:48:40 INFO Start training: 1079 batches/epoch
2024-11-13 12:48:40 INFO ************ Epoch=1 start ************
2024-11-13 12:52:30 INFO [Metrics] AUC-ROC: 0.766423 - AUC-PR: 0.398217 - ACC: 0.835824 - Precision: 0.605593 - Recall: 0.095078 - F1: 0.164353 - MCC: 0.192089 - Logloss: 0.387685 - MSE: 0.121891 - RMSE: 0.349129 - COPC: 0.987518 - KLD: 1.447395
2024-11-13 12:52:30 INFO Save best model: monitor(max): 0.378738
2024-11-13 12:52:31 INFO --- 1079/1079 batches finished ---
2024-11-13 12:52:31 INFO Train loss: 0.407858
2024-11-13 12:52:31 INFO ************ Epoch=1 end ************
2024-11-13 12:56:23 INFO [Metrics] AUC-ROC: 0.778246 - AUC-PR: 0.413244 - ACC: 0.836285 - Precision: 0.586680 - Recall: 0.121390 - F1: 0.201158 - MCC: 0.211872 - Logloss: 0.381672 - MSE: 0.120105 - RMSE: 0.346561 - COPC: 1.002199 - KLD: 1.419072
2024-11-13 12:56:23 INFO Save best model: monitor(max): 0.396575
2024-11-13 12:56:24 INFO --- 1079/1079 batches finished ---
2024-11-13 12:56:24 INFO Train loss: 0.377633
2024-11-13 12:56:24 INFO ************ Epoch=2 end ************
2024-11-13 13:00:16 INFO [Metrics] AUC-ROC: 0.774490 - AUC-PR: 0.403582 - ACC: 0.831499 - Precision: 0.509925 - Recall: 0.197430 - F1: 0.284650 - MCC: 0.240304 - Logloss: 0.389871 - MSE: 0.122638 - RMSE: 0.350197 - COPC: 0.970409 - KLD: 1.448504
2024-11-13 13:00:16 INFO Monitor(max) STOP: 0.384619 !
2024-11-13 13:00:16 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 13:00:16 INFO --- 1079/1079 batches finished ---
2024-11-13 13:00:16 INFO Train loss: 0.347719
2024-11-13 13:00:16 INFO ************ Epoch=3 end ************
2024-11-13 13:03:44 INFO [Metrics] AUC-ROC: 0.770915 - AUC-PR: 0.395354 - ACC: 0.826112 - Precision: 0.476303 - Recall: 0.241623 - F1: 0.320606 - MCC: 0.250625 - Logloss: 0.403624 - MSE: 0.126167 - RMSE: 0.355200 - COPC: 0.955231 - KLD: 1.494367
2024-11-13 13:03:44 INFO Monitor(max) STOP: 0.367291 !
2024-11-13 13:03:44 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 13:03:44 INFO Early stopping at epoch=4
2024-11-13 13:03:44 INFO --- 1079/1079 batches finished ---
2024-11-13 13:03:45 INFO Train loss: 0.318914
2024-11-13 13:03:45 INFO Training finished.
2024-11-13 13:03:45 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AFM/Avazu/AFM_model_seed2020.ckpt
2024-11-13 13:03:45 INFO Start evaluate model
2024-11-13 13:04:17 INFO [Metrics] AUC-ROC: 0.778246 - AUC-PR: 0.413244 - ACC: 0.836285 - Precision: 0.586680 - Recall: 0.121390 - F1: 0.201158 - MCC: 0.211872 - Logloss: 0.381672 - MSE: 0.120105 - RMSE: 0.346561 - COPC: 1.002199 - KLD: 1.419072
2024-11-13 13:04:18 INFO Start testing model
2024-11-13 13:04:50 INFO [Metrics] AUC-ROC: 0.778360 - AUC-PR: 0.413307 - ACC: 0.836428 - Precision: 0.589106 - Recall: 0.121356 - F1: 0.201254 - MCC: 0.212619 - Logloss: 0.381611 - MSE: 0.120079 - RMSE: 0.346524 - COPC: 1.003119 - KLD: 1.418774
