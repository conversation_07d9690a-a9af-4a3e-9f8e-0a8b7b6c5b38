2024-11-13 14:27:22 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='FiGNN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-13 14:27:22 INFO Start process <PERSON><PERSON> !
2024-11-13 14:27:22 INFO Loading Avazu dataset
2024-11-13 14:27:22 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-13 14:27:25 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-13 14:27:25 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-13 14:27:26 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-13 14:27:26 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-13 14:27:26 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-13 14:27:26 INFO Loading data done
2024-11-13 14:27:28 INFO Model: FiGNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fignn): FiGNN_Layer(
    (gnn): ModuleList(
      (0-8): 9 x GraphLayer()
    )
    (leaky_relu): LeakyReLU(negative_slope=0.01)
    (W_attn): Linear(in_features=32, out_features=1, bias=False)
  )
  (fc): PredictionLayer(
    (mlp1): Linear(in_features=16, out_features=1, bias=False)
    (mlp2): Sequential(
      (0): Linear(in_features=384, out_features=24, bias=False)
      (1): Sigmoid()
    )
  )
  (final_activation): Sigmoid()
)
2024-11-13 14:27:28 INFO Model parameters: 134094640
2024-11-13 14:27:28 INFO Start training model
2024-11-13 14:27:28 INFO Start training: 3235 batches/epoch
2024-11-13 14:27:28 INFO ************ Epoch=1 start ************
2024-11-13 14:32:02 INFO [Metrics] AUC-ROC: 0.789417 - AUC-PR: 0.433655 - ACC: 0.838665 - Precision: 0.612672 - Recall: 0.135623 - F1: 0.222084 - MCC: 0.233107 - Logloss: 0.374343 - MSE: 0.117920 - RMSE: 0.343395 - COPC: 1.005447 - KLD: 1.385733
2024-11-13 14:32:02 INFO Save best model: monitor(max): 0.415074
2024-11-13 14:32:03 INFO --- 3235/3235 batches finished ---
2024-11-13 14:32:03 INFO Train loss: 0.392403
2024-11-13 14:32:03 INFO ************ Epoch=1 end ************
2024-11-13 14:36:39 INFO [Metrics] AUC-ROC: 0.768734 - AUC-PR: 0.406466 - ACC: 0.835769 - Precision: 0.559977 - Recall: 0.153253 - F1: 0.240646 - MCC: 0.229413 - Logloss: 0.393770 - MSE: 0.122200 - RMSE: 0.349571 - COPC: 1.075164 - KLD: 1.474038
2024-11-13 14:36:39 INFO Monitor(max) STOP: 0.374963 !
2024-11-13 14:36:39 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 14:36:39 INFO --- 3235/3235 batches finished ---
2024-11-13 14:36:39 INFO Train loss: 0.357771
2024-11-13 14:36:39 INFO ************ Epoch=2 end ************
2024-11-13 14:41:14 INFO [Metrics] AUC-ROC: 0.762153 - AUC-PR: 0.401653 - ACC: 0.831265 - Precision: 0.507039 - Recall: 0.227125 - F1: 0.313721 - MCC: 0.257710 - Logloss: 0.426731 - MSE: 0.127616 - RMSE: 0.357234 - COPC: 1.103032 - KLD: 1.611919
2024-11-13 14:41:14 INFO Monitor(max) STOP: 0.335422 !
2024-11-13 14:41:14 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 14:41:14 INFO Early stopping at epoch=3
2024-11-13 14:41:14 INFO --- 3235/3235 batches finished ---
2024-11-13 14:41:15 INFO Train loss: 0.336157
2024-11-13 14:41:15 INFO Training finished.
2024-11-13 14:41:15 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FiGNN/Avazu/FiGNN_model_seed2019.ckpt
2024-11-13 14:41:15 INFO Start evaluate model
2024-11-13 14:41:46 INFO [Metrics] AUC-ROC: 0.789417 - AUC-PR: 0.433655 - ACC: 0.838665 - Precision: 0.612672 - Recall: 0.135623 - F1: 0.222084 - MCC: 0.233107 - Logloss: 0.374343 - MSE: 0.117920 - RMSE: 0.343395 - COPC: 1.005447 - KLD: 1.385733
2024-11-13 14:41:46 INFO Start testing model
2024-11-13 14:42:17 INFO [Metrics] AUC-ROC: 0.789490 - AUC-PR: 0.433717 - ACC: 0.838728 - Precision: 0.613491 - Recall: 0.135833 - F1: 0.222419 - MCC: 0.233563 - Logloss: 0.374387 - MSE: 0.117902 - RMSE: 0.343369 - COPC: 1.006061 - KLD: 1.386020
