2024-11-18 12:07:07 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='GDCN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=1000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-18 12:07:07 INFO Start process Ava<PERSON> !
2024-11-18 12:07:07 INFO Loading Avazu dataset
2024-11-18 12:07:07 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-18 12:07:10 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-18 12:07:10 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-18 12:07:11 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-18 12:07:11 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-18 12:07:11 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-18 12:07:11 INFO Loading data done
2024-11-18 12:07:13 INFO Model: GDCN(
  (embedding): FeaturesEmbedding(
    (embed_dict): ModuleDict(
      (C1): Embedding(8, 16)
      (banner_pos): Embedding(8, 16)
      (site_id): Embedding(4623, 16)
      (site_domain): Embedding(7307, 16)
      (site_category): Embedding(26, 16)
      (app_id): Embedding(8218, 16)
      (app_domain): Embedding(527, 16)
      (app_category): Embedding(36, 16)
      (device_id): Embedding(2329635, 16)
      (device_ip): Embedding(6011540, 16)
      (device_model): Embedding(8067, 16)
      (device_type): Embedding(6, 16)
      (device_conn_type): Embedding(5, 16)
      (C14): Embedding(2611, 16)
      (C15): Embedding(9, 16)
      (C16): Embedding(10, 16)
      (C17): Embedding(435, 16)
      (C18): Embedding(5, 16)
      (C19): Embedding(69, 16)
      (C20): Embedding(173, 16)
      (C21): Embedding(61, 16)
      (hour): Embedding(25, 16)
      (weekday): Embedding(8, 16)
      (weekend): Embedding(3, 16)
    )
  )
  (embedding_dropout): Dropout(p=0.0, inplace=False)
  (cross_net): GateCorssLayer(
    (w): ModuleList(
      (0-2): 3 x Linear(in_features=384, out_features=384, bias=False)
    )
    (wg): ModuleList(
      (0-2): 3 x Linear(in_features=384, out_features=384, bias=False)
    )
    (b): ParameterList(
        (0): Parameter containing: [torch.float32 of size 384]
        (1): Parameter containing: [torch.float32 of size 384]
        (2): Parameter containing: [torch.float32 of size 384]
    )
    (activation): Sigmoid()
  )
  (mlp): Sequential(
    (0): Linear(in_features=384, out_features=400, bias=True)
    (1): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): ReLU()
    (3): Dropout(p=0.5, inplace=False)
    (4): Linear(in_features=400, out_features=400, bias=True)
    (5): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (6): ReLU()
    (7): Dropout(p=0.5, inplace=False)
    (8): Linear(in_features=400, out_features=400, bias=True)
    (9): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (10): ReLU()
    (11): Dropout(p=0.5, inplace=False)
  )
  (fc): Linear(in_features=784, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-18 12:07:13 INFO Model parameters: 135338513
2024-11-18 12:07:13 INFO Start training model
2024-11-18 12:07:13 INFO Start training: 32344 batches/epoch
2024-11-18 12:07:13 INFO ************ Epoch=1 start ************
2024-11-18 12:29:43 INFO [Metrics] AUC-ROC: 0.765687 - AUC-PR: 0.399291 - ACC: 0.836187 - Precision: 0.606129 - Recall: 0.100775 - F1: 0.172818 - MCC: 0.198076 - Logloss: 0.387618 - MSE: 0.121907 - RMSE: 0.349151 - COPC: 1.013140 - KLD: 1.447100
2024-11-18 12:29:43 INFO Save best model: monitor(max): 0.378069
2024-11-18 12:29:44 INFO --- 32344/32344 batches finished ---
2024-11-18 12:29:44 INFO Train loss: 0.391776
2024-11-18 12:29:44 INFO ************ Epoch=1 end ************
2024-11-18 12:52:45 INFO [Metrics] AUC-ROC: 0.772617 - AUC-PR: 0.406203 - ACC: 0.836219 - Precision: 0.595991 - Recall: 0.110147 - F1: 0.185932 - MCC: 0.204315 - Logloss: 0.384022 - MSE: 0.120991 - RMSE: 0.347838 - COPC: 0.997804 - KLD: 1.429422
2024-11-18 12:52:45 INFO Save best model: monitor(max): 0.388596
2024-11-18 12:52:46 INFO --- 32344/32344 batches finished ---
2024-11-18 12:52:46 INFO Train loss: 0.381956
2024-11-18 12:52:46 INFO ************ Epoch=2 end ************
2024-11-18 13:15:47 INFO [Metrics] AUC-ROC: 0.774136 - AUC-PR: 0.403085 - ACC: 0.833746 - Precision: 0.539742 - Recall: 0.142052 - F1: 0.224911 - MCC: 0.213106 - Logloss: 0.384494 - MSE: 0.121412 - RMSE: 0.348443 - COPC: 0.961324 - KLD: 1.427934
2024-11-18 13:15:47 INFO Save best model: monitor(max): 0.389642
2024-11-18 13:15:48 INFO --- 32344/32344 batches finished ---
2024-11-18 13:15:48 INFO Train loss: 0.371794
2024-11-18 13:15:48 INFO ************ Epoch=3 end ************
2024-11-18 13:37:59 INFO [Metrics] AUC-ROC: 0.774085 - AUC-PR: 0.395998 - ACC: 0.829399 - Precision: 0.493716 - Recall: 0.183902 - F1: 0.267984 - MCC: 0.224170 - Logloss: 0.388818 - MSE: 0.123040 - RMSE: 0.350771 - COPC: 0.948723 - KLD: 1.439848
2024-11-18 13:37:59 INFO Monitor(max) STOP: 0.385267 !
2024-11-18 13:37:59 INFO Reduce learning rate on plateau: 0.000100
2024-11-18 13:37:59 INFO --- 32344/32344 batches finished ---
2024-11-18 13:37:59 INFO Train loss: 0.360967
2024-11-18 13:37:59 INFO ************ Epoch=4 end ************
2024-11-18 13:59:34 INFO [Metrics] AUC-ROC: 0.769776 - AUC-PR: 0.384618 - ACC: 0.824143 - Precision: 0.461006 - Recall: 0.210659 - F1: 0.289177 - MCC: 0.224946 - Logloss: 0.397983 - MSE: 0.126094 - RMSE: 0.355097 - COPC: 0.937368 - KLD: 1.468870
2024-11-18 13:59:34 INFO Monitor(max) STOP: 0.371793 !
2024-11-18 13:59:34 INFO Reduce learning rate on plateau: 0.000010
2024-11-18 13:59:34 INFO Early stopping at epoch=5
2024-11-18 13:59:34 INFO --- 32344/32344 batches finished ---
2024-11-18 13:59:35 INFO Train loss: 0.345296
2024-11-18 13:59:35 INFO Training finished.
2024-11-18 13:59:35 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/GDCN/Avazu/GDCN_model_seed2020.ckpt
2024-11-18 13:59:35 INFO Start evaluate model
2024-11-18 14:00:09 INFO [Metrics] AUC-ROC: 0.774136 - AUC-PR: 0.403085 - ACC: 0.833746 - Precision: 0.539742 - Recall: 0.142052 - F1: 0.224911 - MCC: 0.213106 - Logloss: 0.384494 - MSE: 0.121412 - RMSE: 0.348443 - COPC: 0.961324 - KLD: 1.427934
2024-11-18 14:00:09 INFO Start testing model
2024-11-18 14:00:42 INFO [Metrics] AUC-ROC: 0.774131 - AUC-PR: 0.403124 - ACC: 0.833813 - Precision: 0.540692 - Recall: 0.141572 - F1: 0.224390 - MCC: 0.213079 - Logloss: 0.384540 - MSE: 0.121401 - RMSE: 0.348426 - COPC: 0.961888 - KLD: 1.428226
