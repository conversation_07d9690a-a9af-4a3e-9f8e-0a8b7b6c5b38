2024-11-14 02:50:49 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='WideDeep', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-14 02:50:49 INFO Start process <PERSON><PERSON> !
2024-11-14 02:50:49 INFO Loading Avazu dataset
2024-11-14 02:50:49 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-14 02:50:51 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-14 02:50:52 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-14 02:50:53 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-14 02:50:53 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-14 02:50:53 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-14 02:50:53 INFO Loading data done
2024-11-14 02:50:55 INFO Model: WideDeep(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (C1): Embedding(8, 16)
        (banner_pos): Embedding(8, 16)
        (site_id): Embedding(4623, 16)
        (site_domain): Embedding(7307, 16)
        (site_category): Embedding(26, 16)
        (app_id): Embedding(8218, 16)
        (app_domain): Embedding(527, 16)
        (app_category): Embedding(36, 16)
        (device_id): Embedding(2329635, 16)
        (device_ip): Embedding(6011540, 16)
        (device_model): Embedding(8067, 16)
        (device_type): Embedding(6, 16)
        (device_conn_type): Embedding(5, 16)
        (C14): Embedding(2611, 16)
        (C15): Embedding(9, 16)
        (C16): Embedding(10, 16)
        (C17): Embedding(435, 16)
        (C18): Embedding(5, 16)
        (C19): Embedding(69, 16)
        (C20): Embedding(173, 16)
        (C21): Embedding(61, 16)
        (hour): Embedding(25, 16)
        (weekday): Embedding(8, 16)
        (weekend): Embedding(3, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (C1): Embedding(8, 1, padding_idx=7)
        (banner_pos): Embedding(8, 1, padding_idx=7)
        (site_id): Embedding(4623, 1, padding_idx=4622)
        (site_domain): Embedding(7307, 1, padding_idx=7306)
        (site_category): Embedding(26, 1, padding_idx=25)
        (app_id): Embedding(8218, 1, padding_idx=8217)
        (app_domain): Embedding(527, 1, padding_idx=526)
        (app_category): Embedding(36, 1, padding_idx=35)
        (device_id): Embedding(2329635, 1, padding_idx=2329634)
        (device_ip): Embedding(6011540, 1, padding_idx=6011539)
        (device_model): Embedding(8067, 1, padding_idx=8066)
        (device_type): Embedding(6, 1, padding_idx=5)
        (device_conn_type): Embedding(5, 1, padding_idx=4)
        (C14): Embedding(2611, 1, padding_idx=2610)
        (C15): Embedding(9, 1, padding_idx=8)
        (C16): Embedding(10, 1, padding_idx=9)
        (C17): Embedding(435, 1, padding_idx=434)
        (C18): Embedding(5, 1, padding_idx=4)
        (C19): Embedding(69, 1, padding_idx=68)
        (C20): Embedding(173, 1, padding_idx=172)
        (C21): Embedding(61, 1, padding_idx=60)
        (hour): Embedding(25, 1, padding_idx=24)
        (weekday): Embedding(8, 1, padding_idx=7)
        (weekend): Embedding(3, 1, padding_idx=2)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=384, out_features=2000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=2000, out_features=2000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=2000, out_features=2000, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.2, inplace=False)
      (9): Linear(in_features=2000, out_features=2000, bias=True)
      (10): ReLU()
      (11): Dropout(p=0.2, inplace=False)
      (12): Linear(in_features=2000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-14 02:50:55 INFO Model parameters: 155126056
2024-11-14 02:50:55 INFO Start training model
2024-11-14 02:50:55 INFO Start training: 3235 batches/epoch
2024-11-14 02:50:55 INFO ************ Epoch=1 start ************
2024-11-14 02:57:19 INFO [Metrics] AUC-ROC: 0.791142 - AUC-PR: 0.436779 - ACC: 0.838944 - Precision: 0.619702 - Recall: 0.133384 - F1: 0.219519 - MCC: 0.233382 - Logloss: 0.373279 - MSE: 0.117594 - RMSE: 0.342920 - COPC: 0.994302 - KLD: 1.380995
2024-11-14 02:57:19 INFO Save best model: monitor(max): 0.417863
2024-11-14 02:57:20 INFO --- 3235/3235 batches finished ---
2024-11-14 02:57:20 INFO Train loss: 0.393210
2024-11-14 02:57:20 INFO ************ Epoch=1 end ************
2024-11-14 03:03:34 INFO [Metrics] AUC-ROC: 0.772932 - AUC-PR: 0.409904 - ACC: 0.836162 - Precision: 0.561892 - Recall: 0.159532 - F1: 0.248508 - MCC: 0.235028 - Logloss: 0.391284 - MSE: 0.121097 - RMSE: 0.347990 - COPC: 1.064679 - KLD: 1.463255
2024-11-14 03:03:34 INFO Monitor(max) STOP: 0.381649 !
2024-11-14 03:03:34 INFO Reduce learning rate on plateau: 0.000100
2024-11-14 03:03:34 INFO --- 3235/3235 batches finished ---
2024-11-14 03:03:34 INFO Train loss: 0.345216
2024-11-14 03:03:34 INFO ************ Epoch=2 end ************
2024-11-14 03:11:06 INFO [Metrics] AUC-ROC: 0.738813 - AUC-PR: 0.385153 - ACC: 0.831801 - Precision: 0.511047 - Recall: 0.218876 - F1: 0.306487 - MCC: 0.254528 - Logloss: 0.508528 - MSE: 0.130655 - RMSE: 0.361462 - COPC: 1.135678 - KLD: 2.028003
2024-11-14 03:11:06 INFO Monitor(max) STOP: 0.230285 !
2024-11-14 03:11:06 INFO Reduce learning rate on plateau: 0.000010
2024-11-14 03:11:06 INFO Early stopping at epoch=3
2024-11-14 03:11:06 INFO --- 3235/3235 batches finished ---
2024-11-14 03:11:06 INFO Train loss: 0.310810
2024-11-14 03:11:06 INFO Training finished.
2024-11-14 03:11:06 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/WideDeep/Avazu/WideDeep_model_seed2020.ckpt
2024-11-14 03:11:07 INFO Start evaluate model
2024-11-14 03:11:43 INFO [Metrics] AUC-ROC: 0.791142 - AUC-PR: 0.436779 - ACC: 0.838944 - Precision: 0.619702 - Recall: 0.133384 - F1: 0.219519 - MCC: 0.233382 - Logloss: 0.373279 - MSE: 0.117594 - RMSE: 0.342920 - COPC: 0.994302 - KLD: 1.380995
2024-11-14 03:11:43 INFO Start testing model
2024-11-14 03:12:19 INFO [Metrics] AUC-ROC: 0.791328 - AUC-PR: 0.437076 - ACC: 0.838986 - Precision: 0.620812 - Recall: 0.133027 - F1: 0.219105 - MCC: 0.233416 - Logloss: 0.373191 - MSE: 0.117558 - RMSE: 0.342868 - COPC: 0.994847 - KLD: 1.380640
