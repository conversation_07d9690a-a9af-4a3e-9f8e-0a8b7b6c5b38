2024-11-13 12:32:06 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='AFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=30000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-13 12:32:06 INFO Start process Ava<PERSON> !
2024-11-13 12:32:06 INFO Loading Avazu dataset
2024-11-13 12:32:06 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-13 12:32:08 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-13 12:32:09 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-13 12:32:09 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-13 12:32:09 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-13 12:32:09 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-13 12:32:09 INFO Loading data done
2024-11-13 12:32:11 INFO Model: AFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (elementwise_product_layer): InnerProductLayer()
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (C1): Embedding(8, 1, padding_idx=7)
        (banner_pos): Embedding(8, 1, padding_idx=7)
        (site_id): Embedding(4623, 1, padding_idx=4622)
        (site_domain): Embedding(7307, 1, padding_idx=7306)
        (site_category): Embedding(26, 1, padding_idx=25)
        (app_id): Embedding(8218, 1, padding_idx=8217)
        (app_domain): Embedding(527, 1, padding_idx=526)
        (app_category): Embedding(36, 1, padding_idx=35)
        (device_id): Embedding(2329635, 1, padding_idx=2329634)
        (device_ip): Embedding(6011540, 1, padding_idx=6011539)
        (device_model): Embedding(8067, 1, padding_idx=8066)
        (device_type): Embedding(6, 1, padding_idx=5)
        (device_conn_type): Embedding(5, 1, padding_idx=4)
        (C14): Embedding(2611, 1, padding_idx=2610)
        (C15): Embedding(9, 1, padding_idx=8)
        (C16): Embedding(10, 1, padding_idx=9)
        (C17): Embedding(435, 1, padding_idx=434)
        (C18): Embedding(5, 1, padding_idx=4)
        (C19): Embedding(69, 1, padding_idx=68)
        (C20): Embedding(173, 1, padding_idx=172)
        (C21): Embedding(61, 1, padding_idx=60)
        (hour): Embedding(25, 1, padding_idx=24)
        (weekday): Embedding(8, 1, padding_idx=7)
        (weekend): Embedding(3, 1, padding_idx=2)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (attention): Sequential(
    (0): Linear(in_features=16, out_features=32, bias=True)
    (1): ReLU()
    (2): Linear(in_features=32, out_features=1, bias=False)
    (3): Softmax(dim=1)
  )
  (weight_p): Linear(in_features=16, out_features=1, bias=False)
  (dropout1): Dropout(p=0, inplace=False)
  (dropout2): Dropout(p=0, inplace=False)
  (final_activation): Sigmoid()
)
2024-11-13 12:32:11 INFO Model parameters: 142348648
2024-11-13 12:32:11 INFO Start training model
2024-11-13 12:32:12 INFO Start training: 1079 batches/epoch
2024-11-13 12:32:12 INFO ************ Epoch=1 start ************
2024-11-13 12:35:35 INFO [Metrics] AUC-ROC: 0.766106 - AUC-PR: 0.398168 - ACC: 0.835820 - Precision: 0.609970 - Recall: 0.091885 - F1: 0.159712 - MCC: 0.189942 - Logloss: 0.387828 - MSE: 0.121916 - RMSE: 0.349164 - COPC: 1.002822 - KLD: 1.448225
2024-11-13 12:35:35 INFO Save best model: monitor(max): 0.378278
2024-11-13 12:35:36 INFO --- 1079/1079 batches finished ---
2024-11-13 12:35:36 INFO Train loss: 0.408201
2024-11-13 12:35:36 INFO ************ Epoch=1 end ************
2024-11-13 12:38:59 INFO [Metrics] AUC-ROC: 0.778031 - AUC-PR: 0.412822 - ACC: 0.836250 - Precision: 0.587272 - Recall: 0.119987 - F1: 0.199262 - MCC: 0.210788 - Logloss: 0.381759 - MSE: 0.120130 - RMSE: 0.346598 - COPC: 1.001336 - KLD: 1.419458
2024-11-13 12:38:59 INFO Save best model: monitor(max): 0.396271
2024-11-13 12:39:00 INFO --- 1079/1079 batches finished ---
2024-11-13 12:39:00 INFO Train loss: 0.378130
2024-11-13 12:39:00 INFO ************ Epoch=2 end ************
2024-11-13 12:42:24 INFO [Metrics] AUC-ROC: 0.775116 - AUC-PR: 0.406331 - ACC: 0.832117 - Precision: 0.514748 - Recall: 0.197600 - F1: 0.285575 - MCC: 0.242599 - Logloss: 0.388807 - MSE: 0.122257 - RMSE: 0.349652 - COPC: 0.976194 - KLD: 1.444965
2024-11-13 12:42:24 INFO Monitor(max) STOP: 0.386309 !
2024-11-13 12:42:24 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 12:42:24 INFO --- 1079/1079 batches finished ---
2024-11-13 12:42:25 INFO Train loss: 0.348224
2024-11-13 12:42:25 INFO ************ Epoch=3 end ************
2024-11-13 12:45:52 INFO [Metrics] AUC-ROC: 0.770728 - AUC-PR: 0.396564 - ACC: 0.826222 - Precision: 0.476952 - Recall: 0.242073 - F1: 0.321150 - MCC: 0.251225 - Logloss: 0.404610 - MSE: 0.126353 - RMSE: 0.355461 - COPC: 0.955701 - KLD: 1.497616
2024-11-13 12:45:52 INFO Monitor(max) STOP: 0.366118 !
2024-11-13 12:45:52 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 12:45:52 INFO Early stopping at epoch=4
2024-11-13 12:45:52 INFO --- 1079/1079 batches finished ---
2024-11-13 12:45:52 INFO Train loss: 0.318474
2024-11-13 12:45:52 INFO Training finished.
2024-11-13 12:45:52 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AFM/Avazu/AFM_model_seed2019.ckpt
2024-11-13 12:45:53 INFO Start evaluate model
2024-11-13 12:46:25 INFO [Metrics] AUC-ROC: 0.778031 - AUC-PR: 0.412822 - ACC: 0.836250 - Precision: 0.587272 - Recall: 0.119987 - F1: 0.199262 - MCC: 0.210788 - Logloss: 0.381759 - MSE: 0.120130 - RMSE: 0.346598 - COPC: 1.001336 - KLD: 1.419458
2024-11-13 12:46:25 INFO Start testing model
2024-11-13 12:46:59 INFO [Metrics] AUC-ROC: 0.777987 - AUC-PR: 0.412419 - ACC: 0.836275 - Precision: 0.588206 - Recall: 0.119402 - F1: 0.198508 - MCC: 0.210552 - Logloss: 0.381806 - MSE: 0.120148 - RMSE: 0.346624 - COPC: 1.002257 - KLD: 1.419579
