2024-11-18 09:46:17 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='GDCN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=1000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-18 09:46:17 INFO Start process Ava<PERSON> !
2024-11-18 09:46:17 INFO Loading Avazu dataset
2024-11-18 09:46:17 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-18 09:46:20 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-18 09:46:20 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-18 09:46:21 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-18 09:46:21 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-18 09:46:21 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-18 09:46:21 INFO Loading data done
2024-11-18 09:46:22 INFO Model: GDCN(
  (embedding): FeaturesEmbedding(
    (embed_dict): ModuleDict(
      (C1): Embedding(8, 16)
      (banner_pos): Embedding(8, 16)
      (site_id): Embedding(4623, 16)
      (site_domain): Embedding(7307, 16)
      (site_category): Embedding(26, 16)
      (app_id): Embedding(8218, 16)
      (app_domain): Embedding(527, 16)
      (app_category): Embedding(36, 16)
      (device_id): Embedding(2329635, 16)
      (device_ip): Embedding(6011540, 16)
      (device_model): Embedding(8067, 16)
      (device_type): Embedding(6, 16)
      (device_conn_type): Embedding(5, 16)
      (C14): Embedding(2611, 16)
      (C15): Embedding(9, 16)
      (C16): Embedding(10, 16)
      (C17): Embedding(435, 16)
      (C18): Embedding(5, 16)
      (C19): Embedding(69, 16)
      (C20): Embedding(173, 16)
      (C21): Embedding(61, 16)
      (hour): Embedding(25, 16)
      (weekday): Embedding(8, 16)
      (weekend): Embedding(3, 16)
    )
  )
  (embedding_dropout): Dropout(p=0.0, inplace=False)
  (cross_net): GateCorssLayer(
    (w): ModuleList(
      (0-2): 3 x Linear(in_features=384, out_features=384, bias=False)
    )
    (wg): ModuleList(
      (0-2): 3 x Linear(in_features=384, out_features=384, bias=False)
    )
    (b): ParameterList(
        (0): Parameter containing: [torch.float32 of size 384]
        (1): Parameter containing: [torch.float32 of size 384]
        (2): Parameter containing: [torch.float32 of size 384]
    )
    (activation): Sigmoid()
  )
  (mlp): Sequential(
    (0): Linear(in_features=384, out_features=400, bias=True)
    (1): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): ReLU()
    (3): Dropout(p=0.5, inplace=False)
    (4): Linear(in_features=400, out_features=400, bias=True)
    (5): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (6): ReLU()
    (7): Dropout(p=0.5, inplace=False)
    (8): Linear(in_features=400, out_features=400, bias=True)
    (9): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (10): ReLU()
    (11): Dropout(p=0.5, inplace=False)
  )
  (fc): Linear(in_features=784, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-18 09:46:22 INFO Model parameters: 135338513
2024-11-18 09:46:22 INFO Start training model
2024-11-18 09:46:23 INFO Start training: 32344 batches/epoch
2024-11-18 09:46:23 INFO ************ Epoch=1 start ************
2024-11-18 10:09:15 INFO [Metrics] AUC-ROC: 0.766369 - AUC-PR: 0.399282 - ACC: 0.836145 - Precision: 0.606436 - Recall: 0.099843 - F1: 0.171458 - MCC: 0.197218 - Logloss: 0.387163 - MSE: 0.121836 - RMSE: 0.349050 - COPC: 0.995257 - KLD: 1.444697
2024-11-18 10:09:15 INFO Save best model: monitor(max): 0.379206
2024-11-18 10:09:16 INFO --- 32344/32344 batches finished ---
2024-11-18 10:09:16 INFO Train loss: 0.391806
2024-11-18 10:09:16 INFO ************ Epoch=1 end ************
2024-11-18 10:31:19 INFO [Metrics] AUC-ROC: 0.773639 - AUC-PR: 0.408780 - ACC: 0.836485 - Precision: 0.594382 - Recall: 0.116654 - F1: 0.195031 - MCC: 0.209963 - Logloss: 0.383426 - MSE: 0.120790 - RMSE: 0.347548 - COPC: 1.000184 - KLD: 1.426795
2024-11-18 10:31:19 INFO Save best model: monitor(max): 0.390213
2024-11-18 10:31:19 INFO --- 32344/32344 batches finished ---
2024-11-18 10:31:20 INFO Train loss: 0.381945
2024-11-18 10:31:20 INFO ************ Epoch=2 end ************
2024-11-18 10:53:12 INFO [Metrics] AUC-ROC: 0.776105 - AUC-PR: 0.410744 - ACC: 0.835580 - Precision: 0.559818 - Recall: 0.148415 - F1: 0.234627 - MCC: 0.225531 - Logloss: 0.383132 - MSE: 0.120861 - RMSE: 0.347650 - COPC: 0.931439 - KLD: 1.421508
2024-11-18 10:53:12 INFO Save best model: monitor(max): 0.392973
2024-11-18 10:53:13 INFO --- 32344/32344 batches finished ---
2024-11-18 10:53:13 INFO Train loss: 0.371846
2024-11-18 10:53:13 INFO ************ Epoch=3 end ************
2024-11-18 11:15:34 INFO [Metrics] AUC-ROC: 0.777090 - AUC-PR: 0.409758 - ACC: 0.834671 - Precision: 0.545052 - Recall: 0.159462 - F1: 0.246738 - MCC: 0.228508 - Logloss: 0.383966 - MSE: 0.120967 - RMSE: 0.347803 - COPC: 0.990171 - KLD: 1.426893
2024-11-18 11:15:34 INFO Save best model: monitor(max): 0.393124
2024-11-18 11:15:34 INFO --- 32344/32344 batches finished ---
2024-11-18 11:15:35 INFO Train loss: 0.361067
2024-11-18 11:15:35 INFO ************ Epoch=4 end ************
2024-11-18 11:37:09 INFO [Metrics] AUC-ROC: 0.776084 - AUC-PR: 0.406633 - ACC: 0.832728 - Precision: 0.521386 - Recall: 0.181898 - F1: 0.269703 - MCC: 0.234979 - Logloss: 0.386479 - MSE: 0.121845 - RMSE: 0.349064 - COPC: 0.965305 - KLD: 1.434110
2024-11-18 11:37:09 INFO Monitor(max) STOP: 0.389605 !
2024-11-18 11:37:09 INFO Reduce learning rate on plateau: 0.000100
2024-11-18 11:37:09 INFO --- 32344/32344 batches finished ---
2024-11-18 11:37:09 INFO Train loss: 0.350359
2024-11-18 11:37:09 INFO ************ Epoch=5 end ************
2024-11-18 11:59:42 INFO [Metrics] AUC-ROC: 0.773029 - AUC-PR: 0.397845 - ACC: 0.828312 - Precision: 0.487289 - Recall: 0.212429 - F1: 0.295874 - MCC: 0.239081 - Logloss: 0.394897 - MSE: 0.124288 - RMSE: 0.352545 - COPC: 0.957034 - KLD: 1.463124
2024-11-18 11:59:42 INFO Monitor(max) STOP: 0.378133 !
2024-11-18 11:59:42 INFO Reduce learning rate on plateau: 0.000010
2024-11-18 11:59:42 INFO Early stopping at epoch=6
2024-11-18 11:59:42 INFO --- 32344/32344 batches finished ---
2024-11-18 11:59:42 INFO Train loss: 0.333817
2024-11-18 11:59:42 INFO Training finished.
2024-11-18 11:59:42 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/GDCN/Avazu/GDCN_model_seed2019.ckpt
2024-11-18 11:59:43 INFO Start evaluate model
2024-11-18 12:00:19 INFO [Metrics] AUC-ROC: 0.777090 - AUC-PR: 0.409758 - ACC: 0.834671 - Precision: 0.545052 - Recall: 0.159462 - F1: 0.246738 - MCC: 0.228508 - Logloss: 0.383966 - MSE: 0.120967 - RMSE: 0.347803 - COPC: 0.990171 - KLD: 1.426893
2024-11-18 12:00:19 INFO Start testing model
2024-11-18 12:00:54 INFO [Metrics] AUC-ROC: 0.777319 - AUC-PR: 0.409907 - ACC: 0.834739 - Precision: 0.545850 - Recall: 0.159318 - F1: 0.246647 - MCC: 0.228709 - Logloss: 0.383913 - MSE: 0.120933 - RMSE: 0.347754 - COPC: 0.991024 - KLD: 1.426636
