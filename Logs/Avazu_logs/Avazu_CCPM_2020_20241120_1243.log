2024-11-20 12:43:30 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='CCPM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=6000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-20 12:43:30 INFO Start process Ava<PERSON> !
2024-11-20 12:43:30 INFO Loading Avazu dataset
2024-11-20 12:43:30 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-20 12:43:33 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-20 12:43:33 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-20 12:43:34 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-20 12:43:34 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-20 12:43:34 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-20 12:43:34 INFO Loading data done
2024-11-20 12:43:36 INFO Model: CCPM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (conv_layer): CCPM_ConvLayer(
    (conv_layer): Sequential(
      (0): ZeroPad2d((0, 0, 6, 6))
      (1): Conv2d(1, 128, kernel_size=(7, 1), stride=(1, 1))
      (2): KMaxPooling()
      (3): Tanh()
      (4): ZeroPad2d((0, 0, 4, 4))
      (5): Conv2d(128, 256, kernel_size=(5, 1), stride=(1, 1))
      (6): KMaxPooling()
      (7): Tanh()
      (8): ZeroPad2d((0, 0, 2, 2))
      (9): Conv2d(256, 512, kernel_size=(3, 1), stride=(1, 1))
      (10): KMaxPooling()
      (11): Tanh()
    )
  )
  (fc): Linear(in_features=24576, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-20 12:43:36 INFO Model parameters: 134558065
2024-11-20 12:43:36 INFO Start training model
2024-11-20 12:43:36 INFO Start training: 5391 batches/epoch
2024-11-20 12:43:36 INFO ************ Epoch=1 start ************
2024-11-20 14:28:48 INFO [Metrics] AUC-ROC: 0.790837 - AUC-PR: 0.435031 - ACC: 0.838534 - Precision: 0.630913 - Recall: 0.118351 - F1: 0.199314 - MCC: 0.222763 - Logloss: 0.373691 - MSE: 0.117789 - RMSE: 0.343203 - COPC: 1.017978 - KLD: 1.382142
2024-11-20 14:28:48 INFO Save best model: monitor(max): 0.417146
2024-11-20 14:28:49 INFO --- 5391/5391 batches finished ---
2024-11-20 14:28:49 INFO Train loss: 0.384280
2024-11-20 14:28:49 INFO ************ Epoch=1 end ************
2024-11-20 16:13:47 INFO [Metrics] AUC-ROC: 0.780918 - AUC-PR: 0.415351 - ACC: 0.834186 - Precision: 0.531703 - Recall: 0.197136 - F1: 0.287629 - MCC: 0.249841 - Logloss: 0.387887 - MSE: 0.121648 - RMSE: 0.348780 - COPC: 1.049647 - KLD: 1.440220
2024-11-20 16:13:47 INFO Monitor(max) STOP: 0.393031 !
2024-11-20 16:13:47 INFO Reduce learning rate on plateau: 0.000100
2024-11-20 16:13:47 INFO --- 5391/5391 batches finished ---
2024-11-20 16:13:47 INFO Train loss: 0.316216
2024-11-20 16:13:47 INFO ************ Epoch=2 end ************
2024-11-20 17:58:33 INFO [Metrics] AUC-ROC: 0.769216 - AUC-PR: 0.397077 - ACC: 0.825892 - Precision: 0.475548 - Recall: 0.246384 - F1: 0.324594 - MCC: 0.252913 - Logloss: 0.418286 - MSE: 0.128682 - RMSE: 0.358722 - COPC: 0.974246 - KLD: 1.545967
2024-11-20 17:58:33 INFO Monitor(max) STOP: 0.350931 !
2024-11-20 17:58:33 INFO Reduce learning rate on plateau: 0.000010
2024-11-20 17:58:33 INFO Early stopping at epoch=3
2024-11-20 17:58:33 INFO --- 5391/5391 batches finished ---
2024-11-20 17:58:34 INFO Train loss: 0.267084
2024-11-20 17:58:34 INFO Training finished.
2024-11-20 17:58:34 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/CCPM/Avazu/CCPM_model_seed2020.ckpt
2024-11-20 17:58:34 INFO Start evaluate model
2024-11-20 18:04:47 INFO [Metrics] AUC-ROC: 0.790837 - AUC-PR: 0.435031 - ACC: 0.838534 - Precision: 0.630913 - Recall: 0.118351 - F1: 0.199314 - MCC: 0.222763 - Logloss: 0.373691 - MSE: 0.117789 - RMSE: 0.343203 - COPC: 1.017978 - KLD: 1.382142
2024-11-20 18:04:47 INFO Start testing model
2024-11-20 18:11:00 INFO [Metrics] AUC-ROC: 0.790946 - AUC-PR: 0.435105 - ACC: 0.838454 - Precision: 0.629582 - Recall: 0.118165 - F1: 0.198983 - MCC: 0.222182 - Logloss: 0.373654 - MSE: 0.117770 - RMSE: 0.343177 - COPC: 1.018660 - KLD: 1.381966
