2024-11-13 14:46:58 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='FiGNN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-13 14:46:58 INFO Start process <PERSON><PERSON> !
2024-11-13 14:46:58 INFO Loading Avazu dataset
2024-11-13 14:46:58 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-13 14:47:00 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-13 14:47:01 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-13 14:47:01 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-13 14:47:01 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-13 14:47:01 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-13 14:47:01 INFO Loading data done
2024-11-13 14:47:03 INFO Model: FiGNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fignn): FiGNN_Layer(
    (gnn): ModuleList(
      (0-8): 9 x GraphLayer()
    )
    (leaky_relu): LeakyReLU(negative_slope=0.01)
    (W_attn): Linear(in_features=32, out_features=1, bias=False)
  )
  (fc): PredictionLayer(
    (mlp1): Linear(in_features=16, out_features=1, bias=False)
    (mlp2): Sequential(
      (0): Linear(in_features=384, out_features=24, bias=False)
      (1): Sigmoid()
    )
  )
  (final_activation): Sigmoid()
)
2024-11-13 14:47:03 INFO Model parameters: 134094640
2024-11-13 14:47:03 INFO Start training model
2024-11-13 14:47:04 INFO Start training: 3235 batches/epoch
2024-11-13 14:47:04 INFO ************ Epoch=1 start ************
2024-11-13 14:51:37 INFO [Metrics] AUC-ROC: 0.789361 - AUC-PR: 0.433635 - ACC: 0.838644 - Precision: 0.606361 - Recall: 0.141846 - F1: 0.229909 - MCC: 0.236480 - Logloss: 0.374375 - MSE: 0.117943 - RMSE: 0.343428 - COPC: 0.988645 - KLD: 1.385707
2024-11-13 14:51:37 INFO Save best model: monitor(max): 0.414987
2024-11-13 14:51:38 INFO --- 3235/3235 batches finished ---
2024-11-13 14:51:38 INFO Train loss: 0.392263
2024-11-13 14:51:38 INFO ************ Epoch=1 end ************
2024-11-13 14:56:13 INFO [Metrics] AUC-ROC: 0.774602 - AUC-PR: 0.408560 - ACC: 0.835273 - Precision: 0.548495 - Recall: 0.169152 - F1: 0.258564 - MCC: 0.237096 - Logloss: 0.389376 - MSE: 0.121607 - RMSE: 0.348722 - COPC: 1.038344 - KLD: 1.449003
2024-11-13 14:56:13 INFO Monitor(max) STOP: 0.385226 !
2024-11-13 14:56:13 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 14:56:13 INFO --- 3235/3235 batches finished ---
2024-11-13 14:56:13 INFO Train loss: 0.352476
2024-11-13 14:56:13 INFO ************ Epoch=2 end ************
2024-11-13 15:00:48 INFO [Metrics] AUC-ROC: 0.761473 - AUC-PR: 0.391607 - ACC: 0.830869 - Precision: 0.505030 - Recall: 0.199549 - F1: 0.286067 - MCC: 0.239438 - Logloss: 0.427246 - MSE: 0.128097 - RMSE: 0.357907 - COPC: 1.124329 - KLD: 1.602183
2024-11-13 15:00:48 INFO Monitor(max) STOP: 0.334227 !
2024-11-13 15:00:48 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 15:00:48 INFO Early stopping at epoch=3
2024-11-13 15:00:48 INFO --- 3235/3235 batches finished ---
2024-11-13 15:00:48 INFO Train loss: 0.322264
2024-11-13 15:00:48 INFO Training finished.
2024-11-13 15:00:48 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FiGNN/Avazu/FiGNN_model_seed2020.ckpt
2024-11-13 15:00:49 INFO Start evaluate model
2024-11-13 15:01:19 INFO [Metrics] AUC-ROC: 0.789361 - AUC-PR: 0.433635 - ACC: 0.838644 - Precision: 0.606361 - Recall: 0.141846 - F1: 0.229909 - MCC: 0.236480 - Logloss: 0.374375 - MSE: 0.117943 - RMSE: 0.343428 - COPC: 0.988645 - KLD: 1.385707
2024-11-13 15:01:19 INFO Start testing model
2024-11-13 15:01:50 INFO [Metrics] AUC-ROC: 0.789613 - AUC-PR: 0.433935 - ACC: 0.838825 - Precision: 0.609026 - Recall: 0.141961 - F1: 0.230251 - MCC: 0.237481 - Logloss: 0.374248 - MSE: 0.117894 - RMSE: 0.343358 - COPC: 0.989495 - KLD: 1.385149
