2024-11-14 03:28:01 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='DeepFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classificationc', log_dir='../output/logs/')
2024-11-14 03:28:02 INFO Start process <PERSON><PERSON> !
2024-11-14 03:28:02 INFO Loading Avazu dataset
2024-11-14 03:28:02 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-14 03:28:04 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-14 03:28:04 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-14 03:28:05 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-14 03:28:05 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-14 03:28:05 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-14 03:28:05 INFO Loading data done
2024-11-14 03:28:33 INFO all args: Namespace(dataset_name='Avazu', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='DeepFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-14 03:28:33 INFO Start process Avazu !
2024-11-14 03:28:33 INFO Loading Avazu dataset
2024-11-14 03:28:33 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-14 03:28:36 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-14 03:28:36 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-14 03:28:37 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-14 03:28:37 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-14 03:28:37 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-14 03:28:37 INFO Loading data done
2024-11-14 03:28:39 INFO Model: DeepFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (C1): Embedding(8, 16)
        (banner_pos): Embedding(8, 16)
        (site_id): Embedding(4623, 16)
        (site_domain): Embedding(7307, 16)
        (site_category): Embedding(26, 16)
        (app_id): Embedding(8218, 16)
        (app_domain): Embedding(527, 16)
        (app_category): Embedding(36, 16)
        (device_id): Embedding(2329635, 16)
        (device_ip): Embedding(6011540, 16)
        (device_model): Embedding(8067, 16)
        (device_type): Embedding(6, 16)
        (device_conn_type): Embedding(5, 16)
        (C14): Embedding(2611, 16)
        (C15): Embedding(9, 16)
        (C16): Embedding(10, 16)
        (C17): Embedding(435, 16)
        (C18): Embedding(5, 16)
        (C19): Embedding(69, 16)
        (C20): Embedding(173, 16)
        (C21): Embedding(61, 16)
        (hour): Embedding(25, 16)
        (weekday): Embedding(8, 16)
        (weekend): Embedding(3, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (fm_layer): FM_Layer_v2(
    (inner_product_layer): InnerProductLayer_v2()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (C1): Embedding(8, 1, padding_idx=7)
          (banner_pos): Embedding(8, 1, padding_idx=7)
          (site_id): Embedding(4623, 1, padding_idx=4622)
          (site_domain): Embedding(7307, 1, padding_idx=7306)
          (site_category): Embedding(26, 1, padding_idx=25)
          (app_id): Embedding(8218, 1, padding_idx=8217)
          (app_domain): Embedding(527, 1, padding_idx=526)
          (app_category): Embedding(36, 1, padding_idx=35)
          (device_id): Embedding(2329635, 1, padding_idx=2329634)
          (device_ip): Embedding(6011540, 1, padding_idx=6011539)
          (device_model): Embedding(8067, 1, padding_idx=8066)
          (device_type): Embedding(6, 1, padding_idx=5)
          (device_conn_type): Embedding(5, 1, padding_idx=4)
          (C14): Embedding(2611, 1, padding_idx=2610)
          (C15): Embedding(9, 1, padding_idx=8)
          (C16): Embedding(10, 1, padding_idx=9)
          (C17): Embedding(435, 1, padding_idx=434)
          (C18): Embedding(5, 1, padding_idx=4)
          (C19): Embedding(69, 1, padding_idx=68)
          (C20): Embedding(173, 1, padding_idx=172)
          (C21): Embedding(61, 1, padding_idx=60)
          (hour): Embedding(25, 1, padding_idx=24)
          (weekday): Embedding(8, 1, padding_idx=7)
          (weekend): Embedding(3, 1, padding_idx=2)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=384, out_features=2000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=2000, out_features=2000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=2000, out_features=2000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=2000, out_features=2000, bias=True)
      (7): ReLU()
      (8): Linear(in_features=2000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-14 03:28:39 INFO Model parameters: 155126056
2024-11-14 03:28:39 INFO Start training model
2024-11-14 03:28:39 INFO Start training: 3235 batches/epoch
2024-11-14 03:28:39 INFO ************ Epoch=1 start ************
2024-11-14 03:33:29 INFO [Metrics] AUC-ROC: 0.794713 - AUC-PR: 0.442376 - ACC: 0.839546 - Precision: 0.613790 - Recall: 0.148526 - F1: 0.239176 - MCC: 0.244782 - Logloss: 0.370966 - MSE: 0.116934 - RMSE: 0.341957 - COPC: 0.998308 - KLD: 1.370275
2024-11-14 03:33:29 INFO Save best model: monitor(max): 0.423747
2024-11-14 03:33:30 INFO --- 3235/3235 batches finished ---
2024-11-14 03:33:30 INFO Train loss: 0.380137
2024-11-14 03:33:30 INFO ************ Epoch=1 end ************
2024-11-14 03:38:19 INFO [Metrics] AUC-ROC: 0.771789 - AUC-PR: 0.374555 - ACC: 0.820099 - Precision: 0.445700 - Recall: 0.244006 - F1: 0.315362 - MCC: 0.235245 - Logloss: 0.419225 - MSE: 0.130836 - RMSE: 0.361712 - COPC: 0.892492 - KLD: 1.489965
2024-11-14 03:38:19 INFO Monitor(max) STOP: 0.352564 !
2024-11-14 03:38:19 INFO Reduce learning rate on plateau: 0.000100
2024-11-14 03:38:19 INFO --- 3235/3235 batches finished ---
2024-11-14 03:38:19 INFO Train loss: 0.290389
2024-11-14 03:38:19 INFO ************ Epoch=2 end ************
2024-11-14 03:43:15 INFO [Metrics] AUC-ROC: 0.746943 - AUC-PR: 0.336100 - ACC: 0.782462 - Precision: 0.358683 - Recall: 0.356732 - F1: 0.357705 - MCC: 0.226764 - Logloss: 0.575797 - MSE: 0.164392 - RMSE: 0.405453 - COPC: 0.737173 - KLD: 1.822783
2024-11-14 03:43:15 INFO Monitor(max) STOP: 0.171146 !
2024-11-14 03:43:15 INFO Reduce learning rate on plateau: 0.000010
2024-11-14 03:43:15 INFO Early stopping at epoch=3
2024-11-14 03:43:15 INFO --- 3235/3235 batches finished ---
2024-11-14 03:43:15 INFO Train loss: 0.254033
2024-11-14 03:43:15 INFO Training finished.
2024-11-14 03:43:15 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/DeepFM/Avazu/DeepFM_model_seed2020.ckpt
2024-11-14 03:43:16 INFO Start evaluate model
2024-11-14 03:43:56 INFO [Metrics] AUC-ROC: 0.794713 - AUC-PR: 0.442376 - ACC: 0.839546 - Precision: 0.613790 - Recall: 0.148526 - F1: 0.239176 - MCC: 0.244782 - Logloss: 0.370966 - MSE: 0.116934 - RMSE: 0.341957 - COPC: 0.998308 - KLD: 1.370275
2024-11-14 03:43:56 INFO Start testing model
2024-11-14 03:44:36 INFO [Metrics] AUC-ROC: 0.794967 - AUC-PR: 0.442523 - ACC: 0.839575 - Precision: 0.614401 - Recall: 0.148341 - F1: 0.238982 - MCC: 0.244833 - Logloss: 0.370836 - MSE: 0.116901 - RMSE: 0.341907 - COPC: 0.998988 - KLD: 1.369624
