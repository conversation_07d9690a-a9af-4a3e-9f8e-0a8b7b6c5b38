2024-11-18 01:29:18 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='LSTM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-18 01:29:18 INFO Start process Ava<PERSON> !
2024-11-18 01:29:18 INFO Loading Avazu dataset
2024-11-18 01:29:18 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-18 01:29:21 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-18 01:29:22 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-18 01:29:23 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-18 01:29:23 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-18 01:29:23 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-18 01:29:23 INFO Loading data done
2024-11-18 01:29:24 INFO Model: LSTM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (lstm): LSTM(16, 128, num_layers=2, batch_first=True, dropout=0.2)
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=496, out_features=64, bias=True)
      (1): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Dropout(p=0.2, inplace=False)
      (4): Linear(in_features=64, out_features=32, bias=True)
      (5): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (6): ReLU()
      (7): Dropout(p=0.2, inplace=False)
      (8): Linear(in_features=32, out_features=1, bias=True)
      (9): Sigmoid()
    )
  )
)
2024-11-18 01:29:24 INFO Model parameters: 134215601
2024-11-18 01:29:24 INFO Start training model
2024-11-18 01:29:24 INFO Start training: 3235 batches/epoch
2024-11-18 01:29:24 INFO ************ Epoch=1 start ************
2024-11-18 01:33:20 INFO [Metrics] AUC-ROC: 0.757402 - AUC-PR: 0.386585 - ACC: 0.835244 - Precision: 0.601566 - Recall: 0.088066 - F1: 0.153640 - MCC: 0.183604 - Logloss: 0.392312 - MSE: 0.123201 - RMSE: 0.351000 - COPC: 0.986952 - KLD: 1.469038
2024-11-18 01:33:20 INFO Save best model: monitor(max): 0.365090
2024-11-18 01:33:21 INFO --- 3235/3235 batches finished ---
2024-11-18 01:33:21 INFO Train loss: 0.406015
2024-11-18 01:33:21 INFO ************ Epoch=1 end ************
2024-11-18 01:37:19 INFO [Metrics] AUC-ROC: 0.763971 - AUC-PR: 0.394773 - ACC: 0.835674 - Precision: 0.614034 - Recall: 0.086883 - F1: 0.152227 - MCC: 0.185639 - Logloss: 0.388694 - MSE: 0.122261 - RMSE: 0.349659 - COPC: 0.997061 - KLD: 1.451767
2024-11-18 01:37:19 INFO Save best model: monitor(max): 0.375278
2024-11-18 01:37:20 INFO --- 3235/3235 batches finished ---
2024-11-18 01:37:20 INFO Train loss: 0.391962
2024-11-18 01:37:20 INFO ************ Epoch=2 end ************
2024-11-18 01:41:21 INFO [Metrics] AUC-ROC: 0.768663 - AUC-PR: 0.400655 - ACC: 0.836005 - Precision: 0.615078 - Recall: 0.091450 - F1: 0.159226 - MCC: 0.190860 - Logloss: 0.386145 - MSE: 0.121568 - RMSE: 0.348666 - COPC: 1.010862 - KLD: 1.439605
2024-11-18 01:41:21 INFO Save best model: monitor(max): 0.382518
2024-11-18 01:41:21 INFO --- 3235/3235 batches finished ---
2024-11-18 01:41:22 INFO Train loss: 0.385185
2024-11-18 01:41:22 INFO ************ Epoch=3 end ************
2024-11-18 01:45:23 INFO [Metrics] AUC-ROC: 0.771442 - AUC-PR: 0.404227 - ACC: 0.836032 - Precision: 0.603840 - Recall: 0.099965 - F1: 0.171534 - MCC: 0.196603 - Logloss: 0.385229 - MSE: 0.121181 - RMSE: 0.348110 - COPC: 1.016436 - KLD: 1.435716
2024-11-18 01:45:23 INFO Save best model: monitor(max): 0.386213
2024-11-18 01:45:24 INFO --- 3235/3235 batches finished ---
2024-11-18 01:45:24 INFO Train loss: 0.376648
2024-11-18 01:45:24 INFO ************ Epoch=4 end ************
2024-11-18 01:49:25 INFO [Metrics] AUC-ROC: 0.772995 - AUC-PR: 0.405214 - ACC: 0.835847 - Precision: 0.585768 - Recall: 0.113686 - F1: 0.190416 - MCC: 0.204518 - Logloss: 0.385711 - MSE: 0.121094 - RMSE: 0.347986 - COPC: 1.031528 - KLD: 1.438212
2024-11-18 01:49:25 INFO Save best model: monitor(max): 0.387284
2024-11-18 01:49:26 INFO --- 3235/3235 batches finished ---
2024-11-18 01:49:26 INFO Train loss: 0.366951
2024-11-18 01:49:26 INFO ************ Epoch=5 end ************
2024-11-18 01:53:08 INFO [Metrics] AUC-ROC: 0.773807 - AUC-PR: 0.404254 - ACC: 0.834830 - Precision: 0.556605 - Recall: 0.134232 - F1: 0.216300 - MCC: 0.212877 - Logloss: 0.387155 - MSE: 0.121321 - RMSE: 0.348312 - COPC: 1.016442 - KLD: 1.444399
2024-11-18 01:53:08 INFO Monitor(max) STOP: 0.386652 !
2024-11-18 01:53:08 INFO Reduce learning rate on plateau: 0.000100
2024-11-18 01:53:08 INFO --- 3235/3235 batches finished ---
2024-11-18 01:53:08 INFO Train loss: 0.356939
2024-11-18 01:53:08 INFO ************ Epoch=6 end ************
2024-11-18 01:56:50 INFO [Metrics] AUC-ROC: 0.771431 - AUC-PR: 0.399094 - ACC: 0.833370 - Precision: 0.534308 - Recall: 0.145643 - F1: 0.228894 - MCC: 0.213870 - Logloss: 0.391701 - MSE: 0.122195 - RMSE: 0.349565 - COPC: 1.011545 - KLD: 1.465548
2024-11-18 01:56:50 INFO Monitor(max) STOP: 0.379730 !
2024-11-18 01:56:50 INFO Reduce learning rate on plateau: 0.000010
2024-11-18 01:56:50 INFO Early stopping at epoch=7
2024-11-18 01:56:50 INFO --- 3235/3235 batches finished ---
2024-11-18 01:56:50 INFO Train loss: 0.346385
2024-11-18 01:56:50 INFO Training finished.
2024-11-18 01:56:50 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/LSTM/Avazu/LSTM_model_seed2019.ckpt
2024-11-18 01:56:51 INFO Start evaluate model
2024-11-18 01:57:24 INFO [Metrics] AUC-ROC: 0.772995 - AUC-PR: 0.405214 - ACC: 0.835847 - Precision: 0.585768 - Recall: 0.113686 - F1: 0.190416 - MCC: 0.204518 - Logloss: 0.385711 - MSE: 0.121094 - RMSE: 0.347986 - COPC: 1.031528 - KLD: 1.438212
2024-11-18 01:57:24 INFO Start testing model
2024-11-18 01:57:57 INFO [Metrics] AUC-ROC: 0.773079 - AUC-PR: 0.405369 - ACC: 0.836019 - Precision: 0.588544 - Recall: 0.114003 - F1: 0.191008 - MCC: 0.205676 - Logloss: 0.385647 - MSE: 0.121081 - RMSE: 0.347967 - COPC: 1.032650 - KLD: 1.437878
