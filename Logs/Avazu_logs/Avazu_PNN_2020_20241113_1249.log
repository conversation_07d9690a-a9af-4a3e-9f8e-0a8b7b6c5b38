2024-11-13 12:49:45 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='PNN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-13 12:49:45 INFO Start process <PERSON><PERSON> !
2024-11-13 12:49:45 INFO Loading Avazu dataset
2024-11-13 12:49:45 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-13 12:49:48 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-13 12:49:48 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-13 12:49:49 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-13 12:49:49 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-13 12:49:49 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-13 12:49:49 INFO Loading data done
2024-11-13 12:49:51 INFO Model: PNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=660, out_features=1000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=1000, out_features=1000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=1000, out_features=1000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=1000, out_features=1, bias=True)
      (7): Sigmoid()
    )
  )
)
2024-11-13 12:49:51 INFO Model parameters: 136638641
2024-11-13 12:49:51 INFO Start training model
2024-11-13 12:49:51 INFO Start training: 3235 batches/epoch
2024-11-13 12:49:51 INFO ************ Epoch=1 start ************
2024-11-13 12:55:33 INFO [Metrics] AUC-ROC: 0.795907 - AUC-PR: 0.444038 - ACC: 0.839713 - Precision: 0.612227 - Recall: 0.152900 - F1: 0.244690 - MCC: 0.247973 - Logloss: 0.370377 - MSE: 0.116740 - RMSE: 0.341672 - COPC: 1.021786 - KLD: 1.367443
2024-11-13 12:55:33 INFO Save best model: monitor(max): 0.425529
2024-11-13 12:55:34 INFO --- 3235/3235 batches finished ---
2024-11-13 12:55:34 INFO Train loss: 0.380201
2024-11-13 12:55:34 INFO ************ Epoch=1 end ************
2024-11-13 13:00:43 INFO [Metrics] AUC-ROC: 0.773710 - AUC-PR: 0.398692 - ACC: 0.830234 - Precision: 0.500294 - Recall: 0.196875 - F1: 0.282558 - MCC: 0.235541 - Logloss: 0.399237 - MSE: 0.123963 - RMSE: 0.352084 - COPC: 1.019075 - KLD: 1.487231
2024-11-13 13:00:43 INFO Monitor(max) STOP: 0.374472 !
2024-11-13 13:00:43 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 13:00:43 INFO --- 3235/3235 batches finished ---
2024-11-13 13:00:43 INFO Train loss: 0.290331
2024-11-13 13:00:43 INFO ************ Epoch=2 end ************
2024-11-13 13:05:00 INFO [Metrics] AUC-ROC: 0.752273 - AUC-PR: 0.353327 - ACC: 0.787858 - Precision: 0.371778 - Recall: 0.361452 - F1: 0.366542 - MCC: 0.239202 - Logloss: 0.537240 - MSE: 0.157575 - RMSE: 0.396957 - COPC: 0.760855 - KLD: 1.784208
2024-11-13 13:05:00 INFO Monitor(max) STOP: 0.215033 !
2024-11-13 13:05:00 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 13:05:00 INFO Early stopping at epoch=3
2024-11-13 13:05:00 INFO --- 3235/3235 batches finished ---
2024-11-13 13:05:00 INFO Train loss: 0.250544
2024-11-13 13:05:00 INFO Training finished.
2024-11-13 13:05:00 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/PNN/Avazu/PNN_model_seed2020.ckpt
2024-11-13 13:05:00 INFO Start evaluate model
2024-11-13 13:05:31 INFO [Metrics] AUC-ROC: 0.795907 - AUC-PR: 0.444038 - ACC: 0.839713 - Precision: 0.612227 - Recall: 0.152900 - F1: 0.244690 - MCC: 0.247973 - Logloss: 0.370377 - MSE: 0.116740 - RMSE: 0.341672 - COPC: 1.021786 - KLD: 1.367443
2024-11-13 13:05:31 INFO Start testing model
2024-11-13 13:06:02 INFO [Metrics] AUC-ROC: 0.796166 - AUC-PR: 0.444266 - ACC: 0.839867 - Precision: 0.613800 - Recall: 0.153618 - F1: 0.245735 - MCC: 0.249130 - Logloss: 0.370214 - MSE: 0.116692 - RMSE: 0.341602 - COPC: 1.022554 - KLD: 1.366611
