2024-11-13 12:05:16 INFO all args: Namespace(dataset_name='<PERSON><PERSON>', dataset_path='/data/ctr/Avazu/Avazu_x4', model_name='NFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=30000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-13 12:05:16 INFO Start process Ava<PERSON> !
2024-11-13 12:05:16 INFO Loading Avazu dataset
2024-11-13 12:05:16 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/train.h5
2024-11-13 12:05:18 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/valid.h5
2024-11-13 12:05:19 INFO Load h5 data from /data/ctr/Avazu/Avazu_x4/test.h5
2024-11-13 12:05:19 INFO Train samples: total/32343172, pos/5492052, neg/26851120, ratio/16.98%
2024-11-13 12:05:20 INFO Validation samples: total/4042897, pos/686507, neg/3356390, ratio/16.98%
2024-11-13 12:05:20 INFO Test samples: total/4042898, pos/686507, neg/3356391, ratio/16.98%
2024-11-13 12:05:20 INFO Loading data done
2024-11-13 12:05:22 INFO Model: NFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (C1): Embedding(8, 16, padding_idx=7)
      (banner_pos): Embedding(8, 16, padding_idx=7)
      (site_id): Embedding(4623, 16, padding_idx=4622)
      (site_domain): Embedding(7307, 16, padding_idx=7306)
      (site_category): Embedding(26, 16, padding_idx=25)
      (app_id): Embedding(8218, 16, padding_idx=8217)
      (app_domain): Embedding(527, 16, padding_idx=526)
      (app_category): Embedding(36, 16, padding_idx=35)
      (device_id): Embedding(2329635, 16, padding_idx=2329634)
      (device_ip): Embedding(6011540, 16, padding_idx=6011539)
      (device_model): Embedding(8067, 16, padding_idx=8066)
      (device_type): Embedding(6, 16, padding_idx=5)
      (device_conn_type): Embedding(5, 16, padding_idx=4)
      (C14): Embedding(2611, 16, padding_idx=2610)
      (C15): Embedding(9, 16, padding_idx=8)
      (C16): Embedding(10, 16, padding_idx=9)
      (C17): Embedding(435, 16, padding_idx=434)
      (C18): Embedding(5, 16, padding_idx=4)
      (C19): Embedding(69, 16, padding_idx=68)
      (C20): Embedding(173, 16, padding_idx=172)
      (C21): Embedding(61, 16, padding_idx=60)
      (hour): Embedding(25, 16, padding_idx=24)
      (weekday): Embedding(8, 16, padding_idx=7)
      (weekend): Embedding(3, 16, padding_idx=2)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (C1): Embedding(8, 1, padding_idx=7)
        (banner_pos): Embedding(8, 1, padding_idx=7)
        (site_id): Embedding(4623, 1, padding_idx=4622)
        (site_domain): Embedding(7307, 1, padding_idx=7306)
        (site_category): Embedding(26, 1, padding_idx=25)
        (app_id): Embedding(8218, 1, padding_idx=8217)
        (app_domain): Embedding(527, 1, padding_idx=526)
        (app_category): Embedding(36, 1, padding_idx=35)
        (device_id): Embedding(2329635, 1, padding_idx=2329634)
        (device_ip): Embedding(6011540, 1, padding_idx=6011539)
        (device_model): Embedding(8067, 1, padding_idx=8066)
        (device_type): Embedding(6, 1, padding_idx=5)
        (device_conn_type): Embedding(5, 1, padding_idx=4)
        (C14): Embedding(2611, 1, padding_idx=2610)
        (C15): Embedding(9, 1, padding_idx=8)
        (C16): Embedding(10, 1, padding_idx=9)
        (C17): Embedding(435, 1, padding_idx=434)
        (C18): Embedding(5, 1, padding_idx=4)
        (C19): Embedding(69, 1, padding_idx=68)
        (C20): Embedding(173, 1, padding_idx=172)
        (C21): Embedding(61, 1, padding_idx=60)
        (hour): Embedding(25, 1, padding_idx=24)
        (weekday): Embedding(8, 1, padding_idx=7)
        (weekend): Embedding(3, 1, padding_idx=2)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=16, out_features=1000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=1000, out_features=1000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=1000, out_features=1000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=1000, out_features=1000, bias=True)
      (7): ReLU()
      (8): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-13 12:05:22 INFO Model parameters: 145369056
2024-11-13 12:05:22 INFO Start training model
2024-11-13 12:05:22 INFO Start training: 1079 batches/epoch
2024-11-13 12:05:22 INFO ************ Epoch=1 start ************
2024-11-13 12:08:45 INFO [Metrics] AUC-ROC: 0.788132 - AUC-PR: 0.432031 - ACC: 0.838489 - Precision: 0.630134 - Recall: 0.118264 - F1: 0.199151 - MCC: 0.222444 - Logloss: 0.375155 - MSE: 0.118225 - RMSE: 0.343839 - COPC: 1.040598 - KLD: 1.388576
2024-11-13 12:08:45 INFO Save best model: monitor(max): 0.412977
2024-11-13 12:08:46 INFO --- 1079/1079 batches finished ---
2024-11-13 12:08:46 INFO Train loss: 0.386689
2024-11-13 12:08:46 INFO ************ Epoch=1 end ************
2024-11-13 12:12:09 INFO [Metrics] AUC-ROC: 0.774493 - AUC-PR: 0.388219 - ACC: 0.819529 - Precision: 0.448630 - Recall: 0.274258 - F1: 0.340413 - MCC: 0.252741 - Logloss: 0.407611 - MSE: 0.129032 - RMSE: 0.359211 - COPC: 0.864262 - KLD: 1.474063
2024-11-13 12:12:09 INFO Monitor(max) STOP: 0.366882 !
2024-11-13 12:12:09 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 12:12:09 INFO --- 1079/1079 batches finished ---
2024-11-13 12:12:10 INFO Train loss: 0.320470
2024-11-13 12:12:10 INFO ************ Epoch=2 end ************
2024-11-13 12:15:34 INFO [Metrics] AUC-ROC: 0.761346 - AUC-PR: 0.388141 - ACC: 0.816697 - Precision: 0.440143 - Recall: 0.292240 - F1: 0.351257 - MCC: 0.256664 - Logloss: 0.462932 - MSE: 0.134523 - RMSE: 0.366774 - COPC: 0.881388 - KLD: 1.725445
2024-11-13 12:15:34 INFO Monitor(max) STOP: 0.298414 !
2024-11-13 12:15:34 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 12:15:34 INFO Early stopping at epoch=3
2024-11-13 12:15:34 INFO --- 1079/1079 batches finished ---
2024-11-13 12:15:34 INFO Train loss: 0.260474
2024-11-13 12:15:34 INFO Training finished.
2024-11-13 12:15:34 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/NFM/Avazu/NFM_model_seed2019.ckpt
2024-11-13 12:15:34 INFO Start evaluate model
2024-11-13 12:16:05 INFO [Metrics] AUC-ROC: 0.788132 - AUC-PR: 0.432031 - ACC: 0.838489 - Precision: 0.630134 - Recall: 0.118264 - F1: 0.199151 - MCC: 0.222444 - Logloss: 0.375155 - MSE: 0.118225 - RMSE: 0.343839 - COPC: 1.040598 - KLD: 1.388576
2024-11-13 12:16:06 INFO Start testing model
2024-11-13 12:16:37 INFO [Metrics] AUC-ROC: 0.788311 - AUC-PR: 0.431849 - ACC: 0.838459 - Precision: 0.629980 - Recall: 0.117955 - F1: 0.198705 - MCC: 0.222097 - Logloss: 0.375094 - MSE: 0.118217 - RMSE: 0.343827 - COPC: 1.041241 - KLD: 1.388157
