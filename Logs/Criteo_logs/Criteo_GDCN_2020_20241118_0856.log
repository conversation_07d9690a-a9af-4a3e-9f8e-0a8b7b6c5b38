2024-11-18 08:56:33 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='GDCN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=3000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-18 08:56:33 INFO Start process Criteo !
2024-11-18 08:56:33 INFO Loading Criteo dataset
2024-11-18 08:56:33 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-18 08:56:37 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-18 08:56:38 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-18 08:56:39 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-18 08:56:39 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-18 08:56:39 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-18 08:56:39 INFO Loading data done
2024-11-18 08:56:40 INFO Model: GDCN(
  (embedding): FeaturesEmbedding(
    (embed_dict): ModuleDict(
      (I1): Embedding(43, 16)
      (I2): Embedding(98, 16)
      (I3): Embedding(121, 16)
      (I4): Embedding(40, 16)
      (I5): Embedding(219, 16)
      (I6): Embedding(111, 16)
      (I7): Embedding(79, 16)
      (I8): Embedding(68, 16)
      (I9): Embedding(91, 16)
      (I10): Embedding(5, 16)
      (I11): Embedding(26, 16)
      (I12): Embedding(36, 16)
      (I13): Embedding(71, 16)
      (C1): Embedding(1445, 16)
      (C2): Embedding(553, 16)
      (C3): Embedding(157338, 16)
      (C4): Embedding(117821, 16)
      (C5): Embedding(305, 16)
      (C6): Embedding(17, 16)
      (C7): Embedding(11881, 16)
      (C8): Embedding(629, 16)
      (C9): Embedding(4, 16)
      (C10): Embedding(39529, 16)
      (C11): Embedding(5130, 16)
      (C12): Embedding(156655, 16)
      (C13): Embedding(3175, 16)
      (C14): Embedding(27, 16)
      (C15): Embedding(11042, 16)
      (C16): Embedding(148912, 16)
      (C17): Embedding(11, 16)
      (C18): Embedding(4559, 16)
      (C19): Embedding(2002, 16)
      (C20): Embedding(4, 16)
      (C21): Embedding(154563, 16)
      (C22): Embedding(17, 16)
      (C23): Embedding(16, 16)
      (C24): Embedding(53030, 16)
      (C25): Embedding(81, 16)
      (C26): Embedding(40954, 16)
    )
  )
  (embedding_dropout): Dropout(p=0.0, inplace=False)
  (cross_net): GateCorssLayer(
    (w): ModuleList(
      (0-2): 3 x Linear(in_features=624, out_features=624, bias=False)
    )
    (wg): ModuleList(
      (0-2): 3 x Linear(in_features=624, out_features=624, bias=False)
    )
    (b): ParameterList(
        (0): Parameter containing: [torch.float32 of size 624]
        (1): Parameter containing: [torch.float32 of size 624]
        (2): Parameter containing: [torch.float32 of size 624]
    )
    (activation): Sigmoid()
  )
  (mlp): Sequential(
    (0): Linear(in_features=624, out_features=400, bias=True)
    (1): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): ReLU()
    (3): Dropout(p=0.5, inplace=False)
    (4): Linear(in_features=400, out_features=400, bias=True)
    (5): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (6): ReLU()
    (7): Dropout(p=0.5, inplace=False)
    (8): Linear(in_features=400, out_features=400, bias=True)
    (9): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (10): ReLU()
    (11): Dropout(p=0.5, inplace=False)
  )
  (fc): Linear(in_features=1024, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-18 08:56:40 INFO Model parameters: 17483681
2024-11-18 08:56:40 INFO Start training model
2024-11-18 08:56:40 INFO Start training: 12225 batches/epoch
2024-11-18 08:56:40 INFO ************ Epoch=1 start ************
2024-11-18 09:02:34 INFO [Metrics] AUC-ROC: 0.802145 - AUC-PR: 0.602311 - ACC: 0.791066 - Precision: 0.669249 - Recall: 0.364903 - F1: 0.472292 - MCC: 0.381265 - Logloss: 0.448775 - MSE: 0.145406 - RMSE: 0.381322 - COPC: 1.006748 - KLD: 1.033188
2024-11-18 09:02:34 INFO Save best model: monitor(max): 0.353370
2024-11-18 09:02:34 INFO --- 12225/12225 batches finished ---
2024-11-18 09:02:34 INFO Train loss: 0.456416
2024-11-18 09:02:34 INFO ************ Epoch=1 end ************
2024-11-18 09:08:26 INFO [Metrics] AUC-ROC: 0.805611 - AUC-PR: 0.607724 - ACC: 0.792638 - Precision: 0.661341 - Recall: 0.390839 - F1: 0.491319 - MCC: 0.392013 - Logloss: 0.445671 - MSE: 0.144304 - RMSE: 0.379874 - COPC: 0.995097 - KLD: 1.024926
2024-11-18 09:08:26 INFO Save best model: monitor(max): 0.359940
2024-11-18 09:08:26 INFO --- 12225/12225 batches finished ---
2024-11-18 09:08:26 INFO Train loss: 0.442213
2024-11-18 09:08:26 INFO ************ Epoch=2 end ************
2024-11-18 09:14:19 INFO [Metrics] AUC-ROC: 0.805211 - AUC-PR: 0.606899 - ACC: 0.792526 - Precision: 0.655046 - Recall: 0.401914 - F1: 0.498169 - MCC: 0.394575 - Logloss: 0.446484 - MSE: 0.144468 - RMSE: 0.380089 - COPC: 0.998976 - KLD: 1.027150
2024-11-18 09:14:19 INFO Monitor(max) STOP: 0.358727 !
2024-11-18 09:14:19 INFO Reduce learning rate on plateau: 0.000100
2024-11-18 09:14:19 INFO --- 12225/12225 batches finished ---
2024-11-18 09:14:19 INFO Train loss: 0.434506
2024-11-18 09:14:19 INFO ************ Epoch=3 end ************
2024-11-18 09:20:29 INFO [Metrics] AUC-ROC: 0.802575 - AUC-PR: 0.601925 - ACC: 0.790831 - Precision: 0.641568 - Recall: 0.416133 - F1: 0.504826 - MCC: 0.394085 - Logloss: 0.452008 - MSE: 0.145876 - RMSE: 0.381937 - COPC: 1.008093 - KLD: 1.041224
2024-11-18 09:20:29 INFO Monitor(max) STOP: 0.350566 !
2024-11-18 09:20:29 INFO Reduce learning rate on plateau: 0.000010
2024-11-18 09:20:29 INFO Early stopping at epoch=4
2024-11-18 09:20:29 INFO --- 12225/12225 batches finished ---
2024-11-18 09:20:29 INFO Train loss: 0.417314
2024-11-18 09:20:29 INFO Training finished.
2024-11-18 09:20:29 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/GDCN/Criteo/GDCN_model_seed2020.ckpt
2024-11-18 09:20:29 INFO Start evaluate model
2024-11-18 09:21:09 INFO [Metrics] AUC-ROC: 0.805611 - AUC-PR: 0.607724 - ACC: 0.792638 - Precision: 0.661341 - Recall: 0.390839 - F1: 0.491319 - MCC: 0.392013 - Logloss: 0.445671 - MSE: 0.144304 - RMSE: 0.379874 - COPC: 0.995097 - KLD: 1.024926
2024-11-18 09:21:09 INFO Start testing model
2024-11-18 09:21:48 INFO [Metrics] AUC-ROC: 0.805974 - AUC-PR: 0.608155 - ACC: 0.792708 - Precision: 0.661289 - Recall: 0.391497 - F1: 0.491824 - MCC: 0.392370 - Logloss: 0.445328 - MSE: 0.144208 - RMSE: 0.379747 - COPC: 0.994669 - KLD: 1.023860
