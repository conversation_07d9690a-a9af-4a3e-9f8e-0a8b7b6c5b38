2024-11-12 07:12:13 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='AFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=30000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-12 07:12:13 INFO Start process Criteo !
2024-11-12 07:12:13 INFO Loading Criteo dataset
2024-11-12 07:12:13 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-12 07:12:18 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-12 07:12:19 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-12 07:12:20 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-12 07:12:20 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 07:12:20 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 07:12:20 INFO Loading data done
2024-11-12 07:12:21 INFO Model: AFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (elementwise_product_layer): InnerProductLayer()
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 1, padding_idx=42)
        (I2): Embedding(98, 1, padding_idx=97)
        (I3): Embedding(121, 1, padding_idx=120)
        (I4): Embedding(40, 1, padding_idx=39)
        (I5): Embedding(219, 1, padding_idx=218)
        (I6): Embedding(111, 1, padding_idx=110)
        (I7): Embedding(79, 1, padding_idx=78)
        (I8): Embedding(68, 1, padding_idx=67)
        (I9): Embedding(91, 1, padding_idx=90)
        (I10): Embedding(5, 1, padding_idx=4)
        (I11): Embedding(26, 1, padding_idx=25)
        (I12): Embedding(36, 1, padding_idx=35)
        (I13): Embedding(71, 1, padding_idx=70)
        (C1): Embedding(1445, 1, padding_idx=1444)
        (C2): Embedding(553, 1, padding_idx=552)
        (C3): Embedding(157338, 1, padding_idx=157337)
        (C4): Embedding(117821, 1, padding_idx=117820)
        (C5): Embedding(305, 1, padding_idx=304)
        (C6): Embedding(17, 1, padding_idx=16)
        (C7): Embedding(11881, 1, padding_idx=11880)
        (C8): Embedding(629, 1, padding_idx=628)
        (C9): Embedding(4, 1, padding_idx=3)
        (C10): Embedding(39529, 1, padding_idx=39528)
        (C11): Embedding(5130, 1, padding_idx=5129)
        (C12): Embedding(156655, 1, padding_idx=156654)
        (C13): Embedding(3175, 1, padding_idx=3174)
        (C14): Embedding(27, 1, padding_idx=26)
        (C15): Embedding(11042, 1, padding_idx=11041)
        (C16): Embedding(148912, 1, padding_idx=148911)
        (C17): Embedding(11, 1, padding_idx=10)
        (C18): Embedding(4559, 1, padding_idx=4558)
        (C19): Embedding(2002, 1, padding_idx=2001)
        (C20): Embedding(4, 1, padding_idx=3)
        (C21): Embedding(154563, 1, padding_idx=154562)
        (C22): Embedding(17, 1, padding_idx=16)
        (C23): Embedding(16, 1, padding_idx=15)
        (C24): Embedding(53030, 1, padding_idx=53029)
        (C25): Embedding(81, 1, padding_idx=80)
        (C26): Embedding(40954, 1, padding_idx=40953)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (attention): Sequential(
    (0): Linear(in_features=16, out_features=16, bias=True)
    (1): ReLU()
    (2): Linear(in_features=16, out_features=1, bias=False)
    (3): Softmax(dim=1)
  )
  (weight_p): Linear(in_features=16, out_features=1, bias=False)
  (dropout1): Dropout(p=0, inplace=False)
  (dropout2): Dropout(p=0, inplace=False)
  (final_activation): Sigmoid()
)
2024-11-12 07:12:21 INFO Model parameters: 15482341
2024-11-12 07:12:21 INFO Start training model
2024-11-12 07:12:21 INFO Start training: 1223 batches/epoch
2024-11-12 07:12:21 INFO ************ Epoch=1 start ************
2024-11-12 07:16:23 INFO [Metrics] AUC-ROC: 0.791418 - AUC-PR: 0.584406 - ACC: 0.786319 - Precision: 0.660823 - Recall: 0.341126 - F1: 0.449970 - MCC: 0.361847 - Logloss: 0.458167 - MSE: 0.148709 - RMSE: 0.385629 - COPC: 1.013472 - KLD: 1.057884
2024-11-12 07:16:23 INFO Save best model: monitor(max): 0.333251
2024-11-12 07:16:23 INFO --- 1223/1223 batches finished ---
2024-11-12 07:16:23 INFO Train loss: 0.474019
2024-11-12 07:16:23 INFO ************ Epoch=1 end ************
2024-11-12 07:20:25 INFO [Metrics] AUC-ROC: 0.794061 - AUC-PR: 0.588558 - ACC: 0.787349 - Precision: 0.659227 - Recall: 0.352033 - F1: 0.458971 - MCC: 0.367547 - Logloss: 0.455875 - MSE: 0.147909 - RMSE: 0.384589 - COPC: 1.006453 - KLD: 1.051861
2024-11-12 07:20:25 INFO Save best model: monitor(max): 0.338186
2024-11-12 07:20:25 INFO --- 1223/1223 batches finished ---
2024-11-12 07:20:25 INFO Train loss: 0.458071
2024-11-12 07:20:25 INFO ************ Epoch=2 end ************
2024-11-12 07:24:26 INFO [Metrics] AUC-ROC: 0.795980 - AUC-PR: 0.591258 - ACC: 0.788158 - Precision: 0.657884 - Recall: 0.360876 - F1: 0.466086 - MCC: 0.372076 - Logloss: 0.454254 - MSE: 0.147352 - RMSE: 0.383864 - COPC: 1.000383 - KLD: 1.047431
2024-11-12 07:24:26 INFO Save best model: monitor(max): 0.341726
2024-11-12 07:24:26 INFO --- 1223/1223 batches finished ---
2024-11-12 07:24:26 INFO Train loss: 0.456238
2024-11-12 07:24:26 INFO ************ Epoch=3 end ************
2024-11-12 07:28:27 INFO [Metrics] AUC-ROC: 0.796938 - AUC-PR: 0.592584 - ACC: 0.788466 - Precision: 0.663407 - Recall: 0.354051 - F1: 0.461700 - MCC: 0.371229 - Logloss: 0.453523 - MSE: 0.147108 - RMSE: 0.383547 - COPC: 1.019861 - KLD: 1.045171
2024-11-12 07:28:27 INFO Save best model: monitor(max): 0.343415
2024-11-12 07:28:28 INFO --- 1223/1223 batches finished ---
2024-11-12 07:28:28 INFO Train loss: 0.455082
2024-11-12 07:28:28 INFO ************ Epoch=4 end ************
2024-11-12 07:32:28 INFO [Metrics] AUC-ROC: 0.797647 - AUC-PR: 0.593959 - ACC: 0.788870 - Precision: 0.659438 - Recall: 0.363951 - F1: 0.469036 - MCC: 0.374850 - Logloss: 0.452797 - MSE: 0.146838 - RMSE: 0.383194 - COPC: 1.004014 - KLD: 1.043537
2024-11-12 07:32:28 INFO Save best model: monitor(max): 0.344850
2024-11-12 07:32:28 INFO --- 1223/1223 batches finished ---
2024-11-12 07:32:28 INFO Train loss: 0.454385
2024-11-12 07:32:28 INFO ************ Epoch=5 end ************
2024-11-12 07:36:29 INFO [Metrics] AUC-ROC: 0.798349 - AUC-PR: 0.595110 - ACC: 0.789140 - Precision: 0.666669 - Recall: 0.354088 - F1: 0.462518 - MCC: 0.373164 - Logloss: 0.452326 - MSE: 0.146679 - RMSE: 0.382987 - COPC: 1.026183 - KLD: 1.041937
2024-11-12 07:36:29 INFO Save best model: monitor(max): 0.346024
2024-11-12 07:36:29 INFO --- 1223/1223 batches finished ---
2024-11-12 07:36:29 INFO Train loss: 0.453773
2024-11-12 07:36:29 INFO ************ Epoch=6 end ************
2024-11-12 07:40:32 INFO [Metrics] AUC-ROC: 0.799129 - AUC-PR: 0.596470 - ACC: 0.789611 - Precision: 0.661406 - Recall: 0.366512 - F1: 0.471659 - MCC: 0.377566 - Logloss: 0.451505 - MSE: 0.146367 - RMSE: 0.382580 - COPC: 1.004058 - KLD: 1.040178
2024-11-12 07:40:32 INFO Save best model: monitor(max): 0.347624
2024-11-12 07:40:32 INFO --- 1223/1223 batches finished ---
2024-11-12 07:40:32 INFO Train loss: 0.453128
2024-11-12 07:40:32 INFO ************ Epoch=7 end ************
2024-11-12 07:44:35 INFO [Metrics] AUC-ROC: 0.799815 - AUC-PR: 0.597650 - ACC: 0.789964 - Precision: 0.660797 - Recall: 0.370397 - F1: 0.474706 - MCC: 0.379527 - Logloss: 0.450886 - MSE: 0.146145 - RMSE: 0.382290 - COPC: 1.000489 - KLD: 1.038576
2024-11-12 07:44:35 INFO Save best model: monitor(max): 0.348929
2024-11-12 07:44:35 INFO --- 1223/1223 batches finished ---
2024-11-12 07:44:35 INFO Train loss: 0.452537
2024-11-12 07:44:35 INFO ************ Epoch=8 end ************
2024-11-12 07:48:37 INFO [Metrics] AUC-ROC: 0.800494 - AUC-PR: 0.598664 - ACC: 0.790251 - Precision: 0.660027 - Recall: 0.374054 - F1: 0.477498 - MCC: 0.381246 - Logloss: 0.450330 - MSE: 0.145944 - RMSE: 0.382027 - COPC: 0.994025 - KLD: 1.037083
2024-11-12 07:48:37 INFO Save best model: monitor(max): 0.350164
2024-11-12 07:48:37 INFO --- 1223/1223 batches finished ---
2024-11-12 07:48:37 INFO Train loss: 0.451949
2024-11-12 07:48:37 INFO ************ Epoch=9 end ************
2024-11-12 07:52:40 INFO [Metrics] AUC-ROC: 0.801092 - AUC-PR: 0.599637 - ACC: 0.790430 - Precision: 0.657045 - Recall: 0.380891 - F1: 0.482231 - MCC: 0.383486 - Logloss: 0.449817 - MSE: 0.145760 - RMSE: 0.381785 - COPC: 0.988424 - KLD: 1.035677
2024-11-12 07:52:40 INFO Save best model: monitor(max): 0.351275
2024-11-12 07:52:40 INFO --- 1223/1223 batches finished ---
2024-11-12 07:52:40 INFO Train loss: 0.451372
2024-11-12 07:52:40 INFO ************ Epoch=10 end ************
2024-11-12 07:56:42 INFO [Metrics] AUC-ROC: 0.801655 - AUC-PR: 0.600451 - ACC: 0.790703 - Precision: 0.661901 - Recall: 0.374380 - F1: 0.478254 - MCC: 0.382576 - Logloss: 0.449318 - MSE: 0.145588 - RMSE: 0.381559 - COPC: 0.999958 - KLD: 1.034373
2024-11-12 07:56:42 INFO Save best model: monitor(max): 0.352337
2024-11-12 07:56:42 INFO --- 1223/1223 batches finished ---
2024-11-12 07:56:42 INFO Train loss: 0.450873
2024-11-12 07:56:42 INFO ************ Epoch=11 end ************
2024-11-12 08:00:42 INFO [Metrics] AUC-ROC: 0.802081 - AUC-PR: 0.601132 - ACC: 0.790893 - Precision: 0.659048 - Recall: 0.380985 - F1: 0.482845 - MCC: 0.384770 - Logloss: 0.448952 - MSE: 0.145457 - RMSE: 0.381388 - COPC: 0.991983 - KLD: 1.033392
2024-11-12 08:00:42 INFO Save best model: monitor(max): 0.353129
2024-11-12 08:00:43 INFO --- 1223/1223 batches finished ---
2024-11-12 08:00:43 INFO Train loss: 0.450405
2024-11-12 08:00:43 INFO ************ Epoch=12 end ************
2024-11-12 08:04:46 INFO [Metrics] AUC-ROC: 0.802469 - AUC-PR: 0.601741 - ACC: 0.791020 - Precision: 0.659687 - Recall: 0.380859 - F1: 0.482916 - MCC: 0.385086 - Logloss: 0.448606 - MSE: 0.145340 - RMSE: 0.381235 - COPC: 0.992140 - KLD: 1.032453
2024-11-12 08:04:46 INFO Save best model: monitor(max): 0.353863
2024-11-12 08:04:46 INFO --- 1223/1223 batches finished ---
2024-11-12 08:04:46 INFO Train loss: 0.450000
2024-11-12 08:04:46 INFO ************ Epoch=13 end ************
2024-11-12 08:08:47 INFO [Metrics] AUC-ROC: 0.802725 - AUC-PR: 0.602156 - ACC: 0.791141 - Precision: 0.656382 - Recall: 0.387947 - F1: 0.487665 - MCC: 0.387237 - Logloss: 0.448398 - MSE: 0.145270 - RMSE: 0.381143 - COPC: 0.985859 - KLD: 1.031809
2024-11-12 08:08:47 INFO Save best model: monitor(max): 0.354327
2024-11-12 08:08:47 INFO --- 1223/1223 batches finished ---
2024-11-12 08:08:47 INFO Train loss: 0.449640
2024-11-12 08:08:47 INFO ************ Epoch=14 end ************
2024-11-12 08:13:01 INFO [Metrics] AUC-ROC: 0.803033 - AUC-PR: 0.602637 - ACC: 0.791285 - Precision: 0.664405 - Recall: 0.374663 - F1: 0.479137 - MCC: 0.384258 - Logloss: 0.448097 - MSE: 0.145171 - RMSE: 0.381012 - COPC: 1.006466 - KLD: 1.031073
2024-11-12 08:13:01 INFO Save best model: monitor(max): 0.354935
2024-11-12 08:13:01 INFO --- 1223/1223 batches finished ---
2024-11-12 08:13:01 INFO Train loss: 0.449324
2024-11-12 08:13:01 INFO ************ Epoch=15 end ************
2024-11-12 08:17:10 INFO [Metrics] AUC-ROC: 0.803357 - AUC-PR: 0.603261 - ACC: 0.791408 - Precision: 0.661960 - Recall: 0.379903 - F1: 0.482752 - MCC: 0.385905 - Logloss: 0.447786 - MSE: 0.145052 - RMSE: 0.380857 - COPC: 1.001324 - KLD: 1.030330
2024-11-12 08:17:10 INFO Save best model: monitor(max): 0.355571
2024-11-12 08:17:10 INFO --- 1223/1223 batches finished ---
2024-11-12 08:17:10 INFO Train loss: 0.449033
2024-11-12 08:17:10 INFO ************ Epoch=16 end ************
2024-11-12 08:21:14 INFO [Metrics] AUC-ROC: 0.803561 - AUC-PR: 0.603559 - ACC: 0.791521 - Precision: 0.666200 - Recall: 0.373463 - F1: 0.478619 - MCC: 0.384619 - Logloss: 0.447660 - MSE: 0.145013 - RMSE: 0.380806 - COPC: 1.016034 - KLD: 1.029843
2024-11-12 08:21:14 INFO Save best model: monitor(max): 0.355901
2024-11-12 08:21:14 INFO --- 1223/1223 batches finished ---
2024-11-12 08:21:14 INFO Train loss: 0.448740
2024-11-12 08:21:14 INFO ************ Epoch=17 end ************
2024-11-12 08:25:18 INFO [Metrics] AUC-ROC: 0.803811 - AUC-PR: 0.603964 - ACC: 0.791707 - Precision: 0.660597 - Recall: 0.384731 - F1: 0.486263 - MCC: 0.387940 - Logloss: 0.447382 - MSE: 0.144908 - RMSE: 0.380668 - COPC: 0.997448 - KLD: 1.029241
2024-11-12 08:25:18 INFO Save best model: monitor(max): 0.356429
2024-11-12 08:25:19 INFO --- 1223/1223 batches finished ---
2024-11-12 08:25:19 INFO Train loss: 0.448461
2024-11-12 08:25:19 INFO ************ Epoch=18 end ************
2024-11-12 08:29:29 INFO [Metrics] AUC-ROC: 0.803878 - AUC-PR: 0.604100 - ACC: 0.791653 - Precision: 0.664580 - Recall: 0.377263 - F1: 0.481304 - MCC: 0.385921 - Logloss: 0.447386 - MSE: 0.144902 - RMSE: 0.380660 - COPC: 1.016157 - KLD: 1.029177
2024-11-12 08:29:29 INFO Save best model: monitor(max): 0.356492
2024-11-12 08:29:29 INFO --- 1223/1223 batches finished ---
2024-11-12 08:29:29 INFO Train loss: 0.448197
2024-11-12 08:29:29 INFO ************ Epoch=19 end ************
2024-11-12 08:33:39 INFO [Metrics] AUC-ROC: 0.804069 - AUC-PR: 0.604377 - ACC: 0.791800 - Precision: 0.660672 - Recall: 0.385348 - F1: 0.486776 - MCC: 0.388351 - Logloss: 0.447159 - MSE: 0.144830 - RMSE: 0.380566 - COPC: 0.998058 - KLD: 1.028647
2024-11-12 08:33:39 INFO Save best model: monitor(max): 0.356910
2024-11-12 08:33:39 INFO --- 1223/1223 batches finished ---
2024-11-12 08:33:39 INFO Train loss: 0.447949
2024-11-12 08:33:39 INFO ************ Epoch=20 end ************
2024-11-12 08:37:47 INFO [Metrics] AUC-ROC: 0.804240 - AUC-PR: 0.604750 - ACC: 0.791849 - Precision: 0.659533 - Recall: 0.387823 - F1: 0.488434 - MCC: 0.389114 - Logloss: 0.446997 - MSE: 0.144773 - RMSE: 0.380491 - COPC: 0.994580 - KLD: 1.028228
2024-11-12 08:37:47 INFO Save best model: monitor(max): 0.357243
2024-11-12 08:37:47 INFO --- 1223/1223 batches finished ---
2024-11-12 08:37:48 INFO Train loss: 0.447709
2024-11-12 08:37:48 INFO ************ Epoch=21 end ************
2024-11-12 08:42:05 INFO [Metrics] AUC-ROC: 0.804405 - AUC-PR: 0.604916 - ACC: 0.791946 - Precision: 0.664848 - Recall: 0.379110 - F1: 0.482874 - MCC: 0.387188 - Logloss: 0.446873 - MSE: 0.144729 - RMSE: 0.380433 - COPC: 1.008123 - KLD: 1.027865
2024-11-12 08:42:05 INFO Save best model: monitor(max): 0.357532
2024-11-12 08:42:05 INFO --- 1223/1223 batches finished ---
2024-11-12 08:42:05 INFO Train loss: 0.447464
2024-11-12 08:42:05 INFO ************ Epoch=22 end ************
2024-11-12 08:46:20 INFO [Metrics] AUC-ROC: 0.804549 - AUC-PR: 0.605136 - ACC: 0.792032 - Precision: 0.659638 - Recall: 0.389109 - F1: 0.489482 - MCC: 0.389938 - Logloss: 0.446744 - MSE: 0.144679 - RMSE: 0.380367 - COPC: 0.995324 - KLD: 1.027540
2024-11-12 08:46:20 INFO Save best model: monitor(max): 0.357805
2024-11-12 08:46:20 INFO --- 1223/1223 batches finished ---
2024-11-12 08:46:21 INFO Train loss: 0.447235
2024-11-12 08:46:21 INFO ************ Epoch=23 end ************
2024-11-12 08:50:24 INFO [Metrics] AUC-ROC: 0.804747 - AUC-PR: 0.605458 - ACC: 0.792049 - Precision: 0.657545 - Recall: 0.393160 - F1: 0.492090 - MCC: 0.391024 - Logloss: 0.446605 - MSE: 0.144636 - RMSE: 0.380311 - COPC: 0.985204 - KLD: 1.027070
2024-11-12 08:50:24 INFO Save best model: monitor(max): 0.358142
2024-11-12 08:50:25 INFO --- 1223/1223 batches finished ---
2024-11-12 08:50:25 INFO Train loss: 0.447006
2024-11-12 08:50:25 INFO ************ Epoch=24 end ************
2024-11-12 08:54:33 INFO [Metrics] AUC-ROC: 0.804839 - AUC-PR: 0.605657 - ACC: 0.792049 - Precision: 0.657920 - Recall: 0.392453 - F1: 0.491640 - MCC: 0.390842 - Logloss: 0.446516 - MSE: 0.144603 - RMSE: 0.380267 - COPC: 0.984622 - KLD: 1.026855
2024-11-12 08:54:33 INFO Save best model: monitor(max): 0.358323
2024-11-12 08:54:33 INFO --- 1223/1223 batches finished ---
2024-11-12 08:54:33 INFO Train loss: 0.446765
2024-11-12 08:54:33 INFO ************ Epoch=25 end ************
2024-11-12 08:58:38 INFO [Metrics] AUC-ROC: 0.804803 - AUC-PR: 0.605640 - ACC: 0.792088 - Precision: 0.657463 - Recall: 0.393636 - F1: 0.492439 - MCC: 0.391252 - Logloss: 0.446512 - MSE: 0.144599 - RMSE: 0.380262 - COPC: 0.991818 - KLD: 1.026931
2024-11-12 08:58:38 INFO Monitor(max) STOP: 0.358291 !
2024-11-12 08:58:38 INFO Reduce learning rate on plateau: 0.000100
2024-11-12 08:58:38 INFO --- 1223/1223 batches finished ---
2024-11-12 08:58:38 INFO Train loss: 0.446528
2024-11-12 08:58:38 INFO ************ Epoch=26 end ************
2024-11-12 09:02:44 INFO [Metrics] AUC-ROC: 0.805460 - AUC-PR: 0.606714 - ACC: 0.792436 - Precision: 0.661585 - Recall: 0.388781 - F1: 0.489756 - MCC: 0.390946 - Logloss: 0.445911 - MSE: 0.144380 - RMSE: 0.379974 - COPC: 1.000704 - KLD: 1.025390
2024-11-12 09:02:44 INFO Save best model: monitor(max): 0.359549
2024-11-12 09:02:44 INFO --- 1223/1223 batches finished ---
2024-11-12 09:02:44 INFO Train loss: 0.443925
2024-11-12 09:02:44 INFO ************ Epoch=27 end ************
2024-11-12 09:06:51 INFO [Metrics] AUC-ROC: 0.805507 - AUC-PR: 0.606798 - ACC: 0.792469 - Precision: 0.660514 - Recall: 0.391000 - F1: 0.491218 - MCC: 0.391597 - Logloss: 0.445876 - MSE: 0.144366 - RMSE: 0.379955 - COPC: 0.998037 - KLD: 1.025293
2024-11-12 09:06:51 INFO Save best model: monitor(max): 0.359632
2024-11-12 09:06:51 INFO --- 1223/1223 batches finished ---
2024-11-12 09:06:52 INFO Train loss: 0.443504
2024-11-12 09:06:52 INFO ************ Epoch=28 end ************
2024-11-12 09:10:54 INFO [Metrics] AUC-ROC: 0.805489 - AUC-PR: 0.606774 - ACC: 0.792437 - Precision: 0.660138 - Recall: 0.391442 - F1: 0.491462 - MCC: 0.391624 - Logloss: 0.445895 - MSE: 0.144371 - RMSE: 0.379962 - COPC: 0.998140 - KLD: 1.025350
2024-11-12 09:10:54 INFO Monitor(max) STOP: 0.359595 !
2024-11-12 09:10:54 INFO Reduce learning rate on plateau: 0.000010
2024-11-12 09:10:54 INFO --- 1223/1223 batches finished ---
2024-11-12 09:10:54 INFO Train loss: 0.443310
2024-11-12 09:10:54 INFO ************ Epoch=29 end ************
2024-11-12 09:14:56 INFO [Metrics] AUC-ROC: 0.805498 - AUC-PR: 0.606792 - ACC: 0.792448 - Precision: 0.660654 - Recall: 0.390576 - F1: 0.490922 - MCC: 0.391433 - Logloss: 0.445890 - MSE: 0.144368 - RMSE: 0.379958 - COPC: 1.000314 - KLD: 1.025344
2024-11-12 09:14:56 INFO Monitor(max) STOP: 0.359608 !
2024-11-12 09:14:56 INFO Reduce learning rate on plateau: 0.000001
2024-11-12 09:14:56 INFO Early stopping at epoch=30
2024-11-12 09:14:56 INFO --- 1223/1223 batches finished ---
2024-11-12 09:14:57 INFO Train loss: 0.442849
2024-11-12 09:14:57 INFO Training finished.
2024-11-12 09:14:57 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AFM/Criteo/AFM_model.ckpt
2024-11-12 09:14:57 INFO Start evaluate model
2024-11-12 09:15:35 INFO [Metrics] AUC-ROC: 0.805507 - AUC-PR: 0.606798 - ACC: 0.792469 - Precision: 0.660514 - Recall: 0.391000 - F1: 0.491218 - MCC: 0.391597 - Logloss: 0.445876 - MSE: 0.144366 - RMSE: 0.379955 - COPC: 0.998037 - KLD: 1.025293
2024-11-12 09:15:35 INFO Start testing model
2024-11-12 09:16:14 INFO [Metrics] AUC-ROC: 0.805902 - AUC-PR: 0.606956 - ACC: 0.792466 - Precision: 0.660054 - Recall: 0.391832 - F1: 0.491746 - MCC: 0.391802 - Logloss: 0.445588 - MSE: 0.144282 - RMSE: 0.379845 - COPC: 0.997810 - KLD: 1.024300
