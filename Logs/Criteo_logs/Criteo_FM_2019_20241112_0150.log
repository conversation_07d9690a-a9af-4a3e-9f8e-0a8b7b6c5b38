2024-11-12 01:50:43 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='FM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=65536, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-12 01:50:43 INFO Start process Criteo !
2024-11-12 01:50:43 INFO Loading Criteo dataset
2024-11-12 01:50:43 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-12 01:50:48 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-12 01:50:49 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-12 01:50:50 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-12 01:50:50 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 01:50:50 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 01:50:50 INFO Loading data done
2024-11-12 01:50:50 INFO Model: FM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fm_layer): FM_Layer(
    (inner_product_layer): InnerProductLayer()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (I1): Embedding(43, 1, padding_idx=42)
          (I2): Embedding(98, 1, padding_idx=97)
          (I3): Embedding(121, 1, padding_idx=120)
          (I4): Embedding(40, 1, padding_idx=39)
          (I5): Embedding(219, 1, padding_idx=218)
          (I6): Embedding(111, 1, padding_idx=110)
          (I7): Embedding(79, 1, padding_idx=78)
          (I8): Embedding(68, 1, padding_idx=67)
          (I9): Embedding(91, 1, padding_idx=90)
          (I10): Embedding(5, 1, padding_idx=4)
          (I11): Embedding(26, 1, padding_idx=25)
          (I12): Embedding(36, 1, padding_idx=35)
          (I13): Embedding(71, 1, padding_idx=70)
          (C1): Embedding(1445, 1, padding_idx=1444)
          (C2): Embedding(553, 1, padding_idx=552)
          (C3): Embedding(157338, 1, padding_idx=157337)
          (C4): Embedding(117821, 1, padding_idx=117820)
          (C5): Embedding(305, 1, padding_idx=304)
          (C6): Embedding(17, 1, padding_idx=16)
          (C7): Embedding(11881, 1, padding_idx=11880)
          (C8): Embedding(629, 1, padding_idx=628)
          (C9): Embedding(4, 1, padding_idx=3)
          (C10): Embedding(39529, 1, padding_idx=39528)
          (C11): Embedding(5130, 1, padding_idx=5129)
          (C12): Embedding(156655, 1, padding_idx=156654)
          (C13): Embedding(3175, 1, padding_idx=3174)
          (C14): Embedding(27, 1, padding_idx=26)
          (C15): Embedding(11042, 1, padding_idx=11041)
          (C16): Embedding(148912, 1, padding_idx=148911)
          (C17): Embedding(11, 1, padding_idx=10)
          (C18): Embedding(4559, 1, padding_idx=4558)
          (C19): Embedding(2002, 1, padding_idx=2001)
          (C20): Embedding(4, 1, padding_idx=3)
          (C21): Embedding(154563, 1, padding_idx=154562)
          (C22): Embedding(17, 1, padding_idx=16)
          (C23): Embedding(16, 1, padding_idx=15)
          (C24): Embedding(53030, 1, padding_idx=53029)
          (C25): Embedding(81, 1, padding_idx=80)
          (C26): Embedding(40954, 1, padding_idx=40953)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
    (final_activation): Sigmoid()
  )
)
2024-11-12 01:50:50 INFO Model parameters: 15482037
2024-11-12 01:50:50 INFO Start training model
2024-11-12 01:50:50 INFO Start training: 560 batches/epoch
2024-11-12 01:50:50 INFO ************ Epoch=1 start ************
2024-11-12 01:54:43 INFO [Metrics] AUC-ROC: 0.799047 - AUC-PR: 0.596094 - ACC: 0.789559 - Precision: 0.661376 - Recall: 0.366147 - F1: 0.471349 - MCC: 0.377328 - Logloss: 0.452219 - MSE: 0.146478 - RMSE: 0.382724 - COPC: 1.004060 - KLD: 1.042221
2024-11-12 01:54:43 INFO Save best model: monitor(max): 0.346828
2024-11-12 01:54:43 INFO --- 560/560 batches finished ---
2024-11-12 01:54:43 INFO Train loss: 0.470825
2024-11-12 01:54:43 INFO ************ Epoch=1 end ************
2024-11-12 01:58:37 INFO [Metrics] AUC-ROC: 0.803522 - AUC-PR: 0.602969 - ACC: 0.791317 - Precision: 0.665111 - Recall: 0.373711 - F1: 0.478541 - MCC: 0.384112 - Logloss: 0.448045 - MSE: 0.145120 - RMSE: 0.380946 - COPC: 1.013508 - KLD: 1.030718
2024-11-12 01:58:37 INFO Save best model: monitor(max): 0.355477
2024-11-12 01:58:37 INFO --- 560/560 batches finished ---
2024-11-12 01:58:37 INFO Train loss: 0.449841
2024-11-12 01:58:37 INFO ************ Epoch=2 end ************
2024-11-12 02:02:32 INFO [Metrics] AUC-ROC: 0.805102 - AUC-PR: 0.605393 - ACC: 0.792128 - Precision: 0.663416 - Recall: 0.383047 - F1: 0.485673 - MCC: 0.388666 - Logloss: 0.446514 - MSE: 0.144606 - RMSE: 0.380271 - COPC: 1.008877 - KLD: 1.026573
2024-11-12 02:02:32 INFO Save best model: monitor(max): 0.358588
2024-11-12 02:02:32 INFO --- 560/560 batches finished ---
2024-11-12 02:02:32 INFO Train loss: 0.445468
2024-11-12 02:02:32 INFO ************ Epoch=3 end ************
2024-11-12 02:06:26 INFO [Metrics] AUC-ROC: 0.805783 - AUC-PR: 0.606562 - ACC: 0.792382 - Precision: 0.657745 - Recall: 0.395492 - F1: 0.493968 - MCC: 0.392517 - Logloss: 0.445914 - MSE: 0.144412 - RMSE: 0.380016 - COPC: 0.998237 - KLD: 1.024811
2024-11-12 02:06:26 INFO Save best model: monitor(max): 0.359869
2024-11-12 02:06:26 INFO --- 560/560 batches finished ---
2024-11-12 02:06:26 INFO Train loss: 0.443074
2024-11-12 02:06:26 INFO ************ Epoch=4 end ************
2024-11-12 02:10:22 INFO [Metrics] AUC-ROC: 0.805992 - AUC-PR: 0.606608 - ACC: 0.792343 - Precision: 0.662381 - Recall: 0.386595 - F1: 0.488235 - MCC: 0.390141 - Logloss: 0.445814 - MSE: 0.144392 - RMSE: 0.379989 - COPC: 1.016532 - KLD: 1.024432
2024-11-12 02:10:22 INFO Save best model: monitor(max): 0.360178
2024-11-12 02:10:22 INFO --- 560/560 batches finished ---
2024-11-12 02:10:22 INFO Train loss: 0.441600
2024-11-12 02:10:22 INFO ************ Epoch=5 end ************
2024-11-12 02:14:16 INFO [Metrics] AUC-ROC: 0.806100 - AUC-PR: 0.606959 - ACC: 0.792409 - Precision: 0.655069 - Recall: 0.400907 - F1: 0.497401 - MCC: 0.394003 - Logloss: 0.445684 - MSE: 0.144352 - RMSE: 0.379937 - COPC: 0.993179 - KLD: 1.023946
2024-11-12 02:14:16 INFO Save best model: monitor(max): 0.360416
2024-11-12 02:14:16 INFO --- 560/560 batches finished ---
2024-11-12 02:14:16 INFO Train loss: 0.440663
2024-11-12 02:14:16 INFO ************ Epoch=6 end ************
2024-11-12 02:18:10 INFO [Metrics] AUC-ROC: 0.806004 - AUC-PR: 0.606637 - ACC: 0.792294 - Precision: 0.652055 - Recall: 0.406004 - F1: 0.500419 - MCC: 0.395051 - Logloss: 0.445827 - MSE: 0.144406 - RMSE: 0.380007 - COPC: 0.987388 - KLD: 1.024151
2024-11-12 02:18:10 INFO Monitor(max) STOP: 0.360177 !
2024-11-12 02:18:10 INFO Reduce learning rate on plateau: 0.000100
2024-11-12 02:18:10 INFO --- 560/560 batches finished ---
2024-11-12 02:18:10 INFO Train loss: 0.439931
2024-11-12 02:18:10 INFO ************ Epoch=7 end ************
2024-11-12 02:22:04 INFO [Metrics] AUC-ROC: 0.806976 - AUC-PR: 0.608395 - ACC: 0.792912 - Precision: 0.656497 - Recall: 0.402229 - F1: 0.498830 - MCC: 0.395675 - Logloss: 0.444912 - MSE: 0.144055 - RMSE: 0.379546 - COPC: 1.000695 - KLD: 1.022076
2024-11-12 02:22:04 INFO Save best model: monitor(max): 0.362063
2024-11-12 02:22:04 INFO --- 560/560 batches finished ---
2024-11-12 02:22:04 INFO Train loss: 0.432896
2024-11-12 02:22:04 INFO ************ Epoch=8 end ************
2024-11-12 02:26:01 INFO [Metrics] AUC-ROC: 0.807007 - AUC-PR: 0.608411 - ACC: 0.792874 - Precision: 0.655035 - Recall: 0.404806 - F1: 0.500381 - MCC: 0.396251 - Logloss: 0.444924 - MSE: 0.144062 - RMSE: 0.379555 - COPC: 0.998050 - KLD: 1.022026
2024-11-12 02:26:01 INFO Save best model: monitor(max): 0.362082
2024-11-12 02:26:01 INFO --- 560/560 batches finished ---
2024-11-12 02:26:01 INFO Train loss: 0.432117
2024-11-12 02:26:01 INFO ************ Epoch=9 end ************
2024-11-12 02:29:54 INFO [Metrics] AUC-ROC: 0.806981 - AUC-PR: 0.608405 - ACC: 0.792830 - Precision: 0.653959 - Recall: 0.406597 - F1: 0.501431 - MCC: 0.396608 - Logloss: 0.445002 - MSE: 0.144087 - RMSE: 0.379588 - COPC: 0.997653 - KLD: 1.022187
2024-11-12 02:29:54 INFO Monitor(max) STOP: 0.361980 !
2024-11-12 02:29:54 INFO Reduce learning rate on plateau: 0.000010
2024-11-12 02:29:54 INFO --- 560/560 batches finished ---
2024-11-12 02:29:54 INFO Train loss: 0.431717
2024-11-12 02:29:54 INFO ************ Epoch=10 end ************
2024-11-12 02:33:47 INFO [Metrics] AUC-ROC: 0.806999 - AUC-PR: 0.608412 - ACC: 0.792853 - Precision: 0.654856 - Recall: 0.404988 - F1: 0.500467 - MCC: 0.396243 - Logloss: 0.444980 - MSE: 0.144078 - RMSE: 0.379576 - COPC: 1.000399 - KLD: 1.022172
2024-11-12 02:33:47 INFO Monitor(max) STOP: 0.362019 !
2024-11-12 02:33:47 INFO Reduce learning rate on plateau: 0.000001
2024-11-12 02:33:47 INFO Early stopping at epoch=11
2024-11-12 02:33:47 INFO --- 560/560 batches finished ---
2024-11-12 02:33:47 INFO Train loss: 0.430611
2024-11-12 02:33:47 INFO Training finished.
2024-11-12 02:33:47 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FM/Criteo/FM_model.ckpt
2024-11-12 02:33:47 INFO Start evaluate model
2024-11-12 02:34:25 INFO [Metrics] AUC-ROC: 0.807007 - AUC-PR: 0.608411 - ACC: 0.792874 - Precision: 0.655035 - Recall: 0.404806 - F1: 0.500381 - MCC: 0.396251 - Logloss: 0.444924 - MSE: 0.144062 - RMSE: 0.379555 - COPC: 0.998050 - KLD: 1.022026
2024-11-12 02:34:25 INFO Start testing model
2024-11-12 02:35:02 INFO [Metrics] AUC-ROC: 0.807295 - AUC-PR: 0.608614 - ACC: 0.792958 - Precision: 0.654714 - Recall: 0.406138 - F1: 0.501303 - MCC: 0.396821 - Logloss: 0.444696 - MSE: 0.143992 - RMSE: 0.379462 - COPC: 0.997655 - KLD: 1.021263
