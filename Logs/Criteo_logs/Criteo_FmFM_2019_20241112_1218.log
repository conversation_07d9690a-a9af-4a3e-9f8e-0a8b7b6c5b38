2024-11-12 12:18:34 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='FmFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-12 12:18:34 INFO Start process Criteo !
2024-11-12 12:18:34 INFO Loading Criteo dataset
2024-11-12 12:18:34 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-12 12:18:39 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-12 12:18:39 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-12 12:18:41 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-12 12:18:41 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 12:18:41 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 12:18:41 INFO Loading data done
2024-11-12 12:18:41 INFO Model: FmFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 16)
        (I2): Embedding(98, 16)
        (I3): Embedding(121, 16)
        (I4): Embedding(40, 16)
        (I5): Embedding(219, 16)
        (I6): Embedding(111, 16)
        (I7): Embedding(79, 16)
        (I8): Embedding(68, 16)
        (I9): Embedding(91, 16)
        (I10): Embedding(5, 16)
        (I11): Embedding(26, 16)
        (I12): Embedding(36, 16)
        (I13): Embedding(71, 16)
        (C1): Embedding(1445, 16)
        (C2): Embedding(553, 16)
        (C3): Embedding(157338, 16)
        (C4): Embedding(117821, 16)
        (C5): Embedding(305, 16)
        (C6): Embedding(17, 16)
        (C7): Embedding(11881, 16)
        (C8): Embedding(629, 16)
        (C9): Embedding(4, 16)
        (C10): Embedding(39529, 16)
        (C11): Embedding(5130, 16)
        (C12): Embedding(156655, 16)
        (C13): Embedding(3175, 16)
        (C14): Embedding(27, 16)
        (C15): Embedding(11042, 16)
        (C16): Embedding(148912, 16)
        (C17): Embedding(11, 16)
        (C18): Embedding(4559, 16)
        (C19): Embedding(2002, 16)
        (C20): Embedding(4, 16)
        (C21): Embedding(154563, 16)
        (C22): Embedding(17, 16)
        (C23): Embedding(16, 16)
        (C24): Embedding(53030, 16)
        (C25): Embedding(81, 16)
        (C26): Embedding(40954, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 1, padding_idx=42)
        (I2): Embedding(98, 1, padding_idx=97)
        (I3): Embedding(121, 1, padding_idx=120)
        (I4): Embedding(40, 1, padding_idx=39)
        (I5): Embedding(219, 1, padding_idx=218)
        (I6): Embedding(111, 1, padding_idx=110)
        (I7): Embedding(79, 1, padding_idx=78)
        (I8): Embedding(68, 1, padding_idx=67)
        (I9): Embedding(91, 1, padding_idx=90)
        (I10): Embedding(5, 1, padding_idx=4)
        (I11): Embedding(26, 1, padding_idx=25)
        (I12): Embedding(36, 1, padding_idx=35)
        (I13): Embedding(71, 1, padding_idx=70)
        (C1): Embedding(1445, 1, padding_idx=1444)
        (C2): Embedding(553, 1, padding_idx=552)
        (C3): Embedding(157338, 1, padding_idx=157337)
        (C4): Embedding(117821, 1, padding_idx=117820)
        (C5): Embedding(305, 1, padding_idx=304)
        (C6): Embedding(17, 1, padding_idx=16)
        (C7): Embedding(11881, 1, padding_idx=11880)
        (C8): Embedding(629, 1, padding_idx=628)
        (C9): Embedding(4, 1, padding_idx=3)
        (C10): Embedding(39529, 1, padding_idx=39528)
        (C11): Embedding(5130, 1, padding_idx=5129)
        (C12): Embedding(156655, 1, padding_idx=156654)
        (C13): Embedding(3175, 1, padding_idx=3174)
        (C14): Embedding(27, 1, padding_idx=26)
        (C15): Embedding(11042, 1, padding_idx=11041)
        (C16): Embedding(148912, 1, padding_idx=148911)
        (C17): Embedding(11, 1, padding_idx=10)
        (C18): Embedding(4559, 1, padding_idx=4558)
        (C19): Embedding(2002, 1, padding_idx=2001)
        (C20): Embedding(4, 1, padding_idx=3)
        (C21): Embedding(154563, 1, padding_idx=154562)
        (C22): Embedding(17, 1, padding_idx=16)
        (C23): Embedding(16, 1, padding_idx=15)
        (C24): Embedding(53030, 1, padding_idx=53029)
        (C25): Embedding(81, 1, padding_idx=80)
        (C26): Embedding(40954, 1, padding_idx=40953)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (final_activation): Sigmoid()
)
2024-11-12 12:18:41 INFO Model parameters: 15671732
2024-11-12 12:18:41 INFO Start training model
2024-11-12 12:18:41 INFO Start training: 3668 batches/epoch
2024-11-12 12:18:41 INFO ************ Epoch=1 start ************
2024-11-12 12:29:25 INFO [Metrics] AUC-ROC: 0.805790 - AUC-PR: 0.607757 - ACC: 0.792620 - Precision: 0.668159 - Recall: 0.378715 - F1: 0.483424 - MCC: 0.388955 - Logloss: 0.445602 - MSE: 0.144265 - RMSE: 0.379823 - COPC: 1.017034 - KLD: 1.024667
2024-11-12 12:29:25 INFO Save best model: monitor(max): 0.360189
2024-11-12 12:29:25 INFO --- 3668/3668 batches finished ---
2024-11-12 12:29:25 INFO Train loss: 0.456313
2024-11-12 12:29:25 INFO ************ Epoch=1 end ************
2024-11-12 12:40:18 INFO [Metrics] AUC-ROC: 0.808998 - AUC-PR: 0.612990 - ACC: 0.794146 - Precision: 0.662124 - Recall: 0.401431 - F1: 0.499827 - MCC: 0.398744 - Logloss: 0.442631 - MSE: 0.143212 - RMSE: 0.378434 - COPC: 0.991424 - KLD: 1.016896
2024-11-12 12:40:18 INFO Save best model: monitor(max): 0.366367
2024-11-12 12:40:18 INFO --- 3668/3668 batches finished ---
2024-11-12 12:40:18 INFO Train loss: 0.447551
2024-11-12 12:40:18 INFO ************ Epoch=2 end ************
2024-11-12 12:51:07 INFO [Metrics] AUC-ROC: 0.809963 - AUC-PR: 0.614270 - ACC: 0.794668 - Precision: 0.661279 - Recall: 0.407196 - F1: 0.504027 - MCC: 0.401595 - Logloss: 0.441819 - MSE: 0.142941 - RMSE: 0.378075 - COPC: 0.992591 - KLD: 1.014561
2024-11-12 12:51:07 INFO Save best model: monitor(max): 0.368144
2024-11-12 12:51:08 INFO --- 3668/3668 batches finished ---
2024-11-12 12:51:08 INFO Train loss: 0.445184
2024-11-12 12:51:08 INFO ************ Epoch=3 end ************
2024-11-12 13:01:56 INFO [Metrics] AUC-ROC: 0.810325 - AUC-PR: 0.614873 - ACC: 0.794733 - Precision: 0.660214 - Recall: 0.409764 - F1: 0.505678 - MCC: 0.402423 - Logloss: 0.441484 - MSE: 0.142829 - RMSE: 0.377928 - COPC: 0.988425 - KLD: 1.013639
2024-11-12 13:01:56 INFO Save best model: monitor(max): 0.368840
2024-11-12 13:01:56 INFO --- 3668/3668 batches finished ---
2024-11-12 13:01:56 INFO Train loss: 0.443785
2024-11-12 13:01:56 INFO ************ Epoch=4 end ************
2024-11-12 13:12:48 INFO [Metrics] AUC-ROC: 0.810140 - AUC-PR: 0.614666 - ACC: 0.794609 - Precision: 0.661029 - Recall: 0.407200 - F1: 0.503957 - MCC: 0.401440 - Logloss: 0.441697 - MSE: 0.142889 - RMSE: 0.378007 - COPC: 0.999225 - KLD: 1.014289
2024-11-12 13:12:48 INFO Monitor(max) STOP: 0.368443 !
2024-11-12 13:12:48 INFO Reduce learning rate on plateau: 0.000100
2024-11-12 13:12:48 INFO --- 3668/3668 batches finished ---
2024-11-12 13:12:48 INFO Train loss: 0.442746
2024-11-12 13:12:48 INFO ************ Epoch=5 end ************
2024-11-12 13:23:05 INFO [Metrics] AUC-ROC: 0.810457 - AUC-PR: 0.614851 - ACC: 0.794637 - Precision: 0.655103 - Recall: 0.419197 - F1: 0.511248 - MCC: 0.404632 - Logloss: 0.441834 - MSE: 0.142925 - RMSE: 0.378054 - COPC: 0.996303 - KLD: 1.014273
2024-11-12 13:23:05 INFO Monitor(max) STOP: 0.368623 !
2024-11-12 13:23:05 INFO Reduce learning rate on plateau: 0.000010
2024-11-12 13:23:05 INFO Early stopping at epoch=6
2024-11-12 13:23:05 INFO --- 3668/3668 batches finished ---
2024-11-12 13:23:05 INFO Train loss: 0.430363
2024-11-12 13:23:05 INFO Training finished.
2024-11-12 13:23:05 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FmFM/Criteo/FmFM_model.ckpt
2024-11-12 13:23:05 INFO Start evaluate model
2024-11-12 13:23:49 INFO [Metrics] AUC-ROC: 0.810325 - AUC-PR: 0.614873 - ACC: 0.794733 - Precision: 0.660214 - Recall: 0.409764 - F1: 0.505678 - MCC: 0.402423 - Logloss: 0.441484 - MSE: 0.142829 - RMSE: 0.377928 - COPC: 0.988425 - KLD: 1.013639
2024-11-12 13:23:49 INFO Start testing model
2024-11-12 13:24:42 INFO [Metrics] AUC-ROC: 0.810765 - AUC-PR: 0.615275 - ACC: 0.794843 - Precision: 0.660035 - Recall: 0.410999 - F1: 0.506564 - MCC: 0.403031 - Logloss: 0.441125 - MSE: 0.142714 - RMSE: 0.377776 - COPC: 0.988162 - KLD: 1.012526
