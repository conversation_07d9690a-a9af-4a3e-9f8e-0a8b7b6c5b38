2024-11-12 13:23:44 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='PNN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-12 13:23:44 INFO Start process Criteo !
2024-11-12 13:23:44 INFO Loading Criteo dataset
2024-11-12 13:23:44 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-12 13:23:49 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-12 13:23:49 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-12 13:23:51 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-12 13:23:51 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 13:23:51 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 13:23:51 INFO Loading data done
2024-11-12 13:23:51 INFO Model: PNN(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=1365, out_features=1000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=1000, out_features=1000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=1000, out_features=1, bias=True)
      (7): Sigmoid()
    )
  )
)
2024-11-12 13:23:51 INFO Model parameters: 16939329
2024-11-12 13:23:51 INFO Start training model
2024-11-12 13:23:52 INFO Start training: 3668 batches/epoch
2024-11-12 13:23:52 INFO ************ Epoch=1 start ************
2024-11-12 13:28:46 INFO [Metrics] AUC-ROC: 0.807176 - AUC-PR: 0.610766 - ACC: 0.793376 - Precision: 0.659467 - Recall: 0.400265 - F1: 0.498167 - MCC: 0.396395 - Logloss: 0.444245 - MSE: 0.143761 - RMSE: 0.379159 - COPC: 1.004546 - KLD: 1.021572
2024-11-12 13:28:46 INFO Save best model: monitor(max): 0.362931
2024-11-12 13:28:46 INFO --- 3668/3668 batches finished ---
2024-11-12 13:28:46 INFO Train loss: 0.459061
2024-11-12 13:28:46 INFO ************ Epoch=1 end ************
2024-11-12 13:33:52 INFO [Metrics] AUC-ROC: 0.809144 - AUC-PR: 0.614047 - ACC: 0.794373 - Precision: 0.671936 - Recall: 0.385865 - F1: 0.490218 - MCC: 0.395536 - Logloss: 0.442755 - MSE: 0.143201 - RMSE: 0.378419 - COPC: 1.038065 - KLD: 1.017163
2024-11-12 13:33:52 INFO Save best model: monitor(max): 0.366389
2024-11-12 13:33:52 INFO --- 3668/3668 batches finished ---
2024-11-12 13:33:52 INFO Train loss: 0.453727
2024-11-12 13:33:52 INFO ************ Epoch=2 end ************
2024-11-12 13:38:58 INFO [Metrics] AUC-ROC: 0.810074 - AUC-PR: 0.615565 - ACC: 0.795016 - Precision: 0.670045 - Recall: 0.394001 - F1: 0.496216 - MCC: 0.399254 - Logloss: 0.441702 - MSE: 0.142841 - RMSE: 0.377944 - COPC: 1.025382 - KLD: 1.014674
2024-11-12 13:38:58 INFO Save best model: monitor(max): 0.368371
2024-11-12 13:38:58 INFO --- 3668/3668 batches finished ---
2024-11-12 13:38:58 INFO Train loss: 0.452165
2024-11-12 13:38:58 INFO ************ Epoch=3 end ************
2024-11-12 13:44:02 INFO [Metrics] AUC-ROC: 0.810580 - AUC-PR: 0.616501 - ACC: 0.794966 - Precision: 0.659914 - Recall: 0.412225 - F1: 0.507458 - MCC: 0.403668 - Logloss: 0.441047 - MSE: 0.142662 - RMSE: 0.377706 - COPC: 0.981734 - KLD: 1.012907
2024-11-12 13:44:02 INFO Save best model: monitor(max): 0.369533
2024-11-12 13:44:02 INFO --- 3668/3668 batches finished ---
2024-11-12 13:44:03 INFO Train loss: 0.451166
2024-11-12 13:44:03 INFO ************ Epoch=4 end ************
2024-11-12 13:49:07 INFO [Metrics] AUC-ROC: 0.810809 - AUC-PR: 0.616607 - ACC: 0.795083 - Precision: 0.675420 - Recall: 0.385497 - F1: 0.490844 - MCC: 0.397422 - Logloss: 0.440993 - MSE: 0.142634 - RMSE: 0.377670 - COPC: 1.027041 - KLD: 1.012552
2024-11-12 13:49:07 INFO Save best model: monitor(max): 0.369816
2024-11-12 13:49:07 INFO --- 3668/3668 batches finished ---
2024-11-12 13:49:07 INFO Train loss: 0.450446
2024-11-12 13:49:07 INFO ************ Epoch=5 end ************
2024-11-12 13:54:05 INFO [Metrics] AUC-ROC: 0.811034 - AUC-PR: 0.617078 - ACC: 0.795178 - Precision: 0.660390 - Recall: 0.412996 - F1: 0.508183 - MCC: 0.404421 - Logloss: 0.440676 - MSE: 0.142513 - RMSE: 0.377509 - COPC: 0.996291 - KLD: 1.012049
2024-11-12 13:54:05 INFO Save best model: monitor(max): 0.370358
2024-11-12 13:54:06 INFO --- 3668/3668 batches finished ---
2024-11-12 13:54:06 INFO Train loss: 0.449802
2024-11-12 13:54:06 INFO ************ Epoch=6 end ************
2024-11-12 13:59:04 INFO [Metrics] AUC-ROC: 0.811181 - AUC-PR: 0.617145 - ACC: 0.795378 - Precision: 0.672080 - Recall: 0.393285 - F1: 0.496203 - MCC: 0.400073 - Logloss: 0.441005 - MSE: 0.142564 - RMSE: 0.377576 - COPC: 1.038196 - KLD: 1.012565
2024-11-12 13:59:04 INFO Monitor(max) STOP: 0.370176 !
2024-11-12 13:59:04 INFO Reduce learning rate on plateau: 0.000100
2024-11-12 13:59:04 INFO --- 3668/3668 batches finished ---
2024-11-12 13:59:04 INFO Train loss: 0.449261
2024-11-12 13:59:04 INFO ************ Epoch=7 end ************
2024-11-12 14:04:03 INFO [Metrics] AUC-ROC: 0.813301 - AUC-PR: 0.620214 - ACC: 0.796136 - Precision: 0.664743 - Recall: 0.412277 - F1: 0.508919 - MCC: 0.406760 - Logloss: 0.438778 - MSE: 0.141850 - RMSE: 0.376630 - COPC: 1.008799 - KLD: 1.006845
2024-11-12 14:04:03 INFO Save best model: monitor(max): 0.374523
2024-11-12 14:04:03 INFO --- 3668/3668 batches finished ---
2024-11-12 14:04:03 INFO Train loss: 0.436564
2024-11-12 14:04:03 INFO ************ Epoch=8 end ************
2024-11-12 14:08:58 INFO [Metrics] AUC-ROC: 0.813280 - AUC-PR: 0.620092 - ACC: 0.796141 - Precision: 0.662707 - Recall: 0.416202 - F1: 0.511295 - MCC: 0.407766 - Logloss: 0.439051 - MSE: 0.141916 - RMSE: 0.376717 - COPC: 1.007067 - KLD: 1.007436
2024-11-12 14:08:58 INFO Monitor(max) STOP: 0.374228 !
2024-11-12 14:08:58 INFO Reduce learning rate on plateau: 0.000010
2024-11-12 14:08:58 INFO --- 3668/3668 batches finished ---
2024-11-12 14:08:59 INFO Train loss: 0.431542
2024-11-12 14:08:59 INFO ************ Epoch=9 end ************
2024-11-12 14:13:56 INFO [Metrics] AUC-ROC: 0.812720 - AUC-PR: 0.619202 - ACC: 0.795917 - Precision: 0.660181 - Recall: 0.419349 - F1: 0.512902 - MCC: 0.407985 - Logloss: 0.439888 - MSE: 0.142146 - RMSE: 0.377023 - COPC: 1.011129 - KLD: 1.009797
2024-11-12 14:13:56 INFO Monitor(max) STOP: 0.372832 !
2024-11-12 14:13:56 INFO Reduce learning rate on plateau: 0.000001
2024-11-12 14:13:56 INFO Early stopping at epoch=10
2024-11-12 14:13:56 INFO --- 3668/3668 batches finished ---
2024-11-12 14:13:56 INFO Train loss: 0.426704
2024-11-12 14:13:56 INFO Training finished.
2024-11-12 14:13:56 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/PNN/Criteo/PNN_model.ckpt
2024-11-12 14:13:56 INFO Start evaluate model
2024-11-12 14:14:34 INFO [Metrics] AUC-ROC: 0.813301 - AUC-PR: 0.620214 - ACC: 0.796136 - Precision: 0.664743 - Recall: 0.412277 - F1: 0.508919 - MCC: 0.406760 - Logloss: 0.438778 - MSE: 0.141850 - RMSE: 0.376630 - COPC: 1.008799 - KLD: 1.006845
2024-11-12 14:14:34 INFO Start testing model
2024-11-12 14:15:12 INFO [Metrics] AUC-ROC: 0.813745 - AUC-PR: 0.620650 - ACC: 0.796373 - Precision: 0.665014 - Recall: 0.413631 - F1: 0.510030 - MCC: 0.407726 - Logloss: 0.438381 - MSE: 0.141734 - RMSE: 0.376476 - COPC: 1.008320 - KLD: 1.005610
