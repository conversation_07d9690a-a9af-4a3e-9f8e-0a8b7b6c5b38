2024-11-12 05:15:49 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='AFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=60000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-12 05:15:49 INFO Start process Criteo !
2024-11-12 05:15:49 INFO Loading Criteo dataset
2024-11-12 05:15:49 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-12 05:15:54 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-12 05:15:54 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-12 05:15:55 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-12 05:15:55 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 05:15:55 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 05:15:55 INFO Loading data done
2024-11-12 05:15:56 INFO Model: AFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (elementwise_product_layer): InnerProductLayer()
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 1, padding_idx=42)
        (I2): Embedding(98, 1, padding_idx=97)
        (I3): Embedding(121, 1, padding_idx=120)
        (I4): Embedding(40, 1, padding_idx=39)
        (I5): Embedding(219, 1, padding_idx=218)
        (I6): Embedding(111, 1, padding_idx=110)
        (I7): Embedding(79, 1, padding_idx=78)
        (I8): Embedding(68, 1, padding_idx=67)
        (I9): Embedding(91, 1, padding_idx=90)
        (I10): Embedding(5, 1, padding_idx=4)
        (I11): Embedding(26, 1, padding_idx=25)
        (I12): Embedding(36, 1, padding_idx=35)
        (I13): Embedding(71, 1, padding_idx=70)
        (C1): Embedding(1445, 1, padding_idx=1444)
        (C2): Embedding(553, 1, padding_idx=552)
        (C3): Embedding(157338, 1, padding_idx=157337)
        (C4): Embedding(117821, 1, padding_idx=117820)
        (C5): Embedding(305, 1, padding_idx=304)
        (C6): Embedding(17, 1, padding_idx=16)
        (C7): Embedding(11881, 1, padding_idx=11880)
        (C8): Embedding(629, 1, padding_idx=628)
        (C9): Embedding(4, 1, padding_idx=3)
        (C10): Embedding(39529, 1, padding_idx=39528)
        (C11): Embedding(5130, 1, padding_idx=5129)
        (C12): Embedding(156655, 1, padding_idx=156654)
        (C13): Embedding(3175, 1, padding_idx=3174)
        (C14): Embedding(27, 1, padding_idx=26)
        (C15): Embedding(11042, 1, padding_idx=11041)
        (C16): Embedding(148912, 1, padding_idx=148911)
        (C17): Embedding(11, 1, padding_idx=10)
        (C18): Embedding(4559, 1, padding_idx=4558)
        (C19): Embedding(2002, 1, padding_idx=2001)
        (C20): Embedding(4, 1, padding_idx=3)
        (C21): Embedding(154563, 1, padding_idx=154562)
        (C22): Embedding(17, 1, padding_idx=16)
        (C23): Embedding(16, 1, padding_idx=15)
        (C24): Embedding(53030, 1, padding_idx=53029)
        (C25): Embedding(81, 1, padding_idx=80)
        (C26): Embedding(40954, 1, padding_idx=40953)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (attention): Sequential(
    (0): Linear(in_features=16, out_features=16, bias=True)
    (1): ReLU()
    (2): Linear(in_features=16, out_features=1, bias=False)
    (3): Softmax(dim=1)
  )
  (weight_p): Linear(in_features=16, out_features=1, bias=False)
  (dropout1): Dropout(p=0, inplace=False)
  (dropout2): Dropout(p=0, inplace=False)
  (final_activation): Sigmoid()
)
2024-11-12 05:15:56 INFO Model parameters: 15482341
2024-11-12 05:15:56 INFO Start training model
2024-11-12 05:15:56 INFO Start training: 612 batches/epoch
2024-11-12 05:15:56 INFO ************ Epoch=1 start ************
2024-11-12 05:19:55 INFO [Metrics] AUC-ROC: 0.789395 - AUC-PR: 0.581054 - ACC: 0.785566 - Precision: 0.657245 - Recall: 0.340854 - F1: 0.448902 - MCC: 0.359606 - Logloss: 0.459915 - MSE: 0.149304 - RMSE: 0.386399 - COPC: 1.003366 - KLD: 1.062674
2024-11-12 05:19:55 INFO Save best model: monitor(max): 0.329480
2024-11-12 05:19:55 INFO --- 612/612 batches finished ---
2024-11-12 05:19:55 INFO Train loss: 0.484328
2024-11-12 05:19:55 INFO ************ Epoch=1 end ************
2024-11-12 05:23:53 INFO [Metrics] AUC-ROC: 0.793088 - AUC-PR: 0.586949 - ACC: 0.787045 - Precision: 0.658528 - Recall: 0.350748 - F1: 0.457709 - MCC: 0.366358 - Logloss: 0.456729 - MSE: 0.148198 - RMSE: 0.384964 - COPC: 1.003365 - KLD: 1.054145
2024-11-12 05:23:53 INFO Save best model: monitor(max): 0.336359
2024-11-12 05:23:53 INFO --- 612/612 batches finished ---
2024-11-12 05:23:53 INFO Train loss: 0.459320
2024-11-12 05:23:53 INFO ************ Epoch=2 end ************
2024-11-12 05:28:13 INFO [Metrics] AUC-ROC: 0.794523 - AUC-PR: 0.589166 - ACC: 0.787695 - Precision: 0.653906 - Recall: 0.364128 - F1: 0.467775 - MCC: 0.371632 - Logloss: 0.455490 - MSE: 0.147770 - RMSE: 0.384408 - COPC: 0.990277 - KLD: 1.050804
2024-11-12 05:28:13 INFO Save best model: monitor(max): 0.339033
2024-11-12 05:28:13 INFO --- 612/612 batches finished ---
2024-11-12 05:28:13 INFO Train loss: 0.457191
2024-11-12 05:28:13 INFO ************ Epoch=3 end ************
2024-11-12 05:32:17 INFO [Metrics] AUC-ROC: 0.795456 - AUC-PR: 0.590732 - ACC: 0.788023 - Precision: 0.656033 - Recall: 0.363025 - F1: 0.467405 - MCC: 0.372254 - Logloss: 0.454682 - MSE: 0.147485 - RMSE: 0.384038 - COPC: 0.992567 - KLD: 1.048701
2024-11-12 05:32:17 INFO Save best model: monitor(max): 0.340774
2024-11-12 05:32:17 INFO --- 612/612 batches finished ---
2024-11-12 05:32:17 INFO Train loss: 0.456101
2024-11-12 05:32:17 INFO ************ Epoch=4 end ************
2024-11-12 05:36:20 INFO [Metrics] AUC-ROC: 0.796192 - AUC-PR: 0.591911 - ACC: 0.788388 - Precision: 0.661019 - Recall: 0.357383 - F1: 0.463936 - MCC: 0.371838 - Logloss: 0.454039 - MSE: 0.147264 - RMSE: 0.383750 - COPC: 1.009707 - KLD: 1.046925
2024-11-12 05:36:20 INFO Save best model: monitor(max): 0.342153
2024-11-12 05:36:20 INFO --- 612/612 batches finished ---
2024-11-12 05:36:21 INFO Train loss: 0.455341
2024-11-12 05:36:21 INFO ************ Epoch=5 end ************
2024-11-12 05:40:24 INFO [Metrics] AUC-ROC: 0.796908 - AUC-PR: 0.593066 - ACC: 0.788629 - Precision: 0.659167 - Recall: 0.362479 - F1: 0.467743 - MCC: 0.373804 - Logloss: 0.453403 - MSE: 0.147036 - RMSE: 0.383453 - COPC: 1.006844 - KLD: 1.045269
2024-11-12 05:40:24 INFO Save best model: monitor(max): 0.343505
2024-11-12 05:40:24 INFO --- 612/612 batches finished ---
2024-11-12 05:40:25 INFO Train loss: 0.454680
2024-11-12 05:40:25 INFO ************ Epoch=6 end ************
2024-11-12 05:44:33 INFO [Metrics] AUC-ROC: 0.797700 - AUC-PR: 0.594504 - ACC: 0.789013 - Precision: 0.657707 - Recall: 0.368145 - F1: 0.472059 - MCC: 0.376318 - Logloss: 0.452712 - MSE: 0.146777 - RMSE: 0.383115 - COPC: 0.995241 - KLD: 1.043549
2024-11-12 05:44:33 INFO Save best model: monitor(max): 0.344988
2024-11-12 05:44:33 INFO --- 612/612 batches finished ---
2024-11-12 05:44:33 INFO Train loss: 0.453971
2024-11-12 05:44:33 INFO ************ Epoch=7 end ************
2024-11-12 05:48:46 INFO [Metrics] AUC-ROC: 0.798354 - AUC-PR: 0.595625 - ACC: 0.789290 - Precision: 0.660575 - Recall: 0.365373 - F1: 0.470504 - MCC: 0.376384 - Logloss: 0.452133 - MSE: 0.146571 - RMSE: 0.382846 - COPC: 1.005281 - KLD: 1.042023
2024-11-12 05:48:46 INFO Save best model: monitor(max): 0.346220
2024-11-12 05:48:46 INFO --- 612/612 batches finished ---
2024-11-12 05:48:46 INFO Train loss: 0.453246
2024-11-12 05:48:46 INFO ************ Epoch=8 end ************
2024-11-12 05:52:55 INFO [Metrics] AUC-ROC: 0.799032 - AUC-PR: 0.596696 - ACC: 0.789660 - Precision: 0.658374 - Recall: 0.372214 - F1: 0.475566 - MCC: 0.379147 - Logloss: 0.451542 - MSE: 0.146360 - RMSE: 0.382570 - COPC: 0.996755 - KLD: 1.040471
2024-11-12 05:52:55 INFO Save best model: monitor(max): 0.347490
2024-11-12 05:52:55 INFO --- 612/612 batches finished ---
2024-11-12 05:52:56 INFO Train loss: 0.452594
2024-11-12 05:52:56 INFO ************ Epoch=9 end ************
2024-11-12 05:57:07 INFO [Metrics] AUC-ROC: 0.799497 - AUC-PR: 0.597483 - ACC: 0.789834 - Precision: 0.660936 - Recall: 0.369114 - F1: 0.473687 - MCC: 0.378843 - Logloss: 0.451133 - MSE: 0.146214 - RMSE: 0.382380 - COPC: 1.005180 - KLD: 1.039387
2024-11-12 05:57:07 INFO Save best model: monitor(max): 0.348364
2024-11-12 05:57:07 INFO --- 612/612 batches finished ---
2024-11-12 05:57:08 INFO Train loss: 0.452034
2024-11-12 05:57:08 INFO ************ Epoch=10 end ************
2024-11-12 06:01:18 INFO [Metrics] AUC-ROC: 0.799961 - AUC-PR: 0.598227 - ACC: 0.790181 - Precision: 0.659381 - Recall: 0.374636 - F1: 0.477803 - MCC: 0.381201 - Logloss: 0.450722 - MSE: 0.146068 - RMSE: 0.382188 - COPC: 0.996295 - KLD: 1.038300
2024-11-12 06:01:18 INFO Save best model: monitor(max): 0.349240
2024-11-12 06:01:18 INFO --- 612/612 batches finished ---
2024-11-12 06:01:18 INFO Train loss: 0.451521
2024-11-12 06:01:18 INFO ************ Epoch=11 end ************
2024-11-12 06:05:19 INFO [Metrics] AUC-ROC: 0.800378 - AUC-PR: 0.598866 - ACC: 0.790285 - Precision: 0.659022 - Recall: 0.376122 - F1: 0.478914 - MCC: 0.381867 - Logloss: 0.450352 - MSE: 0.145942 - RMSE: 0.382023 - COPC: 0.998697 - KLD: 1.037313
2024-11-12 06:05:19 INFO Save best model: monitor(max): 0.350026
2024-11-12 06:05:19 INFO --- 612/612 batches finished ---
2024-11-12 06:05:19 INFO Train loss: 0.451004
2024-11-12 06:05:19 INFO ************ Epoch=12 end ************
2024-11-12 06:09:19 INFO [Metrics] AUC-ROC: 0.800752 - AUC-PR: 0.599434 - ACC: 0.790470 - Precision: 0.659102 - Recall: 0.377469 - F1: 0.480026 - MCC: 0.382716 - Logloss: 0.450038 - MSE: 0.145826 - RMSE: 0.381872 - COPC: 0.998606 - KLD: 1.036472
2024-11-12 06:09:19 INFO Save best model: monitor(max): 0.350713
2024-11-12 06:09:19 INFO --- 612/612 batches finished ---
2024-11-12 06:09:19 INFO Train loss: 0.450537
2024-11-12 06:09:19 INFO ************ Epoch=13 end ************
2024-11-12 06:13:21 INFO [Metrics] AUC-ROC: 0.801185 - AUC-PR: 0.600059 - ACC: 0.790567 - Precision: 0.662009 - Recall: 0.373106 - F1: 0.477241 - MCC: 0.381879 - Logloss: 0.449693 - MSE: 0.145699 - RMSE: 0.381706 - COPC: 1.004471 - KLD: 1.035534
2024-11-12 06:13:21 INFO Save best model: monitor(max): 0.351492
2024-11-12 06:13:21 INFO --- 612/612 batches finished ---
2024-11-12 06:13:22 INFO Train loss: 0.450078
2024-11-12 06:13:22 INFO ************ Epoch=14 end ************
2024-11-12 06:17:41 INFO [Metrics] AUC-ROC: 0.801491 - AUC-PR: 0.600473 - ACC: 0.790701 - Precision: 0.662241 - Recall: 0.373770 - F1: 0.477844 - MCC: 0.382416 - Logloss: 0.449433 - MSE: 0.145613 - RMSE: 0.381592 - COPC: 1.008141 - KLD: 1.034782
2024-11-12 06:17:41 INFO Save best model: monitor(max): 0.352059
2024-11-12 06:17:41 INFO --- 612/612 batches finished ---
2024-11-12 06:17:41 INFO Train loss: 0.449564
2024-11-12 06:17:41 INFO ************ Epoch=15 end ************
2024-11-12 06:21:45 INFO [Metrics] AUC-ROC: 0.801794 - AUC-PR: 0.600958 - ACC: 0.790735 - Precision: 0.653268 - Recall: 0.390579 - F1: 0.488870 - MCC: 0.386842 - Logloss: 0.449299 - MSE: 0.145560 - RMSE: 0.381523 - COPC: 0.975624 - KLD: 1.034159
2024-11-12 06:21:45 INFO Save best model: monitor(max): 0.352495
2024-11-12 06:21:45 INFO --- 612/612 batches finished ---
2024-11-12 06:21:45 INFO Train loss: 0.449147
2024-11-12 06:21:45 INFO ************ Epoch=16 end ************
2024-11-12 06:25:45 INFO [Metrics] AUC-ROC: 0.802043 - AUC-PR: 0.601310 - ACC: 0.790889 - Precision: 0.660463 - Recall: 0.378405 - F1: 0.481144 - MCC: 0.384101 - Logloss: 0.448946 - MSE: 0.145441 - RMSE: 0.381368 - COPC: 0.998931 - KLD: 1.033511
2024-11-12 06:25:45 INFO Save best model: monitor(max): 0.353098
2024-11-12 06:25:45 INFO --- 612/612 batches finished ---
2024-11-12 06:25:45 INFO Train loss: 0.448738
2024-11-12 06:25:45 INFO ************ Epoch=17 end ************
2024-11-12 06:29:46 INFO [Metrics] AUC-ROC: 0.802209 - AUC-PR: 0.601524 - ACC: 0.790865 - Precision: 0.662290 - Recall: 0.374991 - F1: 0.478854 - MCC: 0.383176 - Logloss: 0.448819 - MSE: 0.145398 - RMSE: 0.381311 - COPC: 1.006724 - KLD: 1.033127
2024-11-12 06:29:46 INFO Save best model: monitor(max): 0.353390
2024-11-12 06:29:46 INFO --- 612/612 batches finished ---
2024-11-12 06:29:46 INFO Train loss: 0.448374
2024-11-12 06:29:46 INFO ************ Epoch=18 end ************
2024-11-12 06:33:46 INFO [Metrics] AUC-ROC: 0.802334 - AUC-PR: 0.601888 - ACC: 0.791044 - Precision: 0.654552 - Recall: 0.390644 - F1: 0.489280 - MCC: 0.387682 - Logloss: 0.448728 - MSE: 0.145365 - RMSE: 0.381268 - COPC: 0.985209 - KLD: 1.032816
2024-11-12 06:33:46 INFO Save best model: monitor(max): 0.353607
2024-11-12 06:33:46 INFO --- 612/612 batches finished ---
2024-11-12 06:33:46 INFO Train loss: 0.448030
2024-11-12 06:33:46 INFO ************ Epoch=19 end ************
2024-11-12 06:37:47 INFO [Metrics] AUC-ROC: 0.802443 - AUC-PR: 0.602003 - ACC: 0.791126 - Precision: 0.661195 - Recall: 0.379002 - F1: 0.481821 - MCC: 0.384904 - Logloss: 0.448610 - MSE: 0.145314 - RMSE: 0.381201 - COPC: 1.007824 - KLD: 1.032632
2024-11-12 06:37:47 INFO Save best model: monitor(max): 0.353833
2024-11-12 06:37:47 INFO --- 612/612 batches finished ---
2024-11-12 06:37:48 INFO Train loss: 0.447691
2024-11-12 06:37:48 INFO ************ Epoch=20 end ************
2024-11-12 06:41:47 INFO [Metrics] AUC-ROC: 0.802650 - AUC-PR: 0.602367 - ACC: 0.791218 - Precision: 0.659272 - Recall: 0.383207 - F1: 0.484687 - MCC: 0.386223 - Logloss: 0.448420 - MSE: 0.145246 - RMSE: 0.381112 - COPC: 1.001575 - KLD: 1.032152
2024-11-12 06:41:47 INFO Save best model: monitor(max): 0.354230
2024-11-12 06:41:48 INFO --- 612/612 batches finished ---
2024-11-12 06:41:48 INFO Train loss: 0.447342
2024-11-12 06:41:48 INFO ************ Epoch=21 end ************
2024-11-12 06:45:46 INFO [Metrics] AUC-ROC: 0.802636 - AUC-PR: 0.602421 - ACC: 0.791183 - Precision: 0.657724 - Recall: 0.385779 - F1: 0.486316 - MCC: 0.386790 - Logloss: 0.448410 - MSE: 0.145248 - RMSE: 0.381115 - COPC: 0.996680 - KLD: 1.032128
2024-11-12 06:45:46 INFO Monitor(max) STOP: 0.354227 !
2024-11-12 06:45:46 INFO Reduce learning rate on plateau: 0.000100
2024-11-12 06:45:46 INFO --- 612/612 batches finished ---
2024-11-12 06:45:47 INFO Train loss: 0.447026
2024-11-12 06:45:47 INFO ************ Epoch=22 end ************
2024-11-12 06:49:47 INFO [Metrics] AUC-ROC: 0.802965 - AUC-PR: 0.602868 - ACC: 0.791440 - Precision: 0.659544 - Recall: 0.384501 - F1: 0.485794 - MCC: 0.387156 - Logloss: 0.448169 - MSE: 0.145151 - RMSE: 0.380987 - COPC: 1.004582 - KLD: 1.031491
2024-11-12 06:49:47 INFO Save best model: monitor(max): 0.354796
2024-11-12 06:49:47 INFO --- 612/612 batches finished ---
2024-11-12 06:49:47 INFO Train loss: 0.444569
2024-11-12 06:49:47 INFO ************ Epoch=23 end ************
2024-11-12 06:53:50 INFO [Metrics] AUC-ROC: 0.802962 - AUC-PR: 0.602880 - ACC: 0.791404 - Precision: 0.659163 - Recall: 0.384909 - F1: 0.486016 - MCC: 0.387164 - Logloss: 0.448165 - MSE: 0.145149 - RMSE: 0.380984 - COPC: 1.003163 - KLD: 1.031483
2024-11-12 06:53:50 INFO Save best model: monitor(max): 0.354797
2024-11-12 06:53:50 INFO --- 612/612 batches finished ---
2024-11-12 06:53:50 INFO Train loss: 0.444168
2024-11-12 06:53:50 INFO ************ Epoch=24 end ************
2024-11-12 06:57:52 INFO [Metrics] AUC-ROC: 0.802914 - AUC-PR: 0.602792 - ACC: 0.791359 - Precision: 0.657326 - Recall: 0.387956 - F1: 0.487932 - MCC: 0.387827 - Logloss: 0.448217 - MSE: 0.145168 - RMSE: 0.381009 - COPC: 0.998704 - KLD: 1.031603
2024-11-12 06:57:52 INFO Monitor(max) STOP: 0.354698 !
2024-11-12 06:57:52 INFO Reduce learning rate on plateau: 0.000010
2024-11-12 06:57:52 INFO --- 612/612 batches finished ---
2024-11-12 06:57:52 INFO Train loss: 0.443966
2024-11-12 06:57:52 INFO ************ Epoch=25 end ************
2024-11-12 07:01:52 INFO [Metrics] AUC-ROC: 0.802906 - AUC-PR: 0.602772 - ACC: 0.791373 - Precision: 0.657911 - Recall: 0.386970 - F1: 0.487312 - MCC: 0.387608 - Logloss: 0.448236 - MSE: 0.145172 - RMSE: 0.381014 - COPC: 1.001470 - KLD: 1.031664
2024-11-12 07:01:52 INFO Monitor(max) STOP: 0.354670 !
2024-11-12 07:01:52 INFO Reduce learning rate on plateau: 0.000001
2024-11-12 07:01:52 INFO Early stopping at epoch=26
2024-11-12 07:01:52 INFO --- 612/612 batches finished ---
2024-11-12 07:01:52 INFO Train loss: 0.443535
2024-11-12 07:01:52 INFO Training finished.
2024-11-12 07:01:52 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AFM/Criteo/AFM_model.ckpt
2024-11-12 07:01:52 INFO Start evaluate model
2024-11-12 07:02:30 INFO [Metrics] AUC-ROC: 0.802962 - AUC-PR: 0.602880 - ACC: 0.791404 - Precision: 0.659163 - Recall: 0.384909 - F1: 0.486016 - MCC: 0.387164 - Logloss: 0.448165 - MSE: 0.145149 - RMSE: 0.380984 - COPC: 1.003163 - KLD: 1.031483
2024-11-12 07:02:30 INFO Start testing model
2024-11-12 07:03:08 INFO [Metrics] AUC-ROC: 0.803320 - AUC-PR: 0.603028 - ACC: 0.791428 - Precision: 0.658862 - Recall: 0.385655 - F1: 0.486528 - MCC: 0.387419 - Logloss: 0.447885 - MSE: 0.145081 - RMSE: 0.380895 - COPC: 1.002915 - KLD: 1.030505
