2024-11-18 08:27:55 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='GDCN', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=3000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-18 08:27:55 INFO Start process Criteo !
2024-11-18 08:27:55 INFO Loading Criteo dataset
2024-11-18 08:27:55 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-18 08:27:59 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-18 08:28:00 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-18 08:28:01 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-18 08:28:01 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-18 08:28:01 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-18 08:28:01 INFO Loading data done
2024-11-18 08:28:02 INFO Model: GDCN(
  (embedding): FeaturesEmbedding(
    (embed_dict): ModuleDict(
      (I1): Embedding(43, 16)
      (I2): Embedding(98, 16)
      (I3): Embedding(121, 16)
      (I4): Embedding(40, 16)
      (I5): Embedding(219, 16)
      (I6): Embedding(111, 16)
      (I7): Embedding(79, 16)
      (I8): Embedding(68, 16)
      (I9): Embedding(91, 16)
      (I10): Embedding(5, 16)
      (I11): Embedding(26, 16)
      (I12): Embedding(36, 16)
      (I13): Embedding(71, 16)
      (C1): Embedding(1445, 16)
      (C2): Embedding(553, 16)
      (C3): Embedding(157338, 16)
      (C4): Embedding(117821, 16)
      (C5): Embedding(305, 16)
      (C6): Embedding(17, 16)
      (C7): Embedding(11881, 16)
      (C8): Embedding(629, 16)
      (C9): Embedding(4, 16)
      (C10): Embedding(39529, 16)
      (C11): Embedding(5130, 16)
      (C12): Embedding(156655, 16)
      (C13): Embedding(3175, 16)
      (C14): Embedding(27, 16)
      (C15): Embedding(11042, 16)
      (C16): Embedding(148912, 16)
      (C17): Embedding(11, 16)
      (C18): Embedding(4559, 16)
      (C19): Embedding(2002, 16)
      (C20): Embedding(4, 16)
      (C21): Embedding(154563, 16)
      (C22): Embedding(17, 16)
      (C23): Embedding(16, 16)
      (C24): Embedding(53030, 16)
      (C25): Embedding(81, 16)
      (C26): Embedding(40954, 16)
    )
  )
  (embedding_dropout): Dropout(p=0.0, inplace=False)
  (cross_net): GateCorssLayer(
    (w): ModuleList(
      (0-2): 3 x Linear(in_features=624, out_features=624, bias=False)
    )
    (wg): ModuleList(
      (0-2): 3 x Linear(in_features=624, out_features=624, bias=False)
    )
    (b): ParameterList(
        (0): Parameter containing: [torch.float32 of size 624]
        (1): Parameter containing: [torch.float32 of size 624]
        (2): Parameter containing: [torch.float32 of size 624]
    )
    (activation): Sigmoid()
  )
  (mlp): Sequential(
    (0): Linear(in_features=624, out_features=400, bias=True)
    (1): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (2): ReLU()
    (3): Dropout(p=0.5, inplace=False)
    (4): Linear(in_features=400, out_features=400, bias=True)
    (5): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (6): ReLU()
    (7): Dropout(p=0.5, inplace=False)
    (8): Linear(in_features=400, out_features=400, bias=True)
    (9): BatchNorm1d(400, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
    (10): ReLU()
    (11): Dropout(p=0.5, inplace=False)
  )
  (fc): Linear(in_features=1024, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-18 08:28:02 INFO Model parameters: 17483681
2024-11-18 08:28:02 INFO Start training model
2024-11-18 08:28:02 INFO Start training: 12225 batches/epoch
2024-11-18 08:28:02 INFO ************ Epoch=1 start ************
2024-11-18 08:33:57 INFO [Metrics] AUC-ROC: 0.802006 - AUC-PR: 0.602408 - ACC: 0.791081 - Precision: 0.664168 - Recall: 0.373459 - F1: 0.478090 - MCC: 0.383393 - Logloss: 0.448751 - MSE: 0.145395 - RMSE: 0.381307 - COPC: 1.007556 - KLD: 1.033304
2024-11-18 08:33:57 INFO Save best model: monitor(max): 0.353256
2024-11-18 08:33:57 INFO --- 12225/12225 batches finished ---
2024-11-18 08:33:57 INFO Train loss: 0.456362
2024-11-18 08:33:57 INFO ************ Epoch=1 end ************
2024-11-18 08:40:16 INFO [Metrics] AUC-ROC: 0.805758 - AUC-PR: 0.608124 - ACC: 0.792656 - Precision: 0.654001 - Recall: 0.405068 - F1: 0.500279 - MCC: 0.395747 - Logloss: 0.445740 - MSE: 0.144293 - RMSE: 0.379859 - COPC: 0.987623 - KLD: 1.025052
2024-11-18 08:40:16 INFO Save best model: monitor(max): 0.360019
2024-11-18 08:40:16 INFO --- 12225/12225 batches finished ---
2024-11-18 08:40:16 INFO Train loss: 0.442151
2024-11-18 08:40:16 INFO ************ Epoch=2 end ************
2024-11-18 08:46:24 INFO [Metrics] AUC-ROC: 0.805069 - AUC-PR: 0.606989 - ACC: 0.792195 - Precision: 0.647287 - Recall: 0.415234 - F1: 0.505921 - MCC: 0.397298 - Logloss: 0.446821 - MSE: 0.144598 - RMSE: 0.380260 - COPC: 0.978541 - KLD: 1.027641
2024-11-18 08:46:24 INFO Monitor(max) STOP: 0.358248 !
2024-11-18 08:46:24 INFO Reduce learning rate on plateau: 0.000100
2024-11-18 08:46:24 INFO --- 12225/12225 batches finished ---
2024-11-18 08:46:24 INFO Train loss: 0.434315
2024-11-18 08:46:24 INFO ************ Epoch=3 end ************
2024-11-18 08:52:23 INFO [Metrics] AUC-ROC: 0.802419 - AUC-PR: 0.601814 - ACC: 0.790763 - Precision: 0.640353 - Recall: 0.418335 - F1: 0.506064 - MCC: 0.394537 - Logloss: 0.451898 - MSE: 0.145874 - RMSE: 0.381934 - COPC: 1.001168 - KLD: 1.041033
2024-11-18 08:52:23 INFO Monitor(max) STOP: 0.350521 !
2024-11-18 08:52:23 INFO Reduce learning rate on plateau: 0.000010
2024-11-18 08:52:23 INFO Early stopping at epoch=4
2024-11-18 08:52:23 INFO --- 12225/12225 batches finished ---
2024-11-18 08:52:23 INFO Train loss: 0.417000
2024-11-18 08:52:23 INFO Training finished.
2024-11-18 08:52:23 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/GDCN/Criteo/GDCN_model_seed2019.ckpt
2024-11-18 08:52:23 INFO Start evaluate model
2024-11-18 08:53:00 INFO [Metrics] AUC-ROC: 0.805758 - AUC-PR: 0.608124 - ACC: 0.792656 - Precision: 0.654001 - Recall: 0.405068 - F1: 0.500279 - MCC: 0.395747 - Logloss: 0.445740 - MSE: 0.144293 - RMSE: 0.379859 - COPC: 0.987623 - KLD: 1.025052
2024-11-18 08:53:00 INFO Start testing model
2024-11-18 08:53:37 INFO [Metrics] AUC-ROC: 0.806230 - AUC-PR: 0.608572 - ACC: 0.792727 - Precision: 0.653710 - Recall: 0.406247 - F1: 0.501091 - MCC: 0.396246 - Logloss: 0.445318 - MSE: 0.144182 - RMSE: 0.379714 - COPC: 0.987058 - KLD: 1.023684
