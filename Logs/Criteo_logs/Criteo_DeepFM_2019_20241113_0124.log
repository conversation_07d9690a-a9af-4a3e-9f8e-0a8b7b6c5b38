2024-11-13 01:24:26 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='DeepFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-13 01:24:26 INFO Start process Criteo !
2024-11-13 01:24:26 INFO Loading Criteo dataset
2024-11-13 01:24:26 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-13 01:24:31 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-13 01:24:32 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-13 01:24:33 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-13 01:24:33 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 01:24:33 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 01:24:33 INFO Loading data done
2024-11-13 01:24:34 INFO Model: DeepFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 16)
        (I2): Embedding(98, 16)
        (I3): Embedding(121, 16)
        (I4): Embedding(40, 16)
        (I5): Embedding(219, 16)
        (I6): Embedding(111, 16)
        (I7): Embedding(79, 16)
        (I8): Embedding(68, 16)
        (I9): Embedding(91, 16)
        (I10): Embedding(5, 16)
        (I11): Embedding(26, 16)
        (I12): Embedding(36, 16)
        (I13): Embedding(71, 16)
        (C1): Embedding(1445, 16)
        (C2): Embedding(553, 16)
        (C3): Embedding(157338, 16)
        (C4): Embedding(117821, 16)
        (C5): Embedding(305, 16)
        (C6): Embedding(17, 16)
        (C7): Embedding(11881, 16)
        (C8): Embedding(629, 16)
        (C9): Embedding(4, 16)
        (C10): Embedding(39529, 16)
        (C11): Embedding(5130, 16)
        (C12): Embedding(156655, 16)
        (C13): Embedding(3175, 16)
        (C14): Embedding(27, 16)
        (C15): Embedding(11042, 16)
        (C16): Embedding(148912, 16)
        (C17): Embedding(11, 16)
        (C18): Embedding(4559, 16)
        (C19): Embedding(2002, 16)
        (C20): Embedding(4, 16)
        (C21): Embedding(154563, 16)
        (C22): Embedding(17, 16)
        (C23): Embedding(16, 16)
        (C24): Embedding(53030, 16)
        (C25): Embedding(81, 16)
        (C26): Embedding(40954, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (fm_layer): FM_Layer_v2(
    (inner_product_layer): InnerProductLayer_v2()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (I1): Embedding(43, 1, padding_idx=42)
          (I2): Embedding(98, 1, padding_idx=97)
          (I3): Embedding(121, 1, padding_idx=120)
          (I4): Embedding(40, 1, padding_idx=39)
          (I5): Embedding(219, 1, padding_idx=218)
          (I6): Embedding(111, 1, padding_idx=110)
          (I7): Embedding(79, 1, padding_idx=78)
          (I8): Embedding(68, 1, padding_idx=67)
          (I9): Embedding(91, 1, padding_idx=90)
          (I10): Embedding(5, 1, padding_idx=4)
          (I11): Embedding(26, 1, padding_idx=25)
          (I12): Embedding(36, 1, padding_idx=35)
          (I13): Embedding(71, 1, padding_idx=70)
          (C1): Embedding(1445, 1, padding_idx=1444)
          (C2): Embedding(553, 1, padding_idx=552)
          (C3): Embedding(157338, 1, padding_idx=157337)
          (C4): Embedding(117821, 1, padding_idx=117820)
          (C5): Embedding(305, 1, padding_idx=304)
          (C6): Embedding(17, 1, padding_idx=16)
          (C7): Embedding(11881, 1, padding_idx=11880)
          (C8): Embedding(629, 1, padding_idx=628)
          (C9): Embedding(4, 1, padding_idx=3)
          (C10): Embedding(39529, 1, padding_idx=39528)
          (C11): Embedding(5130, 1, padding_idx=5129)
          (C12): Embedding(156655, 1, padding_idx=156654)
          (C13): Embedding(3175, 1, padding_idx=3174)
          (C14): Embedding(27, 1, padding_idx=26)
          (C15): Embedding(11042, 1, padding_idx=11041)
          (C16): Embedding(148912, 1, padding_idx=148911)
          (C17): Embedding(11, 1, padding_idx=10)
          (C18): Embedding(4559, 1, padding_idx=4558)
          (C19): Embedding(2002, 1, padding_idx=2001)
          (C20): Embedding(4, 1, padding_idx=3)
          (C21): Embedding(154563, 1, padding_idx=154562)
          (C22): Embedding(17, 1, padding_idx=16)
          (C23): Embedding(16, 1, padding_idx=15)
          (C24): Embedding(53030, 1, padding_idx=53029)
          (C25): Embedding(81, 1, padding_idx=80)
          (C26): Embedding(40954, 1, padding_idx=40953)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=624, out_features=1000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=1000, out_features=1000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=1000, out_features=1000, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.2, inplace=False)
      (9): Linear(in_features=1000, out_features=1000, bias=True)
      (10): ReLU()
      (11): Dropout(p=0.2, inplace=False)
      (12): Linear(in_features=1000, out_features=1000, bias=True)
      (13): ReLU()
      (14): Dropout(p=0.2, inplace=False)
      (15): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-13 01:24:34 INFO Model parameters: 20112037
2024-11-13 01:24:34 INFO Start training model
2024-11-13 01:24:34 INFO Start training: 3668 batches/epoch
2024-11-13 01:24:34 INFO ************ Epoch=1 start ************
2024-11-13 01:30:01 INFO [Metrics] AUC-ROC: 0.805598 - AUC-PR: 0.607843 - ACC: 0.792577 - Precision: 0.663285 - Recall: 0.386836 - F1: 0.488672 - MCC: 0.390839 - Logloss: 0.445969 - MSE: 0.144371 - RMSE: 0.379962 - COPC: 0.971827 - KLD: 1.025725
2024-11-13 01:30:01 INFO Save best model: monitor(max): 0.359629
2024-11-13 01:30:01 INFO --- 3668/3668 batches finished ---
2024-11-13 01:30:01 INFO Train loss: 0.459861
2024-11-13 01:30:01 INFO ************ Epoch=1 end ************
2024-11-13 01:35:28 INFO [Metrics] AUC-ROC: 0.807599 - AUC-PR: 0.611129 - ACC: 0.793493 - Precision: 0.670217 - Recall: 0.381998 - F1: 0.486633 - MCC: 0.392171 - Logloss: 0.443937 - MSE: 0.143701 - RMSE: 0.379080 - COPC: 1.025064 - KLD: 1.020262
2024-11-13 01:35:28 INFO Save best model: monitor(max): 0.363662
2024-11-13 01:35:28 INFO --- 3668/3668 batches finished ---
2024-11-13 01:35:29 INFO Train loss: 0.455019
2024-11-13 01:35:29 INFO ************ Epoch=2 end ************
2024-11-13 01:40:58 INFO [Metrics] AUC-ROC: 0.808553 - AUC-PR: 0.613094 - ACC: 0.793920 - Precision: 0.661128 - Recall: 0.401495 - F1: 0.499593 - MCC: 0.398157 - Logloss: 0.442921 - MSE: 0.143325 - RMSE: 0.378583 - COPC: 0.992754 - KLD: 1.017863
2024-11-13 01:40:58 INFO Save best model: monitor(max): 0.365632
2024-11-13 01:40:58 INFO --- 3668/3668 batches finished ---
2024-11-13 01:40:58 INFO Train loss: 0.453740
2024-11-13 01:40:58 INFO ************ Epoch=3 end ************
2024-11-13 01:46:25 INFO [Metrics] AUC-ROC: 0.809007 - AUC-PR: 0.613814 - ACC: 0.794375 - Precision: 0.664216 - Recall: 0.399372 - F1: 0.498820 - MCC: 0.398837 - Logloss: 0.442618 - MSE: 0.143177 - RMSE: 0.378388 - COPC: 0.981164 - KLD: 1.017202
2024-11-13 01:46:25 INFO Save best model: monitor(max): 0.366389
2024-11-13 01:46:26 INFO --- 3668/3668 batches finished ---
2024-11-13 01:46:26 INFO Train loss: 0.453010
2024-11-13 01:46:26 INFO ************ Epoch=4 end ************
2024-11-13 01:51:54 INFO [Metrics] AUC-ROC: 0.809371 - AUC-PR: 0.614402 - ACC: 0.794545 - Precision: 0.664899 - Recall: 0.399469 - F1: 0.499088 - MCC: 0.399319 - Logloss: 0.442098 - MSE: 0.143043 - RMSE: 0.378211 - COPC: 0.999301 - KLD: 1.015820
2024-11-13 01:51:54 INFO Save best model: monitor(max): 0.367273
2024-11-13 01:51:54 INFO --- 3668/3668 batches finished ---
2024-11-13 01:51:54 INFO Train loss: 0.452546
2024-11-13 01:51:54 INFO ************ Epoch=5 end ************
2024-11-13 01:57:20 INFO [Metrics] AUC-ROC: 0.809468 - AUC-PR: 0.614634 - ACC: 0.794503 - Precision: 0.663549 - Recall: 0.401615 - F1: 0.500376 - MCC: 0.399743 - Logloss: 0.442284 - MSE: 0.143044 - RMSE: 0.378212 - COPC: 0.977112 - KLD: 1.016352
2024-11-13 01:57:20 INFO Monitor(max) STOP: 0.367184 !
2024-11-13 01:57:20 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 01:57:20 INFO --- 3668/3668 batches finished ---
2024-11-13 01:57:20 INFO Train loss: 0.452230
2024-11-13 01:57:20 INFO ************ Epoch=6 end ************
2024-11-13 02:02:47 INFO [Metrics] AUC-ROC: 0.813337 - AUC-PR: 0.620780 - ACC: 0.796328 - Precision: 0.664772 - Recall: 0.413737 - F1: 0.510039 - MCC: 0.407635 - Logloss: 0.438465 - MSE: 0.141780 - RMSE: 0.376536 - COPC: 0.989501 - KLD: 1.006100
2024-11-13 02:02:47 INFO Save best model: monitor(max): 0.374873
2024-11-13 02:02:47 INFO --- 3668/3668 batches finished ---
2024-11-13 02:02:47 INFO Train loss: 0.440983
2024-11-13 02:02:47 INFO ************ Epoch=7 end ************
2024-11-13 02:08:17 INFO [Metrics] AUC-ROC: 0.813789 - AUC-PR: 0.621457 - ACC: 0.796540 - Precision: 0.664401 - Recall: 0.416109 - F1: 0.511727 - MCC: 0.408788 - Logloss: 0.438040 - MSE: 0.141636 - RMSE: 0.376346 - COPC: 0.990474 - KLD: 1.004940
2024-11-13 02:08:17 INFO Save best model: monitor(max): 0.375749
2024-11-13 02:08:17 INFO --- 3668/3668 batches finished ---
2024-11-13 02:08:17 INFO Train loss: 0.436384
2024-11-13 02:08:17 INFO ************ Epoch=8 end ************
2024-11-13 02:13:46 INFO [Metrics] AUC-ROC: 0.813846 - AUC-PR: 0.621490 - ACC: 0.796535 - Precision: 0.663141 - Recall: 0.418494 - F1: 0.513150 - MCC: 0.409380 - Logloss: 0.438000 - MSE: 0.141629 - RMSE: 0.376336 - COPC: 0.989281 - KLD: 1.004794
2024-11-13 02:13:46 INFO Save best model: monitor(max): 0.375845
2024-11-13 02:13:46 INFO --- 3668/3668 batches finished ---
2024-11-13 02:13:46 INFO Train loss: 0.434269
2024-11-13 02:13:46 INFO ************ Epoch=9 end ************
2024-11-13 02:19:09 INFO [Metrics] AUC-ROC: 0.813701 - AUC-PR: 0.621165 - ACC: 0.796309 - Precision: 0.660828 - Recall: 0.421215 - F1: 0.514491 - MCC: 0.409484 - Logloss: 0.438216 - MSE: 0.141708 - RMSE: 0.376442 - COPC: 0.988758 - KLD: 1.005223
2024-11-13 02:19:09 INFO Monitor(max) STOP: 0.375485 !
2024-11-13 02:19:09 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 02:19:09 INFO --- 3668/3668 batches finished ---
2024-11-13 02:19:09 INFO Train loss: 0.432618
2024-11-13 02:19:09 INFO ************ Epoch=10 end ************
2024-11-13 02:24:27 INFO [Metrics] AUC-ROC: 0.813334 - AUC-PR: 0.620639 - ACC: 0.796009 - Precision: 0.656924 - Recall: 0.426692 - F1: 0.517350 - MCC: 0.410134 - Logloss: 0.438656 - MSE: 0.141859 - RMSE: 0.376642 - COPC: 0.987265 - KLD: 1.006376
2024-11-13 02:24:27 INFO Monitor(max) STOP: 0.374678 !
2024-11-13 02:24:27 INFO Reduce learning rate on plateau: 0.000001
2024-11-13 02:24:27 INFO Early stopping at epoch=11
2024-11-13 02:24:27 INFO --- 3668/3668 batches finished ---
2024-11-13 02:24:27 INFO Train loss: 0.428387
2024-11-13 02:24:27 INFO Training finished.
2024-11-13 02:24:27 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/DeepFM/Criteo/DeepFM_model_seed2019.ckpt
2024-11-13 02:24:27 INFO Start evaluate model
2024-11-13 02:25:05 INFO [Metrics] AUC-ROC: 0.813846 - AUC-PR: 0.621490 - ACC: 0.796535 - Precision: 0.663141 - Recall: 0.418494 - F1: 0.513150 - MCC: 0.409380 - Logloss: 0.438000 - MSE: 0.141629 - RMSE: 0.376336 - COPC: 0.989281 - KLD: 1.004794
2024-11-13 02:25:05 INFO Start testing model
2024-11-13 02:25:41 INFO [Metrics] AUC-ROC: 0.814278 - AUC-PR: 0.621928 - ACC: 0.796697 - Precision: 0.662967 - Recall: 0.420110 - F1: 0.514311 - MCC: 0.410211 - Logloss: 0.437635 - MSE: 0.141508 - RMSE: 0.376175 - COPC: 0.988795 - KLD: 1.003691
