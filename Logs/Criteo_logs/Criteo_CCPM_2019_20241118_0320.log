2024-11-18 03:20:08 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='CCPM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=6000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-18 03:20:08 INFO Start process Criteo !
2024-11-18 03:20:08 INFO Loading Criteo dataset
2024-11-18 03:20:08 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-18 03:20:13 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-18 03:20:14 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-18 03:20:15 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-18 03:20:15 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-18 03:20:15 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-18 03:20:15 INFO Loading data done
2024-11-18 03:20:15 INFO Model: CCPM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (conv_layer): CCPM_ConvLayer(
    (conv_layer): Sequential(
      (0): ZeroPad2d((0, 0, 6, 6))
      (1): Conv2d(1, 64, kernel_size=(7, 1), stride=(1, 1))
      (2): KMaxPooling()
      (3): Tanh()
      (4): ZeroPad2d((0, 0, 4, 4))
      (5): Conv2d(64, 128, kernel_size=(5, 1), stride=(1, 1))
      (6): KMaxPooling()
      (7): Tanh()
      (8): ZeroPad2d((0, 0, 2, 2))
      (9): Conv2d(128, 256, kernel_size=(3, 1), stride=(1, 1))
      (10): KMaxPooling()
      (11): Tanh()
    )
  )
  (fc): Linear(in_features=12288, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-18 03:20:15 INFO Model parameters: 14723777
2024-11-18 03:20:15 INFO Start training model
2024-11-18 03:20:16 INFO Start training: 6113 batches/epoch
2024-11-18 03:20:16 INFO ************ Epoch=1 start ************
2024-11-18 05:03:41 INFO [Metrics] AUC-ROC: 0.801484 - AUC-PR: 0.600837 - ACC: 0.790766 - Precision: 0.663434 - Recall: 0.372226 - F1: 0.476889 - MCC: 0.382212 - Logloss: 0.449521 - MSE: 0.145633 - RMSE: 0.381619 - COPC: 1.024630 - KLD: 1.034994
2024-11-18 05:03:41 INFO Save best model: monitor(max): 0.351963
2024-11-18 05:03:41 INFO --- 6113/6113 batches finished ---
2024-11-18 05:03:41 INFO Train loss: 0.464935
2024-11-18 05:03:41 INFO ************ Epoch=1 end ************
2024-11-18 06:46:14 INFO [Metrics] AUC-ROC: 0.803016 - AUC-PR: 0.602912 - ACC: 0.791261 - Precision: 0.650230 - Recall: 0.401066 - F1: 0.496121 - MCC: 0.391031 - Logloss: 0.448226 - MSE: 0.145215 - RMSE: 0.381071 - COPC: 0.972285 - KLD: 1.031091
2024-11-18 06:46:14 INFO Save best model: monitor(max): 0.354790
2024-11-18 06:46:14 INFO --- 6113/6113 batches finished ---
2024-11-18 06:46:15 INFO Train loss: 0.459301
2024-11-18 06:46:15 INFO ************ Epoch=2 end ************
2024-11-18 08:36:44 INFO [Metrics] AUC-ROC: 0.803605 - AUC-PR: 0.604444 - ACC: 0.791683 - Precision: 0.662426 - Recall: 0.381264 - F1: 0.483973 - MCC: 0.387000 - Logloss: 0.447448 - MSE: 0.144922 - RMSE: 0.380687 - COPC: 1.008924 - KLD: 1.029711
2024-11-18 08:36:44 INFO Save best model: monitor(max): 0.356156
2024-11-18 08:36:44 INFO --- 6113/6113 batches finished ---
2024-11-18 08:36:44 INFO Train loss: 0.458352
2024-11-18 08:36:44 INFO ************ Epoch=3 end ************
2024-11-18 10:59:37 INFO [Metrics] AUC-ROC: 0.803866 - AUC-PR: 0.604990 - ACC: 0.791877 - Precision: 0.666030 - Recall: 0.376533 - F1: 0.481088 - MCC: 0.386360 - Logloss: 0.447297 - MSE: 0.144860 - RMSE: 0.380605 - COPC: 1.021557 - KLD: 1.029209
2024-11-18 10:59:37 INFO Save best model: monitor(max): 0.356569
2024-11-18 10:59:37 INFO --- 6113/6113 batches finished ---
2024-11-18 10:59:38 INFO Train loss: 0.457900
2024-11-18 10:59:38 INFO ************ Epoch=4 end ************
2024-11-18 13:46:19 INFO [Metrics] AUC-ROC: 0.803939 - AUC-PR: 0.605078 - ACC: 0.791836 - Precision: 0.656914 - Recall: 0.392626 - F1: 0.491495 - MCC: 0.390317 - Logloss: 0.447145 - MSE: 0.144830 - RMSE: 0.380565 - COPC: 0.994190 - KLD: 1.028862
2024-11-18 13:46:19 INFO Save best model: monitor(max): 0.356794
2024-11-18 13:46:19 INFO --- 6113/6113 batches finished ---
2024-11-18 13:46:20 INFO Train loss: 0.457632
2024-11-18 13:46:20 INFO ************ Epoch=5 end ************
2024-11-18 15:34:38 INFO [Metrics] AUC-ROC: 0.804299 - AUC-PR: 0.605655 - ACC: 0.792020 - Precision: 0.667355 - Recall: 0.375413 - F1: 0.480517 - MCC: 0.386486 - Logloss: 0.447067 - MSE: 0.144778 - RMSE: 0.380497 - COPC: 1.031665 - KLD: 1.028401
2024-11-18 15:34:38 INFO Save best model: monitor(max): 0.357232
2024-11-18 15:34:38 INFO --- 6113/6113 batches finished ---
2024-11-18 15:34:38 INFO Train loss: 0.457427
2024-11-18 15:34:38 INFO ************ Epoch=6 end ************
2024-11-18 17:16:59 INFO [Metrics] AUC-ROC: 0.804340 - AUC-PR: 0.605885 - ACC: 0.792043 - Precision: 0.658233 - Recall: 0.391811 - F1: 0.491223 - MCC: 0.390659 - Logloss: 0.446741 - MSE: 0.144675 - RMSE: 0.380361 - COPC: 0.995231 - KLD: 1.027913
2024-11-18 17:16:59 INFO Save best model: monitor(max): 0.357599
2024-11-18 17:17:00 INFO --- 6113/6113 batches finished ---
2024-11-18 17:17:00 INFO Train loss: 0.457240
2024-11-18 17:17:00 INFO ************ Epoch=7 end ************
2024-11-18 18:59:11 INFO [Metrics] AUC-ROC: 0.804814 - AUC-PR: 0.606236 - ACC: 0.791982 - Precision: 0.650741 - Recall: 0.406092 - F1: 0.500099 - MCC: 0.394263 - Logloss: 0.446546 - MSE: 0.144624 - RMSE: 0.380294 - COPC: 0.976577 - KLD: 1.026832
2024-11-18 18:59:11 INFO Save best model: monitor(max): 0.358268
2024-11-18 18:59:11 INFO --- 6113/6113 batches finished ---
2024-11-18 18:59:11 INFO Train loss: 0.457049
2024-11-18 18:59:11 INFO ************ Epoch=8 end ************
2024-11-18 20:41:13 INFO [Metrics] AUC-ROC: 0.804755 - AUC-PR: 0.606225 - ACC: 0.791874 - Precision: 0.646755 - Recall: 0.413635 - F1: 0.504570 - MCC: 0.396038 - Logloss: 0.447245 - MSE: 0.144847 - RMSE: 0.380588 - COPC: 0.943221 - KLD: 1.027660
2024-11-18 20:41:13 INFO Monitor(max) STOP: 0.357509 !
2024-11-18 20:41:13 INFO Reduce learning rate on plateau: 0.000100
2024-11-18 20:41:13 INFO --- 6113/6113 batches finished ---
2024-11-18 20:41:14 INFO Train loss: 0.456910
2024-11-18 20:41:14 INFO ************ Epoch=9 end ************
2024-11-18 22:23:20 INFO [Metrics] AUC-ROC: 0.808784 - AUC-PR: 0.612635 - ACC: 0.793974 - Precision: 0.663388 - Recall: 0.397722 - F1: 0.497298 - MCC: 0.397348 - Logloss: 0.442822 - MSE: 0.143302 - RMSE: 0.378552 - COPC: 1.004678 - KLD: 1.017412
2024-11-18 22:23:20 INFO Save best model: monitor(max): 0.365962
2024-11-18 22:23:20 INFO --- 6113/6113 batches finished ---
2024-11-18 22:23:20 INFO Train loss: 0.447132
2024-11-18 22:23:20 INFO ************ Epoch=10 end ************
2024-11-19 00:07:31 INFO [Metrics] AUC-ROC: 0.809471 - AUC-PR: 0.613703 - ACC: 0.794300 - Precision: 0.661275 - Recall: 0.404260 - F1: 0.501770 - MCC: 0.399872 - Logloss: 0.442200 - MSE: 0.143090 - RMSE: 0.378273 - COPC: 0.990917 - KLD: 1.015672
2024-11-19 00:07:31 INFO Save best model: monitor(max): 0.367271
2024-11-19 00:07:31 INFO --- 6113/6113 batches finished ---
2024-11-19 00:07:31 INFO Train loss: 0.443106
2024-11-19 00:07:31 INFO ************ Epoch=11 end ************
2024-11-19 01:57:04 INFO [Metrics] AUC-ROC: 0.809629 - AUC-PR: 0.613920 - ACC: 0.794373 - Precision: 0.661884 - Recall: 0.403688 - F1: 0.501504 - MCC: 0.399919 - Logloss: 0.442115 - MSE: 0.143055 - RMSE: 0.378227 - COPC: 1.003395 - KLD: 1.015457
2024-11-19 01:57:04 INFO Save best model: monitor(max): 0.367514
2024-11-19 01:57:04 INFO --- 6113/6113 batches finished ---
2024-11-19 01:57:04 INFO Train loss: 0.441208
2024-11-19 01:57:04 INFO ************ Epoch=12 end ************
2024-11-19 03:43:29 INFO [Metrics] AUC-ROC: 0.809633 - AUC-PR: 0.613878 - ACC: 0.794337 - Precision: 0.658920 - Recall: 0.409086 - F1: 0.504782 - MCC: 0.401208 - Logloss: 0.442202 - MSE: 0.143071 - RMSE: 0.378248 - COPC: 1.000054 - KLD: 1.015671
2024-11-19 03:43:29 INFO Monitor(max) STOP: 0.367431 !
2024-11-19 03:43:29 INFO Reduce learning rate on plateau: 0.000010
2024-11-19 03:43:29 INFO --- 6113/6113 batches finished ---
2024-11-19 03:43:29 INFO Train loss: 0.439736
2024-11-19 03:43:29 INFO ************ Epoch=13 end ************
2024-11-19 05:26:23 INFO [Metrics] AUC-ROC: 0.809149 - AUC-PR: 0.612975 - ACC: 0.793939 - Precision: 0.654141 - Recall: 0.415415 - F1: 0.508136 - MCC: 0.401828 - Logloss: 0.442924 - MSE: 0.143313 - RMSE: 0.378567 - COPC: 0.997200 - KLD: 1.017394
2024-11-19 05:26:23 INFO Monitor(max) STOP: 0.366225 !
2024-11-19 05:26:23 INFO Reduce learning rate on plateau: 0.000001
2024-11-19 05:26:23 INFO Early stopping at epoch=14
2024-11-19 05:26:23 INFO --- 6113/6113 batches finished ---
2024-11-19 05:26:23 INFO Train loss: 0.435024
2024-11-19 05:26:23 INFO Training finished.
2024-11-19 05:26:23 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/CCPM/Criteo/CCPM_model_seed2019.ckpt
2024-11-19 05:26:23 INFO Start evaluate model
2024-11-19 05:32:51 INFO [Metrics] AUC-ROC: 0.809629 - AUC-PR: 0.613920 - ACC: 0.794373 - Precision: 0.661884 - Recall: 0.403688 - F1: 0.501504 - MCC: 0.399919 - Logloss: 0.442115 - MSE: 0.143055 - RMSE: 0.378227 - COPC: 1.003395 - KLD: 1.015457
2024-11-19 05:32:51 INFO Start testing model
2024-11-19 05:39:19 INFO [Metrics] AUC-ROC: 0.809893 - AUC-PR: 0.614213 - ACC: 0.794516 - Precision: 0.661899 - Recall: 0.404804 - F1: 0.502369 - MCC: 0.400584 - Logloss: 0.441887 - MSE: 0.142981 - RMSE: 0.378128 - COPC: 1.002876 - KLD: 1.014758
