2024-11-18 01:28:44 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='LSTM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-18 01:28:44 INFO Start process Criteo !
2024-11-18 01:28:44 INFO Loading Criteo dataset
2024-11-18 01:28:44 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-18 01:28:49 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-18 01:28:49 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-18 01:28:50 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-18 01:28:51 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-18 01:28:51 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-18 01:28:51 INFO Loading data done
2024-11-18 01:28:51 INFO Model: LSTM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (lstm): LSTM(16, 128, num_layers=2, batch_first=True, dropout=0.2)
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=736, out_features=64, bias=True)
      (1): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Dropout(p=0.2, inplace=False)
      (4): Linear(in_features=64, out_features=32, bias=True)
      (5): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (6): ReLU()
      (7): Dropout(p=0.2, inplace=False)
      (8): Linear(in_features=32, out_features=1, bias=True)
      (9): Sigmoid()
    )
  )
)
2024-11-18 01:28:51 INFO Model parameters: 14827649
2024-11-18 01:28:51 INFO Start training model
2024-11-18 01:28:51 INFO Start training: 3668 batches/epoch
2024-11-18 01:28:51 INFO ************ Epoch=1 start ************
2024-11-18 01:33:02 INFO [Metrics] AUC-ROC: 0.795104 - AUC-PR: 0.589474 - ACC: 0.787681 - Precision: 0.658969 - Recall: 0.355151 - F1: 0.461550 - MCC: 0.369278 - Logloss: 0.455274 - MSE: 0.147712 - RMSE: 0.384333 - COPC: 0.995360 - KLD: 1.049836
2024-11-18 01:33:02 INFO Save best model: monitor(max): 0.339830
2024-11-18 01:33:02 INFO --- 3668/3668 batches finished ---
2024-11-18 01:33:02 INFO Train loss: 0.470333
2024-11-18 01:33:02 INFO ************ Epoch=1 end ************
2024-11-18 01:37:20 INFO [Metrics] AUC-ROC: 0.799665 - AUC-PR: 0.597108 - ACC: 0.789703 - Precision: 0.656870 - Recall: 0.375276 - F1: 0.477660 - MCC: 0.380053 - Logloss: 0.451116 - MSE: 0.146236 - RMSE: 0.382408 - COPC: 1.000003 - KLD: 1.039028
2024-11-18 01:37:20 INFO Save best model: monitor(max): 0.348549
2024-11-18 01:37:20 INFO --- 3668/3668 batches finished ---
2024-11-18 01:37:20 INFO Train loss: 0.454361
2024-11-18 01:37:20 INFO ************ Epoch=2 end ************
2024-11-18 01:41:38 INFO [Metrics] AUC-ROC: 0.801676 - AUC-PR: 0.600638 - ACC: 0.790722 - Precision: 0.657098 - Recall: 0.383182 - F1: 0.484077 - MCC: 0.384871 - Logloss: 0.449314 - MSE: 0.145577 - RMSE: 0.381545 - COPC: 1.003204 - KLD: 1.034402
2024-11-18 01:41:38 INFO Save best model: monitor(max): 0.352363
2024-11-18 01:41:38 INFO --- 3668/3668 batches finished ---
2024-11-18 01:41:38 INFO Train loss: 0.450290
2024-11-18 01:41:38 INFO ************ Epoch=3 end ************
2024-11-18 01:45:57 INFO [Metrics] AUC-ROC: 0.802723 - AUC-PR: 0.602474 - ACC: 0.791065 - Precision: 0.648929 - Recall: 0.402091 - F1: 0.496525 - MCC: 0.390796 - Logloss: 0.448483 - MSE: 0.145267 - RMSE: 0.381139 - COPC: 0.981529 - KLD: 1.032094
2024-11-18 01:45:57 INFO Save best model: monitor(max): 0.354239
2024-11-18 01:45:57 INFO --- 3668/3668 batches finished ---
2024-11-18 01:45:57 INFO Train loss: 0.447824
2024-11-18 01:45:57 INFO ************ Epoch=4 end ************
2024-11-18 01:50:17 INFO [Metrics] AUC-ROC: 0.803265 - AUC-PR: 0.603473 - ACC: 0.791472 - Precision: 0.660120 - Recall: 0.383711 - F1: 0.485319 - MCC: 0.387043 - Logloss: 0.447971 - MSE: 0.145061 - RMSE: 0.380869 - COPC: 0.996021 - KLD: 1.031017
2024-11-18 01:50:17 INFO Save best model: monitor(max): 0.355294
2024-11-18 01:50:17 INFO --- 3668/3668 batches finished ---
2024-11-18 01:50:17 INFO Train loss: 0.446072
2024-11-18 01:50:17 INFO ************ Epoch=5 end ************
2024-11-18 01:54:35 INFO [Metrics] AUC-ROC: 0.803661 - AUC-PR: 0.604157 - ACC: 0.791717 - Precision: 0.661712 - Recall: 0.382808 - F1: 0.485024 - MCC: 0.387483 - Logloss: 0.447708 - MSE: 0.144944 - RMSE: 0.380716 - COPC: 1.013298 - KLD: 1.030317
2024-11-18 01:54:35 INFO Save best model: monitor(max): 0.355953
2024-11-18 01:54:35 INFO --- 3668/3668 batches finished ---
2024-11-18 01:54:35 INFO Train loss: 0.444672
2024-11-18 01:54:35 INFO ************ Epoch=6 end ************
2024-11-18 01:58:51 INFO [Metrics] AUC-ROC: 0.803981 - AUC-PR: 0.604719 - ACC: 0.791853 - Precision: 0.654631 - Recall: 0.397180 - F1: 0.494397 - MCC: 0.391553 - Logloss: 0.447435 - MSE: 0.144806 - RMSE: 0.380533 - COPC: 0.993391 - KLD: 1.029796
2024-11-18 01:58:51 INFO Save best model: monitor(max): 0.356546
2024-11-18 01:58:51 INFO --- 3668/3668 batches finished ---
2024-11-18 01:58:51 INFO Train loss: 0.443449
2024-11-18 01:58:51 INFO ************ Epoch=7 end ************
2024-11-18 02:02:55 INFO [Metrics] AUC-ROC: 0.804223 - AUC-PR: 0.605120 - ACC: 0.792016 - Precision: 0.657355 - Recall: 0.393252 - F1: 0.492108 - MCC: 0.390960 - Logloss: 0.447335 - MSE: 0.144729 - RMSE: 0.380432 - COPC: 1.001433 - KLD: 1.029705
2024-11-18 02:02:55 INFO Save best model: monitor(max): 0.356888
2024-11-18 02:02:56 INFO --- 3668/3668 batches finished ---
2024-11-18 02:02:56 INFO Train loss: 0.442374
2024-11-18 02:02:56 INFO ************ Epoch=8 end ************
2024-11-18 02:07:06 INFO [Metrics] AUC-ROC: 0.804239 - AUC-PR: 0.605257 - ACC: 0.792081 - Precision: 0.654861 - Recall: 0.398608 - F1: 0.495568 - MCC: 0.392531 - Logloss: 0.447343 - MSE: 0.144702 - RMSE: 0.380398 - COPC: 1.002943 - KLD: 1.029825
2024-11-18 02:07:06 INFO Save best model: monitor(max): 0.356897
2024-11-18 02:07:06 INFO --- 3668/3668 batches finished ---
2024-11-18 02:07:06 INFO Train loss: 0.441361
2024-11-18 02:07:06 INFO ************ Epoch=9 end ************
2024-11-18 02:11:16 INFO [Metrics] AUC-ROC: 0.804385 - AUC-PR: 0.605422 - ACC: 0.792146 - Precision: 0.652636 - Recall: 0.403583 - F1: 0.498747 - MCC: 0.394018 - Logloss: 0.447286 - MSE: 0.144670 - RMSE: 0.380355 - COPC: 0.996248 - KLD: 1.029638
2024-11-18 02:11:16 INFO Save best model: monitor(max): 0.357099
2024-11-18 02:11:16 INFO --- 3668/3668 batches finished ---
2024-11-18 02:11:16 INFO Train loss: 0.440401
2024-11-18 02:11:16 INFO ************ Epoch=10 end ************
2024-11-18 02:15:35 INFO [Metrics] AUC-ROC: 0.804302 - AUC-PR: 0.605398 - ACC: 0.792187 - Precision: 0.655255 - Recall: 0.398706 - F1: 0.495757 - MCC: 0.392837 - Logloss: 0.447322 - MSE: 0.144674 - RMSE: 0.380360 - COPC: 0.994287 - KLD: 1.029767
2024-11-18 02:15:35 INFO Monitor(max) STOP: 0.356980 !
2024-11-18 02:15:35 INFO Reduce learning rate on plateau: 0.000100
2024-11-18 02:15:35 INFO --- 3668/3668 batches finished ---
2024-11-18 02:15:35 INFO Train loss: 0.439513
2024-11-18 02:15:35 INFO ************ Epoch=11 end ************
2024-11-18 02:19:57 INFO [Metrics] AUC-ROC: 0.804409 - AUC-PR: 0.605578 - ACC: 0.792169 - Precision: 0.651537 - Recall: 0.406017 - F1: 0.500277 - MCC: 0.394729 - Logloss: 0.447535 - MSE: 0.144649 - RMSE: 0.380327 - COPC: 0.996463 - KLD: 1.030588
2024-11-18 02:19:57 INFO Monitor(max) STOP: 0.356875 !
2024-11-18 02:19:57 INFO Reduce learning rate on plateau: 0.000010
2024-11-18 02:19:57 INFO Early stopping at epoch=12
2024-11-18 02:19:57 INFO --- 3668/3668 batches finished ---
2024-11-18 02:19:57 INFO Train loss: 0.437127
2024-11-18 02:19:57 INFO Training finished.
2024-11-18 02:19:57 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/LSTM/Criteo/LSTM_model_seed2020.ckpt
2024-11-18 02:19:57 INFO Start evaluate model
2024-11-18 02:20:38 INFO [Metrics] AUC-ROC: 0.804385 - AUC-PR: 0.605422 - ACC: 0.792146 - Precision: 0.652636 - Recall: 0.403583 - F1: 0.498747 - MCC: 0.394018 - Logloss: 0.447286 - MSE: 0.144670 - RMSE: 0.380355 - COPC: 0.996248 - KLD: 1.029638
2024-11-18 02:20:38 INFO Start testing model
2024-11-18 02:21:17 INFO [Metrics] AUC-ROC: 0.804801 - AUC-PR: 0.605754 - ACC: 0.792156 - Precision: 0.652266 - Recall: 0.404424 - F1: 0.499280 - MCC: 0.394270 - Logloss: 0.446956 - MSE: 0.144568 - RMSE: 0.380221 - COPC: 0.995948 - KLD: 1.028570
