2024-11-13 04:55:55 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='AutoInt', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-13 04:55:55 INFO Start process Criteo !
2024-11-13 04:55:55 INFO Loading Criteo dataset
2024-11-13 04:55:55 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-13 04:56:00 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-13 04:56:00 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-13 04:56:01 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-13 04:56:01 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 04:56:01 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 04:56:01 INFO Loading data done
2024-11-13 04:56:02 INFO Model: AutoInt(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (self_attention): Sequential(
    (0): MultiHeadSelfAttention(
      (W_q): Linear(in_features=16, out_features=64, bias=False)
      (W_k): Linear(in_features=16, out_features=64, bias=False)
      (W_v): Linear(in_features=16, out_features=64, bias=False)
      (W_res): Linear(in_features=16, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (1): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (2): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (3): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (4): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
  )
  (fc): Linear(in_features=2496, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-13 04:56:02 INFO Model parameters: 14627073
2024-11-13 04:56:02 INFO Start training model
2024-11-13 04:56:02 INFO Start training: 3668 batches/epoch
2024-11-13 04:56:02 INFO ************ Epoch=1 start ************
2024-11-13 05:04:03 INFO [Metrics] AUC-ROC: 0.798532 - AUC-PR: 0.596163 - ACC: 0.788774 - Precision: 0.635753 - Recall: 0.411223 - F1: 0.499412 - MCC: 0.387495 - Logloss: 0.453139 - MSE: 0.146979 - RMSE: 0.383379 - COPC: 0.929221 - KLD: 1.042142
2024-11-13 05:04:03 INFO Save best model: monitor(max): 0.345393
2024-11-13 05:04:03 INFO --- 3668/3668 batches finished ---
2024-11-13 05:04:04 INFO Train loss: 0.465330
2024-11-13 05:04:04 INFO ************ Epoch=1 end ************
2024-11-13 05:12:18 INFO [Metrics] AUC-ROC: 0.801564 - AUC-PR: 0.601192 - ACC: 0.790625 - Precision: 0.659280 - Recall: 0.378403 - F1: 0.480828 - MCC: 0.383379 - Logloss: 0.449180 - MSE: 0.145583 - RMSE: 0.381554 - COPC: 1.002247 - KLD: 1.034246
2024-11-13 05:12:18 INFO Save best model: monitor(max): 0.352384
2024-11-13 05:12:18 INFO --- 3668/3668 batches finished ---
2024-11-13 05:12:18 INFO Train loss: 0.459066
2024-11-13 05:12:18 INFO ************ Epoch=2 end ************
2024-11-13 05:19:32 INFO [Metrics] AUC-ROC: 0.802928 - AUC-PR: 0.603502 - ACC: 0.791024 - Precision: 0.675823 - Recall: 0.354395 - F1: 0.464966 - MCC: 0.378680 - Logloss: 0.448598 - MSE: 0.145328 - RMSE: 0.381218 - COPC: 1.053589 - KLD: 1.031588
2024-11-13 05:19:32 INFO Save best model: monitor(max): 0.354330
2024-11-13 05:19:32 INFO --- 3668/3668 batches finished ---
2024-11-13 05:19:32 INFO Train loss: 0.457356
2024-11-13 05:19:32 INFO ************ Epoch=3 end ************
2024-11-13 05:25:52 INFO [Metrics] AUC-ROC: 0.803973 - AUC-PR: 0.605205 - ACC: 0.791631 - Precision: 0.670533 - Recall: 0.367188 - F1: 0.474523 - MCC: 0.383413 - Logloss: 0.447278 - MSE: 0.144895 - RMSE: 0.380650 - COPC: 1.033845 - KLD: 1.028682
2024-11-13 05:25:52 INFO Save best model: monitor(max): 0.356695
2024-11-13 05:25:52 INFO --- 3668/3668 batches finished ---
2024-11-13 05:25:52 INFO Train loss: 0.456312
2024-11-13 05:25:52 INFO ************ Epoch=4 end ************
2024-11-13 05:33:06 INFO [Metrics] AUC-ROC: 0.804744 - AUC-PR: 0.606277 - ACC: 0.791959 - Precision: 0.668531 - Recall: 0.372979 - F1: 0.478820 - MCC: 0.385726 - Logloss: 0.446696 - MSE: 0.144665 - RMSE: 0.380348 - COPC: 1.035856 - KLD: 1.027217
2024-11-13 05:33:06 INFO Save best model: monitor(max): 0.358049
2024-11-13 05:33:06 INFO --- 3668/3668 batches finished ---
2024-11-13 05:33:06 INFO Train loss: 0.455555
2024-11-13 05:33:06 INFO ************ Epoch=5 end ************
2024-11-13 05:39:44 INFO [Metrics] AUC-ROC: 0.805184 - AUC-PR: 0.606940 - ACC: 0.792282 - Precision: 0.656443 - Recall: 0.397177 - F1: 0.494911 - MCC: 0.392689 - Logloss: 0.446070 - MSE: 0.144480 - RMSE: 0.380105 - COPC: 0.981650 - KLD: 1.025732
2024-11-13 05:39:44 INFO Save best model: monitor(max): 0.359114
2024-11-13 05:39:44 INFO --- 3668/3668 batches finished ---
2024-11-13 05:39:44 INFO Train loss: 0.455004
2024-11-13 05:39:44 INFO ************ Epoch=6 end ************
2024-11-13 05:46:10 INFO [Metrics] AUC-ROC: 0.805685 - AUC-PR: 0.607608 - ACC: 0.792295 - Precision: 0.674955 - Recall: 0.365266 - F1: 0.474011 - MCC: 0.384853 - Logloss: 0.445817 - MSE: 0.144413 - RMSE: 0.380017 - COPC: 1.035225 - KLD: 1.024559
2024-11-13 05:46:10 INFO Save best model: monitor(max): 0.359868
2024-11-13 05:46:10 INFO --- 3668/3668 batches finished ---
2024-11-13 05:46:10 INFO Train loss: 0.454486
2024-11-13 05:46:10 INFO ************ Epoch=7 end ************
2024-11-13 05:54:42 INFO [Metrics] AUC-ROC: 0.806034 - AUC-PR: 0.608513 - ACC: 0.792696 - Precision: 0.668977 - Recall: 0.377933 - F1: 0.483000 - MCC: 0.388978 - Logloss: 0.445317 - MSE: 0.144196 - RMSE: 0.379732 - COPC: 1.023621 - KLD: 1.023879
2024-11-13 05:54:42 INFO Save best model: monitor(max): 0.360717
2024-11-13 05:54:42 INFO --- 3668/3668 batches finished ---
2024-11-13 05:54:42 INFO Train loss: 0.454057
2024-11-13 05:54:42 INFO ************ Epoch=8 end ************
2024-11-13 06:03:14 INFO [Metrics] AUC-ROC: 0.806534 - AUC-PR: 0.609307 - ACC: 0.792960 - Precision: 0.667486 - Recall: 0.382498 - F1: 0.486316 - MCC: 0.390817 - Logloss: 0.444764 - MSE: 0.144002 - RMSE: 0.379476 - COPC: 1.010835 - KLD: 1.022578
2024-11-13 06:03:14 INFO Save best model: monitor(max): 0.361769
2024-11-13 06:03:14 INFO --- 3668/3668 batches finished ---
2024-11-13 06:03:14 INFO Train loss: 0.453674
2024-11-13 06:03:14 INFO ************ Epoch=9 end ************
2024-11-13 06:11:39 INFO [Metrics] AUC-ROC: 0.806718 - AUC-PR: 0.609410 - ACC: 0.792865 - Precision: 0.673419 - Recall: 0.371981 - F1: 0.479241 - MCC: 0.388038 - Logloss: 0.444882 - MSE: 0.144053 - RMSE: 0.379543 - COPC: 1.037104 - KLD: 1.022196
2024-11-13 06:11:39 INFO Save best model: monitor(max): 0.361836
2024-11-13 06:11:40 INFO --- 3668/3668 batches finished ---
2024-11-13 06:11:40 INFO Train loss: 0.453340
2024-11-13 06:11:40 INFO ************ Epoch=10 end ************
2024-11-13 06:20:12 INFO [Metrics] AUC-ROC: 0.806986 - AUC-PR: 0.609912 - ACC: 0.793088 - Precision: 0.669683 - Recall: 0.379776 - F1: 0.484687 - MCC: 0.390513 - Logloss: 0.444502 - MSE: 0.143907 - RMSE: 0.379350 - COPC: 1.024649 - KLD: 1.021670
2024-11-13 06:20:12 INFO Save best model: monitor(max): 0.362484
2024-11-13 06:20:12 INFO --- 3668/3668 batches finished ---
2024-11-13 06:20:12 INFO Train loss: 0.453101
2024-11-13 06:20:12 INFO ************ Epoch=11 end ************
2024-11-13 06:28:42 INFO [Metrics] AUC-ROC: 0.807186 - AUC-PR: 0.610331 - ACC: 0.793158 - Precision: 0.662070 - Recall: 0.393653 - F1: 0.493739 - MCC: 0.394127 - Logloss: 0.444150 - MSE: 0.143791 - RMSE: 0.379198 - COPC: 1.000943 - KLD: 1.020989
2024-11-13 06:28:42 INFO Save best model: monitor(max): 0.363036
2024-11-13 06:28:42 INFO --- 3668/3668 batches finished ---
2024-11-13 06:28:42 INFO Train loss: 0.452873
2024-11-13 06:28:42 INFO ************ Epoch=12 end ************
2024-11-13 06:35:10 INFO [Metrics] AUC-ROC: 0.807251 - AUC-PR: 0.610214 - ACC: 0.793378 - Precision: 0.665760 - Recall: 0.388758 - F1: 0.490877 - MCC: 0.393500 - Logloss: 0.444136 - MSE: 0.143789 - RMSE: 0.379195 - COPC: 1.002499 - KLD: 1.020831
2024-11-13 06:35:10 INFO Save best model: monitor(max): 0.363115
2024-11-13 06:35:10 INFO --- 3668/3668 batches finished ---
2024-11-13 06:35:10 INFO Train loss: 0.452652
2024-11-13 06:35:10 INFO ************ Epoch=13 end ************
2024-11-13 06:41:28 INFO [Metrics] AUC-ROC: 0.807618 - AUC-PR: 0.610889 - ACC: 0.793532 - Precision: 0.663180 - Recall: 0.394603 - F1: 0.494795 - MCC: 0.395376 - Logloss: 0.443786 - MSE: 0.143670 - RMSE: 0.379038 - COPC: 0.993044 - KLD: 1.019952
2024-11-13 06:41:28 INFO Save best model: monitor(max): 0.363833
2024-11-13 06:41:28 INFO --- 3668/3668 batches finished ---
2024-11-13 06:41:29 INFO Train loss: 0.452481
2024-11-13 06:41:29 INFO ************ Epoch=14 end ************
2024-11-13 06:47:54 INFO [Metrics] AUC-ROC: 0.807615 - AUC-PR: 0.610847 - ACC: 0.793398 - Precision: 0.658946 - Recall: 0.401437 - F1: 0.498924 - MCC: 0.396754 - Logloss: 0.443822 - MSE: 0.143686 - RMSE: 0.379059 - COPC: 0.988661 - KLD: 1.019980
2024-11-13 06:47:54 INFO Monitor(max) STOP: 0.363792 !
2024-11-13 06:47:54 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 06:47:54 INFO --- 3668/3668 batches finished ---
2024-11-13 06:47:54 INFO Train loss: 0.452322
2024-11-13 06:47:54 INFO ************ Epoch=15 end ************
2024-11-13 06:54:27 INFO [Metrics] AUC-ROC: 0.810698 - AUC-PR: 0.615937 - ACC: 0.794851 - Precision: 0.662000 - Recall: 0.407281 - F1: 0.504301 - MCC: 0.402099 - Logloss: 0.441019 - MSE: 0.142686 - RMSE: 0.377738 - COPC: 0.998153 - KLD: 1.012671
2024-11-13 06:54:27 INFO Save best model: monitor(max): 0.369679
2024-11-13 06:54:27 INFO --- 3668/3668 batches finished ---
2024-11-13 06:54:27 INFO Train loss: 0.443607
2024-11-13 06:54:27 INFO ************ Epoch=16 end ************
2024-11-13 07:01:13 INFO [Metrics] AUC-ROC: 0.811039 - AUC-PR: 0.616459 - ACC: 0.794991 - Precision: 0.658781 - Recall: 0.414657 - F1: 0.508959 - MCC: 0.404363 - Logloss: 0.440780 - MSE: 0.142601 - RMSE: 0.377626 - COPC: 0.989555 - KLD: 1.011890
2024-11-13 07:01:13 INFO Save best model: monitor(max): 0.370259
2024-11-13 07:01:13 INFO --- 3668/3668 batches finished ---
2024-11-13 07:01:14 INFO Train loss: 0.439749
2024-11-13 07:01:14 INFO ************ Epoch=17 end ************
2024-11-13 07:07:59 INFO [Metrics] AUC-ROC: 0.810846 - AUC-PR: 0.616074 - ACC: 0.794880 - Precision: 0.657203 - Recall: 0.416908 - F1: 0.510177 - MCC: 0.404659 - Logloss: 0.441054 - MSE: 0.142693 - RMSE: 0.377747 - COPC: 0.989166 - KLD: 1.012521
2024-11-13 07:07:59 INFO Monitor(max) STOP: 0.369792 !
2024-11-13 07:07:59 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 07:07:59 INFO --- 3668/3668 batches finished ---
2024-11-13 07:07:59 INFO Train loss: 0.437493
2024-11-13 07:07:59 INFO ************ Epoch=18 end ************
2024-11-13 07:14:54 INFO [Metrics] AUC-ROC: 0.809472 - AUC-PR: 0.613855 - ACC: 0.794182 - Precision: 0.653869 - Recall: 0.417995 - F1: 0.509979 - MCC: 0.403142 - Logloss: 0.442764 - MSE: 0.143209 - RMSE: 0.378430 - COPC: 0.998775 - KLD: 1.017205
2024-11-13 07:14:54 INFO Monitor(max) STOP: 0.366708 !
2024-11-13 07:14:54 INFO Reduce learning rate on plateau: 0.000001
2024-11-13 07:14:54 INFO Early stopping at epoch=19
2024-11-13 07:14:54 INFO --- 3668/3668 batches finished ---
2024-11-13 07:14:55 INFO Train loss: 0.431903
2024-11-13 07:14:55 INFO Training finished.
2024-11-13 07:14:55 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AutoInt/Criteo/AutoInt_model_seed2020.ckpt
2024-11-13 07:14:55 INFO Start evaluate model
2024-11-13 07:15:33 INFO [Metrics] AUC-ROC: 0.811039 - AUC-PR: 0.616459 - ACC: 0.794991 - Precision: 0.658781 - Recall: 0.414657 - F1: 0.508959 - MCC: 0.404363 - Logloss: 0.440780 - MSE: 0.142601 - RMSE: 0.377626 - COPC: 0.989555 - KLD: 1.011890
2024-11-13 07:15:33 INFO Start testing model
2024-11-13 07:16:11 INFO [Metrics] AUC-ROC: 0.811627 - AUC-PR: 0.617296 - ACC: 0.795231 - Precision: 0.659163 - Recall: 0.415838 - F1: 0.509962 - MCC: 0.405293 - Logloss: 0.440238 - MSE: 0.142420 - RMSE: 0.377386 - COPC: 0.989146 - KLD: 1.010374
