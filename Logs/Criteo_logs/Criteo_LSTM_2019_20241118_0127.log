2024-11-18 01:27:56 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='LSTM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-18 01:27:56 INFO Start process Criteo !
2024-11-18 01:27:56 INFO Loading Criteo dataset
2024-11-18 01:27:56 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-18 01:28:03 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-18 01:28:03 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-18 01:28:05 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-18 01:28:05 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-18 01:28:05 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-18 01:28:05 INFO Loading data done
2024-11-18 01:28:06 INFO Model: LSTM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (lstm): LSTM(16, 128, num_layers=2, batch_first=True, dropout=0.2)
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=736, out_features=64, bias=True)
      (1): BatchNorm1d(64, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (2): ReLU()
      (3): Dropout(p=0.2, inplace=False)
      (4): Linear(in_features=64, out_features=32, bias=True)
      (5): BatchNorm1d(32, eps=1e-05, momentum=0.1, affine=True, track_running_stats=True)
      (6): ReLU()
      (7): Dropout(p=0.2, inplace=False)
      (8): Linear(in_features=32, out_features=1, bias=True)
      (9): Sigmoid()
    )
  )
)
2024-11-18 01:28:06 INFO Model parameters: 14827649
2024-11-18 01:28:06 INFO Start training model
2024-11-18 01:28:06 INFO Start training: 3668 batches/epoch
2024-11-18 01:28:06 INFO ************ Epoch=1 start ************
2024-11-18 01:32:08 INFO [Metrics] AUC-ROC: 0.795281 - AUC-PR: 0.589736 - ACC: 0.787786 - Precision: 0.655048 - Recall: 0.362828 - F1: 0.466992 - MCC: 0.371545 - Logloss: 0.454961 - MSE: 0.147617 - RMSE: 0.384209 - COPC: 0.999523 - KLD: 1.049044
2024-11-18 01:32:08 INFO Save best model: monitor(max): 0.340320
2024-11-18 01:32:08 INFO --- 3668/3668 batches finished ---
2024-11-18 01:32:08 INFO Train loss: 0.470669
2024-11-18 01:32:08 INFO ************ Epoch=1 end ************
2024-11-18 01:36:24 INFO [Metrics] AUC-ROC: 0.799825 - AUC-PR: 0.597214 - ACC: 0.789673 - Precision: 0.666766 - Recall: 0.358090 - F1: 0.465943 - MCC: 0.375659 - Logloss: 0.451255 - MSE: 0.146297 - RMSE: 0.382488 - COPC: 0.997937 - KLD: 1.039190
2024-11-18 01:36:24 INFO Save best model: monitor(max): 0.348570
2024-11-18 01:36:24 INFO --- 3668/3668 batches finished ---
2024-11-18 01:36:24 INFO Train loss: 0.454195
2024-11-18 01:36:24 INFO ************ Epoch=2 end ************
2024-11-18 01:40:40 INFO [Metrics] AUC-ROC: 0.801528 - AUC-PR: 0.600208 - ACC: 0.790656 - Precision: 0.656107 - Recall: 0.384489 - F1: 0.484849 - MCC: 0.385031 - Logloss: 0.449523 - MSE: 0.145656 - RMSE: 0.381650 - COPC: 0.990788 - KLD: 1.034848
2024-11-18 01:40:40 INFO Save best model: monitor(max): 0.352005
2024-11-18 01:40:40 INFO --- 3668/3668 batches finished ---
2024-11-18 01:40:40 INFO Train loss: 0.450266
2024-11-18 01:40:40 INFO ************ Epoch=3 end ************
2024-11-18 01:44:55 INFO [Metrics] AUC-ROC: 0.802550 - AUC-PR: 0.601993 - ACC: 0.791031 - Precision: 0.658456 - Recall: 0.383187 - F1: 0.484450 - MCC: 0.385710 - Logloss: 0.448590 - MSE: 0.145301 - RMSE: 0.381183 - COPC: 1.000882 - KLD: 1.032522
2024-11-18 01:44:55 INFO Save best model: monitor(max): 0.353960
2024-11-18 01:44:55 INFO --- 3668/3668 batches finished ---
2024-11-18 01:44:55 INFO Train loss: 0.447887
2024-11-18 01:44:55 INFO ************ Epoch=4 end ************
2024-11-18 01:49:10 INFO [Metrics] AUC-ROC: 0.803163 - AUC-PR: 0.603155 - ACC: 0.791408 - Precision: 0.654233 - Recall: 0.394279 - F1: 0.492031 - MCC: 0.389609 - Logloss: 0.448066 - MSE: 0.145093 - RMSE: 0.380911 - COPC: 0.990651 - KLD: 1.031211
2024-11-18 01:49:10 INFO Save best model: monitor(max): 0.355097
2024-11-18 01:49:10 INFO --- 3668/3668 batches finished ---
2024-11-18 01:49:10 INFO Train loss: 0.446145
2024-11-18 01:49:10 INFO ************ Epoch=5 end ************
2024-11-18 01:53:26 INFO [Metrics] AUC-ROC: 0.803427 - AUC-PR: 0.603884 - ACC: 0.791641 - Precision: 0.656649 - Recall: 0.391535 - F1: 0.490565 - MCC: 0.389511 - Logloss: 0.447896 - MSE: 0.144989 - RMSE: 0.380775 - COPC: 0.989777 - KLD: 1.030996
2024-11-18 01:53:26 INFO Save best model: monitor(max): 0.355531
2024-11-18 01:53:26 INFO --- 3668/3668 batches finished ---
2024-11-18 01:53:26 INFO Train loss: 0.444741
2024-11-18 01:53:26 INFO ************ Epoch=6 end ************
2024-11-18 01:57:39 INFO [Metrics] AUC-ROC: 0.803934 - AUC-PR: 0.604600 - ACC: 0.791769 - Precision: 0.652024 - Recall: 0.401676 - F1: 0.497110 - MCC: 0.392523 - Logloss: 0.447499 - MSE: 0.144826 - RMSE: 0.380561 - COPC: 0.996564 - KLD: 1.029981
2024-11-18 01:57:39 INFO Save best model: monitor(max): 0.356435
2024-11-18 01:57:39 INFO --- 3668/3668 batches finished ---
2024-11-18 01:57:39 INFO Train loss: 0.443513
2024-11-18 01:57:39 INFO ************ Epoch=7 end ************
2024-11-18 02:01:43 INFO [Metrics] AUC-ROC: 0.804060 - AUC-PR: 0.604849 - ACC: 0.792027 - Precision: 0.658587 - Recall: 0.391020 - F1: 0.490699 - MCC: 0.390413 - Logloss: 0.447417 - MSE: 0.144790 - RMSE: 0.380512 - COPC: 0.992690 - KLD: 1.029793
2024-11-18 02:01:43 INFO Save best model: monitor(max): 0.356642
2024-11-18 02:01:43 INFO --- 3668/3668 batches finished ---
2024-11-18 02:01:44 INFO Train loss: 0.442413
2024-11-18 02:01:44 INFO ************ Epoch=8 end ************
2024-11-18 02:05:48 INFO [Metrics] AUC-ROC: 0.804260 - AUC-PR: 0.605206 - ACC: 0.792004 - Precision: 0.653957 - Recall: 0.399758 - F1: 0.496196 - MCC: 0.392631 - Logloss: 0.447285 - MSE: 0.144705 - RMSE: 0.380401 - COPC: 0.993882 - KLD: 1.029568
2024-11-18 02:05:48 INFO Save best model: monitor(max): 0.356975
2024-11-18 02:05:48 INFO --- 3668/3668 batches finished ---
2024-11-18 02:05:48 INFO Train loss: 0.441408
2024-11-18 02:05:48 INFO ************ Epoch=9 end ************
2024-11-18 02:09:55 INFO [Metrics] AUC-ROC: 0.804291 - AUC-PR: 0.605316 - ACC: 0.792085 - Precision: 0.658647 - Recall: 0.391379 - F1: 0.490999 - MCC: 0.390662 - Logloss: 0.447237 - MSE: 0.144677 - RMSE: 0.380365 - COPC: 0.996630 - KLD: 1.029461
2024-11-18 02:09:55 INFO Save best model: monitor(max): 0.357054
2024-11-18 02:09:55 INFO --- 3668/3668 batches finished ---
2024-11-18 02:09:55 INFO Train loss: 0.440437
2024-11-18 02:09:55 INFO ************ Epoch=10 end ************
2024-11-18 02:14:05 INFO [Metrics] AUC-ROC: 0.804441 - AUC-PR: 0.605489 - ACC: 0.792081 - Precision: 0.652693 - Recall: 0.402928 - F1: 0.498263 - MCC: 0.393674 - Logloss: 0.447253 - MSE: 0.144650 - RMSE: 0.380329 - COPC: 0.986077 - KLD: 1.029449
2024-11-18 02:14:05 INFO Save best model: monitor(max): 0.357189
2024-11-18 02:14:05 INFO --- 3668/3668 batches finished ---
2024-11-18 02:14:05 INFO Train loss: 0.439509
2024-11-18 02:14:05 INFO ************ Epoch=11 end ************
2024-11-18 02:18:26 INFO [Metrics] AUC-ROC: 0.804433 - AUC-PR: 0.605387 - ACC: 0.792097 - Precision: 0.651020 - Recall: 0.406479 - F1: 0.500475 - MCC: 0.394665 - Logloss: 0.447346 - MSE: 0.144657 - RMSE: 0.380338 - COPC: 0.992988 - KLD: 1.029805
2024-11-18 02:18:26 INFO Monitor(max) STOP: 0.357086 !
2024-11-18 02:18:26 INFO Reduce learning rate on plateau: 0.000100
2024-11-18 02:18:26 INFO --- 3668/3668 batches finished ---
2024-11-18 02:18:26 INFO Train loss: 0.438614
2024-11-18 02:18:26 INFO ************ Epoch=12 end ************
2024-11-18 02:22:32 INFO [Metrics] AUC-ROC: 0.804347 - AUC-PR: 0.605411 - ACC: 0.792085 - Precision: 0.651188 - Recall: 0.406036 - F1: 0.500189 - MCC: 0.394517 - Logloss: 0.447547 - MSE: 0.144664 - RMSE: 0.380347 - COPC: 0.989067 - KLD: 1.030475
2024-11-18 02:22:32 INFO Monitor(max) STOP: 0.356800 !
2024-11-18 02:22:32 INFO Reduce learning rate on plateau: 0.000010
2024-11-18 02:22:32 INFO Early stopping at epoch=13
2024-11-18 02:22:32 INFO --- 3668/3668 batches finished ---
2024-11-18 02:22:32 INFO Train loss: 0.436260
2024-11-18 02:22:32 INFO Training finished.
2024-11-18 02:22:32 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/LSTM/Criteo/LSTM_model_seed2019.ckpt
2024-11-18 02:22:32 INFO Start evaluate model
2024-11-18 02:23:09 INFO [Metrics] AUC-ROC: 0.804441 - AUC-PR: 0.605489 - ACC: 0.792081 - Precision: 0.652693 - Recall: 0.402928 - F1: 0.498263 - MCC: 0.393674 - Logloss: 0.447253 - MSE: 0.144650 - RMSE: 0.380329 - COPC: 0.986077 - KLD: 1.029449
2024-11-18 02:23:09 INFO Start testing model
2024-11-18 02:23:47 INFO [Metrics] AUC-ROC: 0.804875 - AUC-PR: 0.605822 - ACC: 0.792110 - Precision: 0.652470 - Recall: 0.403620 - F1: 0.498727 - MCC: 0.393935 - Logloss: 0.446895 - MSE: 0.144552 - RMSE: 0.380200 - COPC: 0.985783 - KLD: 1.028283
