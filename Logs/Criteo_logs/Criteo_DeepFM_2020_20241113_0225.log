2024-11-13 02:25:55 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='DeepFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-13 02:25:55 INFO Start process Criteo !
2024-11-13 02:25:55 INFO Loading Criteo dataset
2024-11-13 02:25:55 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-13 02:25:59 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-13 02:26:00 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-13 02:26:01 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-13 02:26:01 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 02:26:01 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 02:26:01 INFO Loading data done
2024-11-13 02:26:02 INFO Model: DeepFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 16)
        (I2): Embedding(98, 16)
        (I3): Embedding(121, 16)
        (I4): Embedding(40, 16)
        (I5): Embedding(219, 16)
        (I6): Embedding(111, 16)
        (I7): Embedding(79, 16)
        (I8): Embedding(68, 16)
        (I9): Embedding(91, 16)
        (I10): Embedding(5, 16)
        (I11): Embedding(26, 16)
        (I12): Embedding(36, 16)
        (I13): Embedding(71, 16)
        (C1): Embedding(1445, 16)
        (C2): Embedding(553, 16)
        (C3): Embedding(157338, 16)
        (C4): Embedding(117821, 16)
        (C5): Embedding(305, 16)
        (C6): Embedding(17, 16)
        (C7): Embedding(11881, 16)
        (C8): Embedding(629, 16)
        (C9): Embedding(4, 16)
        (C10): Embedding(39529, 16)
        (C11): Embedding(5130, 16)
        (C12): Embedding(156655, 16)
        (C13): Embedding(3175, 16)
        (C14): Embedding(27, 16)
        (C15): Embedding(11042, 16)
        (C16): Embedding(148912, 16)
        (C17): Embedding(11, 16)
        (C18): Embedding(4559, 16)
        (C19): Embedding(2002, 16)
        (C20): Embedding(4, 16)
        (C21): Embedding(154563, 16)
        (C22): Embedding(17, 16)
        (C23): Embedding(16, 16)
        (C24): Embedding(53030, 16)
        (C25): Embedding(81, 16)
        (C26): Embedding(40954, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (fm_layer): FM_Layer_v2(
    (inner_product_layer): InnerProductLayer_v2()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (I1): Embedding(43, 1, padding_idx=42)
          (I2): Embedding(98, 1, padding_idx=97)
          (I3): Embedding(121, 1, padding_idx=120)
          (I4): Embedding(40, 1, padding_idx=39)
          (I5): Embedding(219, 1, padding_idx=218)
          (I6): Embedding(111, 1, padding_idx=110)
          (I7): Embedding(79, 1, padding_idx=78)
          (I8): Embedding(68, 1, padding_idx=67)
          (I9): Embedding(91, 1, padding_idx=90)
          (I10): Embedding(5, 1, padding_idx=4)
          (I11): Embedding(26, 1, padding_idx=25)
          (I12): Embedding(36, 1, padding_idx=35)
          (I13): Embedding(71, 1, padding_idx=70)
          (C1): Embedding(1445, 1, padding_idx=1444)
          (C2): Embedding(553, 1, padding_idx=552)
          (C3): Embedding(157338, 1, padding_idx=157337)
          (C4): Embedding(117821, 1, padding_idx=117820)
          (C5): Embedding(305, 1, padding_idx=304)
          (C6): Embedding(17, 1, padding_idx=16)
          (C7): Embedding(11881, 1, padding_idx=11880)
          (C8): Embedding(629, 1, padding_idx=628)
          (C9): Embedding(4, 1, padding_idx=3)
          (C10): Embedding(39529, 1, padding_idx=39528)
          (C11): Embedding(5130, 1, padding_idx=5129)
          (C12): Embedding(156655, 1, padding_idx=156654)
          (C13): Embedding(3175, 1, padding_idx=3174)
          (C14): Embedding(27, 1, padding_idx=26)
          (C15): Embedding(11042, 1, padding_idx=11041)
          (C16): Embedding(148912, 1, padding_idx=148911)
          (C17): Embedding(11, 1, padding_idx=10)
          (C18): Embedding(4559, 1, padding_idx=4558)
          (C19): Embedding(2002, 1, padding_idx=2001)
          (C20): Embedding(4, 1, padding_idx=3)
          (C21): Embedding(154563, 1, padding_idx=154562)
          (C22): Embedding(17, 1, padding_idx=16)
          (C23): Embedding(16, 1, padding_idx=15)
          (C24): Embedding(53030, 1, padding_idx=53029)
          (C25): Embedding(81, 1, padding_idx=80)
          (C26): Embedding(40954, 1, padding_idx=40953)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=624, out_features=1000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=1000, out_features=1000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=1000, out_features=1000, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.2, inplace=False)
      (9): Linear(in_features=1000, out_features=1000, bias=True)
      (10): ReLU()
      (11): Dropout(p=0.2, inplace=False)
      (12): Linear(in_features=1000, out_features=1000, bias=True)
      (13): ReLU()
      (14): Dropout(p=0.2, inplace=False)
      (15): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-13 02:26:02 INFO Model parameters: 20112037
2024-11-13 02:26:02 INFO Start training model
2024-11-13 02:26:02 INFO Start training: 3668 batches/epoch
2024-11-13 02:26:02 INFO ************ Epoch=1 start ************
2024-11-13 02:31:32 INFO [Metrics] AUC-ROC: 0.805281 - AUC-PR: 0.607657 - ACC: 0.792438 - Precision: 0.671312 - Recall: 0.372110 - F1: 0.478813 - MCC: 0.386862 - Logloss: 0.445969 - MSE: 0.144385 - RMSE: 0.379981 - COPC: 1.008872 - KLD: 1.025975
2024-11-13 02:31:32 INFO Save best model: monitor(max): 0.359312
2024-11-13 02:31:32 INFO --- 3668/3668 batches finished ---
2024-11-13 02:31:32 INFO Train loss: 0.459822
2024-11-13 02:31:32 INFO ************ Epoch=1 end ************
2024-11-13 02:37:19 INFO [Metrics] AUC-ROC: 0.807471 - AUC-PR: 0.611014 - ACC: 0.793519 - Precision: 0.666785 - Recall: 0.388071 - F1: 0.490607 - MCC: 0.393719 - Logloss: 0.443992 - MSE: 0.143692 - RMSE: 0.379067 - COPC: 0.993476 - KLD: 1.020736
2024-11-13 02:37:19 INFO Save best model: monitor(max): 0.363479
2024-11-13 02:37:19 INFO --- 3668/3668 batches finished ---
2024-11-13 02:37:19 INFO Train loss: 0.455052
2024-11-13 02:37:19 INFO ************ Epoch=2 end ************
2024-11-13 02:44:04 INFO [Metrics] AUC-ROC: 0.808621 - AUC-PR: 0.612885 - ACC: 0.793937 - Precision: 0.670104 - Recall: 0.385602 - F1: 0.489518 - MCC: 0.394266 - Logloss: 0.442848 - MSE: 0.143339 - RMSE: 0.378601 - COPC: 1.011924 - KLD: 1.017532
2024-11-13 02:44:04 INFO Save best model: monitor(max): 0.365773
2024-11-13 02:44:04 INFO --- 3668/3668 batches finished ---
2024-11-13 02:44:04 INFO Train loss: 0.453806
2024-11-13 02:44:04 INFO ************ Epoch=3 end ************
2024-11-13 02:50:44 INFO [Metrics] AUC-ROC: 0.809162 - AUC-PR: 0.613913 - ACC: 0.794290 - Precision: 0.665623 - Recall: 0.396158 - F1: 0.496697 - MCC: 0.397810 - Logloss: 0.442296 - MSE: 0.143128 - RMSE: 0.378322 - COPC: 1.000385 - KLD: 1.016250
2024-11-13 02:50:44 INFO Save best model: monitor(max): 0.366866
2024-11-13 02:50:44 INFO --- 3668/3668 batches finished ---
2024-11-13 02:50:44 INFO Train loss: 0.453106
2024-11-13 02:50:44 INFO ************ Epoch=4 end ************
2024-11-13 02:57:28 INFO [Metrics] AUC-ROC: 0.809652 - AUC-PR: 0.614479 - ACC: 0.794465 - Precision: 0.667099 - Recall: 0.394891 - F1: 0.496109 - MCC: 0.397970 - Logloss: 0.441894 - MSE: 0.142993 - RMSE: 0.378144 - COPC: 1.002932 - KLD: 1.015107
2024-11-13 02:57:28 INFO Save best model: monitor(max): 0.367758
2024-11-13 02:57:28 INFO --- 3668/3668 batches finished ---
2024-11-13 02:57:29 INFO Train loss: 0.452608
2024-11-13 02:57:29 INFO ************ Epoch=5 end ************
2024-11-13 03:04:20 INFO [Metrics] AUC-ROC: 0.809709 - AUC-PR: 0.614581 - ACC: 0.794649 - Precision: 0.661627 - Recall: 0.406382 - F1: 0.503503 - MCC: 0.401338 - Logloss: 0.441899 - MSE: 0.142980 - RMSE: 0.378127 - COPC: 0.985676 - KLD: 1.015047
2024-11-13 03:04:20 INFO Save best model: monitor(max): 0.367810
2024-11-13 03:04:20 INFO --- 3668/3668 batches finished ---
2024-11-13 03:04:20 INFO Train loss: 0.452209
2024-11-13 03:04:20 INFO ************ Epoch=6 end ************
2024-11-13 03:11:07 INFO [Metrics] AUC-ROC: 0.809886 - AUC-PR: 0.614669 - ACC: 0.794550 - Precision: 0.664084 - Recall: 0.401002 - F1: 0.500051 - MCC: 0.399716 - Logloss: 0.441877 - MSE: 0.142963 - RMSE: 0.378104 - COPC: 0.981407 - KLD: 1.014917
2024-11-13 03:11:07 INFO Save best model: monitor(max): 0.368009
2024-11-13 03:11:07 INFO --- 3668/3668 batches finished ---
2024-11-13 03:11:07 INFO Train loss: 0.451896
2024-11-13 03:11:07 INFO ************ Epoch=7 end ************
2024-11-13 03:17:50 INFO [Metrics] AUC-ROC: 0.809880 - AUC-PR: 0.614812 - ACC: 0.794516 - Precision: 0.664404 - Recall: 0.400143 - F1: 0.499474 - MCC: 0.399409 - Logloss: 0.441762 - MSE: 0.142943 - RMSE: 0.378078 - COPC: 0.985206 - KLD: 1.014677
2024-11-13 03:17:50 INFO Save best model: monitor(max): 0.368118
2024-11-13 03:17:50 INFO --- 3668/3668 batches finished ---
2024-11-13 03:17:50 INFO Train loss: 0.451659
2024-11-13 03:17:50 INFO ************ Epoch=8 end ************
2024-11-13 03:24:28 INFO [Metrics] AUC-ROC: 0.810146 - AUC-PR: 0.615337 - ACC: 0.794811 - Precision: 0.667551 - Recall: 0.396779 - F1: 0.497722 - MCC: 0.399370 - Logloss: 0.441582 - MSE: 0.142840 - RMSE: 0.377941 - COPC: 0.987748 - KLD: 1.014364
2024-11-13 03:24:28 INFO Save best model: monitor(max): 0.368564
2024-11-13 03:24:28 INFO --- 3668/3668 batches finished ---
2024-11-13 03:24:29 INFO Train loss: 0.451456
2024-11-13 03:24:29 INFO ************ Epoch=9 end ************
2024-11-13 03:31:16 INFO [Metrics] AUC-ROC: 0.809912 - AUC-PR: 0.615053 - ACC: 0.794557 - Precision: 0.655572 - Recall: 0.417577 - F1: 0.510184 - MCC: 0.403998 - Logloss: 0.442178 - MSE: 0.143032 - RMSE: 0.378196 - COPC: 0.956087 - KLD: 1.015167
2024-11-13 03:31:16 INFO Monitor(max) STOP: 0.367734 !
2024-11-13 03:31:16 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 03:31:16 INFO --- 3668/3668 batches finished ---
2024-11-13 03:31:16 INFO Train loss: 0.451298
2024-11-13 03:31:16 INFO ************ Epoch=10 end ************
2024-11-13 03:37:53 INFO [Metrics] AUC-ROC: 0.813385 - AUC-PR: 0.620720 - ACC: 0.796350 - Precision: 0.665471 - Recall: 0.412596 - F1: 0.509376 - MCC: 0.407407 - Logloss: 0.438450 - MSE: 0.141770 - RMSE: 0.376523 - COPC: 0.988008 - KLD: 1.005998
2024-11-13 03:37:53 INFO Save best model: monitor(max): 0.374935
2024-11-13 03:37:53 INFO --- 3668/3668 batches finished ---
2024-11-13 03:37:53 INFO Train loss: 0.439770
2024-11-13 03:37:53 INFO ************ Epoch=11 end ************
2024-11-13 03:44:35 INFO [Metrics] AUC-ROC: 0.813778 - AUC-PR: 0.621287 - ACC: 0.796582 - Precision: 0.664810 - Recall: 0.415666 - F1: 0.511513 - MCC: 0.408789 - Logloss: 0.438099 - MSE: 0.141656 - RMSE: 0.376372 - COPC: 0.985765 - KLD: 1.004987
2024-11-13 03:44:35 INFO Save best model: monitor(max): 0.375679
2024-11-13 03:44:35 INFO --- 3668/3668 batches finished ---
2024-11-13 03:44:35 INFO Train loss: 0.434958
2024-11-13 03:44:35 INFO ************ Epoch=12 end ************
2024-11-13 03:51:15 INFO [Metrics] AUC-ROC: 0.813731 - AUC-PR: 0.621262 - ACC: 0.796530 - Precision: 0.662913 - Recall: 0.418890 - F1: 0.513380 - MCC: 0.409466 - Logloss: 0.438209 - MSE: 0.141688 - RMSE: 0.376415 - COPC: 0.979004 - KLD: 1.005203
2024-11-13 03:51:15 INFO Monitor(max) STOP: 0.375523 !
2024-11-13 03:51:15 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 03:51:15 INFO --- 3668/3668 batches finished ---
2024-11-13 03:51:15 INFO Train loss: 0.432833
2024-11-13 03:51:15 INFO ************ Epoch=13 end ************
2024-11-13 03:57:38 INFO [Metrics] AUC-ROC: 0.813539 - AUC-PR: 0.621030 - ACC: 0.796381 - Precision: 0.660306 - Recall: 0.422835 - F1: 0.515538 - MCC: 0.410089 - Logloss: 0.438306 - MSE: 0.141740 - RMSE: 0.376484 - COPC: 0.986130 - KLD: 1.005546
2024-11-13 03:57:38 INFO Monitor(max) STOP: 0.375233 !
2024-11-13 03:57:38 INFO Reduce learning rate on plateau: 0.000001
2024-11-13 03:57:38 INFO Early stopping at epoch=14
2024-11-13 03:57:38 INFO --- 3668/3668 batches finished ---
2024-11-13 03:57:38 INFO Train loss: 0.429105
2024-11-13 03:57:38 INFO Training finished.
2024-11-13 03:57:38 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/DeepFM/Criteo/DeepFM_model_seed2020.ckpt
2024-11-13 03:57:38 INFO Start evaluate model
2024-11-13 03:58:16 INFO [Metrics] AUC-ROC: 0.813778 - AUC-PR: 0.621287 - ACC: 0.796582 - Precision: 0.664810 - Recall: 0.415666 - F1: 0.511513 - MCC: 0.408789 - Logloss: 0.438099 - MSE: 0.141656 - RMSE: 0.376372 - COPC: 0.985765 - KLD: 1.004987
2024-11-13 03:58:16 INFO Start testing model
2024-11-13 03:58:55 INFO [Metrics] AUC-ROC: 0.814192 - AUC-PR: 0.621643 - ACC: 0.796620 - Precision: 0.664435 - Recall: 0.416679 - F1: 0.512168 - MCC: 0.409143 - Logloss: 0.437765 - MSE: 0.141551 - RMSE: 0.376233 - COPC: 0.985314 - KLD: 1.003950
