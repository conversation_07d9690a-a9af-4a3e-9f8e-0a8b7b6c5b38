2024-11-13 03:58:18 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='WideDeep', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-13 03:58:18 INFO Start process Criteo !
2024-11-13 03:58:18 INFO Loading Criteo dataset
2024-11-13 03:58:18 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-13 03:58:22 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-13 03:58:23 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-13 03:58:24 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-13 03:58:24 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 03:58:24 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 03:58:24 INFO Loading data done
2024-11-13 03:58:25 INFO Model: WideDeep(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 16)
        (I2): Embedding(98, 16)
        (I3): Embedding(121, 16)
        (I4): Embedding(40, 16)
        (I5): Embedding(219, 16)
        (I6): Embedding(111, 16)
        (I7): Embedding(79, 16)
        (I8): Embedding(68, 16)
        (I9): Embedding(91, 16)
        (I10): Embedding(5, 16)
        (I11): Embedding(26, 16)
        (I12): Embedding(36, 16)
        (I13): Embedding(71, 16)
        (C1): Embedding(1445, 16)
        (C2): Embedding(553, 16)
        (C3): Embedding(157338, 16)
        (C4): Embedding(117821, 16)
        (C5): Embedding(305, 16)
        (C6): Embedding(17, 16)
        (C7): Embedding(11881, 16)
        (C8): Embedding(629, 16)
        (C9): Embedding(4, 16)
        (C10): Embedding(39529, 16)
        (C11): Embedding(5130, 16)
        (C12): Embedding(156655, 16)
        (C13): Embedding(3175, 16)
        (C14): Embedding(27, 16)
        (C15): Embedding(11042, 16)
        (C16): Embedding(148912, 16)
        (C17): Embedding(11, 16)
        (C18): Embedding(4559, 16)
        (C19): Embedding(2002, 16)
        (C20): Embedding(4, 16)
        (C21): Embedding(154563, 16)
        (C22): Embedding(17, 16)
        (C23): Embedding(16, 16)
        (C24): Embedding(53030, 16)
        (C25): Embedding(81, 16)
        (C26): Embedding(40954, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 1, padding_idx=42)
        (I2): Embedding(98, 1, padding_idx=97)
        (I3): Embedding(121, 1, padding_idx=120)
        (I4): Embedding(40, 1, padding_idx=39)
        (I5): Embedding(219, 1, padding_idx=218)
        (I6): Embedding(111, 1, padding_idx=110)
        (I7): Embedding(79, 1, padding_idx=78)
        (I8): Embedding(68, 1, padding_idx=67)
        (I9): Embedding(91, 1, padding_idx=90)
        (I10): Embedding(5, 1, padding_idx=4)
        (I11): Embedding(26, 1, padding_idx=25)
        (I12): Embedding(36, 1, padding_idx=35)
        (I13): Embedding(71, 1, padding_idx=70)
        (C1): Embedding(1445, 1, padding_idx=1444)
        (C2): Embedding(553, 1, padding_idx=552)
        (C3): Embedding(157338, 1, padding_idx=157337)
        (C4): Embedding(117821, 1, padding_idx=117820)
        (C5): Embedding(305, 1, padding_idx=304)
        (C6): Embedding(17, 1, padding_idx=16)
        (C7): Embedding(11881, 1, padding_idx=11880)
        (C8): Embedding(629, 1, padding_idx=628)
        (C9): Embedding(4, 1, padding_idx=3)
        (C10): Embedding(39529, 1, padding_idx=39528)
        (C11): Embedding(5130, 1, padding_idx=5129)
        (C12): Embedding(156655, 1, padding_idx=156654)
        (C13): Embedding(3175, 1, padding_idx=3174)
        (C14): Embedding(27, 1, padding_idx=26)
        (C15): Embedding(11042, 1, padding_idx=11041)
        (C16): Embedding(148912, 1, padding_idx=148911)
        (C17): Embedding(11, 1, padding_idx=10)
        (C18): Embedding(4559, 1, padding_idx=4558)
        (C19): Embedding(2002, 1, padding_idx=2001)
        (C20): Embedding(4, 1, padding_idx=3)
        (C21): Embedding(154563, 1, padding_idx=154562)
        (C22): Embedding(17, 1, padding_idx=16)
        (C23): Embedding(16, 1, padding_idx=15)
        (C24): Embedding(53030, 1, padding_idx=53029)
        (C25): Embedding(81, 1, padding_idx=80)
        (C26): Embedding(40954, 1, padding_idx=40953)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=624, out_features=1000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=1000, out_features=1000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=1000, out_features=1000, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.2, inplace=False)
      (9): Linear(in_features=1000, out_features=1000, bias=True)
      (10): ReLU()
      (11): Dropout(p=0.2, inplace=False)
      (12): Linear(in_features=1000, out_features=1000, bias=True)
      (13): ReLU()
      (14): Dropout(p=0.2, inplace=False)
      (15): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-13 03:58:25 INFO Model parameters: 20112037
2024-11-13 03:58:25 INFO Start training model
2024-11-13 03:58:25 INFO Start training: 3668 batches/epoch
2024-11-13 03:58:25 INFO ************ Epoch=1 start ************
2024-11-13 04:04:28 INFO [Metrics] AUC-ROC: 0.805162 - AUC-PR: 0.607574 - ACC: 0.792418 - Precision: 0.670355 - Recall: 0.373512 - F1: 0.479727 - MCC: 0.387139 - Logloss: 0.446044 - MSE: 0.144424 - RMSE: 0.380032 - COPC: 1.011254 - KLD: 1.026061
2024-11-13 04:04:28 INFO Save best model: monitor(max): 0.359118
2024-11-13 04:04:28 INFO --- 3668/3668 batches finished ---
2024-11-13 04:04:28 INFO Train loss: 0.460042
2024-11-13 04:04:28 INFO ************ Epoch=1 end ************
2024-11-13 04:10:31 INFO [Metrics] AUC-ROC: 0.807570 - AUC-PR: 0.611602 - ACC: 0.793558 - Precision: 0.662505 - Recall: 0.396045 - F1: 0.495738 - MCC: 0.395809 - Logloss: 0.443842 - MSE: 0.143641 - RMSE: 0.379000 - COPC: 0.981549 - KLD: 1.020399
2024-11-13 04:10:31 INFO Save best model: monitor(max): 0.363728
2024-11-13 04:10:31 INFO --- 3668/3668 batches finished ---
2024-11-13 04:10:31 INFO Train loss: 0.454855
2024-11-13 04:10:31 INFO ************ Epoch=2 end ************
2024-11-13 04:16:26 INFO [Metrics] AUC-ROC: 0.808661 - AUC-PR: 0.613125 - ACC: 0.794000 - Precision: 0.671758 - Recall: 0.383313 - F1: 0.488107 - MCC: 0.393894 - Logloss: 0.442810 - MSE: 0.143319 - RMSE: 0.378575 - COPC: 1.001837 - KLD: 1.017571
2024-11-13 04:16:26 INFO Save best model: monitor(max): 0.365851
2024-11-13 04:16:27 INFO --- 3668/3668 batches finished ---
2024-11-13 04:16:27 INFO Train loss: 0.453381
2024-11-13 04:16:27 INFO ************ Epoch=3 end ************
2024-11-13 04:22:31 INFO [Metrics] AUC-ROC: 0.809214 - AUC-PR: 0.614204 - ACC: 0.794369 - Precision: 0.670366 - Recall: 0.388475 - F1: 0.491897 - MCC: 0.396147 - Logloss: 0.442368 - MSE: 0.143109 - RMSE: 0.378298 - COPC: 1.002428 - KLD: 1.016581
2024-11-13 04:22:31 INFO Save best model: monitor(max): 0.366845
2024-11-13 04:22:31 INFO --- 3668/3668 batches finished ---
2024-11-13 04:22:31 INFO Train loss: 0.452575
2024-11-13 04:22:31 INFO ************ Epoch=4 end ************
2024-11-13 04:28:36 INFO [Metrics] AUC-ROC: 0.809795 - AUC-PR: 0.614987 - ACC: 0.794724 - Precision: 0.663758 - Recall: 0.402973 - F1: 0.501489 - MCC: 0.400674 - Logloss: 0.441803 - MSE: 0.142933 - RMSE: 0.378065 - COPC: 0.984352 - KLD: 1.014934
2024-11-13 04:28:36 INFO Save best model: monitor(max): 0.367991
2024-11-13 04:28:36 INFO --- 3668/3668 batches finished ---
2024-11-13 04:28:36 INFO Train loss: 0.451964
2024-11-13 04:28:36 INFO ************ Epoch=5 end ************
2024-11-13 04:34:39 INFO [Metrics] AUC-ROC: 0.810001 - AUC-PR: 0.615481 - ACC: 0.794835 - Precision: 0.661233 - Recall: 0.408617 - F1: 0.505101 - MCC: 0.402398 - Logloss: 0.441678 - MSE: 0.142871 - RMSE: 0.377983 - COPC: 0.976608 - KLD: 1.014565
2024-11-13 04:34:39 INFO Save best model: monitor(max): 0.368323
2024-11-13 04:34:40 INFO --- 3668/3668 batches finished ---
2024-11-13 04:34:40 INFO Train loss: 0.451565
2024-11-13 04:34:40 INFO ************ Epoch=6 end ************
2024-11-13 04:40:42 INFO [Metrics] AUC-ROC: 0.810047 - AUC-PR: 0.615162 - ACC: 0.794758 - Precision: 0.666561 - Recall: 0.398138 - F1: 0.498513 - MCC: 0.399564 - Logloss: 0.441786 - MSE: 0.142908 - RMSE: 0.378031 - COPC: 0.980824 - KLD: 1.014787
2024-11-13 04:40:42 INFO Monitor(max) STOP: 0.368261 !
2024-11-13 04:40:42 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 04:40:42 INFO --- 3668/3668 batches finished ---
2024-11-13 04:40:42 INFO Train loss: 0.451233
2024-11-13 04:40:42 INFO ************ Epoch=7 end ************
2024-11-13 04:46:42 INFO [Metrics] AUC-ROC: 0.813416 - AUC-PR: 0.620724 - ACC: 0.796352 - Precision: 0.664388 - Recall: 0.414652 - F1: 0.510620 - MCC: 0.407927 - Logloss: 0.438456 - MSE: 0.141770 - RMSE: 0.376524 - COPC: 0.985585 - KLD: 1.005978
2024-11-13 04:46:42 INFO Save best model: monitor(max): 0.374960
2024-11-13 04:46:42 INFO --- 3668/3668 batches finished ---
2024-11-13 04:46:42 INFO Train loss: 0.440486
2024-11-13 04:46:42 INFO ************ Epoch=8 end ************
2024-11-13 04:52:53 INFO [Metrics] AUC-ROC: 0.813743 - AUC-PR: 0.621228 - ACC: 0.796475 - Precision: 0.661265 - Recall: 0.421685 - F1: 0.514974 - MCC: 0.410038 - Logloss: 0.438139 - MSE: 0.141679 - RMSE: 0.376402 - COPC: 0.986020 - KLD: 1.005065
2024-11-13 04:52:53 INFO Save best model: monitor(max): 0.375604
2024-11-13 04:52:53 INFO --- 3668/3668 batches finished ---
2024-11-13 04:52:53 INFO Train loss: 0.435941
2024-11-13 04:52:53 INFO ************ Epoch=9 end ************
2024-11-13 04:58:40 INFO [Metrics] AUC-ROC: 0.813759 - AUC-PR: 0.621211 - ACC: 0.796491 - Precision: 0.662242 - Recall: 0.419891 - F1: 0.513928 - MCC: 0.409619 - Logloss: 0.438127 - MSE: 0.141670 - RMSE: 0.376391 - COPC: 0.986266 - KLD: 1.005030
2024-11-13 04:58:40 INFO Save best model: monitor(max): 0.375632
2024-11-13 04:58:40 INFO --- 3668/3668 batches finished ---
2024-11-13 04:58:41 INFO Train loss: 0.433684
2024-11-13 04:58:41 INFO ************ Epoch=10 end ************
2024-11-13 05:04:51 INFO [Metrics] AUC-ROC: 0.813580 - AUC-PR: 0.621023 - ACC: 0.796390 - Precision: 0.659338 - Recall: 0.424847 - F1: 0.516735 - MCC: 0.410631 - Logloss: 0.438315 - MSE: 0.141745 - RMSE: 0.376490 - COPC: 0.982641 - KLD: 1.005467
2024-11-13 05:04:51 INFO Monitor(max) STOP: 0.375265 !
2024-11-13 05:04:51 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 05:04:51 INFO --- 3668/3668 batches finished ---
2024-11-13 05:04:51 INFO Train loss: 0.431985
2024-11-13 05:04:51 INFO ************ Epoch=11 end ************
2024-11-13 05:11:11 INFO [Metrics] AUC-ROC: 0.813260 - AUC-PR: 0.620524 - ACC: 0.796149 - Precision: 0.656046 - Recall: 0.429672 - F1: 0.519259 - MCC: 0.411279 - Logloss: 0.438698 - MSE: 0.141883 - RMSE: 0.376674 - COPC: 0.983136 - KLD: 1.006436
2024-11-13 05:11:11 INFO Monitor(max) STOP: 0.374562 !
2024-11-13 05:11:11 INFO Reduce learning rate on plateau: 0.000001
2024-11-13 05:11:11 INFO Early stopping at epoch=12
2024-11-13 05:11:11 INFO --- 3668/3668 batches finished ---
2024-11-13 05:11:11 INFO Train loss: 0.427883
2024-11-13 05:11:11 INFO Training finished.
2024-11-13 05:11:11 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/WideDeep/Criteo/WideDeep_model_seed2020.ckpt
2024-11-13 05:11:12 INFO Start evaluate model
2024-11-13 05:11:49 INFO [Metrics] AUC-ROC: 0.813759 - AUC-PR: 0.621211 - ACC: 0.796491 - Precision: 0.662242 - Recall: 0.419891 - F1: 0.513928 - MCC: 0.409619 - Logloss: 0.438127 - MSE: 0.141670 - RMSE: 0.376391 - COPC: 0.986266 - KLD: 1.005030
2024-11-13 05:11:49 INFO Start testing model
2024-11-13 05:12:27 INFO [Metrics] AUC-ROC: 0.814098 - AUC-PR: 0.621533 - ACC: 0.796477 - Precision: 0.661828 - Recall: 0.420589 - F1: 0.514326 - MCC: 0.409761 - Logloss: 0.437844 - MSE: 0.141580 - RMSE: 0.376271 - COPC: 0.985874 - KLD: 1.004157
