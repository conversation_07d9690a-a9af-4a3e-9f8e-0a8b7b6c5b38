2024-11-12 02:50:45 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='FM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=65536, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-12 02:50:45 INFO Start process Criteo !
2024-11-12 02:50:45 INFO Loading Criteo dataset
2024-11-12 02:50:45 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-12 02:50:50 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-12 02:50:51 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-12 02:50:52 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-12 02:50:52 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 02:50:52 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 02:50:52 INFO Loading data done
2024-11-12 02:50:53 INFO Model: FM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (fm_layer): FM_Layer(
    (inner_product_layer): InnerProductLayer()
    (lr_layer): LR_Layer(
      (embedding_layer): EmbeddingLayer(
        (embedding_layer): ModuleDict(
          (I1): Embedding(43, 1, padding_idx=42)
          (I2): Embedding(98, 1, padding_idx=97)
          (I3): Embedding(121, 1, padding_idx=120)
          (I4): Embedding(40, 1, padding_idx=39)
          (I5): Embedding(219, 1, padding_idx=218)
          (I6): Embedding(111, 1, padding_idx=110)
          (I7): Embedding(79, 1, padding_idx=78)
          (I8): Embedding(68, 1, padding_idx=67)
          (I9): Embedding(91, 1, padding_idx=90)
          (I10): Embedding(5, 1, padding_idx=4)
          (I11): Embedding(26, 1, padding_idx=25)
          (I12): Embedding(36, 1, padding_idx=35)
          (I13): Embedding(71, 1, padding_idx=70)
          (C1): Embedding(1445, 1, padding_idx=1444)
          (C2): Embedding(553, 1, padding_idx=552)
          (C3): Embedding(157338, 1, padding_idx=157337)
          (C4): Embedding(117821, 1, padding_idx=117820)
          (C5): Embedding(305, 1, padding_idx=304)
          (C6): Embedding(17, 1, padding_idx=16)
          (C7): Embedding(11881, 1, padding_idx=11880)
          (C8): Embedding(629, 1, padding_idx=628)
          (C9): Embedding(4, 1, padding_idx=3)
          (C10): Embedding(39529, 1, padding_idx=39528)
          (C11): Embedding(5130, 1, padding_idx=5129)
          (C12): Embedding(156655, 1, padding_idx=156654)
          (C13): Embedding(3175, 1, padding_idx=3174)
          (C14): Embedding(27, 1, padding_idx=26)
          (C15): Embedding(11042, 1, padding_idx=11041)
          (C16): Embedding(148912, 1, padding_idx=148911)
          (C17): Embedding(11, 1, padding_idx=10)
          (C18): Embedding(4559, 1, padding_idx=4558)
          (C19): Embedding(2002, 1, padding_idx=2001)
          (C20): Embedding(4, 1, padding_idx=3)
          (C21): Embedding(154563, 1, padding_idx=154562)
          (C22): Embedding(17, 1, padding_idx=16)
          (C23): Embedding(16, 1, padding_idx=15)
          (C24): Embedding(53030, 1, padding_idx=53029)
          (C25): Embedding(81, 1, padding_idx=80)
          (C26): Embedding(40954, 1, padding_idx=40953)
        )
        (seq_encoder_layer): ModuleDict()
      )
    )
    (final_activation): Sigmoid()
  )
)
2024-11-12 02:50:53 INFO Model parameters: 15482037
2024-11-12 02:50:53 INFO Start training model
2024-11-12 02:50:53 INFO Start training: 560 batches/epoch
2024-11-12 02:50:53 INFO ************ Epoch=1 start ************
2024-11-12 02:54:45 INFO [Metrics] AUC-ROC: 0.798510 - AUC-PR: 0.595124 - ACC: 0.789349 - Precision: 0.657887 - Recall: 0.370563 - F1: 0.474089 - MCC: 0.377868 - Logloss: 0.452717 - MSE: 0.146657 - RMSE: 0.382959 - COPC: 0.996449 - KLD: 1.043393
2024-11-12 02:54:45 INFO Save best model: monitor(max): 0.345793
2024-11-12 02:54:45 INFO --- 560/560 batches finished ---
2024-11-12 02:54:45 INFO Train loss: 0.471473
2024-11-12 02:54:45 INFO ************ Epoch=1 end ************
2024-11-12 02:58:39 INFO [Metrics] AUC-ROC: 0.803379 - AUC-PR: 0.602725 - ACC: 0.791329 - Precision: 0.654155 - Recall: 0.393774 - F1: 0.491616 - MCC: 0.389265 - Logloss: 0.448195 - MSE: 0.145160 - RMSE: 0.380999 - COPC: 0.983581 - KLD: 1.030832
2024-11-12 02:58:39 INFO Save best model: monitor(max): 0.355184
2024-11-12 02:58:39 INFO --- 560/560 batches finished ---
2024-11-12 02:58:39 INFO Train loss: 0.450258
2024-11-12 02:58:39 INFO ************ Epoch=2 end ************
2024-11-12 03:02:31 INFO [Metrics] AUC-ROC: 0.805111 - AUC-PR: 0.605441 - ACC: 0.792101 - Precision: 0.660669 - Recall: 0.387768 - F1: 0.488702 - MCC: 0.389783 - Logloss: 0.446543 - MSE: 0.144615 - RMSE: 0.380283 - COPC: 1.005211 - KLD: 1.026582
2024-11-12 03:02:31 INFO Save best model: monitor(max): 0.358568
2024-11-12 03:02:31 INFO --- 560/560 batches finished ---
2024-11-12 03:02:31 INFO Train loss: 0.445661
2024-11-12 03:02:31 INFO ************ Epoch=3 end ************
2024-11-12 03:06:24 INFO [Metrics] AUC-ROC: 0.805659 - AUC-PR: 0.606262 - ACC: 0.792367 - Precision: 0.658819 - Recall: 0.393336 - F1: 0.492583 - MCC: 0.391920 - Logloss: 0.446036 - MSE: 0.144456 - RMSE: 0.380073 - COPC: 1.001544 - KLD: 1.025126
2024-11-12 03:06:24 INFO Save best model: monitor(max): 0.359623
2024-11-12 03:06:24 INFO --- 560/560 batches finished ---
2024-11-12 03:06:24 INFO Train loss: 0.443201
2024-11-12 03:06:24 INFO ************ Epoch=4 end ************
2024-11-12 03:10:15 INFO [Metrics] AUC-ROC: 0.805972 - AUC-PR: 0.606748 - ACC: 0.792435 - Precision: 0.659438 - Recall: 0.392729 - F1: 0.492280 - MCC: 0.391948 - Logloss: 0.445728 - MSE: 0.144357 - RMSE: 0.379943 - COPC: 1.004256 - KLD: 1.024293
2024-11-12 03:10:15 INFO Save best model: monitor(max): 0.360244
2024-11-12 03:10:15 INFO --- 560/560 batches finished ---
2024-11-12 03:10:15 INFO Train loss: 0.441738
2024-11-12 03:10:15 INFO ************ Epoch=5 end ************
2024-11-12 03:14:06 INFO [Metrics] AUC-ROC: 0.806051 - AUC-PR: 0.606777 - ACC: 0.792446 - Precision: 0.659419 - Recall: 0.392849 - F1: 0.492369 - MCC: 0.392007 - Logloss: 0.445731 - MSE: 0.144365 - RMSE: 0.379954 - COPC: 1.007400 - KLD: 1.024177
2024-11-12 03:14:06 INFO Save best model: monitor(max): 0.360320
2024-11-12 03:14:06 INFO --- 560/560 batches finished ---
2024-11-12 03:14:06 INFO Train loss: 0.440750
2024-11-12 03:14:06 INFO ************ Epoch=6 end ************
2024-11-12 03:17:59 INFO [Metrics] AUC-ROC: 0.806000 - AUC-PR: 0.606708 - ACC: 0.792427 - Precision: 0.663438 - Recall: 0.385373 - F1: 0.487545 - MCC: 0.390064 - Logloss: 0.445862 - MSE: 0.144409 - RMSE: 0.380011 - COPC: 1.023073 - KLD: 1.024483
2024-11-12 03:17:59 INFO Monitor(max) STOP: 0.360138 !
2024-11-12 03:17:59 INFO Reduce learning rate on plateau: 0.000100
2024-11-12 03:17:59 INFO --- 560/560 batches finished ---
2024-11-12 03:17:59 INFO Train loss: 0.440046
2024-11-12 03:17:59 INFO ************ Epoch=7 end ************
2024-11-12 03:21:50 INFO [Metrics] AUC-ROC: 0.807028 - AUC-PR: 0.608574 - ACC: 0.792808 - Precision: 0.654882 - Recall: 0.404566 - F1: 0.500153 - MCC: 0.396015 - Logloss: 0.444861 - MSE: 0.144044 - RMSE: 0.379532 - COPC: 0.996242 - KLD: 1.021868
2024-11-12 03:21:50 INFO Save best model: monitor(max): 0.362167
2024-11-12 03:21:50 INFO --- 560/560 batches finished ---
2024-11-12 03:21:50 INFO Train loss: 0.433065
2024-11-12 03:21:50 INFO ************ Epoch=8 end ************
2024-11-12 03:25:41 INFO [Metrics] AUC-ROC: 0.807072 - AUC-PR: 0.608583 - ACC: 0.792853 - Precision: 0.655107 - Recall: 0.404487 - F1: 0.500158 - MCC: 0.396111 - Logloss: 0.444851 - MSE: 0.144038 - RMSE: 0.379524 - COPC: 0.998615 - KLD: 1.021839
2024-11-12 03:25:41 INFO Save best model: monitor(max): 0.362221
2024-11-12 03:25:41 INFO --- 560/560 batches finished ---
2024-11-12 03:25:42 INFO Train loss: 0.432279
2024-11-12 03:25:42 INFO ************ Epoch=9 end ************
2024-11-12 03:29:31 INFO [Metrics] AUC-ROC: 0.807046 - AUC-PR: 0.608571 - ACC: 0.792871 - Precision: 0.654322 - Recall: 0.406204 - F1: 0.501239 - MCC: 0.396611 - Logloss: 0.444921 - MSE: 0.144060 - RMSE: 0.379553 - COPC: 0.998313 - KLD: 1.021991
2024-11-12 03:29:31 INFO Monitor(max) STOP: 0.362125 !
2024-11-12 03:29:31 INFO Reduce learning rate on plateau: 0.000010
2024-11-12 03:29:31 INFO --- 560/560 batches finished ---
2024-11-12 03:29:31 INFO Train loss: 0.431884
2024-11-12 03:29:31 INFO ************ Epoch=10 end ************
2024-11-12 03:33:23 INFO [Metrics] AUC-ROC: 0.807046 - AUC-PR: 0.608521 - ACC: 0.792867 - Precision: 0.654844 - Recall: 0.405130 - F1: 0.500573 - MCC: 0.396318 - Logloss: 0.444910 - MSE: 0.144058 - RMSE: 0.379550 - COPC: 0.999667 - KLD: 1.021979
2024-11-12 03:33:23 INFO Monitor(max) STOP: 0.362136 !
2024-11-12 03:33:23 INFO Reduce learning rate on plateau: 0.000001
2024-11-12 03:33:23 INFO Early stopping at epoch=11
2024-11-12 03:33:23 INFO --- 560/560 batches finished ---
2024-11-12 03:33:23 INFO Train loss: 0.430776
2024-11-12 03:33:23 INFO Training finished.
2024-11-12 03:33:23 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FM/Criteo/FM_model.ckpt
2024-11-12 03:33:23 INFO Start evaluate model
2024-11-12 03:34:00 INFO [Metrics] AUC-ROC: 0.807072 - AUC-PR: 0.608583 - ACC: 0.792853 - Precision: 0.655107 - Recall: 0.404487 - F1: 0.500158 - MCC: 0.396111 - Logloss: 0.444851 - MSE: 0.144038 - RMSE: 0.379524 - COPC: 0.998615 - KLD: 1.021839
2024-11-12 03:34:00 INFO Start testing model
2024-11-12 03:34:37 INFO [Metrics] AUC-ROC: 0.807423 - AUC-PR: 0.608744 - ACC: 0.792955 - Precision: 0.654914 - Recall: 0.405710 - F1: 0.501035 - MCC: 0.396700 - Logloss: 0.444610 - MSE: 0.143964 - RMSE: 0.379425 - COPC: 0.998154 - KLD: 1.020981
