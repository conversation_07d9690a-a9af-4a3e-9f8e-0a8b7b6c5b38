2024-11-13 02:27:46 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='WideDeep', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-13 02:27:46 INFO Start process Criteo !
2024-11-13 02:27:46 INFO Loading Criteo dataset
2024-11-13 02:27:46 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-13 02:27:50 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-13 02:27:51 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-13 02:27:52 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-13 02:27:52 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 02:27:52 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 02:27:52 INFO Loading data done
2024-11-13 02:27:53 INFO Model: WideDeep(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 16)
        (I2): Embedding(98, 16)
        (I3): Embedding(121, 16)
        (I4): Embedding(40, 16)
        (I5): Embedding(219, 16)
        (I6): Embedding(111, 16)
        (I7): Embedding(79, 16)
        (I8): Embedding(68, 16)
        (I9): Embedding(91, 16)
        (I10): Embedding(5, 16)
        (I11): Embedding(26, 16)
        (I12): Embedding(36, 16)
        (I13): Embedding(71, 16)
        (C1): Embedding(1445, 16)
        (C2): Embedding(553, 16)
        (C3): Embedding(157338, 16)
        (C4): Embedding(117821, 16)
        (C5): Embedding(305, 16)
        (C6): Embedding(17, 16)
        (C7): Embedding(11881, 16)
        (C8): Embedding(629, 16)
        (C9): Embedding(4, 16)
        (C10): Embedding(39529, 16)
        (C11): Embedding(5130, 16)
        (C12): Embedding(156655, 16)
        (C13): Embedding(3175, 16)
        (C14): Embedding(27, 16)
        (C15): Embedding(11042, 16)
        (C16): Embedding(148912, 16)
        (C17): Embedding(11, 16)
        (C18): Embedding(4559, 16)
        (C19): Embedding(2002, 16)
        (C20): Embedding(4, 16)
        (C21): Embedding(154563, 16)
        (C22): Embedding(17, 16)
        (C23): Embedding(16, 16)
        (C24): Embedding(53030, 16)
        (C25): Embedding(81, 16)
        (C26): Embedding(40954, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 1, padding_idx=42)
        (I2): Embedding(98, 1, padding_idx=97)
        (I3): Embedding(121, 1, padding_idx=120)
        (I4): Embedding(40, 1, padding_idx=39)
        (I5): Embedding(219, 1, padding_idx=218)
        (I6): Embedding(111, 1, padding_idx=110)
        (I7): Embedding(79, 1, padding_idx=78)
        (I8): Embedding(68, 1, padding_idx=67)
        (I9): Embedding(91, 1, padding_idx=90)
        (I10): Embedding(5, 1, padding_idx=4)
        (I11): Embedding(26, 1, padding_idx=25)
        (I12): Embedding(36, 1, padding_idx=35)
        (I13): Embedding(71, 1, padding_idx=70)
        (C1): Embedding(1445, 1, padding_idx=1444)
        (C2): Embedding(553, 1, padding_idx=552)
        (C3): Embedding(157338, 1, padding_idx=157337)
        (C4): Embedding(117821, 1, padding_idx=117820)
        (C5): Embedding(305, 1, padding_idx=304)
        (C6): Embedding(17, 1, padding_idx=16)
        (C7): Embedding(11881, 1, padding_idx=11880)
        (C8): Embedding(629, 1, padding_idx=628)
        (C9): Embedding(4, 1, padding_idx=3)
        (C10): Embedding(39529, 1, padding_idx=39528)
        (C11): Embedding(5130, 1, padding_idx=5129)
        (C12): Embedding(156655, 1, padding_idx=156654)
        (C13): Embedding(3175, 1, padding_idx=3174)
        (C14): Embedding(27, 1, padding_idx=26)
        (C15): Embedding(11042, 1, padding_idx=11041)
        (C16): Embedding(148912, 1, padding_idx=148911)
        (C17): Embedding(11, 1, padding_idx=10)
        (C18): Embedding(4559, 1, padding_idx=4558)
        (C19): Embedding(2002, 1, padding_idx=2001)
        (C20): Embedding(4, 1, padding_idx=3)
        (C21): Embedding(154563, 1, padding_idx=154562)
        (C22): Embedding(17, 1, padding_idx=16)
        (C23): Embedding(16, 1, padding_idx=15)
        (C24): Embedding(53030, 1, padding_idx=53029)
        (C25): Embedding(81, 1, padding_idx=80)
        (C26): Embedding(40954, 1, padding_idx=40953)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=624, out_features=1000, bias=True)
      (1): ReLU()
      (2): Dropout(p=0.2, inplace=False)
      (3): Linear(in_features=1000, out_features=1000, bias=True)
      (4): ReLU()
      (5): Dropout(p=0.2, inplace=False)
      (6): Linear(in_features=1000, out_features=1000, bias=True)
      (7): ReLU()
      (8): Dropout(p=0.2, inplace=False)
      (9): Linear(in_features=1000, out_features=1000, bias=True)
      (10): ReLU()
      (11): Dropout(p=0.2, inplace=False)
      (12): Linear(in_features=1000, out_features=1000, bias=True)
      (13): ReLU()
      (14): Dropout(p=0.2, inplace=False)
      (15): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-13 02:27:53 INFO Model parameters: 20112037
2024-11-13 02:27:53 INFO Start training model
2024-11-13 02:27:53 INFO Start training: 3668 batches/epoch
2024-11-13 02:27:53 INFO ************ Epoch=1 start ************
2024-11-13 02:33:32 INFO [Metrics] AUC-ROC: 0.805284 - AUC-PR: 0.607614 - ACC: 0.792584 - Precision: 0.662813 - Recall: 0.387739 - F1: 0.489263 - MCC: 0.391085 - Logloss: 0.446063 - MSE: 0.144419 - RMSE: 0.380025 - COPC: 0.975903 - KLD: 1.026088
2024-11-13 02:33:32 INFO Save best model: monitor(max): 0.359221
2024-11-13 02:33:32 INFO --- 3668/3668 batches finished ---
2024-11-13 02:33:33 INFO Train loss: 0.460068
2024-11-13 02:33:33 INFO ************ Epoch=1 end ************
2024-11-13 02:39:33 INFO [Metrics] AUC-ROC: 0.807468 - AUC-PR: 0.610983 - ACC: 0.793442 - Precision: 0.667489 - Recall: 0.386249 - F1: 0.489337 - MCC: 0.393061 - Logloss: 0.444000 - MSE: 0.143724 - RMSE: 0.379110 - COPC: 1.021802 - KLD: 1.020522
2024-11-13 02:39:33 INFO Save best model: monitor(max): 0.363468
2024-11-13 02:39:33 INFO --- 3668/3668 batches finished ---
2024-11-13 02:39:33 INFO Train loss: 0.455029
2024-11-13 02:39:33 INFO ************ Epoch=2 end ************
2024-11-13 02:46:13 INFO [Metrics] AUC-ROC: 0.808548 - AUC-PR: 0.613118 - ACC: 0.794063 - Precision: 0.662612 - Recall: 0.399861 - F1: 0.498747 - MCC: 0.398125 - Logloss: 0.442907 - MSE: 0.143321 - RMSE: 0.378578 - COPC: 0.995956 - KLD: 1.017838
2024-11-13 02:46:13 INFO Save best model: monitor(max): 0.365640
2024-11-13 02:46:13 INFO --- 3668/3668 batches finished ---
2024-11-13 02:46:13 INFO Train loss: 0.453603
2024-11-13 02:46:13 INFO ************ Epoch=3 end ************
2024-11-13 02:52:55 INFO [Metrics] AUC-ROC: 0.809022 - AUC-PR: 0.613986 - ACC: 0.794430 - Precision: 0.667255 - Recall: 0.394339 - F1: 0.495716 - MCC: 0.397739 - Logloss: 0.442622 - MSE: 0.143148 - RMSE: 0.378349 - COPC: 0.989002 - KLD: 1.017449
2024-11-13 02:52:55 INFO Save best model: monitor(max): 0.366400
2024-11-13 02:52:55 INFO --- 3668/3668 batches finished ---
2024-11-13 02:52:55 INFO Train loss: 0.452678
2024-11-13 02:52:55 INFO ************ Epoch=4 end ************
2024-11-13 02:59:46 INFO [Metrics] AUC-ROC: 0.809744 - AUC-PR: 0.614966 - ACC: 0.794633 - Precision: 0.667591 - Recall: 0.395325 - F1: 0.496588 - MCC: 0.398532 - Logloss: 0.441877 - MSE: 0.142952 - RMSE: 0.378090 - COPC: 0.994998 - KLD: 1.015194
2024-11-13 02:59:46 INFO Save best model: monitor(max): 0.367867
2024-11-13 02:59:46 INFO --- 3668/3668 batches finished ---
2024-11-13 02:59:46 INFO Train loss: 0.452065
2024-11-13 02:59:46 INFO ************ Epoch=5 end ************
2024-11-13 03:06:29 INFO [Metrics] AUC-ROC: 0.809925 - AUC-PR: 0.615142 - ACC: 0.794615 - Precision: 0.660607 - Recall: 0.408056 - F1: 0.504490 - MCC: 0.401674 - Logloss: 0.441972 - MSE: 0.142957 - RMSE: 0.378096 - COPC: 0.970409 - KLD: 1.015200
2024-11-13 03:06:29 INFO Save best model: monitor(max): 0.367954
2024-11-13 03:06:29 INFO --- 3668/3668 batches finished ---
2024-11-13 03:06:29 INFO Train loss: 0.451584
2024-11-13 03:06:29 INFO ************ Epoch=6 end ************
2024-11-13 03:13:15 INFO [Metrics] AUC-ROC: 0.809997 - AUC-PR: 0.615599 - ACC: 0.794793 - Precision: 0.673799 - Recall: 0.385959 - F1: 0.490789 - MCC: 0.396721 - Logloss: 0.441759 - MSE: 0.142866 - RMSE: 0.377976 - COPC: 0.998303 - KLD: 1.015164
2024-11-13 03:13:15 INFO Save best model: monitor(max): 0.368238
2024-11-13 03:13:16 INFO --- 3668/3668 batches finished ---
2024-11-13 03:13:16 INFO Train loss: 0.451200
2024-11-13 03:13:16 INFO ************ Epoch=7 end ************
2024-11-13 03:19:57 INFO [Metrics] AUC-ROC: 0.810229 - AUC-PR: 0.615479 - ACC: 0.794781 - Precision: 0.671491 - Recall: 0.389726 - F1: 0.493202 - MCC: 0.397582 - Logloss: 0.441511 - MSE: 0.142832 - RMSE: 0.377931 - COPC: 0.999619 - KLD: 1.014031
2024-11-13 03:19:57 INFO Save best model: monitor(max): 0.368717
2024-11-13 03:19:57 INFO --- 3668/3668 batches finished ---
2024-11-13 03:19:57 INFO Train loss: 0.450928
2024-11-13 03:19:57 INFO ************ Epoch=8 end ************
2024-11-13 03:26:40 INFO [Metrics] AUC-ROC: 0.810070 - AUC-PR: 0.615310 - ACC: 0.794723 - Precision: 0.674475 - Recall: 0.384321 - F1: 0.489641 - MCC: 0.396142 - Logloss: 0.441949 - MSE: 0.142929 - RMSE: 0.378059 - COPC: 0.998592 - KLD: 1.015285
2024-11-13 03:26:40 INFO Monitor(max) STOP: 0.368121 !
2024-11-13 03:26:40 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 03:26:40 INFO --- 3668/3668 batches finished ---
2024-11-13 03:26:40 INFO Train loss: 0.450684
2024-11-13 03:26:40 INFO ************ Epoch=9 end ************
2024-11-13 03:33:16 INFO [Metrics] AUC-ROC: 0.813361 - AUC-PR: 0.620627 - ACC: 0.796228 - Precision: 0.664651 - Recall: 0.413181 - F1: 0.509581 - MCC: 0.407232 - Logloss: 0.438470 - MSE: 0.141782 - RMSE: 0.376540 - COPC: 0.991421 - KLD: 1.006066
2024-11-13 03:33:16 INFO Save best model: monitor(max): 0.374892
2024-11-13 03:33:16 INFO --- 3668/3668 batches finished ---
2024-11-13 03:33:17 INFO Train loss: 0.439833
2024-11-13 03:33:17 INFO ************ Epoch=10 end ************
2024-11-13 03:40:02 INFO [Metrics] AUC-ROC: 0.813678 - AUC-PR: 0.621098 - ACC: 0.796400 - Precision: 0.664031 - Recall: 0.415711 - F1: 0.511317 - MCC: 0.408320 - Logloss: 0.438201 - MSE: 0.141690 - RMSE: 0.376417 - COPC: 0.987043 - KLD: 1.005288
2024-11-13 03:40:02 INFO Save best model: monitor(max): 0.375477
2024-11-13 03:40:02 INFO --- 3668/3668 batches finished ---
2024-11-13 03:40:02 INFO Train loss: 0.435237
2024-11-13 03:40:02 INFO ************ Epoch=11 end ************
2024-11-13 03:46:41 INFO [Metrics] AUC-ROC: 0.813636 - AUC-PR: 0.620994 - ACC: 0.796453 - Precision: 0.664824 - Recall: 0.414622 - F1: 0.510726 - MCC: 0.408186 - Logloss: 0.438227 - MSE: 0.141700 - RMSE: 0.376430 - COPC: 0.991635 - KLD: 1.005362
2024-11-13 03:46:41 INFO Monitor(max) STOP: 0.375408 !
2024-11-13 03:46:41 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 03:46:41 INFO --- 3668/3668 batches finished ---
2024-11-13 03:46:41 INFO Train loss: 0.433006
2024-11-13 03:46:41 INFO ************ Epoch=12 end ************
2024-11-13 03:53:23 INFO [Metrics] AUC-ROC: 0.813406 - AUC-PR: 0.620707 - ACC: 0.796313 - Precision: 0.661040 - Recall: 0.420826 - F1: 0.514265 - MCC: 0.409394 - Logloss: 0.438449 - MSE: 0.141786 - RMSE: 0.376545 - COPC: 0.991733 - KLD: 1.005965
2024-11-13 03:53:23 INFO Monitor(max) STOP: 0.374957 !
2024-11-13 03:53:23 INFO Reduce learning rate on plateau: 0.000001
2024-11-13 03:53:23 INFO Early stopping at epoch=13
2024-11-13 03:53:23 INFO --- 3668/3668 batches finished ---
2024-11-13 03:53:23 INFO Train loss: 0.429239
2024-11-13 03:53:23 INFO Training finished.
2024-11-13 03:53:23 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/WideDeep/Criteo/WideDeep_model_seed2019.ckpt
2024-11-13 03:53:24 INFO Start evaluate model
2024-11-13 03:54:02 INFO [Metrics] AUC-ROC: 0.813678 - AUC-PR: 0.621098 - ACC: 0.796400 - Precision: 0.664031 - Recall: 0.415711 - F1: 0.511317 - MCC: 0.408320 - Logloss: 0.438201 - MSE: 0.141690 - RMSE: 0.376417 - COPC: 0.987043 - KLD: 1.005288
2024-11-13 03:54:02 INFO Start testing model
2024-11-13 03:54:40 INFO [Metrics] AUC-ROC: 0.814055 - AUC-PR: 0.621484 - ACC: 0.796539 - Precision: 0.663954 - Recall: 0.416959 - F1: 0.512237 - MCC: 0.409001 - Logloss: 0.437880 - MSE: 0.141587 - RMSE: 0.376281 - COPC: 0.986564 - KLD: 1.004316
