2024-11-12 12:11:06 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='NFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=30000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-12 12:11:06 INFO Start process Criteo !
2024-11-12 12:11:06 INFO Loading Criteo dataset
2024-11-12 12:11:06 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-12 12:11:11 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-12 12:11:12 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-12 12:11:13 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-12 12:11:13 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 12:11:13 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 12:11:13 INFO Loading data done
2024-11-12 12:11:14 INFO Model: NFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 1, padding_idx=42)
        (I2): Embedding(98, 1, padding_idx=97)
        (I3): Embedding(121, 1, padding_idx=120)
        (I4): Embedding(40, 1, padding_idx=39)
        (I5): Embedding(219, 1, padding_idx=218)
        (I6): Embedding(111, 1, padding_idx=110)
        (I7): Embedding(79, 1, padding_idx=78)
        (I8): Embedding(68, 1, padding_idx=67)
        (I9): Embedding(91, 1, padding_idx=90)
        (I10): Embedding(5, 1, padding_idx=4)
        (I11): Embedding(26, 1, padding_idx=25)
        (I12): Embedding(36, 1, padding_idx=35)
        (I13): Embedding(71, 1, padding_idx=70)
        (C1): Embedding(1445, 1, padding_idx=1444)
        (C2): Embedding(553, 1, padding_idx=552)
        (C3): Embedding(157338, 1, padding_idx=157337)
        (C4): Embedding(117821, 1, padding_idx=117820)
        (C5): Embedding(305, 1, padding_idx=304)
        (C6): Embedding(17, 1, padding_idx=16)
        (C7): Embedding(11881, 1, padding_idx=11880)
        (C8): Embedding(629, 1, padding_idx=628)
        (C9): Embedding(4, 1, padding_idx=3)
        (C10): Embedding(39529, 1, padding_idx=39528)
        (C11): Embedding(5130, 1, padding_idx=5129)
        (C12): Embedding(156655, 1, padding_idx=156654)
        (C13): Embedding(3175, 1, padding_idx=3174)
        (C14): Embedding(27, 1, padding_idx=26)
        (C15): Embedding(11042, 1, padding_idx=11041)
        (C16): Embedding(148912, 1, padding_idx=148911)
        (C17): Embedding(11, 1, padding_idx=10)
        (C18): Embedding(4559, 1, padding_idx=4558)
        (C19): Embedding(2002, 1, padding_idx=2001)
        (C20): Embedding(4, 1, padding_idx=3)
        (C21): Embedding(154563, 1, padding_idx=154562)
        (C22): Embedding(17, 1, padding_idx=16)
        (C23): Embedding(16, 1, padding_idx=15)
        (C24): Embedding(53030, 1, padding_idx=53029)
        (C25): Embedding(81, 1, padding_idx=80)
        (C26): Embedding(40954, 1, padding_idx=40953)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=16, out_features=1000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=1000, out_features=1000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=1000, out_features=1000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-12 12:11:14 INFO Model parameters: 17502037
2024-11-12 12:11:14 INFO Start training model
2024-11-12 12:11:14 INFO Start training: 1223 batches/epoch
2024-11-12 12:11:14 INFO ************ Epoch=1 start ************
2024-11-12 12:15:20 INFO [Metrics] AUC-ROC: 0.800450 - AUC-PR: 0.599352 - ACC: 0.790116 - Precision: 0.662170 - Recall: 0.369234 - F1: 0.474102 - MCC: 0.379657 - Logloss: 0.450405 - MSE: 0.145971 - RMSE: 0.382061 - COPC: 1.027073 - KLD: 1.037331
2024-11-12 12:15:20 INFO Save best model: monitor(max): 0.350045
2024-11-12 12:15:20 INFO --- 1223/1223 batches finished ---
2024-11-12 12:15:20 INFO Train loss: 0.464142
2024-11-12 12:15:20 INFO ************ Epoch=1 end ************
2024-11-12 12:19:30 INFO [Metrics] AUC-ROC: 0.803064 - AUC-PR: 0.603644 - ACC: 0.791372 - Precision: 0.655761 - Recall: 0.391027 - F1: 0.489919 - MCC: 0.388660 - Logloss: 0.447896 - MSE: 0.145109 - RMSE: 0.380932 - COPC: 0.999958 - KLD: 1.030886
2024-11-12 12:19:30 INFO Save best model: monitor(max): 0.355168
2024-11-12 12:19:30 INFO --- 1223/1223 batches finished ---
2024-11-12 12:19:31 INFO Train loss: 0.455898
2024-11-12 12:19:31 INFO ************ Epoch=2 end ************
2024-11-12 12:23:42 INFO [Metrics] AUC-ROC: 0.803908 - AUC-PR: 0.604684 - ACC: 0.791848 - Precision: 0.659963 - Recall: 0.387025 - F1: 0.487918 - MCC: 0.388908 - Logloss: 0.447202 - MSE: 0.144862 - RMSE: 0.380607 - COPC: 0.993532 - KLD: 1.028941
2024-11-12 12:23:42 INFO Save best model: monitor(max): 0.356707
2024-11-12 12:23:42 INFO --- 1223/1223 batches finished ---
2024-11-12 12:23:43 INFO Train loss: 0.454538
2024-11-12 12:23:43 INFO ************ Epoch=3 end ************
2024-11-12 12:27:54 INFO [Metrics] AUC-ROC: 0.804395 - AUC-PR: 0.605438 - ACC: 0.792044 - Precision: 0.661946 - Recall: 0.384994 - F1: 0.486838 - MCC: 0.388924 - Logloss: 0.446848 - MSE: 0.144727 - RMSE: 0.380430 - COPC: 1.012180 - KLD: 1.027966
2024-11-12 12:27:54 INFO Save best model: monitor(max): 0.357547
2024-11-12 12:27:54 INFO --- 1223/1223 batches finished ---
2024-11-12 12:27:55 INFO Train loss: 0.454017
2024-11-12 12:27:55 INFO ************ Epoch=4 end ************
2024-11-12 12:32:08 INFO [Metrics] AUC-ROC: 0.804557 - AUC-PR: 0.605878 - ACC: 0.792061 - Precision: 0.660713 - Recall: 0.387367 - F1: 0.488395 - MCC: 0.389572 - Logloss: 0.446594 - MSE: 0.144639 - RMSE: 0.380315 - COPC: 1.004302 - KLD: 1.027429
2024-11-12 12:32:08 INFO Save best model: monitor(max): 0.357963
2024-11-12 12:32:08 INFO --- 1223/1223 batches finished ---
2024-11-12 12:32:09 INFO Train loss: 0.453698
2024-11-12 12:32:09 INFO ************ Epoch=5 end ************
2024-11-12 12:36:22 INFO [Metrics] AUC-ROC: 0.804795 - AUC-PR: 0.606388 - ACC: 0.792276 - Precision: 0.659346 - Recall: 0.391616 - F1: 0.491379 - MCC: 0.391236 - Logloss: 0.446378 - MSE: 0.144552 - RMSE: 0.380200 - COPC: 0.997526 - KLD: 1.026933
2024-11-12 12:36:22 INFO Save best model: monitor(max): 0.358417
2024-11-12 12:36:22 INFO --- 1223/1223 batches finished ---
2024-11-12 12:36:22 INFO Train loss: 0.453490
2024-11-12 12:36:22 INFO ************ Epoch=6 end ************
2024-11-12 12:40:34 INFO [Metrics] AUC-ROC: 0.804887 - AUC-PR: 0.606368 - ACC: 0.792257 - Precision: 0.655710 - Recall: 0.398390 - F1: 0.495643 - MCC: 0.392938 - Logloss: 0.446358 - MSE: 0.144560 - RMSE: 0.380210 - COPC: 0.992653 - KLD: 1.026770
2024-11-12 12:40:34 INFO Save best model: monitor(max): 0.358529
2024-11-12 12:40:35 INFO --- 1223/1223 batches finished ---
2024-11-12 12:40:35 INFO Train loss: 0.453317
2024-11-12 12:40:35 INFO ************ Epoch=7 end ************
2024-11-12 12:44:47 INFO [Metrics] AUC-ROC: 0.804937 - AUC-PR: 0.606568 - ACC: 0.792287 - Precision: 0.657644 - Recall: 0.394912 - F1: 0.493488 - MCC: 0.392114 - Logloss: 0.446248 - MSE: 0.144520 - RMSE: 0.380157 - COPC: 0.988511 - KLD: 1.026466
2024-11-12 12:44:47 INFO Save best model: monitor(max): 0.358689
2024-11-12 12:44:48 INFO --- 1223/1223 batches finished ---
2024-11-12 12:44:48 INFO Train loss: 0.453155
2024-11-12 12:44:48 INFO ************ Epoch=8 end ************
2024-11-12 12:49:02 INFO [Metrics] AUC-ROC: 0.805024 - AUC-PR: 0.606572 - ACC: 0.792327 - Precision: 0.662376 - Recall: 0.386482 - F1: 0.488143 - MCC: 0.390071 - Logloss: 0.446176 - MSE: 0.144503 - RMSE: 0.380135 - COPC: 0.998520 - KLD: 1.026289
2024-11-12 12:49:02 INFO Save best model: monitor(max): 0.358848
2024-11-12 12:49:02 INFO --- 1223/1223 batches finished ---
2024-11-12 12:49:03 INFO Train loss: 0.453031
2024-11-12 12:49:03 INFO ************ Epoch=9 end ************
2024-11-12 12:53:15 INFO [Metrics] AUC-ROC: 0.805192 - AUC-PR: 0.606787 - ACC: 0.792317 - Precision: 0.668948 - Recall: 0.375054 - F1: 0.480635 - MCC: 0.387227 - Logloss: 0.446060 - MSE: 0.144472 - RMSE: 0.380095 - COPC: 1.006623 - KLD: 1.025880
2024-11-12 12:53:15 INFO Save best model: monitor(max): 0.359132
2024-11-12 12:53:15 INFO --- 1223/1223 batches finished ---
2024-11-12 12:53:15 INFO Train loss: 0.452901
2024-11-12 12:53:15 INFO ************ Epoch=10 end ************
2024-11-12 12:57:28 INFO [Metrics] AUC-ROC: 0.805393 - AUC-PR: 0.607227 - ACC: 0.792418 - Precision: 0.663140 - Recall: 0.385838 - F1: 0.487836 - MCC: 0.390157 - Logloss: 0.445815 - MSE: 0.144376 - RMSE: 0.379969 - COPC: 1.000464 - KLD: 1.025344
2024-11-12 12:57:28 INFO Save best model: monitor(max): 0.359578
2024-11-12 12:57:28 INFO --- 1223/1223 batches finished ---
2024-11-12 12:57:28 INFO Train loss: 0.452796
2024-11-12 12:57:28 INFO ************ Epoch=11 end ************
2024-11-12 13:01:44 INFO [Metrics] AUC-ROC: 0.805289 - AUC-PR: 0.606972 - ACC: 0.792539 - Precision: 0.659929 - Recall: 0.392649 - F1: 0.492354 - MCC: 0.392206 - Logloss: 0.445946 - MSE: 0.144420 - RMSE: 0.380026 - COPC: 1.000663 - KLD: 1.025674
2024-11-12 13:01:44 INFO Monitor(max) STOP: 0.359344 !
2024-11-12 13:01:44 INFO Reduce learning rate on plateau: 0.000100
2024-11-12 13:01:44 INFO --- 1223/1223 batches finished ---
2024-11-12 13:01:45 INFO Train loss: 0.452671
2024-11-12 13:01:45 INFO ************ Epoch=12 end ************
2024-11-12 13:05:57 INFO [Metrics] AUC-ROC: 0.807920 - AUC-PR: 0.611119 - ACC: 0.793530 - Precision: 0.656685 - Recall: 0.406916 - F1: 0.502474 - MCC: 0.398525 - Logloss: 0.443741 - MSE: 0.143623 - RMSE: 0.378976 - COPC: 0.997693 - KLD: 1.019807
2024-11-12 13:05:57 INFO Save best model: monitor(max): 0.364179
2024-11-12 13:05:58 INFO --- 1223/1223 batches finished ---
2024-11-12 13:05:58 INFO Train loss: 0.444116
2024-11-12 13:05:58 INFO ************ Epoch=13 end ************
2024-11-12 13:10:09 INFO [Metrics] AUC-ROC: 0.808266 - AUC-PR: 0.611725 - ACC: 0.793786 - Precision: 0.661596 - Recall: 0.399541 - F1: 0.498211 - MCC: 0.397302 - Logloss: 0.443365 - MSE: 0.143481 - RMSE: 0.378789 - COPC: 1.005950 - KLD: 1.018882
2024-11-12 13:10:09 INFO Save best model: monitor(max): 0.364902
2024-11-12 13:10:09 INFO --- 1223/1223 batches finished ---
2024-11-12 13:10:09 INFO Train loss: 0.440928
2024-11-12 13:10:09 INFO ************ Epoch=14 end ************
2024-11-12 13:14:23 INFO [Metrics] AUC-ROC: 0.808218 - AUC-PR: 0.611640 - ACC: 0.793726 - Precision: 0.658417 - Recall: 0.405116 - F1: 0.501602 - MCC: 0.398572 - Logloss: 0.443524 - MSE: 0.143514 - RMSE: 0.378832 - COPC: 1.003009 - KLD: 1.019344
2024-11-12 13:14:23 INFO Monitor(max) STOP: 0.364694 !
2024-11-12 13:14:23 INFO Reduce learning rate on plateau: 0.000010
2024-11-12 13:14:23 INFO --- 1223/1223 batches finished ---
2024-11-12 13:14:24 INFO Train loss: 0.439399
2024-11-12 13:14:24 INFO ************ Epoch=15 end ************
2024-11-12 13:18:37 INFO [Metrics] AUC-ROC: 0.807906 - AUC-PR: 0.611138 - ACC: 0.793462 - Precision: 0.653128 - Recall: 0.413542 - F1: 0.506428 - MCC: 0.400096 - Logloss: 0.444131 - MSE: 0.143685 - RMSE: 0.379058 - COPC: 1.000678 - KLD: 1.020979
2024-11-12 13:18:37 INFO Monitor(max) STOP: 0.363775 !
2024-11-12 13:18:37 INFO Reduce learning rate on plateau: 0.000001
2024-11-12 13:18:37 INFO Early stopping at epoch=16
2024-11-12 13:18:37 INFO --- 1223/1223 batches finished ---
2024-11-12 13:18:37 INFO Train loss: 0.436282
2024-11-12 13:18:37 INFO Training finished.
2024-11-12 13:18:37 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/NFM/Criteo/NFM_model.ckpt
2024-11-12 13:18:37 INFO Start evaluate model
2024-11-12 13:19:17 INFO [Metrics] AUC-ROC: 0.808266 - AUC-PR: 0.611725 - ACC: 0.793786 - Precision: 0.661596 - Recall: 0.399541 - F1: 0.498211 - MCC: 0.397302 - Logloss: 0.443365 - MSE: 0.143481 - RMSE: 0.378789 - COPC: 1.005950 - KLD: 1.018882
2024-11-12 13:19:17 INFO Start testing model
2024-11-12 13:19:57 INFO [Metrics] AUC-ROC: 0.808802 - AUC-PR: 0.612184 - ACC: 0.793837 - Precision: 0.661424 - Recall: 0.400276 - F1: 0.498733 - MCC: 0.397627 - Logloss: 0.442896 - MSE: 0.143353 - RMSE: 0.378619 - COPC: 1.005478 - KLD: 1.017389
