2024-11-19 13:00:20 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='CCPM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=6000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-19 13:00:20 INFO Start process Criteo !
2024-11-19 13:00:20 INFO Loading Criteo dataset
2024-11-19 13:00:20 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-19 13:00:24 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-19 13:00:25 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-19 13:00:26 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-19 13:00:26 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-19 13:00:26 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-19 13:00:26 INFO Loading data done
2024-11-19 13:00:27 INFO Model: CCPM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (conv_layer): CCPM_ConvLayer(
    (conv_layer): Sequential(
      (0): ZeroPad2d((0, 0, 6, 6))
      (1): Conv2d(1, 64, kernel_size=(7, 1), stride=(1, 1))
      (2): KMaxPooling()
      (3): Tanh()
      (4): ZeroPad2d((0, 0, 4, 4))
      (5): Conv2d(64, 128, kernel_size=(5, 1), stride=(1, 1))
      (6): KMaxPooling()
      (7): Tanh()
      (8): ZeroPad2d((0, 0, 2, 2))
      (9): Conv2d(128, 256, kernel_size=(3, 1), stride=(1, 1))
      (10): KMaxPooling()
      (11): Tanh()
    )
  )
  (fc): Linear(in_features=12288, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-19 13:00:27 INFO Model parameters: 14723777
2024-11-19 13:00:27 INFO Start training model
2024-11-19 13:00:27 INFO Start training: 6113 batches/epoch
2024-11-19 13:00:27 INFO ************ Epoch=1 start ************
2024-11-19 14:43:34 INFO [Metrics] AUC-ROC: 0.801264 - AUC-PR: 0.600565 - ACC: 0.790530 - Precision: 0.667341 - Recall: 0.363839 - F1: 0.470926 - MCC: 0.379489 - Logloss: 0.449699 - MSE: 0.145713 - RMSE: 0.381724 - COPC: 1.027721 - KLD: 1.035310
2024-11-19 14:43:34 INFO Save best model: monitor(max): 0.351564
2024-11-19 14:43:34 INFO --- 6113/6113 batches finished ---
2024-11-19 14:43:34 INFO Train loss: 0.465051
2024-11-19 14:43:34 INFO ************ Epoch=1 end ************
2024-11-19 16:27:08 INFO [Metrics] AUC-ROC: 0.802915 - AUC-PR: 0.603383 - ACC: 0.791129 - Precision: 0.673144 - Recall: 0.359248 - F1: 0.468476 - MCC: 0.380106 - Logloss: 0.448336 - MSE: 0.145233 - RMSE: 0.381094 - COPC: 1.010451 - KLD: 1.031931
2024-11-19 16:27:08 INFO Save best model: monitor(max): 0.354579
2024-11-19 16:27:08 INFO --- 6113/6113 batches finished ---
2024-11-19 16:27:08 INFO Train loss: 0.459474
2024-11-19 16:27:08 INFO ************ Epoch=2 end ************
2024-11-19 18:10:29 INFO [Metrics] AUC-ROC: 0.803539 - AUC-PR: 0.603802 - ACC: 0.791413 - Precision: 0.649006 - Recall: 0.404889 - F1: 0.498675 - MCC: 0.392460 - Logloss: 0.447758 - MSE: 0.145074 - RMSE: 0.380885 - COPC: 0.975656 - KLD: 1.029892
2024-11-19 18:10:29 INFO Save best model: monitor(max): 0.355781
2024-11-19 18:10:29 INFO --- 6113/6113 batches finished ---
2024-11-19 18:10:29 INFO Train loss: 0.458433
2024-11-19 18:10:29 INFO ************ Epoch=3 end ************
2024-11-19 19:53:11 INFO [Metrics] AUC-ROC: 0.803955 - AUC-PR: 0.605045 - ACC: 0.792006 - Precision: 0.662917 - Recall: 0.382960 - F1: 0.485469 - MCC: 0.388309 - Logloss: 0.447831 - MSE: 0.144954 - RMSE: 0.380728 - COPC: 1.036488 - KLD: 1.030546
2024-11-19 19:53:11 INFO Save best model: monitor(max): 0.356124
2024-11-19 19:53:11 INFO --- 6113/6113 batches finished ---
2024-11-19 19:53:12 INFO Train loss: 0.457993
2024-11-19 19:53:12 INFO ************ Epoch=4 end ************
2024-11-19 21:35:57 INFO [Metrics] AUC-ROC: 0.804195 - AUC-PR: 0.605341 - ACC: 0.791564 - Precision: 0.644665 - Recall: 0.415560 - F1: 0.505359 - MCC: 0.395780 - Logloss: 0.447409 - MSE: 0.144939 - RMSE: 0.380708 - COPC: 0.956669 - KLD: 1.028255
2024-11-19 21:35:57 INFO Save best model: monitor(max): 0.356786
2024-11-19 21:35:57 INFO --- 6113/6113 batches finished ---
2024-11-19 21:35:57 INFO Train loss: 0.457728
2024-11-19 21:35:57 INFO ************ Epoch=5 end ************
2024-11-19 23:18:39 INFO [Metrics] AUC-ROC: 0.804173 - AUC-PR: 0.605317 - ACC: 0.791710 - Precision: 0.671281 - Recall: 0.366591 - F1: 0.474212 - MCC: 0.383493 - Logloss: 0.447105 - MSE: 0.144829 - RMSE: 0.380565 - COPC: 1.029853 - KLD: 1.028334
2024-11-19 23:18:39 INFO Save best model: monitor(max): 0.357069
2024-11-19 23:18:39 INFO --- 6113/6113 batches finished ---
2024-11-19 23:18:39 INFO Train loss: 0.457496
2024-11-19 23:18:39 INFO ************ Epoch=6 end ************
2024-11-20 01:01:59 INFO [Metrics] AUC-ROC: 0.804667 - AUC-PR: 0.605965 - ACC: 0.792076 - Precision: 0.655413 - Recall: 0.397482 - F1: 0.494854 - MCC: 0.392221 - Logloss: 0.446629 - MSE: 0.144642 - RMSE: 0.380318 - COPC: 0.976934 - KLD: 1.027178
2024-11-20 01:01:59 INFO Save best model: monitor(max): 0.358039
2024-11-20 01:01:59 INFO --- 6113/6113 batches finished ---
2024-11-20 01:02:00 INFO Train loss: 0.457413
2024-11-20 01:02:00 INFO ************ Epoch=7 end ************
2024-11-20 02:45:33 INFO [Metrics] AUC-ROC: 0.804269 - AUC-PR: 0.605298 - ACC: 0.791926 - Precision: 0.655633 - Recall: 0.395823 - F1: 0.493629 - MCC: 0.391389 - Logloss: 0.447043 - MSE: 0.144775 - RMSE: 0.380494 - COPC: 0.974188 - KLD: 1.028280
2024-11-20 02:45:33 INFO Monitor(max) STOP: 0.357227 !
2024-11-20 02:45:33 INFO Reduce learning rate on plateau: 0.000100
2024-11-20 02:45:33 INFO --- 6113/6113 batches finished ---
2024-11-20 02:45:33 INFO Train loss: 0.457298
2024-11-20 02:45:33 INFO ************ Epoch=8 end ************
2024-11-20 04:28:34 INFO [Metrics] AUC-ROC: 0.808946 - AUC-PR: 0.613037 - ACC: 0.794097 - Precision: 0.661394 - Recall: 0.402414 - F1: 0.500380 - MCC: 0.398864 - Logloss: 0.442691 - MSE: 0.143236 - RMSE: 0.378465 - COPC: 0.996002 - KLD: 1.017082
2024-11-20 04:28:34 INFO Save best model: monitor(max): 0.366255
2024-11-20 04:28:35 INFO --- 6113/6113 batches finished ---
2024-11-20 04:28:35 INFO Train loss: 0.447323
2024-11-20 04:28:35 INFO ************ Epoch=9 end ************
2024-11-20 06:11:27 INFO [Metrics] AUC-ROC: 0.809715 - AUC-PR: 0.614213 - ACC: 0.794400 - Precision: 0.662097 - Recall: 0.403503 - F1: 0.501423 - MCC: 0.399944 - Logloss: 0.442063 - MSE: 0.143005 - RMSE: 0.378160 - COPC: 1.004076 - KLD: 1.015444
2024-11-20 06:11:27 INFO Save best model: monitor(max): 0.367651
2024-11-20 06:11:27 INFO --- 6113/6113 batches finished ---
2024-11-20 06:11:27 INFO Train loss: 0.443162
2024-11-20 06:11:27 INFO ************ Epoch=10 end ************
2024-11-20 07:54:29 INFO [Metrics] AUC-ROC: 0.809891 - AUC-PR: 0.614497 - ACC: 0.794477 - Precision: 0.663423 - Recall: 0.401646 - F1: 0.500364 - MCC: 0.399683 - Logloss: 0.441831 - MSE: 0.142935 - RMSE: 0.378068 - COPC: 1.001301 - KLD: 1.014803
2024-11-20 07:54:29 INFO Save best model: monitor(max): 0.368060
2024-11-20 07:54:29 INFO --- 6113/6113 batches finished ---
2024-11-20 07:54:29 INFO Train loss: 0.441162
2024-11-20 07:54:29 INFO ************ Epoch=11 end ************
2024-11-20 09:38:07 INFO [Metrics] AUC-ROC: 0.809877 - AUC-PR: 0.614380 - ACC: 0.794430 - Precision: 0.657456 - Recall: 0.412731 - F1: 0.507112 - MCC: 0.402397 - Logloss: 0.442056 - MSE: 0.143006 - RMSE: 0.378161 - COPC: 0.996875 - KLD: 1.015285
2024-11-20 09:38:07 INFO Monitor(max) STOP: 0.367821 !
2024-11-20 09:38:07 INFO Reduce learning rate on plateau: 0.000010
2024-11-20 09:38:07 INFO --- 6113/6113 batches finished ---
2024-11-20 09:38:07 INFO Train loss: 0.439601
2024-11-20 09:38:07 INFO ************ Epoch=12 end ************
2024-11-20 11:21:25 INFO [Metrics] AUC-ROC: 0.809269 - AUC-PR: 0.613259 - ACC: 0.794094 - Precision: 0.655719 - Recall: 0.413474 - F1: 0.507154 - MCC: 0.401718 - Logloss: 0.442865 - MSE: 0.143263 - RMSE: 0.378501 - COPC: 1.001040 - KLD: 1.017350
2024-11-20 11:21:25 INFO Monitor(max) STOP: 0.366404 !
2024-11-20 11:21:25 INFO Reduce learning rate on plateau: 0.000001
2024-11-20 11:21:25 INFO Early stopping at epoch=13
2024-11-20 11:21:25 INFO --- 6113/6113 batches finished ---
2024-11-20 11:21:25 INFO Train loss: 0.434545
2024-11-20 11:21:25 INFO Training finished.
2024-11-20 11:21:25 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/CCPM/Criteo/CCPM_model_seed2020.ckpt
2024-11-20 11:21:25 INFO Start evaluate model
2024-11-20 11:27:56 INFO [Metrics] AUC-ROC: 0.809891 - AUC-PR: 0.614497 - ACC: 0.794477 - Precision: 0.663423 - Recall: 0.401646 - F1: 0.500364 - MCC: 0.399683 - Logloss: 0.441831 - MSE: 0.142935 - RMSE: 0.378068 - COPC: 1.001301 - KLD: 1.014803
2024-11-20 11:27:56 INFO Start testing model
2024-11-20 11:34:27 INFO [Metrics] AUC-ROC: 0.810248 - AUC-PR: 0.614730 - ACC: 0.794727 - Precision: 0.663676 - Recall: 0.403150 - F1: 0.501602 - MCC: 0.400726 - Logloss: 0.441513 - MSE: 0.142857 - RMSE: 0.377965 - COPC: 1.000855 - KLD: 1.013732
