2024-11-12 13:26:45 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='FmFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-06, save_best_only=True, seed=2020, task='binary_classification', log_dir='../output/logs/')
2024-11-12 13:26:45 INFO Start process Criteo !
2024-11-12 13:26:45 INFO Loading Criteo dataset
2024-11-12 13:26:45 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-12 13:26:50 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-12 13:26:50 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-12 13:26:51 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-12 13:26:51 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 13:26:51 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 13:26:51 INFO Loading data done
2024-11-12 13:26:52 INFO Model: FmFM(
  (embedding_layer): EmbeddingLayer_v3(
    (embedding_layer): EmbeddingDictLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 16)
        (I2): Embedding(98, 16)
        (I3): Embedding(121, 16)
        (I4): Embedding(40, 16)
        (I5): Embedding(219, 16)
        (I6): Embedding(111, 16)
        (I7): Embedding(79, 16)
        (I8): Embedding(68, 16)
        (I9): Embedding(91, 16)
        (I10): Embedding(5, 16)
        (I11): Embedding(26, 16)
        (I12): Embedding(36, 16)
        (I13): Embedding(71, 16)
        (C1): Embedding(1445, 16)
        (C2): Embedding(553, 16)
        (C3): Embedding(157338, 16)
        (C4): Embedding(117821, 16)
        (C5): Embedding(305, 16)
        (C6): Embedding(17, 16)
        (C7): Embedding(11881, 16)
        (C8): Embedding(629, 16)
        (C9): Embedding(4, 16)
        (C10): Embedding(39529, 16)
        (C11): Embedding(5130, 16)
        (C12): Embedding(156655, 16)
        (C13): Embedding(3175, 16)
        (C14): Embedding(27, 16)
        (C15): Embedding(11042, 16)
        (C16): Embedding(148912, 16)
        (C17): Embedding(11, 16)
        (C18): Embedding(4559, 16)
        (C19): Embedding(2002, 16)
        (C20): Embedding(4, 16)
        (C21): Embedding(154563, 16)
        (C22): Embedding(17, 16)
        (C23): Embedding(16, 16)
        (C24): Embedding(53030, 16)
        (C25): Embedding(81, 16)
        (C26): Embedding(40954, 16)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 1, padding_idx=42)
        (I2): Embedding(98, 1, padding_idx=97)
        (I3): Embedding(121, 1, padding_idx=120)
        (I4): Embedding(40, 1, padding_idx=39)
        (I5): Embedding(219, 1, padding_idx=218)
        (I6): Embedding(111, 1, padding_idx=110)
        (I7): Embedding(79, 1, padding_idx=78)
        (I8): Embedding(68, 1, padding_idx=67)
        (I9): Embedding(91, 1, padding_idx=90)
        (I10): Embedding(5, 1, padding_idx=4)
        (I11): Embedding(26, 1, padding_idx=25)
        (I12): Embedding(36, 1, padding_idx=35)
        (I13): Embedding(71, 1, padding_idx=70)
        (C1): Embedding(1445, 1, padding_idx=1444)
        (C2): Embedding(553, 1, padding_idx=552)
        (C3): Embedding(157338, 1, padding_idx=157337)
        (C4): Embedding(117821, 1, padding_idx=117820)
        (C5): Embedding(305, 1, padding_idx=304)
        (C6): Embedding(17, 1, padding_idx=16)
        (C7): Embedding(11881, 1, padding_idx=11880)
        (C8): Embedding(629, 1, padding_idx=628)
        (C9): Embedding(4, 1, padding_idx=3)
        (C10): Embedding(39529, 1, padding_idx=39528)
        (C11): Embedding(5130, 1, padding_idx=5129)
        (C12): Embedding(156655, 1, padding_idx=156654)
        (C13): Embedding(3175, 1, padding_idx=3174)
        (C14): Embedding(27, 1, padding_idx=26)
        (C15): Embedding(11042, 1, padding_idx=11041)
        (C16): Embedding(148912, 1, padding_idx=148911)
        (C17): Embedding(11, 1, padding_idx=10)
        (C18): Embedding(4559, 1, padding_idx=4558)
        (C19): Embedding(2002, 1, padding_idx=2001)
        (C20): Embedding(4, 1, padding_idx=3)
        (C21): Embedding(154563, 1, padding_idx=154562)
        (C22): Embedding(17, 1, padding_idx=16)
        (C23): Embedding(16, 1, padding_idx=15)
        (C24): Embedding(53030, 1, padding_idx=53029)
        (C25): Embedding(81, 1, padding_idx=80)
        (C26): Embedding(40954, 1, padding_idx=40953)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (final_activation): Sigmoid()
)
2024-11-12 13:26:52 INFO Model parameters: 15671732
2024-11-12 13:26:52 INFO Start training model
2024-11-12 13:26:52 INFO Start training: 3668 batches/epoch
2024-11-12 13:26:52 INFO ************ Epoch=1 start ************
2024-11-12 13:39:21 INFO [Metrics] AUC-ROC: 0.805854 - AUC-PR: 0.607862 - ACC: 0.792765 - Precision: 0.668856 - Recall: 0.378670 - F1: 0.483570 - MCC: 0.389349 - Logloss: 0.445533 - MSE: 0.144227 - RMSE: 0.379772 - COPC: 1.011503 - KLD: 1.024556
2024-11-12 13:39:21 INFO Save best model: monitor(max): 0.360321
2024-11-12 13:39:21 INFO --- 3668/3668 batches finished ---
2024-11-12 13:39:22 INFO Train loss: 0.456350
2024-11-12 13:39:22 INFO ************ Epoch=1 end ************
2024-11-12 13:51:53 INFO [Metrics] AUC-ROC: 0.809046 - AUC-PR: 0.612951 - ACC: 0.794163 - Precision: 0.668425 - Recall: 0.390219 - F1: 0.492766 - MCC: 0.396004 - Logloss: 0.442629 - MSE: 0.143216 - RMSE: 0.378439 - COPC: 1.011207 - KLD: 1.016843
2024-11-12 13:51:53 INFO Save best model: monitor(max): 0.366417
2024-11-12 13:51:53 INFO --- 3668/3668 batches finished ---
2024-11-12 13:51:54 INFO Train loss: 0.447544
2024-11-12 13:51:54 INFO ************ Epoch=2 end ************
2024-11-12 14:03:59 INFO [Metrics] AUC-ROC: 0.810045 - AUC-PR: 0.614505 - ACC: 0.794554 - Precision: 0.658273 - Recall: 0.412122 - F1: 0.506894 - MCC: 0.402564 - Logloss: 0.441787 - MSE: 0.142934 - RMSE: 0.378066 - COPC: 0.982440 - KLD: 1.014341
2024-11-12 14:03:59 INFO Save best model: monitor(max): 0.368258
2024-11-12 14:03:59 INFO --- 3668/3668 batches finished ---
2024-11-12 14:03:59 INFO Train loss: 0.445163
2024-11-12 14:03:59 INFO ************ Epoch=3 end ************
2024-11-12 14:15:40 INFO [Metrics] AUC-ROC: 0.810204 - AUC-PR: 0.614781 - ACC: 0.794648 - Precision: 0.665347 - Recall: 0.399461 - F1: 0.499207 - MCC: 0.399593 - Logloss: 0.441580 - MSE: 0.142848 - RMSE: 0.377953 - COPC: 1.005403 - KLD: 1.014043
2024-11-12 14:15:40 INFO Save best model: monitor(max): 0.368624
2024-11-12 14:15:40 INFO --- 3668/3668 batches finished ---
2024-11-12 14:15:40 INFO Train loss: 0.443714
2024-11-12 14:15:40 INFO ************ Epoch=4 end ************
2024-11-12 14:24:55 INFO [Metrics] AUC-ROC: 0.810142 - AUC-PR: 0.614608 - ACC: 0.794797 - Precision: 0.664741 - Recall: 0.401742 - F1: 0.500814 - MCC: 0.400563 - Logloss: 0.441777 - MSE: 0.142904 - RMSE: 0.378027 - COPC: 1.013454 - KLD: 1.014529
2024-11-12 14:24:55 INFO Monitor(max) STOP: 0.368365 !
2024-11-12 14:24:55 INFO Reduce learning rate on plateau: 0.000100
2024-11-12 14:24:55 INFO --- 3668/3668 batches finished ---
2024-11-12 14:24:55 INFO Train loss: 0.442673
2024-11-12 14:24:55 INFO ************ Epoch=5 end ************
2024-11-12 14:34:15 INFO [Metrics] AUC-ROC: 0.810327 - AUC-PR: 0.614729 - ACC: 0.794624 - Precision: 0.658006 - Recall: 0.413211 - F1: 0.507639 - MCC: 0.403028 - Logloss: 0.441975 - MSE: 0.142949 - RMSE: 0.378087 - COPC: 1.006677 - KLD: 1.014840
2024-11-12 14:34:15 INFO Monitor(max) STOP: 0.368352 !
2024-11-12 14:34:15 INFO Reduce learning rate on plateau: 0.000010
2024-11-12 14:34:15 INFO Early stopping at epoch=6
2024-11-12 14:34:15 INFO --- 3668/3668 batches finished ---
2024-11-12 14:34:15 INFO Train loss: 0.430268
2024-11-12 14:34:15 INFO Training finished.
2024-11-12 14:34:15 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/FmFM/Criteo/FmFM_model.ckpt
2024-11-12 14:34:15 INFO Start evaluate model
2024-11-12 14:34:56 INFO [Metrics] AUC-ROC: 0.810204 - AUC-PR: 0.614781 - ACC: 0.794648 - Precision: 0.665347 - Recall: 0.399461 - F1: 0.499207 - MCC: 0.399593 - Logloss: 0.441580 - MSE: 0.142848 - RMSE: 0.377953 - COPC: 1.005403 - KLD: 1.014043
2024-11-12 14:34:56 INFO Start testing model
2024-11-12 14:35:37 INFO [Metrics] AUC-ROC: 0.810609 - AUC-PR: 0.615131 - ACC: 0.794817 - Precision: 0.665253 - Recall: 0.400959 - F1: 0.500349 - MCC: 0.400420 - Logloss: 0.441243 - MSE: 0.142751 - RMSE: 0.377824 - COPC: 1.004874 - KLD: 1.012983
