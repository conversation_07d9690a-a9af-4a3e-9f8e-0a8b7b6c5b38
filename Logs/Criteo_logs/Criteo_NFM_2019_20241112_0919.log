2024-11-12 09:19:31 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='NFM', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=30000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-12 09:19:31 INFO Start process Criteo !
2024-11-12 09:19:31 INFO Loading Criteo dataset
2024-11-12 09:19:31 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-12 09:19:36 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-12 09:19:36 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-12 09:19:38 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-12 09:19:38 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 09:19:38 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-12 09:19:38 INFO Loading data done
2024-11-12 09:19:38 INFO Model: NFM(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (lr_layer): LR_Layer(
    (embedding_layer): EmbeddingLayer(
      (embedding_layer): ModuleDict(
        (I1): Embedding(43, 1, padding_idx=42)
        (I2): Embedding(98, 1, padding_idx=97)
        (I3): Embedding(121, 1, padding_idx=120)
        (I4): Embedding(40, 1, padding_idx=39)
        (I5): Embedding(219, 1, padding_idx=218)
        (I6): Embedding(111, 1, padding_idx=110)
        (I7): Embedding(79, 1, padding_idx=78)
        (I8): Embedding(68, 1, padding_idx=67)
        (I9): Embedding(91, 1, padding_idx=90)
        (I10): Embedding(5, 1, padding_idx=4)
        (I11): Embedding(26, 1, padding_idx=25)
        (I12): Embedding(36, 1, padding_idx=35)
        (I13): Embedding(71, 1, padding_idx=70)
        (C1): Embedding(1445, 1, padding_idx=1444)
        (C2): Embedding(553, 1, padding_idx=552)
        (C3): Embedding(157338, 1, padding_idx=157337)
        (C4): Embedding(117821, 1, padding_idx=117820)
        (C5): Embedding(305, 1, padding_idx=304)
        (C6): Embedding(17, 1, padding_idx=16)
        (C7): Embedding(11881, 1, padding_idx=11880)
        (C8): Embedding(629, 1, padding_idx=628)
        (C9): Embedding(4, 1, padding_idx=3)
        (C10): Embedding(39529, 1, padding_idx=39528)
        (C11): Embedding(5130, 1, padding_idx=5129)
        (C12): Embedding(156655, 1, padding_idx=156654)
        (C13): Embedding(3175, 1, padding_idx=3174)
        (C14): Embedding(27, 1, padding_idx=26)
        (C15): Embedding(11042, 1, padding_idx=11041)
        (C16): Embedding(148912, 1, padding_idx=148911)
        (C17): Embedding(11, 1, padding_idx=10)
        (C18): Embedding(4559, 1, padding_idx=4558)
        (C19): Embedding(2002, 1, padding_idx=2001)
        (C20): Embedding(4, 1, padding_idx=3)
        (C21): Embedding(154563, 1, padding_idx=154562)
        (C22): Embedding(17, 1, padding_idx=16)
        (C23): Embedding(16, 1, padding_idx=15)
        (C24): Embedding(53030, 1, padding_idx=53029)
        (C25): Embedding(81, 1, padding_idx=80)
        (C26): Embedding(40954, 1, padding_idx=40953)
      )
      (seq_encoder_layer): ModuleDict()
    )
  )
  (inner_product_layer): InnerProductLayer()
  (dnn): DNN_Layer(
    (dnn): Sequential(
      (0): Linear(in_features=16, out_features=1000, bias=True)
      (1): ReLU()
      (2): Linear(in_features=1000, out_features=1000, bias=True)
      (3): ReLU()
      (4): Linear(in_features=1000, out_features=1000, bias=True)
      (5): ReLU()
      (6): Linear(in_features=1000, out_features=1, bias=True)
    )
  )
  (final_activation): Sigmoid()
)
2024-11-12 09:19:38 INFO Model parameters: 17502037
2024-11-12 09:19:38 INFO Start training model
2024-11-12 09:19:39 INFO Start training: 1223 batches/epoch
2024-11-12 09:19:39 INFO ************ Epoch=1 start ************
2024-11-12 09:23:45 INFO [Metrics] AUC-ROC: 0.799963 - AUC-PR: 0.598614 - ACC: 0.789945 - Precision: 0.645001 - Recall: 0.400762 - F1: 0.494361 - MCC: 0.387523 - Logloss: 0.451265 - MSE: 0.146273 - RMSE: 0.382456 - COPC: 0.949764 - KLD: 1.038674
2024-11-12 09:23:45 INFO Save best model: monitor(max): 0.348698
2024-11-12 09:23:45 INFO --- 1223/1223 batches finished ---
2024-11-12 09:23:45 INFO Train loss: 0.464288
2024-11-12 09:23:45 INFO ************ Epoch=1 end ************
2024-11-12 09:27:50 INFO [Metrics] AUC-ROC: 0.803033 - AUC-PR: 0.603296 - ACC: 0.791352 - Precision: 0.657702 - Recall: 0.387195 - F1: 0.487433 - MCC: 0.387611 - Logloss: 0.447998 - MSE: 0.145148 - RMSE: 0.380984 - COPC: 0.985519 - KLD: 1.030957
2024-11-12 09:27:50 INFO Save best model: monitor(max): 0.355035
2024-11-12 09:27:51 INFO --- 1223/1223 batches finished ---
2024-11-12 09:27:51 INFO Train loss: 0.455873
2024-11-12 09:27:51 INFO ************ Epoch=2 end ************
2024-11-12 09:31:54 INFO [Metrics] AUC-ROC: 0.803765 - AUC-PR: 0.604658 - ACC: 0.791834 - Precision: 0.664615 - Recall: 0.378631 - F1: 0.482425 - MCC: 0.386760 - Logloss: 0.447269 - MSE: 0.144897 - RMSE: 0.380654 - COPC: 1.009461 - KLD: 1.029146
2024-11-12 09:31:54 INFO Save best model: monitor(max): 0.356496
2024-11-12 09:31:54 INFO --- 1223/1223 batches finished ---
2024-11-12 09:31:54 INFO Train loss: 0.454439
2024-11-12 09:31:54 INFO ************ Epoch=3 end ************
2024-11-12 09:36:01 INFO [Metrics] AUC-ROC: 0.804335 - AUC-PR: 0.605627 - ACC: 0.792053 - Precision: 0.664286 - Recall: 0.380922 - F1: 0.484193 - MCC: 0.387930 - Logloss: 0.447110 - MSE: 0.144768 - RMSE: 0.380484 - COPC: 1.029939 - KLD: 1.028664
2024-11-12 09:36:01 INFO Save best model: monitor(max): 0.357225
2024-11-12 09:36:01 INFO --- 1223/1223 batches finished ---
2024-11-12 09:36:01 INFO Train loss: 0.453784
2024-11-12 09:36:01 INFO ************ Epoch=4 end ************
2024-11-12 09:40:15 INFO [Metrics] AUC-ROC: 0.804598 - AUC-PR: 0.605964 - ACC: 0.792167 - Precision: 0.654754 - Recall: 0.399528 - F1: 0.496248 - MCC: 0.393000 - Logloss: 0.446641 - MSE: 0.144662 - RMSE: 0.380345 - COPC: 0.977810 - KLD: 1.027302
2024-11-12 09:40:15 INFO Save best model: monitor(max): 0.357958
2024-11-12 09:40:15 INFO --- 1223/1223 batches finished ---
2024-11-12 09:40:15 INFO Train loss: 0.453391
2024-11-12 09:40:15 INFO ************ Epoch=5 end ************
2024-11-12 09:44:24 INFO [Metrics] AUC-ROC: 0.805004 - AUC-PR: 0.606532 - ACC: 0.792240 - Precision: 0.658508 - Recall: 0.392896 - F1: 0.492152 - MCC: 0.391469 - Logloss: 0.446289 - MSE: 0.144540 - RMSE: 0.380184 - COPC: 0.979281 - KLD: 1.026413
2024-11-12 09:44:24 INFO Save best model: monitor(max): 0.358715
2024-11-12 09:44:24 INFO --- 1223/1223 batches finished ---
2024-11-12 09:44:24 INFO Train loss: 0.453109
2024-11-12 09:44:24 INFO ************ Epoch=6 end ************
2024-11-12 09:48:34 INFO [Metrics] AUC-ROC: 0.805047 - AUC-PR: 0.606771 - ACC: 0.792232 - Precision: 0.662520 - Recall: 0.385464 - F1: 0.487369 - MCC: 0.389554 - Logloss: 0.446161 - MSE: 0.144485 - RMSE: 0.380112 - COPC: 1.012607 - KLD: 1.026261
2024-11-12 09:48:34 INFO Save best model: monitor(max): 0.358885
2024-11-12 09:48:34 INFO --- 1223/1223 batches finished ---
2024-11-12 09:48:34 INFO Train loss: 0.452862
2024-11-12 09:48:34 INFO ************ Epoch=7 end ************
2024-11-12 09:52:53 INFO [Metrics] AUC-ROC: 0.805303 - AUC-PR: 0.607056 - ACC: 0.792425 - Precision: 0.664885 - Recall: 0.382815 - F1: 0.485879 - MCC: 0.389424 - Logloss: 0.445989 - MSE: 0.144424 - RMSE: 0.380032 - COPC: 1.016191 - KLD: 1.025723
2024-11-12 09:52:53 INFO Save best model: monitor(max): 0.359315
2024-11-12 09:52:53 INFO --- 1223/1223 batches finished ---
2024-11-12 09:52:53 INFO Train loss: 0.452668
2024-11-12 09:52:53 INFO ************ Epoch=8 end ************
2024-11-12 09:57:07 INFO [Metrics] AUC-ROC: 0.805465 - AUC-PR: 0.607421 - ACC: 0.792441 - Precision: 0.656871 - Recall: 0.397645 - F1: 0.495396 - MCC: 0.393232 - Logloss: 0.445800 - MSE: 0.144371 - RMSE: 0.379961 - COPC: 0.983058 - KLD: 1.025174
2024-11-12 09:57:07 INFO Save best model: monitor(max): 0.359665
2024-11-12 09:57:07 INFO --- 1223/1223 batches finished ---
2024-11-12 09:57:07 INFO Train loss: 0.452483
2024-11-12 09:57:07 INFO ************ Epoch=9 end ************
2024-11-12 10:01:15 INFO [Metrics] AUC-ROC: 0.805415 - AUC-PR: 0.607450 - ACC: 0.792476 - Precision: 0.659471 - Recall: 0.392998 - F1: 0.492501 - MCC: 0.392127 - Logloss: 0.445824 - MSE: 0.144373 - RMSE: 0.379965 - COPC: 0.984594 - KLD: 1.025353
2024-11-12 10:01:15 INFO Monitor(max) STOP: 0.359591 !
2024-11-12 10:01:15 INFO Reduce learning rate on plateau: 0.000100
2024-11-12 10:01:15 INFO --- 1223/1223 batches finished ---
2024-11-12 10:01:16 INFO Train loss: 0.452356
2024-11-12 10:01:16 INFO ************ Epoch=10 end ************
2024-11-12 10:05:28 INFO [Metrics] AUC-ROC: 0.808180 - AUC-PR: 0.611666 - ACC: 0.793672 - Precision: 0.658075 - Recall: 0.405347 - F1: 0.501680 - MCC: 0.398491 - Logloss: 0.443432 - MSE: 0.143523 - RMSE: 0.378844 - COPC: 0.994412 - KLD: 1.018960
2024-11-12 10:05:28 INFO Save best model: monitor(max): 0.364748
2024-11-12 10:05:28 INFO --- 1223/1223 batches finished ---
2024-11-12 10:05:29 INFO Train loss: 0.443873
2024-11-12 10:05:29 INFO ************ Epoch=11 end ************
2024-11-12 10:09:39 INFO [Metrics] AUC-ROC: 0.808465 - AUC-PR: 0.612070 - ACC: 0.793719 - Precision: 0.659085 - Recall: 0.403765 - F1: 0.500758 - MCC: 0.398204 - Logloss: 0.443231 - MSE: 0.143433 - RMSE: 0.378726 - COPC: 1.002005 - KLD: 1.018505
2024-11-12 10:09:39 INFO Save best model: monitor(max): 0.365235
2024-11-12 10:09:39 INFO --- 1223/1223 batches finished ---
2024-11-12 10:09:39 INFO Train loss: 0.440738
2024-11-12 10:09:39 INFO ************ Epoch=12 end ************
2024-11-12 10:13:46 INFO [Metrics] AUC-ROC: 0.808511 - AUC-PR: 0.612089 - ACC: 0.793673 - Precision: 0.655396 - Recall: 0.410659 - F1: 0.504935 - MCC: 0.399880 - Logloss: 0.443212 - MSE: 0.143439 - RMSE: 0.378733 - COPC: 0.987085 - KLD: 1.018225
2024-11-12 10:13:46 INFO Save best model: monitor(max): 0.365300
2024-11-12 10:13:46 INFO --- 1223/1223 batches finished ---
2024-11-12 10:13:46 INFO Train loss: 0.439228
2024-11-12 10:13:46 INFO ************ Epoch=13 end ************
2024-11-12 10:18:08 INFO [Metrics] AUC-ROC: 0.808452 - AUC-PR: 0.611932 - ACC: 0.793668 - Precision: 0.655146 - Recall: 0.411127 - F1: 0.505214 - MCC: 0.399992 - Logloss: 0.443419 - MSE: 0.143478 - RMSE: 0.378785 - COPC: 0.997427 - KLD: 1.018955
2024-11-12 10:18:08 INFO Monitor(max) STOP: 0.365033 !
2024-11-12 10:18:08 INFO Reduce learning rate on plateau: 0.000010
2024-11-12 10:18:08 INFO --- 1223/1223 batches finished ---
2024-11-12 10:18:08 INFO Train loss: 0.438063
2024-11-12 10:18:08 INFO ************ Epoch=14 end ************
2024-11-12 10:22:21 INFO [Metrics] AUC-ROC: 0.807958 - AUC-PR: 0.611204 - ACC: 0.793329 - Precision: 0.652185 - Recall: 0.414392 - F1: 0.506781 - MCC: 0.399980 - Logloss: 0.444248 - MSE: 0.143686 - RMSE: 0.379059 - COPC: 1.001281 - KLD: 1.021346
2024-11-12 10:22:21 INFO Monitor(max) STOP: 0.363709 !
2024-11-12 10:22:21 INFO Reduce learning rate on plateau: 0.000001
2024-11-12 10:22:21 INFO Early stopping at epoch=15
2024-11-12 10:22:21 INFO --- 1223/1223 batches finished ---
2024-11-12 10:22:21 INFO Train loss: 0.434779
2024-11-12 10:22:21 INFO Training finished.
2024-11-12 10:22:21 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/NFM/Criteo/NFM_model.ckpt
2024-11-12 10:22:22 INFO Start evaluate model
2024-11-12 10:23:04 INFO [Metrics] AUC-ROC: 0.808511 - AUC-PR: 0.612089 - ACC: 0.793673 - Precision: 0.655396 - Recall: 0.410659 - F1: 0.504935 - MCC: 0.399880 - Logloss: 0.443212 - MSE: 0.143439 - RMSE: 0.378733 - COPC: 0.987085 - KLD: 1.018225
2024-11-12 10:23:04 INFO Start testing model
2024-11-12 10:23:44 INFO [Metrics] AUC-ROC: 0.808950 - AUC-PR: 0.612577 - ACC: 0.793904 - Precision: 0.655678 - Recall: 0.411994 - F1: 0.506027 - MCC: 0.400834 - Logloss: 0.442833 - MSE: 0.143320 - RMSE: 0.378576 - COPC: 0.986635 - KLD: 1.017083
