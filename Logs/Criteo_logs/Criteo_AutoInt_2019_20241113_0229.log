2024-11-13 02:29:19 INFO all args: Namespace(dataset_name='Criteo', dataset_path='/data/ctr/Criteo/Criteo_x4', model_name='AutoInt', model_output_path='../output/', metrics=['AUC-ROC', 'AUC-PR', 'ACC', 'Precision', 'Recall', 'F1', 'MCC', 'Logloss', 'MSE', 'RMSE', 'COPC', 'KLD'], verbose=1, optimizer='adam', loss='binary_crossentropy', epochs=99, batch_size=10000, embedding_dim=16, embedding_dropout=0.0, every_x_epochs=1, learning_rate=0.001, monitor={'AUC-ROC': 1, 'Logloss': -1}, monitor_mode='max', patience=2, regularizer=1e-05, save_best_only=True, seed=2019, task='binary_classification', log_dir='../output/logs/')
2024-11-13 02:29:19 INFO Start process Criteo !
2024-11-13 02:29:19 INFO Loading Criteo dataset
2024-11-13 02:29:19 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/train.h5
2024-11-13 02:29:24 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/valid.h5
2024-11-13 02:29:24 INFO Load h5 data from /data/ctr/Criteo/Criteo_x4/test.h5
2024-11-13 02:29:25 INFO Train samples: total/36672493, pos/9396350, neg/27276143, ratio/25.62%
2024-11-13 02:29:26 INFO Validation samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 02:29:26 INFO Test samples: total/4584062, pos/1174544, neg/3409518, ratio/25.62%
2024-11-13 02:29:26 INFO Loading data done
2024-11-13 02:29:26 INFO Model: AutoInt(
  (embedding_layer): EmbeddingLayer(
    (embedding_layer): ModuleDict(
      (I1): Embedding(43, 16, padding_idx=42)
      (I2): Embedding(98, 16, padding_idx=97)
      (I3): Embedding(121, 16, padding_idx=120)
      (I4): Embedding(40, 16, padding_idx=39)
      (I5): Embedding(219, 16, padding_idx=218)
      (I6): Embedding(111, 16, padding_idx=110)
      (I7): Embedding(79, 16, padding_idx=78)
      (I8): Embedding(68, 16, padding_idx=67)
      (I9): Embedding(91, 16, padding_idx=90)
      (I10): Embedding(5, 16, padding_idx=4)
      (I11): Embedding(26, 16, padding_idx=25)
      (I12): Embedding(36, 16, padding_idx=35)
      (I13): Embedding(71, 16, padding_idx=70)
      (C1): Embedding(1445, 16, padding_idx=1444)
      (C2): Embedding(553, 16, padding_idx=552)
      (C3): Embedding(157338, 16, padding_idx=157337)
      (C4): Embedding(117821, 16, padding_idx=117820)
      (C5): Embedding(305, 16, padding_idx=304)
      (C6): Embedding(17, 16, padding_idx=16)
      (C7): Embedding(11881, 16, padding_idx=11880)
      (C8): Embedding(629, 16, padding_idx=628)
      (C9): Embedding(4, 16, padding_idx=3)
      (C10): Embedding(39529, 16, padding_idx=39528)
      (C11): Embedding(5130, 16, padding_idx=5129)
      (C12): Embedding(156655, 16, padding_idx=156654)
      (C13): Embedding(3175, 16, padding_idx=3174)
      (C14): Embedding(27, 16, padding_idx=26)
      (C15): Embedding(11042, 16, padding_idx=11041)
      (C16): Embedding(148912, 16, padding_idx=148911)
      (C17): Embedding(11, 16, padding_idx=10)
      (C18): Embedding(4559, 16, padding_idx=4558)
      (C19): Embedding(2002, 16, padding_idx=2001)
      (C20): Embedding(4, 16, padding_idx=3)
      (C21): Embedding(154563, 16, padding_idx=154562)
      (C22): Embedding(17, 16, padding_idx=16)
      (C23): Embedding(16, 16, padding_idx=15)
      (C24): Embedding(53030, 16, padding_idx=53029)
      (C25): Embedding(81, 16, padding_idx=80)
      (C26): Embedding(40954, 16, padding_idx=40953)
    )
    (seq_encoder_layer): ModuleDict()
  )
  (self_attention): Sequential(
    (0): MultiHeadSelfAttention(
      (W_q): Linear(in_features=16, out_features=64, bias=False)
      (W_k): Linear(in_features=16, out_features=64, bias=False)
      (W_v): Linear(in_features=16, out_features=64, bias=False)
      (W_res): Linear(in_features=16, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (1): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (2): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (3): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
    (4): MultiHeadSelfAttention(
      (W_q): Linear(in_features=64, out_features=64, bias=False)
      (W_k): Linear(in_features=64, out_features=64, bias=False)
      (W_v): Linear(in_features=64, out_features=64, bias=False)
      (dot_product_attention): ScaledDotProductAttention(
        (softmax): Softmax(dim=2)
      )
    )
  )
  (fc): Linear(in_features=2496, out_features=1, bias=True)
  (final_activation): Sigmoid()
)
2024-11-13 02:29:26 INFO Model parameters: 14627073
2024-11-13 02:29:26 INFO Start training model
2024-11-13 02:29:26 INFO Start training: 3668 batches/epoch
2024-11-13 02:29:26 INFO ************ Epoch=1 start ************
2024-11-13 02:35:59 INFO [Metrics] AUC-ROC: 0.798501 - AUC-PR: 0.596424 - ACC: 0.789185 - Precision: 0.644536 - Recall: 0.395150 - F1: 0.489933 - MCC: 0.383995 - Logloss: 0.452358 - MSE: 0.146682 - RMSE: 0.382991 - COPC: 0.954101 - KLD: 1.041708
2024-11-13 02:35:59 INFO Save best model: monitor(max): 0.346143
2024-11-13 02:35:59 INFO --- 3668/3668 batches finished ---
2024-11-13 02:35:59 INFO Train loss: 0.465118
2024-11-13 02:35:59 INFO ************ Epoch=1 end ************
2024-11-13 02:44:45 INFO [Metrics] AUC-ROC: 0.801420 - AUC-PR: 0.601143 - ACC: 0.790693 - Precision: 0.665349 - Recall: 0.368404 - F1: 0.474228 - MCC: 0.381063 - Logloss: 0.449401 - MSE: 0.145640 - RMSE: 0.381628 - COPC: 0.995038 - KLD: 1.034942
2024-11-13 02:44:45 INFO Save best model: monitor(max): 0.352019
2024-11-13 02:44:45 INFO --- 3668/3668 batches finished ---
2024-11-13 02:44:45 INFO Train loss: 0.459054
2024-11-13 02:44:45 INFO ************ Epoch=2 end ************
2024-11-13 02:54:04 INFO [Metrics] AUC-ROC: 0.803307 - AUC-PR: 0.604003 - ACC: 0.791445 - Precision: 0.664654 - Recall: 0.375496 - F1: 0.479883 - MCC: 0.384907 - Logloss: 0.447821 - MSE: 0.145071 - RMSE: 0.380882 - COPC: 1.024917 - KLD: 1.030492
2024-11-13 02:54:04 INFO Save best model: monitor(max): 0.355486
2024-11-13 02:54:04 INFO --- 3668/3668 batches finished ---
2024-11-13 02:54:04 INFO Train loss: 0.457086
2024-11-13 02:54:04 INFO ************ Epoch=3 end ************
2024-11-13 03:03:41 INFO [Metrics] AUC-ROC: 0.804087 - AUC-PR: 0.605005 - ACC: 0.791572 - Precision: 0.670915 - Recall: 0.366123 - F1: 0.473728 - MCC: 0.382992 - Logloss: 0.447123 - MSE: 0.144854 - RMSE: 0.380597 - COPC: 1.024419 - KLD: 1.028410
2024-11-13 03:03:41 INFO Save best model: monitor(max): 0.356963
2024-11-13 03:03:41 INFO --- 3668/3668 batches finished ---
2024-11-13 03:03:41 INFO Train loss: 0.455870
2024-11-13 03:03:41 INFO ************ Epoch=4 end ************
2024-11-13 03:12:57 INFO [Metrics] AUC-ROC: 0.805055 - AUC-PR: 0.606841 - ACC: 0.792348 - Precision: 0.657346 - Recall: 0.395975 - F1: 0.494232 - MCC: 0.392551 - Logloss: 0.446143 - MSE: 0.144500 - RMSE: 0.380131 - COPC: 0.984763 - KLD: 1.026122
2024-11-13 03:12:57 INFO Save best model: monitor(max): 0.358911
2024-11-13 03:12:57 INFO --- 3668/3668 batches finished ---
2024-11-13 03:12:57 INFO Train loss: 0.455069
2024-11-13 03:12:57 INFO ************ Epoch=5 end ************
2024-11-13 03:22:20 INFO [Metrics] AUC-ROC: 0.805536 - AUC-PR: 0.607730 - ACC: 0.792419 - Precision: 0.674800 - Recall: 0.366441 - F1: 0.474961 - MCC: 0.385480 - Logloss: 0.445804 - MSE: 0.144385 - RMSE: 0.379980 - COPC: 1.026426 - KLD: 1.024950
2024-11-13 03:22:20 INFO Save best model: monitor(max): 0.359731
2024-11-13 03:22:20 INFO --- 3668/3668 batches finished ---
2024-11-13 03:22:21 INFO Train loss: 0.454355
2024-11-13 03:22:21 INFO ************ Epoch=6 end ************
2024-11-13 03:31:53 INFO [Metrics] AUC-ROC: 0.805952 - AUC-PR: 0.608386 - ACC: 0.792616 - Precision: 0.663397 - Recall: 0.386946 - F1: 0.488790 - MCC: 0.390973 - Logloss: 0.445310 - MSE: 0.144186 - RMSE: 0.379718 - COPC: 1.011377 - KLD: 1.024049
2024-11-13 03:31:53 INFO Save best model: monitor(max): 0.360642
2024-11-13 03:31:53 INFO --- 3668/3668 batches finished ---
2024-11-13 03:31:53 INFO Train loss: 0.453744
2024-11-13 03:31:53 INFO ************ Epoch=7 end ************
2024-11-13 03:41:15 INFO [Metrics] AUC-ROC: 0.806326 - AUC-PR: 0.609018 - ACC: 0.792728 - Precision: 0.674132 - Recall: 0.369812 - F1: 0.477615 - MCC: 0.387141 - Logloss: 0.445160 - MSE: 0.144159 - RMSE: 0.379683 - COPC: 1.033545 - KLD: 1.023166
2024-11-13 03:41:15 INFO Save best model: monitor(max): 0.361165
2024-11-13 03:41:15 INFO --- 3668/3668 batches finished ---
2024-11-13 03:41:15 INFO Train loss: 0.453294
2024-11-13 03:41:15 INFO ************ Epoch=8 end ************
2024-11-13 03:50:41 INFO [Metrics] AUC-ROC: 0.806749 - AUC-PR: 0.609641 - ACC: 0.793051 - Precision: 0.658561 - Recall: 0.399370 - F1: 0.497215 - MCC: 0.395302 - Logloss: 0.444573 - MSE: 0.143941 - RMSE: 0.379396 - COPC: 0.992970 - KLD: 1.022075
2024-11-13 03:50:41 INFO Save best model: monitor(max): 0.362176
2024-11-13 03:50:41 INFO --- 3668/3668 batches finished ---
2024-11-13 03:50:41 INFO Train loss: 0.452925
2024-11-13 03:50:41 INFO ************ Epoch=9 end ************
2024-11-13 03:58:48 INFO [Metrics] AUC-ROC: 0.807080 - AUC-PR: 0.610286 - ACC: 0.793130 - Precision: 0.650491 - Recall: 0.416292 - F1: 0.507683 - MCC: 0.399980 - Logloss: 0.444539 - MSE: 0.143942 - RMSE: 0.379397 - COPC: 0.965875 - KLD: 1.021283
2024-11-13 03:58:48 INFO Save best model: monitor(max): 0.362541
2024-11-13 03:58:48 INFO --- 3668/3668 batches finished ---
2024-11-13 03:58:48 INFO Train loss: 0.452612
2024-11-13 03:58:48 INFO ************ Epoch=10 end ************
2024-11-13 04:06:36 INFO [Metrics] AUC-ROC: 0.807378 - AUC-PR: 0.610677 - ACC: 0.793234 - Precision: 0.651300 - Recall: 0.415451 - F1: 0.507304 - MCC: 0.400020 - Logloss: 0.444452 - MSE: 0.143873 - RMSE: 0.379306 - COPC: 0.956712 - KLD: 1.020838
2024-11-13 04:06:36 INFO Save best model: monitor(max): 0.362926
2024-11-13 04:06:36 INFO --- 3668/3668 batches finished ---
2024-11-13 04:06:36 INFO Train loss: 0.452318
2024-11-13 04:06:36 INFO ************ Epoch=11 end ************
2024-11-13 04:14:11 INFO [Metrics] AUC-ROC: 0.807555 - AUC-PR: 0.610929 - ACC: 0.793446 - Precision: 0.670546 - Recall: 0.381087 - F1: 0.485980 - MCC: 0.391822 - Logloss: 0.443880 - MSE: 0.143694 - RMSE: 0.379070 - COPC: 1.015306 - KLD: 1.020117
2024-11-13 04:14:11 INFO Save best model: monitor(max): 0.363674
2024-11-13 04:14:11 INFO --- 3668/3668 batches finished ---
2024-11-13 04:14:11 INFO Train loss: 0.452082
2024-11-13 04:14:11 INFO ************ Epoch=12 end ************
2024-11-13 04:21:47 INFO [Metrics] AUC-ROC: 0.807520 - AUC-PR: 0.610965 - ACC: 0.793495 - Precision: 0.661346 - Recall: 0.397680 - F1: 0.496691 - MCC: 0.396051 - Logloss: 0.443885 - MSE: 0.143688 - RMSE: 0.379061 - COPC: 1.001019 - KLD: 1.020343
2024-11-13 04:21:47 INFO Monitor(max) STOP: 0.363636 !
2024-11-13 04:21:47 INFO Reduce learning rate on plateau: 0.000100
2024-11-13 04:21:47 INFO --- 3668/3668 batches finished ---
2024-11-13 04:21:47 INFO Train loss: 0.451894
2024-11-13 04:21:47 INFO ************ Epoch=13 end ************
2024-11-13 04:29:27 INFO [Metrics] AUC-ROC: 0.810628 - AUC-PR: 0.616045 - ACC: 0.794854 - Precision: 0.662171 - Recall: 0.406984 - F1: 0.504124 - MCC: 0.402033 - Logloss: 0.441011 - MSE: 0.142681 - RMSE: 0.377731 - COPC: 0.994744 - KLD: 1.012734
2024-11-13 04:29:27 INFO Save best model: monitor(max): 0.369617
2024-11-13 04:29:27 INFO --- 3668/3668 batches finished ---
2024-11-13 04:29:27 INFO Train loss: 0.443500
2024-11-13 04:29:27 INFO ************ Epoch=14 end ************
2024-11-13 04:37:12 INFO [Metrics] AUC-ROC: 0.810979 - AUC-PR: 0.616546 - ACC: 0.795065 - Precision: 0.662788 - Recall: 0.407497 - F1: 0.504696 - MCC: 0.402723 - Logloss: 0.440752 - MSE: 0.142578 - RMSE: 0.377596 - COPC: 0.999980 - KLD: 1.012036
2024-11-13 04:37:12 INFO Save best model: monitor(max): 0.370227
2024-11-13 04:37:12 INFO --- 3668/3668 batches finished ---
2024-11-13 04:37:12 INFO Train loss: 0.440029
2024-11-13 04:37:12 INFO ************ Epoch=15 end ************
2024-11-13 04:44:46 INFO [Metrics] AUC-ROC: 0.810897 - AUC-PR: 0.616311 - ACC: 0.794983 - Precision: 0.661556 - Recall: 0.409183 - F1: 0.505628 - MCC: 0.402934 - Logloss: 0.440864 - MSE: 0.142617 - RMSE: 0.377647 - COPC: 0.998427 - KLD: 1.012296
2024-11-13 04:44:46 INFO Monitor(max) STOP: 0.370032 !
2024-11-13 04:44:46 INFO Reduce learning rate on plateau: 0.000010
2024-11-13 04:44:46 INFO --- 3668/3668 batches finished ---
2024-11-13 04:44:46 INFO Train loss: 0.438054
2024-11-13 04:44:46 INFO ************ Epoch=16 end ************
2024-11-13 04:52:44 INFO [Metrics] AUC-ROC: 0.809845 - AUC-PR: 0.614535 - ACC: 0.794444 - Precision: 0.656547 - Recall: 0.414665 - F1: 0.508297 - MCC: 0.402939 - Logloss: 0.442293 - MSE: 0.143051 - RMSE: 0.378221 - COPC: 1.003633 - KLD: 1.016070
2024-11-13 04:52:44 INFO Monitor(max) STOP: 0.367552 !
2024-11-13 04:52:44 INFO Reduce learning rate on plateau: 0.000001
2024-11-13 04:52:44 INFO Early stopping at epoch=17
2024-11-13 04:52:44 INFO --- 3668/3668 batches finished ---
2024-11-13 04:52:44 INFO Train loss: 0.433003
2024-11-13 04:52:44 INFO Training finished.
2024-11-13 04:52:44 INFO Load best model: /root/ctr-benchmark/ctr-metrics-eval/output/AutoInt/Criteo/AutoInt_model_seed2019.ckpt
2024-11-13 04:52:44 INFO Start evaluate model
2024-11-13 04:53:24 INFO [Metrics] AUC-ROC: 0.810979 - AUC-PR: 0.616546 - ACC: 0.795065 - Precision: 0.662788 - Recall: 0.407497 - F1: 0.504696 - MCC: 0.402723 - Logloss: 0.440752 - MSE: 0.142578 - RMSE: 0.377596 - COPC: 0.999980 - KLD: 1.012036
2024-11-13 04:53:24 INFO Start testing model
2024-11-13 04:54:03 INFO [Metrics] AUC-ROC: 0.811510 - AUC-PR: 0.617304 - ACC: 0.795439 - Precision: 0.663343 - Recall: 0.409416 - F1: 0.506326 - MCC: 0.404198 - Logloss: 0.440262 - MSE: 0.142409 - RMSE: 0.377371 - COPC: 0.999531 - KLD: 1.010667
